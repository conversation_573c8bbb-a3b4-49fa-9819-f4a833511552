#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الحذف في نظام تصفية الكاشير
Test for deletion fix in cashier filter system
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import customtkinter as ctk

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'cashier_filter'))

def test_deletion_functionality():
    """اختبار وظيفة الحذف"""
    print("🧪 بدء اختبار وظيفة الحذف...")
    
    try:
        # استيراد الوحدات المطلوبة
        from cashier_filter.ui.daily_filter import DailyFilterWindow
        
        # إنشاء نافذة اختبار
        root = ctk.CTk()
        root.title("اختبار الحذف - نظام تصفية الكاشير")
        root.geometry("800x600")
        
        # إنشاء بيانات تجريبية للتصفية
        test_filter_data = {
            'cashier_name': 'كاشير تجريبي',
            'cashier_id': 'TEST001',
            'date': '2025-01-01'
        }

        # إنشاء نافذة التصفية اليومية
        filter_window = DailyFilterWindow(root, test_filter_data)
        
        # إضافة بعض البيانات التجريبية
        test_data = [
            ("بنك الراجحي", "1001", "100.50"),
            ("بنك الأهلي", "1002", "250.75"),
            ("بنك سامبا", "1003", "175.25")
        ]
        
        # إضافة البيانات إلى جدول البنك
        for data in test_data:
            filter_window.bank_tree.insert("", "end", values=data)
        
        # تحديث المجموع
        filter_window.totals['bank'] = sum(float(data[2]) for data in test_data)
        filter_window.update_bank_total()
        
        print("✅ تم إنشاء البيانات التجريبية بنجاح")
        print(f"   - عدد العناصر: {len(test_data)}")
        print(f"   - المجموع: {filter_window.totals['bank']:.2f} ريال")
        
        # إرشادات للمستخدم
        instructions = """
        📋 تعليمات الاختبار:
        
        1. انقر مرتين على أي عنصر في جدول المقبوضات البنكية لحذفه
        2. أو انقر بالزر الأيمن واختر "حذف العنصر"
        3. تأكد من أن المجموع يتم تحديثه تلقائياً
        4. تأكد من ظهور رسالة التأكيد قبل الحذف
        
        🔍 ما يجب ملاحظته:
        - يجب أن يختفي العنصر من الجدول
        - يجب أن يتم تحديث المجموع الإجمالي
        - يجب أن تظهر رسالة تأكيد قبل الحذف
        """
        
        # إنشاء نافذة التعليمات
        instructions_window = tk.Toplevel(root)
        instructions_window.title("تعليمات الاختبار")
        instructions_window.geometry("500x400")
        
        text_widget = tk.Text(instructions_window, wrap=tk.WORD, font=("Arial", 12))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, instructions)
        text_widget.config(state=tk.DISABLED)
        
        # تشغيل التطبيق
        print("🚀 تشغيل نافذة الاختبار...")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_amount_parsing():
    """اختبار تحليل المبالغ المختلفة"""
    print("\n🧪 اختبار تحليل المبالغ...")
    
    test_amounts = [
        "100.50",
        "100.50 ريال",
        "100,50",
        "1,234.56",
        "1234.56 SAR",
        "$100.50",
        "100",
        "0.50",
        "",
        "abc123.45def"
    ]
    
    import re
    
    for amount_str in test_amounts:
        try:
            # تطبيق نفس المنطق المستخدم في الكود
            cleaned = (str(amount_str).replace("ريال", "")
                                     .replace(",", "")
                                     .replace("$", "")
                                     .replace("SR", "")
                                     .replace("SAR", "")
                                     .strip())
            
            if not cleaned:
                amount = 0.0
            else:
                try:
                    amount = float(cleaned)
                except ValueError:
                    # محاولة أخيرة لاستخراج الأرقام
                    numbers = re.findall(r'\d+\.?\d*', str(amount_str))
                    if numbers:
                        amount = float(numbers[0])
                    else:
                        amount = 0.0
            
            print(f"   '{amount_str}' -> {amount:.2f}")
            
        except Exception as e:
            print(f"   ❌ خطأ في تحليل '{amount_str}': {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار إصلاح مشكلة الحذف")
    print("=" * 50)
    
    # اختبار تحليل المبالغ
    test_amount_parsing()
    
    # اختبار وظيفة الحذف
    print("\n" + "=" * 50)
    test_deletion_functionality()
