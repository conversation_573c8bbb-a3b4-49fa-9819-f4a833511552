@echo off
chcp 65001 > nul
title خادم تقارير نظام تصفية الكاشير

echo.
echo ========================================
echo 🌐 خادم تقارير نظام تصفية الكاشير
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo    يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python متاح

echo.
echo 🔍 التحقق من Flask...
python -c "import flask" > nul 2>&1
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    echo 📦 جاري تثبيت Flask...
    pip install Flask
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)

echo ✅ Flask متاح

echo.
echo 🔍 التحقق من قاعدة البيانات...
if not exist "db\cashier_filter.db" (
    echo ❌ قاعدة البيانات غير موجودة
    echo    يرجى تشغيل التطبيق الرئيسي أولاً
    pause
    exit /b 1
)

echo ✅ قاعدة البيانات موجودة

echo.
echo 🚀 بدء تشغيل الخادم...
echo.

python start_web_server.py

echo.
echo 👋 تم إنهاء الخادم
pause
