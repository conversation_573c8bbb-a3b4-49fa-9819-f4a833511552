# نظام جدولة المزامنة السحابية المتقدم
import customtkinter as ctk
from tkinter import ttk, messagebox
import json
import os
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path

class CloudSchedulerWindow(ctk.CTkToplevel):
    """نافذة جدولة المزامنة السحابية المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.title("⏰ جدولة المزامنة السحابية المتقدمة")
        self.geometry("800x600")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)
        
        # متغيرات الجدولة
        self.schedule_settings = {
            "auto_sync_enabled": True,
            "sync_interval": 30,  # دقائق
            "daily_sync_time": "02:00",
            "weekly_sync_day": "الأحد",
            "monthly_sync_date": 1,
            "backup_schedule": "يومي",
            "cleanup_old_backups": True,
            "max_backup_age": 30,  # أيام
            "sync_on_startup": True,
            "sync_on_shutdown": True
        }
        
        self.create_widgets()
        self.load_schedule_settings()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        self.create_header()
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f0f2f5", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إنشاء التبويبات
        self.create_tabs(main_container)

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="⏰ جدولة المزامنة السحابية المتقدمة",
            font=("Arial", 20, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب الجدولة التلقائية
        self.auto_sync_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.auto_sync_frame, text="🔄 المزامنة التلقائية")
        self.create_auto_sync_tab()
        
        # تبويب الجدولة المتقدمة
        self.advanced_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.advanced_frame, text="⚙️ الجدولة المتقدمة")
        self.create_advanced_tab()
        
        # تبويب إدارة النسخ الاحتياطية
        self.backup_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.backup_frame, text="💾 إدارة النسخ الاحتياطية")
        self.create_backup_tab()
        
        # تبويب سجل الجدولة
        self.log_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.log_frame, text="📋 سجل الجدولة")
        self.create_log_tab()

    def create_auto_sync_tab(self):
        """إنشاء تبويب المزامنة التلقائية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.auto_sync_frame,
            text="🔄 إعدادات المزامنة التلقائية",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self.auto_sync_frame, fg_color="#f8fafc", corner_radius=15)
        settings_frame.pack(fill="x", padx=20, pady=10)
        
        # تفعيل المزامنة التلقائية
        auto_sync_container = ctk.CTkFrame(settings_frame, fg_color="transparent")
        auto_sync_container.pack(fill="x", padx=20, pady=15)
        
        self.auto_sync_var = ctk.BooleanVar(value=self.schedule_settings["auto_sync_enabled"])
        auto_sync_checkbox = ctk.CTkCheckBox(
            auto_sync_container,
            text="تفعيل المزامنة التلقائية",
            variable=self.auto_sync_var,
            command=self.update_schedule_settings,
            font=("Arial", 14, "bold")
        )
        auto_sync_checkbox.pack(anchor="w")
        
        # فترة المزامنة
        interval_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        interval_frame.pack(fill="x", padx=20, pady=10)
        
        interval_label = ctk.CTkLabel(
            interval_frame,
            text="فترة المزامنة (دقائق):",
            font=("Arial", 12)
        )
        interval_label.pack(side="left")
        
        self.interval_var = ctk.StringVar(value=str(self.schedule_settings["sync_interval"]))
        interval_entry = ctk.CTkEntry(
            interval_frame,
            textvariable=self.interval_var,
            width=100
        )
        interval_entry.pack(side="left", padx=10)
        interval_entry.bind('<KeyRelease>', lambda e: self.update_schedule_settings())
        
        # مزامنة عند البدء
        startup_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        startup_frame.pack(fill="x", padx=20, pady=5)
        
        self.startup_sync_var = ctk.BooleanVar(value=self.schedule_settings["sync_on_startup"])
        startup_checkbox = ctk.CTkCheckBox(
            startup_frame,
            text="مزامنة عند بدء التطبيق",
            variable=self.startup_sync_var,
            command=self.update_schedule_settings,
            font=("Arial", 12)
        )
        startup_checkbox.pack(anchor="w")
        
        # مزامنة عند الإغلاق
        shutdown_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        shutdown_frame.pack(fill="x", padx=20, pady=5)
        
        self.shutdown_sync_var = ctk.BooleanVar(value=self.schedule_settings["sync_on_shutdown"])
        shutdown_checkbox = ctk.CTkCheckBox(
            shutdown_frame,
            text="مزامنة عند إغلاق التطبيق",
            variable=self.shutdown_sync_var,
            command=self.update_schedule_settings,
            font=("Arial", 12)
        )
        shutdown_checkbox.pack(anchor="w")
        
        # حالة المزامنة الحالية
        status_frame = ctk.CTkFrame(self.auto_sync_frame, fg_color="#f8fafc", corner_radius=15)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 حالة المزامنة الحالية",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        status_title.pack(pady=15)
        
        # معلومات الحالة
        self.create_status_info(status_frame)

    def create_advanced_tab(self):
        """إنشاء تبويب الجدولة المتقدمة"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.advanced_frame,
            text="⚙️ إعدادات الجدولة المتقدمة",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الجدولة اليومية
        daily_frame = ctk.CTkFrame(self.advanced_frame, fg_color="#f8fafc", corner_radius=15)
        daily_frame.pack(fill="x", padx=20, pady=10)
        
        daily_title = ctk.CTkLabel(
            daily_frame,
            text="📅 الجدولة اليومية",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        daily_title.pack(pady=15)
        
        # وقت المزامنة اليومية
        time_frame = ctk.CTkFrame(daily_frame, fg_color="transparent")
        time_frame.pack(fill="x", padx=20, pady=10)
        
        time_label = ctk.CTkLabel(
            time_frame,
            text="وقت المزامنة اليومية:",
            font=("Arial", 12)
        )
        time_label.pack(side="left")
        
        self.daily_time_var = ctk.StringVar(value=self.schedule_settings["daily_sync_time"])
        time_entry = ctk.CTkEntry(
            time_frame,
            textvariable=self.daily_time_var,
            width=100,
            placeholder_text="HH:MM"
        )
        time_entry.pack(side="left", padx=10)
        time_entry.bind('<KeyRelease>', lambda e: self.update_schedule_settings())
        
        # إطار الجدولة الأسبوعية
        weekly_frame = ctk.CTkFrame(self.advanced_frame, fg_color="#f8fafc", corner_radius=15)
        weekly_frame.pack(fill="x", padx=20, pady=10)
        
        weekly_title = ctk.CTkLabel(
            weekly_frame,
            text="📆 الجدولة الأسبوعية",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        weekly_title.pack(pady=15)
        
        # يوم المزامنة الأسبوعية
        day_frame = ctk.CTkFrame(weekly_frame, fg_color="transparent")
        day_frame.pack(fill="x", padx=20, pady=10)
        
        day_label = ctk.CTkLabel(
            day_frame,
            text="يوم المزامنة الأسبوعية:",
            font=("Arial", 12)
        )
        day_label.pack(side="left")
        
        days = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
        self.weekly_day_menu = ctk.CTkOptionMenu(
            day_frame,
            values=days,
            command=self.update_schedule_settings,
            width=150
        )
        self.weekly_day_menu.pack(side="left", padx=10)
        self.weekly_day_menu.set(self.schedule_settings["weekly_sync_day"])

    def create_backup_tab(self):
        """إنشاء تبويب إدارة النسخ الاحتياطية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.backup_frame,
            text="💾 إعدادات النسخ الاحتياطية",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار جدولة النسخ الاحتياطية
        schedule_frame = ctk.CTkFrame(self.backup_frame, fg_color="#f8fafc", corner_radius=15)
        schedule_frame.pack(fill="x", padx=20, pady=10)
        
        schedule_title = ctk.CTkLabel(
            schedule_frame,
            text="📋 جدولة النسخ الاحتياطية",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        schedule_title.pack(pady=15)
        
        # تكرار النسخ الاحتياطية
        frequency_frame = ctk.CTkFrame(schedule_frame, fg_color="transparent")
        frequency_frame.pack(fill="x", padx=20, pady=10)
        
        frequency_label = ctk.CTkLabel(
            frequency_frame,
            text="تكرار النسخ الاحتياطية:",
            font=("Arial", 12)
        )
        frequency_label.pack(side="left")
        
        frequencies = ["يومي", "أسبوعي", "شهري", "عند كل مزامنة"]
        self.backup_frequency_menu = ctk.CTkOptionMenu(
            frequency_frame,
            values=frequencies,
            command=self.update_schedule_settings,
            width=150
        )
        self.backup_frequency_menu.pack(side="left", padx=10)
        self.backup_frequency_menu.set(self.schedule_settings["backup_schedule"])
        
        # إطار تنظيف النسخ القديمة
        cleanup_frame = ctk.CTkFrame(self.backup_frame, fg_color="#f8fafc", corner_radius=15)
        cleanup_frame.pack(fill="x", padx=20, pady=10)
        
        cleanup_title = ctk.CTkLabel(
            cleanup_frame,
            text="🧹 تنظيف النسخ القديمة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        cleanup_title.pack(pady=15)
        
        # تفعيل التنظيف التلقائي
        auto_cleanup_frame = ctk.CTkFrame(cleanup_frame, fg_color="transparent")
        auto_cleanup_frame.pack(fill="x", padx=20, pady=10)
        
        self.cleanup_var = ctk.BooleanVar(value=self.schedule_settings["cleanup_old_backups"])
        cleanup_checkbox = ctk.CTkCheckBox(
            auto_cleanup_frame,
            text="تنظيف النسخ القديمة تلقائياً",
            variable=self.cleanup_var,
            command=self.update_schedule_settings,
            font=("Arial", 12)
        )
        cleanup_checkbox.pack(anchor="w")
        
        # عمر النسخ الاحتياطية
        age_frame = ctk.CTkFrame(cleanup_frame, fg_color="transparent")
        age_frame.pack(fill="x", padx=20, pady=10)
        
        age_label = ctk.CTkLabel(
            age_frame,
            text="الاحتفاظ بالنسخ لمدة (أيام):",
            font=("Arial", 12)
        )
        age_label.pack(side="left")
        
        self.backup_age_var = ctk.StringVar(value=str(self.schedule_settings["max_backup_age"]))
        age_entry = ctk.CTkEntry(
            age_frame,
            textvariable=self.backup_age_var,
            width=100
        )
        age_entry.pack(side="left", padx=10)
        age_entry.bind('<KeyRelease>', lambda e: self.update_schedule_settings())

    def create_log_tab(self):
        """إنشاء تبويب سجل الجدولة"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.log_frame,
            text="📋 سجل عمليات الجدولة",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # جدول السجل
        log_frame = ctk.CTkFrame(self.log_frame, fg_color="#f8fafc", corner_radius=15)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # أعمدة الجدول
        columns = ("الوقت", "النوع", "الحالة", "التفاصيل")
        self.log_tree = ttk.Treeview(log_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.log_tree.heading(col, text=col)
            self.log_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_tree.yview)
        self.log_tree.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        log_scrollbar.pack(side="right", fill="y", pady=10)
        
        # تحميل السجل
        self.load_schedule_log()

    def create_status_info(self, parent):
        """إنشاء معلومات الحالة"""
        status_container = ctk.CTkFrame(parent, fg_color="transparent")
        status_container.pack(fill="x", padx=20, pady=10)
        
        # آخر مزامنة
        last_sync_label = ctk.CTkLabel(
            status_container,
            text=f"📅 آخر مزامنة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            font=("Arial", 12),
            text_color="#374151"
        )
        last_sync_label.pack(anchor="w", pady=2)
        
        # المزامنة التالية
        next_sync = datetime.now() + timedelta(minutes=self.schedule_settings["sync_interval"])
        next_sync_label = ctk.CTkLabel(
            status_container,
            text=f"⏰ المزامنة التالية: {next_sync.strftime('%Y-%m-%d %H:%M:%S')}",
            font=("Arial", 12),
            text_color="#374151"
        )
        next_sync_label.pack(anchor="w", pady=2)
        
        # حالة الجدولة
        status_label = ctk.CTkLabel(
            status_container,
            text="✅ الجدولة نشطة",
            font=("Arial", 12),
            text_color="#10b981"
        )
        status_label.pack(anchor="w", pady=2)

    def load_schedule_settings(self):
        """تحميل إعدادات الجدولة"""
        try:
            settings_file = Path("cloud_schedule_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.schedule_settings.update(settings)
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الجدولة: {e}")

    def save_schedule_settings(self):
        """حفظ إعدادات الجدولة"""
        try:
            with open("cloud_schedule_settings.json", 'w', encoding='utf-8') as f:
                json.dump(self.schedule_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الجدولة: {e}")

    def update_schedule_settings(self, *args):
        """تحديث إعدادات الجدولة"""
        try:
            self.schedule_settings['auto_sync_enabled'] = self.auto_sync_var.get()
            self.schedule_settings['sync_on_startup'] = self.startup_sync_var.get()
            self.schedule_settings['sync_on_shutdown'] = self.shutdown_sync_var.get()
            self.schedule_settings['cleanup_old_backups'] = self.cleanup_var.get()
            
            # تحديث القيم النصية
            try:
                self.schedule_settings['sync_interval'] = int(self.interval_var.get())
            except ValueError:
                pass
            
            try:
                self.schedule_settings['max_backup_age'] = int(self.backup_age_var.get())
            except ValueError:
                pass
            
            self.schedule_settings['daily_sync_time'] = self.daily_time_var.get()
            
            self.save_schedule_settings()
            
        except Exception as e:
            print(f"خطأ في تحديث إعدادات الجدولة: {e}")

    def load_schedule_log(self):
        """تحميل سجل الجدولة"""
        try:
            # إضافة سجلات وهمية للعرض
            sample_logs = [
                (datetime.now().strftime('%H:%M:%S'), "مزامنة تلقائية", "نجح", "تم مزامنة 15 ملف"),
                ((datetime.now() - timedelta(minutes=30)).strftime('%H:%M:%S'), "نسخ احتياطي", "نجح", "تم إنشاء نسخة احتياطية"),
                ((datetime.now() - timedelta(hours=1)).strftime('%H:%M:%S'), "مزامنة تلقائية", "نجح", "تم مزامنة 8 ملفات"),
                ((datetime.now() - timedelta(hours=2)).strftime('%H:%M:%S'), "تنظيف", "نجح", "تم حذف 3 نسخ قديمة"),
                ((datetime.now() - timedelta(hours=3)).strftime('%H:%M:%S'), "مزامنة تلقائية", "فشل", "خطأ في الاتصال"),
            ]
            
            for log_entry in sample_logs:
                self.log_tree.insert("", "end", values=log_entry)
                
        except Exception as e:
            print(f"خطأ في تحميل سجل الجدولة: {e}")


# دالة لفتح نافذة جدولة المزامنة
def open_cloud_scheduler(parent=None):
    """فتح نافذة جدولة المزامنة السحابية"""
    try:
        window = CloudSchedulerWindow(parent)
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة جدولة المزامنة: {e}")
        if parent:
            messagebox.showerror("خطأ", f"فشل فتح نافذة جدولة المزامنة: {e}")
        return None


if __name__ == "__main__":
    # اختبار النافذة
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = CloudSchedulerWindow()
    app.mainloop()
