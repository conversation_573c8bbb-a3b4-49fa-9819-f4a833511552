@echo off
chcp 65001 > nul
title نظام تصفية الكاشير

echo ========================================
echo    🏪 نظام تصفية الكاشير - إصدار محسن
echo ========================================
echo.

echo 🔄 جاري تشغيل النظام...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المتطلبات
echo 📦 التحقق من المتطلبات...
pip show customtkinter > nul 2>&1
if errorlevel 1 (
    echo 🔧 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

REM تهيئة قاعدة البيانات
echo 🗄️ تهيئة قاعدة البيانات...
python db/init_db.py

REM تشغيل التطبيق
echo ✅ تشغيل التطبيق...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    pause
)
