#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات إدارة الذاكرة - نظام تصفية الكاشير
Memory Management Improvements Test - Cashier Filter System
"""

import sys
import os
import gc
import time
import psutil
import threading
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent / "cashier_filter"
sys.path.insert(0, str(project_root))

def test_garbage_collection():
    """اختبار تحسينات garbage collection"""
    print("🧹 اختبار تحسينات Garbage Collection...")
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024
    
    print(f"  📊 الذاكرة الأولية: {initial_memory:.2f} MB")
    
    # إنشاء بيانات كبيرة
    large_data = []
    for i in range(10000):
        large_data.append({
            'id': i,
            'data': 'x' * 1000,  # 1KB per item
            'nested': {'value': i * 2, 'text': 'test' * 100}
        })
    
    after_creation = process.memory_info().rss / 1024 / 1024
    memory_increase = after_creation - initial_memory
    
    print(f"  📊 الذاكرة بعد إنشاء البيانات: {after_creation:.2f} MB")
    print(f"  📈 زيادة الذاكرة: {memory_increase:.2f} MB")
    
    # حذف البيانات
    del large_data
    
    # اختبار garbage collection المحسن
    start_time = time.time()
    
    # تشغيل garbage collection متعدد المراحل
    for generation in range(3):
        collected = gc.collect(generation)
        print(f"    🗑️ المرحلة {generation}: تم تنظيف {collected} كائن")
    
    gc_time = (time.time() - start_time) * 1000
    
    # قياس الذاكرة بعد التنظيف
    time.sleep(1)  # انتظار قصير
    after_cleanup = process.memory_info().rss / 1024 / 1024
    memory_recovered = after_creation - after_cleanup
    recovery_rate = (memory_recovered / memory_increase) * 100 if memory_increase > 0 else 0
    
    print(f"  📊 الذاكرة بعد التنظيف: {after_cleanup:.2f} MB")
    print(f"  ♻️ الذاكرة المستردة: {memory_recovered:.2f} MB")
    print(f"  📈 معدل الاسترداد: {recovery_rate:.1f}%")
    print(f"  ⏱️ وقت التنظيف: {gc_time:.2f} مللي ثانية")
    
    # تقييم الأداء
    if recovery_rate > 80 and gc_time < 100:
        print("  ✅ أداء Garbage Collection ممتاز")
        return True
    elif recovery_rate > 60 and gc_time < 200:
        print("  ⚠️ أداء Garbage Collection مقبول")
        return True
    else:
        print("  ❌ أداء Garbage Collection ضعيف")
        return False

def test_database_pool():
    """اختبار مجموعة اتصالات قاعدة البيانات المحسنة"""
    print("\n🗄️ اختبار مجموعة اتصالات قاعدة البيانات...")
    
    try:
        from utils.performance import db_pool
        
        # اختبار الحصول على اتصالات متعددة
        connections = []
        start_time = time.time()
        
        for i in range(10):
            conn = db_pool.get_connection()
            connections.append(conn)
            
            # اختبار الاتصال
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM filters")
            result = cursor.fetchone()
            cursor.close()
        
        connection_time = (time.time() - start_time) * 1000
        
        print(f"  ⚡ وقت إنشاء 10 اتصالات: {connection_time:.2f} مللي ثانية")
        
        # إرجاع الاتصالات
        start_time = time.time()
        for conn in connections:
            db_pool.return_connection(conn)
        
        return_time = (time.time() - start_time) * 1000
        print(f"  ⚡ وقت إرجاع الاتصالات: {return_time:.2f} مللي ثانية")
        
        # اختبار الإحصائيات
        stats = db_pool.get_stats()
        print(f"  📊 الاتصالات النشطة: {stats['active_connections']}")
        print(f"  📊 الحد الأقصى: {stats['max_connections']}")
        
        # تقييم الأداء
        if connection_time < 500 and return_time < 100:
            print("  ✅ أداء مجموعة الاتصالات ممتاز")
            return True
        elif connection_time < 1000 and return_time < 200:
            print("  ⚠️ أداء مجموعة الاتصالات مقبول")
            return True
        else:
            print("  ❌ أداء مجموعة الاتصالات ضعيف")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار مجموعة الاتصالات: {e}")
        return False

def test_cache_manager():
    """اختبار مدير التخزين المؤقت المحسن"""
    print("\n💾 اختبار مدير التخزين المؤقت...")
    
    try:
        from utils.performance import cache_manager
        
        # اختبار التخزين والاسترجاع
        start_time = time.time()
        
        # تخزين بيانات متعددة
        for i in range(1000):
            key = f"test_key_{i}"
            value = {'id': i, 'data': f'test_data_{i}' * 10}
            cache_manager.set(key, value)
        
        storage_time = (time.time() - start_time) * 1000
        
        # اختبار الاسترجاع
        start_time = time.time()
        hits = 0
        
        for i in range(1000):
            key = f"test_key_{i}"
            value = cache_manager.get(key)
            if value:
                hits += 1
        
        retrieval_time = (time.time() - start_time) * 1000
        hit_rate = (hits / 1000) * 100
        
        print(f"  ⚡ وقت التخزين: {storage_time:.2f} مللي ثانية")
        print(f"  ⚡ وقت الاسترجاع: {retrieval_time:.2f} مللي ثانية")
        print(f"  📈 معدل النجاح: {hit_rate:.1f}%")
        
        # اختبار الإحصائيات
        stats = cache_manager.get_stats()
        print(f"  📊 حجم التخزين المؤقت: {stats['cache_size']}")
        print(f"  📊 الحد الأقصى: {stats['max_size']}")
        
        # تنظيف التخزين المؤقت
        cache_manager.clear()
        
        # تقييم الأداء
        if storage_time < 200 and retrieval_time < 100 and hit_rate > 95:
            print("  ✅ أداء التخزين المؤقت ممتاز")
            return True
        elif storage_time < 500 and retrieval_time < 200 and hit_rate > 90:
            print("  ⚠️ أداء التخزين المؤقت مقبول")
            return True
        else:
            print("  ❌ أداء التخزين المؤقت ضعيف")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار التخزين المؤقت: {e}")
        return False

def test_memory_monitoring():
    """اختبار مراقبة الذاكرة المستمرة"""
    print("\n📊 اختبار مراقبة الذاكرة المستمرة...")
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024
    peak_memory = initial_memory
    
    def memory_monitor():
        nonlocal peak_memory
        for _ in range(50):  # مراقبة لمدة 5 ثوان
            current_memory = process.memory_info().rss / 1024 / 1024
            if current_memory > peak_memory:
                peak_memory = current_memory
            time.sleep(0.1)
    
    # بدء المراقبة في خيط منفصل
    monitor_thread = threading.Thread(target=memory_monitor)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # محاكاة عمليات مكثفة
    for i in range(5):
        # إنشاء بيانات مؤقتة
        temp_data = [{'id': j, 'data': 'x' * 500} for j in range(1000)]
        
        # معالجة البيانات
        processed = [item['id'] * 2 for item in temp_data]
        
        # تنظيف فوري
        del temp_data, processed
        gc.collect()
        
        time.sleep(0.5)
    
    # انتظار انتهاء المراقبة
    monitor_thread.join()
    
    final_memory = process.memory_info().rss / 1024 / 1024
    memory_growth = final_memory - initial_memory
    peak_increase = peak_memory - initial_memory
    
    print(f"  📊 الذاكرة الأولية: {initial_memory:.2f} MB")
    print(f"  📊 ذروة الاستخدام: {peak_memory:.2f} MB")
    print(f"  📊 الذاكرة النهائية: {final_memory:.2f} MB")
    print(f"  📈 نمو الذاكرة: {memory_growth:.2f} MB")
    print(f"  📈 ذروة الزيادة: {peak_increase:.2f} MB")
    
    # تقييم إدارة الذاكرة
    if memory_growth < 5 and peak_increase < 20:
        print("  ✅ إدارة الذاكرة ممتازة")
        return True
    elif memory_growth < 10 and peak_increase < 50:
        print("  ⚠️ إدارة الذاكرة مقبولة")
        return True
    else:
        print("  ❌ مشكلة في إدارة الذاكرة")
        return False

def run_memory_tests():
    """تشغيل جميع اختبارات الذاكرة"""
    print("🧪 بدء اختبارات تحسينات إدارة الذاكرة...")
    print("=" * 60)
    
    tests = [
        ("Garbage Collection", test_garbage_collection),
        ("مجموعة اتصالات قاعدة البيانات", test_database_pool),
        ("مدير التخزين المؤقت", test_cache_manager),
        ("مراقبة الذاكرة المستمرة", test_memory_monitoring)
    ]
    
    results = []
    overall_start = time.time()
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    overall_time = time.time() - overall_start
    
    # ملخص النتائج
    print("\n📋 ملخص نتائج اختبارات الذاكرة:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ ممتاز" if result else "❌ يحتاج تحسين"
        print(f"  {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 النتيجة النهائية:")
    print(f"  ✅ نجح: {passed}")
    print(f"  ❌ فشل: {failed}")
    print(f"  ⏱️ إجمالي وقت الاختبار: {overall_time:.2f} ثانية")
    print(f"  📈 معدل النجاح: {(passed / len(results)) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع تحسينات الذاكرة تعمل بشكل ممتاز!")
        print("💪 النظام محسن بالكامل")
    elif failed <= 1:
        print(f"\n⚠️ {failed} اختبار يحتاج تحسين إضافي")
        print("🔧 التحسينات فعالة بشكل عام")
    else:
        print(f"\n❌ {failed} اختبار فشل")
        print("🚨 التحسينات تحتاج مراجعة")

if __name__ == "__main__":
    run_memory_tests()
