#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة التكامل السحابي
Test Cloud Integration Window
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cloud_integration():
    """اختبار نافذة التكامل السحابي"""
    try:
        print("🧪 اختبار نافذة التكامل السحابي...")
        
        # تهيئة customtkinter
        import customtkinter as ctk
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء نافذة رئيسية مخفية
        root = ctk.CTk()
        root.withdraw()
        
        # فتح نافذة التكامل السحابي المبسطة
        from ui.cloud_integration_simple import CloudIntegrationSimpleWindow

        print("✅ تم تحميل الوحدة بنجاح")

        # إنشاء النافذة
        cloud_window = CloudIntegrationSimpleWindow()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("🚀 تشغيل النافذة...")
        
        # تشغيل النافذة
        cloud_window.mainloop()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install customtkinter")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cloud_integration()
