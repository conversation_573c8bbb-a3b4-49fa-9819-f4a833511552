#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقارير التفاعلية
Test Interactive Reports
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    import customtkinter as ctk
    from ui.interactive_reports import InteractiveReportsWindow
    import sqlite3
    import json
    from datetime import datetime
    
    def test_database_connection():
        """اختبار الاتصال بقاعدة البيانات"""
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        
        db_path = BASE_DIR / "db" / "cashier_filter.db"
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # اختبار الاستعلام المستخدم في التقارير التفاعلية
            cursor.execute("""
                SELECT f.date, f.data, COALESCE(c.name, f.admin_name, 'غير محدد') as cashier_name
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                ORDER BY f.date DESC
                LIMIT 10
            """)
            
            results = cursor.fetchall()
            print(f"✅ تم جلب {len(results)} تصفية بنجاح")
            
            for i, (date, data, cashier) in enumerate(results[:3], 1):
                print(f"  {i}. التاريخ: {date} | الكاشير: {cashier}")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def test_interactive_reports_window():
        """اختبار نافذة التقارير التفاعلية"""
        print("\n🖥️ اختبار نافذة التقارير التفاعلية...")
        
        try:
            # إنشاء التطبيق الرئيسي
            ctk.set_appearance_mode("dark")
            ctk.set_default_color_theme("blue")
            
            root = ctk.CTk()
            root.withdraw()  # إخفاء النافذة الرئيسية
            
            # إنشاء نافذة التقارير التفاعلية
            reports_window = InteractiveReportsWindow(root)
            
            print("✅ تم إنشاء نافذة التقارير التفاعلية بنجاح")
            
            # اختبار تحميل البيانات
            reports_window.load_data()
            print(f"✅ تم تحميل {len(reports_window.filters_data)} تصفية")
            
            # عرض النافذة لمدة قصيرة للاختبار
            reports_window.after(3000, lambda: reports_window.destroy())
            reports_window.after(3100, lambda: root.quit())
            
            root.mainloop()
            
            print("✅ اختبار النافذة مكتمل بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار النافذة: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def main():
        """تشغيل جميع الاختبارات"""
        print("🧪 اختبار التقارير التفاعلية")
        print("=" * 50)
        
        # اختبار قاعدة البيانات
        db_test = test_database_connection()
        
        if db_test:
            # اختبار النافذة
            window_test = test_interactive_reports_window()
            
            if window_test:
                print("\n🎉 جميع الاختبارات نجحت!")
                print("✅ التقارير التفاعلية جاهزة للاستخدام")
            else:
                print("\n❌ فشل في اختبار النافذة")
        else:
            print("\n❌ فشل في اختبار قاعدة البيانات")
        
        print("=" * 50)

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت المتطلبات:")
    print("pip install customtkinter")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
