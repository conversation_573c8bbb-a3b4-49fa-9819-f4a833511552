# مدير الخطوط المركزي لضمان استقرار أحجام الخطوط
import json
import os
from typing import Tuple, Union

class FontManager:
    """مدير الخطوط المركزي لضمان استقرار أحجام الخطوط في جميع أنحاء التطبيق"""
    
    def __init__(self):
        self.min_font_size = 12
        self.max_font_size = 18
        self.default_font_size = 14
        self.default_font_family = "Arial"
        
        # أحجام خطوط مختلفة للعناصر المختلفة
        self.font_sizes = {
            "small": 12,      # للنصوص الصغيرة
            "normal": 14,     # للنصوص العادية
            "medium": 16,     # للعناوين الفرعية
            "large": 18,      # للعناوين الرئيسية
            "button": 16,     # للأزرار
            "title": 20       # للعناوين الكبيرة
        }
    
    def ensure_stable_size(self, font_size: Union[int, float, str]) -> int:
        """ضمان أن حجم الخط ضمن الحدود المسموحة"""
        try:
            size = int(float(font_size)) if font_size else self.default_font_size
            return max(min(size, self.max_font_size), self.min_font_size)
        except (ValueError, TypeError):
            return self.default_font_size
    
    def get_font(self, size_type: str = "normal", weight: str = "normal") -> Tuple[str, int, str]:
        """الحصول على خط مع حجم مستقر"""
        base_size = self.font_sizes.get(size_type, self.default_font_size)
        stable_size = self.ensure_stable_size(base_size)
        return (self.default_font_family, stable_size, weight)
    
    def get_button_font(self, base_size: int = None) -> Tuple[str, int, str]:
        """الحصول على خط مناسب للأزرار"""
        if base_size:
            button_size = self.ensure_stable_size(base_size + 2)
        else:
            button_size = self.font_sizes["button"]
        return (self.default_font_family, button_size, "bold")
    
    def get_title_font(self, base_size: int = None) -> Tuple[str, int, str]:
        """الحصول على خط مناسب للعناوين"""
        if base_size:
            title_size = self.ensure_stable_size(base_size + 4)
        else:
            title_size = self.font_sizes["title"]
        return (self.default_font_family, title_size, "bold")
    
    def update_font_sizes(self, base_size: int):
        """تحديث جميع أحجام الخطوط بناءً على حجم أساسي"""
        stable_base = self.ensure_stable_size(base_size)
        
        self.font_sizes.update({
            "small": max(stable_base - 2, self.min_font_size),
            "normal": stable_base,
            "medium": min(stable_base + 2, self.max_font_size),
            "large": min(stable_base + 4, self.max_font_size),
            "button": min(stable_base + 2, self.max_font_size),
            "title": min(stable_base + 6, self.max_font_size)
        })
    
    def load_from_settings(self, settings_file: str = "settings.json"):
        """تحميل إعدادات الخط من ملف الإعدادات"""
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    ui_settings = settings.get("ui", {})
                    base_size = ui_settings.get("font_size", self.default_font_size)
                    self.update_font_sizes(base_size)
                    return True
        except Exception as e:
            print(f"خطأ في تحميل إعدادات الخط: {e}")
        return False
    
    def save_to_settings(self, font_size: int, settings_file: str = "settings.json"):
        """حفظ حجم الخط في ملف الإعدادات"""
        try:
            stable_size = self.ensure_stable_size(font_size)
            
            # تحميل الإعدادات الحالية
            settings = {}
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            
            # تحديث حجم الخط
            if "ui" not in settings:
                settings["ui"] = {}
            settings["ui"]["font_size"] = stable_size
            
            # حفظ الإعدادات
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            
            # تحديث أحجام الخطوط الداخلية
            self.update_font_sizes(stable_size)
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الخط: {e}")
            return False
    
    def get_current_base_size(self) -> int:
        """الحصول على حجم الخط الأساسي الحالي"""
        return self.font_sizes["normal"]

# إنشاء مثيل مشترك من مدير الخطوط
font_manager = FontManager()

# دوال مساعدة للاستخدام السريع
def get_stable_font(size_type: str = "normal", weight: str = "normal") -> Tuple[str, int, str]:
    """الحصول على خط مستقر"""
    return font_manager.get_font(size_type, weight)

def get_stable_font_size(font_size: Union[int, float, str]) -> int:
    """الحصول على حجم خط مستقر"""
    return font_manager.ensure_stable_size(font_size)

def update_all_font_sizes(base_size: int):
    """تحديث جميع أحجام الخطوط"""
    font_manager.update_font_sizes(base_size)

def load_font_settings():
    """تحميل إعدادات الخط"""
    return font_manager.load_from_settings()

def save_font_settings(font_size: int):
    """حفظ إعدادات الخط"""
    return font_manager.save_to_settings(font_size)

# تحميل الإعدادات عند استيراد الوحدة
load_font_settings()
