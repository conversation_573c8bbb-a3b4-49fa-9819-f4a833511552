#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller Utilities for Cashier Filter System
أدوات PyInstaller لنظام تصفية الكاشير

This module provides utilities for handling file paths and resources
when the application is packaged with PyInstaller.
"""

import sys
import os
from pathlib import Path
import tempfile
import shutil

def is_frozen():
    """Check if running in PyInstaller bundle"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def get_base_path():
    """Get the base path for the application"""
    if is_frozen():
        return sys._MEIPASS
    else:
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and PyInstaller"""
    base_path = get_base_path()
    return os.path.join(base_path, relative_path)

def get_data_path(relative_path):
    """Get path for writable data files (outside the bundle)"""
    if is_frozen():
        # For frozen apps, use a writable directory
        app_data = os.path.join(os.path.expanduser("~"), "CashierFilterSystem")
        os.makedirs(app_data, exist_ok=True)
        return os.path.join(app_data, relative_path)
    else:
        # For development, use the project directory
        return os.path.join(get_base_path(), relative_path)

def ensure_writable_database():
    """Ensure database is in a writable location"""
    db_name = "cashier_filter.db"
    
    if is_frozen():
        # Copy database from bundle to writable location if it doesn't exist
        bundle_db = get_resource_path(os.path.join("db", db_name))
        writable_db = get_data_path(os.path.join("db", db_name))
        
        # Create db directory in writable location
        os.makedirs(os.path.dirname(writable_db), exist_ok=True)
        
        # Copy database if it doesn't exist in writable location
        if not os.path.exists(writable_db) and os.path.exists(bundle_db):
            shutil.copy2(bundle_db, writable_db)
        
        return writable_db
    else:
        return get_resource_path(os.path.join("db", db_name))

def get_template_path(template_name):
    """Get path to web template"""
    return get_resource_path(os.path.join("web_templates", template_name))

def get_static_path(static_file):
    """Get path to static web file"""
    return get_resource_path(os.path.join("web_static", static_file))

def get_asset_path(asset_name):
    """Get path to asset file"""
    return get_resource_path(os.path.join("assets", asset_name))

def get_config_path():
    """Get path to config file"""
    if is_frozen():
        return get_data_path("config.py")
    else:
        return get_resource_path("config.py")

def get_settings_path():
    """Get path to settings file"""
    if is_frozen():
        return get_data_path("settings.json")
    else:
        return get_resource_path("settings.json")

def get_logs_path():
    """Get path to logs directory"""
    if is_frozen():
        logs_dir = get_data_path("logs")
        os.makedirs(logs_dir, exist_ok=True)
        return logs_dir
    else:
        return get_resource_path("logs")

def get_reports_path():
    """Get path to reports directory"""
    if is_frozen():
        reports_dir = get_data_path("reports")
        os.makedirs(reports_dir, exist_ok=True)
        return reports_dir
    else:
        return get_resource_path("reports")

def get_backups_path():
    """Get path to backups directory"""
    if is_frozen():
        backups_dir = get_data_path("backups")
        os.makedirs(backups_dir, exist_ok=True)
        return backups_dir
    else:
        return get_resource_path("backups")

def setup_pyinstaller_environment():
    """Setup environment for PyInstaller"""
    if is_frozen():
        # Ensure all necessary directories exist
        for dir_name in ["db", "logs", "reports", "backups"]:
            dir_path = get_data_path(dir_name)
            os.makedirs(dir_path, exist_ok=True)
        
        # Copy essential files if they don't exist
        essential_files = [
            ("settings.json", "settings.json"),
            ("config.py", "config.py"),
        ]
        
        for src_file, dst_file in essential_files:
            src_path = get_resource_path(src_file)
            dst_path = get_data_path(dst_file)
            
            if os.path.exists(src_path) and not os.path.exists(dst_path):
                os.makedirs(os.path.dirname(dst_path), exist_ok=True)
                shutil.copy2(src_path, dst_path)

def get_temp_dir():
    """Get temporary directory for the application"""
    if is_frozen():
        temp_dir = os.path.join(tempfile.gettempdir(), "CashierFilterSystem")
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir
    else:
        return tempfile.gettempdir()

# Initialize PyInstaller environment on import
if is_frozen():
    setup_pyinstaller_environment()
