#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص بيانات قاعدة البيانات
Database Data Diagnosis

فحص بيانات التصفيات في قاعدة البيانات وتحليل المشاكل المحتملة
"""

import sqlite3
import json
import os
from pathlib import Path

DB_PATH = Path(__file__).parent / "db" / "cashier_filter.db"

def check_database_structure():
    """فحص بنية قاعدة البيانات"""
    print("🔍 فحص بنية قاعدة البيانات...")
    print("=" * 50)
    
    if not DB_PATH.exists():
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # فحص الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 الجداول الموجودة:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # فحص بنية جدول filters
        if ('filters',) in tables:
            print("\n🔍 بنية جدول filters:")
            cursor.execute("PRAGMA table_info(filters)")
            columns = cursor.fetchall()
            
            for col in columns:
                print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def analyze_filters_data():
    """تحليل بيانات التصفيات"""
    print("\n📊 تحليل بيانات التصفيات...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) as total FROM filters")
        total = cursor.fetchone()['total']
        print(f"📈 إجمالي التصفيات: {total}")
        
        if total == 0:
            print("⚠️ لا توجد تصفيات في قاعدة البيانات")
            conn.close()
            return
        
        # فحص البيانات المفقودة
        cursor.execute("SELECT COUNT(*) as empty_data FROM filters WHERE data IS NULL OR data = ''")
        empty_data = cursor.fetchone()['empty_data']
        print(f"⚠️ تصفيات بدون بيانات: {empty_data}")
        
        # فحص التصفيات الحديثة
        cursor.execute("""
            SELECT id, date, data, admin_name, sequence_number
            FROM filters 
            ORDER BY id DESC 
            LIMIT 5
        """)
        
        recent_filters = cursor.fetchall()
        print(f"\n🔍 آخر 5 تصفيات:")
        
        for filter_row in recent_filters:
            print(f"\n  📋 التصفية #{filter_row['id']} (تسلسل: {filter_row['sequence_number']})")
            print(f"     التاريخ: {filter_row['date']}")
            print(f"     المسؤول: {filter_row['admin_name'] or 'غير محدد'}")
            
            # تحليل بيانات JSON
            if filter_row['data']:
                try:
                    data = json.loads(filter_row['data'])
                    print(f"     ✅ بيانات JSON صحيحة")
                    
                    # فحص المجاميع
                    if 'totals' in data:
                        totals = data['totals']
                        print(f"     💰 المجاميع:")
                        print(f"        - بنكي: {totals.get('bank', 0)}")
                        print(f"        - نقدي: {totals.get('cash', 0)}")
                        print(f"        - آجل: {totals.get('credit', 0)}")
                        print(f"        - مرتجعات: {totals.get('return', 0)}")
                    else:
                        print(f"     ⚠️ لا توجد مجاميع في البيانات")
                    
                    # فحص المعاملات البنكية
                    if 'bank_transactions' in data:
                        bank_count = len(data['bank_transactions'])
                        print(f"     🏦 المعاملات البنكية: {bank_count}")
                    
                    # فحص تفاصيل النقدي
                    if 'cash_details' in data:
                        cash_items = len(data['cash_details'])
                        print(f"     💵 تفاصيل النقدي: {cash_items} فئة")
                    
                except json.JSONDecodeError as e:
                    print(f"     ❌ خطأ في بيانات JSON: {e}")
                    print(f"     📄 البيانات الخام: {filter_row['data'][:100]}...")
            else:
                print(f"     ❌ لا توجد بيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في تحليل البيانات: {e}")

def check_web_server_compatibility():
    """فحص توافق البيانات مع خادم التقارير"""
    print("\n🌐 فحص توافق خادم التقارير...")
    print("=" * 50)
    
    try:
        # محاكاة ما يفعله خادم التقارير
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT f.*, 
                   COALESCE(c.name, 'غير محدد') as cashier_name,
                   COALESCE(f.admin_name, a.name, 'غير محدد') as admin_name_full
            FROM filters f
            LEFT JOIN cashiers c ON f.cashier_id = c.id
            LEFT JOIN admins a ON f.admin_id = a.id
            ORDER BY COALESCE(f.sequence_number, f.id) DESC
            LIMIT 3
        """)
        
        filters = cursor.fetchall()
        
        print(f"🔍 اختبار معالجة {len(filters)} تصفية:")
        
        for filter_row in filters:
            filter_data = dict(filter_row)
            print(f"\n  📋 التصفية #{filter_data['id']}")
            
            # محاكاة معالجة البيانات
            try:
                if filter_data['data']:
                    details = json.loads(filter_data['data'])
                    
                    # التحقق من المجاميع
                    totals = details.get('totals', {})
                    bank_total = float(totals.get('bank', 0))
                    cash_total = float(totals.get('cash', 0))
                    credit_total = float(totals.get('credit', 0))
                    
                    print(f"     ✅ معالجة ناجحة:")
                    print(f"        - بنكي: {bank_total}")
                    print(f"        - نقدي: {cash_total}")
                    print(f"        - آجل: {credit_total}")
                    print(f"        - المجموع: {bank_total + cash_total + credit_total}")
                    
                    # التحقق من التفاصيل
                    bank_transactions = details.get('bank_transactions', [])
                    cash_details = details.get('cash_details', {})
                    
                    print(f"        - معاملات بنكية: {len(bank_transactions)}")
                    print(f"        - فئات نقدية: {len(cash_details)}")
                    
                else:
                    print(f"     ⚠️ لا توجد بيانات للمعالجة")
                    
            except Exception as e:
                print(f"     ❌ خطأ في المعالجة: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص التوافق: {e}")

def suggest_fixes():
    """اقتراح حلول للمشاكل"""
    print("\n🔧 اقتراحات الإصلاح...")
    print("=" * 50)
    
    print("💡 لحل مشاكل البيانات المفقودة:")
    print("   1. تأكد من تشغيل التطبيق الرئيسي وإنشاء تصفيات جديدة")
    print("   2. تحقق من صحة بيانات JSON في قاعدة البيانات")
    print("   3. أعد تشغيل خادم التقارير بعد التحديثات")
    
    print("\n🌐 لحل مشاكل خادم التقارير:")
    print("   1. أعد تشغيل الخادم: python start_web_server.py")
    print("   2. تحقق من المنفذ 5000 (قد يكون مستخدم)")
    print("   3. تأكد من وجود قاعدة البيانات في المسار الصحيح")
    
    print("\n🔗 لحل مشاكل النفق العالمي:")
    print("   1. تأكد من تشغيل الخادم المحلي أولاً")
    print("   2. تحقق من اتصال الإنترنت")
    print("   3. جرب إعادة تشغيل النفق من الإعدادات")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص شامل لنظام تصفية الكاشير")
    print("=" * 60)
    
    # فحص بنية قاعدة البيانات
    if not check_database_structure():
        return
    
    # تحليل بيانات التصفيات
    analyze_filters_data()
    
    # فحص توافق خادم التقارير
    check_web_server_compatibility()
    
    # اقتراح حلول
    suggest_fixes()
    
    print("\n" + "=" * 60)
    print("✅ انتهى التشخيص")
    print("💡 راجع النتائج أعلاه لحل أي مشاكل")

if __name__ == "__main__":
    main()
