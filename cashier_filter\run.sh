#!/bin/bash

# تعيين الترميز
export LANG=ar_SA.UTF-8
export LC_ALL=ar_SA.UTF-8

echo "========================================"
echo "   🏪 نظام تصفية الكاشير - إصدار محسن"
echo "========================================"
echo ""

echo "🔄 جاري تشغيل النظام..."
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ خطأ: Python غير مثبت على النظام"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ خطأ: يتطلب Python 3.8 أو أحدث"
    echo "الإصدار الحالي: $PYTHON_VERSION"
    exit 1
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ خطأ: pip غير مثبت"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# التحقق من وجود المتطلبات
echo "📦 التحقق من المتطلبات..."
if ! $PIP_CMD show customtkinter &> /dev/null; then
    echo "🔧 تثبيت المتطلبات..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        exit 1
    fi
fi

# تهيئة قاعدة البيانات
echo "🗄️ تهيئة قاعدة البيانات..."
$PYTHON_CMD db/init_db.py

# تشغيل التطبيق
echo "✅ تشغيل التطبيق..."
echo ""
$PYTHON_CMD main.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ حدث خطأ في تشغيل التطبيق"
    read -p "اضغط Enter للمتابعة..."
fi
