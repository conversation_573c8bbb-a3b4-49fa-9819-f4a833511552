# دوال تصدير التصفية إلى PDF وExcel - محسنة
import json
import os
try:
    from fpdf import FPDF
except ImportError:
    try:
        from fpdf2 import FPDF
    except ImportError:
        print("تحذير: مكتبة PDF غير متوفرة")
        FPDF = None
import pandas as pd
from tkinter import messagebox
from datetime import datetime

class ArabicPDF:
    def __init__(self):
        if FPDF is None:
            raise ImportError("مكتبة PDF غير متوفرة")
        self.pdf = FPDF()
        self.pdf.set_auto_page_break(auto=True, margin=15)

    def add_page(self):
        return self.pdf.add_page()

    def set_font(self, *args, **kwargs):
        return self.pdf.set_font(*args, **kwargs)

    def cell(self, *args, **kwargs):
        return self.pdf.cell(*args, **kwargs)

    def ln(self, *args, **kwargs):
        return self.pdf.ln(*args, **kwargs)

    def set_y(self, *args, **kwargs):
        return self.pdf.set_y(*args, **kwargs)

    def output(self, *args, **kwargs):
        return self.pdf.output(*args, **kwargs)

    def header(self):
        # العنوان الرئيسي
        self.pdf.set_font('Arial', 'B', 20)
        self.pdf.cell(0, 15, 'Cashier Filter Report', 0, 1, 'C')
        self.pdf.set_font('Arial', 'B', 16)
        self.pdf.cell(0, 10, 'Filter Report', 0, 1, 'C')
        self.pdf.ln(10)

    def footer(self):
        self.pdf.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')

def export_filter_to_pdf(filter_data, totals, system_sales, filename):
    try:
        if FPDF is None:
            print("مكتبة PDF غير متوفرة")
            return False

        # إنشاء PDF مبسط
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)

        # استخراج معلومات التصفية
        filter_info = filter_data if isinstance(filter_data, dict) else {}
        if not isinstance(totals, dict):
            totals = {}
        if not isinstance(system_sales, (int, float)):
            system_sales = 0

        # العنوان
        sequence_number = filter_info.get('sequence_number', 'N/A')
        pdf.cell(0, 15, f'Cashier Filter Report - #{sequence_number}', 0, 1, 'C')
        pdf.ln(10)

        # معلومات التصفية
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'Filter Information:', 0, 1)
        pdf.set_font('Arial', '', 12)

        # معلومات أساسية فقط
        pdf.cell(0, 8, f"Sequence Number: {sequence_number}", 0, 1)
        pdf.cell(0, 8, f"Cashier: {filter_info.get('cashier_name', 'Not specified')}", 0, 1)
        pdf.cell(0, 8, f"Cashier Number: {filter_info.get('cashier_number', filter_info.get('cashier_id', 'Not specified'))}", 0, 1)
        pdf.cell(0, 8, f"Date: {filter_info.get('date', 'Not specified')}", 0, 1)
        pdf.cell(0, 8, f"Admin: {filter_info.get('admin_name', 'Not specified')}", 0, 1)
        pdf.ln(10)

        # المجاميع
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, 'Totals Summary:', 0, 1)
        pdf.set_font('Arial', '', 12)

        # مجاميع مبسطة
        pdf.cell(0, 8, f"Bank Receipts: {totals.get('bank', 0):.2f} SAR", 0, 1)
        pdf.cell(0, 8, f"Cash Receipts: {totals.get('cash', 0):.2f} SAR", 0, 1)
        pdf.cell(0, 8, f"Credit Sales: {totals.get('credit', 0):.2f} SAR", 0, 1)
        pdf.cell(0, 8, f"System Sales: {system_sales:.2f} SAR", 0, 1)
        pdf.ln(10)

        # المقبوضات البنكية (مؤقتاً فارغ)
        bank_data = []
        if bank_data:
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, 'Bank Receipts:', 0, 1)
            pdf.set_font('Arial', 'B', 10)

            # رؤوس الأعمدة
            col_widths = [60, 60, 40]
            headers = ['Operation Type', 'Bank Name', 'Amount']

            for i, header in enumerate(headers):
                pdf.cell(col_widths[i], 8, header, 1, 0, 'C')
            pdf.ln()

            # البيانات
            pdf.set_font('Arial', '', 10)
            for row in bank_data:
                for i, cell in enumerate(row):
                    pdf.cell(col_widths[i], 8, str(cell), 1, 0, 'C')
                pdf.ln()

            # الإجمالي
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(120, 8, 'Total:', 1, 0, 'R')
            pdf.cell(40, 8, f"{totals.get('bank', 0):.2f} SAR", 1, 1, 'C')
            pdf.ln(5)

        # المقبوضات النقدية (مؤقتاً فارغ)
        cash_data = {}
        if cash_data:
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, 'Cash Receipts:', 0, 1)
            pdf.set_font('Arial', 'B', 10)

            # رؤوس الأعمدة
            col_widths = [40, 30, 40, 40]
            headers = ['Denomination', 'Count', 'Value', 'Total']

            for i, header in enumerate(headers):
                pdf.cell(col_widths[i], 8, header, 1, 0, 'C')
            pdf.ln()

            # البيانات
            pdf.set_font('Arial', '', 10)
            cash_types = [
                (500, "500 SAR"),
                (100, "100 SAR"),
                (50, "50 SAR"),
                (10, "10 SAR"),
                (5, "5 SAR"),
                (1, "1 SAR"),
                (0.5, "0.5 SAR"),
                (0.25, "0.25 SAR")
            ]

            for value, label in cash_types:
                count = cash_data.get(str(value), '0')
                try:
                    count_num = float(count)
                    total_value = count_num * value
                except:
                    count_num = 0
                    total_value = 0

                pdf.cell(col_widths[0], 8, label, 1, 0, 'C')
                pdf.cell(col_widths[1], 8, str(count), 1, 0, 'C')
                pdf.cell(col_widths[2], 8, f"{value:.2f}", 1, 0, 'C')
                pdf.cell(col_widths[3], 8, f"{total_value:.2f}", 1, 1, 'C')

            # الإجمالي
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(110, 8, 'Total:', 1, 0, 'R')
            pdf.cell(40, 8, f"{totals.get('cash', 0):.2f} SAR", 1, 1, 'C')
            pdf.ln(5)

        # باقي الأقسام
        sections = [
            ('credit', 'Credit Sales', ['Client Name', 'Invoice Amount']),
            ('client', 'Client Receipts', ['Client Name', 'Amount Paid']),
            ('return', 'Returns', ['Invoice Number', 'Return Amount'])
        ]

        for section_key, section_title, columns in sections:
            section_data = []  # مؤقتاً فارغ
            if section_data:
                pdf.set_font('Arial', 'B', 14)
                pdf.cell(0, 10, f'{section_title}:', 0, 1)
                pdf.set_font('Arial', 'B', 10)

                # رؤوس الأعمدة
                col_width = 80
                for col in columns:
                    pdf.cell(col_width, 8, col, 1, 0, 'C')
                pdf.ln()

                # البيانات
                pdf.set_font('Arial', '', 10)
                for row in section_data:
                    for cell in row:
                        pdf.cell(col_width, 8, str(cell), 1, 0, 'C')
                    pdf.ln()

                # الإجمالي
                pdf.set_font('Arial', 'B', 10)
                pdf.cell(80, 8, 'Total:', 1, 0, 'R')
                pdf.cell(80, 8, f"{totals.get(section_key, 0):.2f} SAR", 1, 1, 'C')
                pdf.ln(5)

        # ملخص التصفية
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 15, 'Filter Summary', 0, 1, 'C')
        pdf.ln(10)

        # حساب المجاميع
        total_receipts = (
            totals.get('bank', 0) +
            totals.get('cash', 0) +
            totals.get('credit', 0) +
            totals.get('return', 0) -
            totals.get('client', 0)
        )

        try:
            system_sales_value = float(system_sales)
        except:
            system_sales_value = 0.0

        difference = total_receipts - system_sales_value

        # جدول الملخص
        pdf.set_font('Arial', 'B', 12)
        summary_items = [
            ('Bank Receipts:', f"{totals.get('bank', 0):.2f} SAR"),
            ('Cash Receipts:', f"{totals.get('cash', 0):.2f} SAR"),
            ('Credit Sales:', f"{totals.get('credit', 0):.2f} SAR"),
            ('Returns:', f"{totals.get('return', 0):.2f} SAR"),
            ('Client Receipts:', f"-{totals.get('client', 0):.2f} SAR"),
            ('', ''),
            ('Total Receipts:', f"{total_receipts:.2f} SAR"),
            ('System Sales:', f"{system_sales_value:.2f} SAR"),
            ('', ''),
            ('Difference:', f"{difference:.2f} SAR")
        ]

        for label, value in summary_items:
            if label == '':
                pdf.ln(3)
                continue
            pdf.cell(100, 10, label, 1, 0, 'L')
            pdf.cell(60, 10, value, 1, 1, 'R')

        # الحالة النهائية
        pdf.ln(10)
        pdf.set_font('Arial', 'B', 14)
        if difference > 0:
            status = "SURPLUS"
            pdf.set_text_color(0, 128, 0)  # أخضر
        elif difference < 0:
            status = "DEFICIT"
            pdf.set_text_color(255, 0, 0)  # أحمر
        else:
            status = "BALANCED"
            pdf.set_text_color(255, 165, 0)  # برتقالي

        pdf.cell(0, 15, f'Status: {status}', 0, 1, 'C')

        # حفظ الملف
        pdf.output(filename)
        return True

    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تصدير PDF: {e}")
        return False

def export_filter_to_excel(filter_data, totals, system_sales, filename):
    try:
        # استخراج معلومات التصفية
        filter_info = filter_data if isinstance(filter_data, dict) else {}
        if not isinstance(totals, dict):
            totals = {}
        if not isinstance(system_sales, (int, float)):
            system_sales = 0

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # ورقة معلومات التصفية
            info_data = {
                'البيان': ['اسم الكاشير', 'رقم الكاشير', 'اسم المسؤول', 'تاريخ التصفية', 'وقت إنشاء التقرير'],
                'القيمة': [
                    filter_info.get('cashier_name', 'غير محدد'),
                    filter_info.get('cashier_number', filter_info.get('cashier_id', 'غير محدد')),
                    filter_info.get('admin_name', 'غير محدد'),
                    filter_info.get('date', datetime.now().strftime('%Y-%m-%d')),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ]
            }
            info_df = pd.DataFrame(info_data)
            info_df.to_excel(writer, sheet_name='معلومات التصفية', index=False)

            # ورقة المقبوضات البنكية (مؤقتاً فارغ)
            bank_data = []
            if bank_data:
                bank_df = pd.DataFrame(bank_data, columns=['نوع العملية', 'اسم البنك', 'المبلغ'])
                # إضافة صف الإجمالي
                total_row = pd.DataFrame([['', 'الإجمالي', totals.get('bank', 0)]],
                                       columns=['نوع العملية', 'اسم البنك', 'المبلغ'])
                bank_df = pd.concat([bank_df, total_row], ignore_index=True)
                bank_df.to_excel(writer, sheet_name='المقبوضات البنكية', index=False)

            # ورقة المقبوضات النقدية (مؤقتاً فارغ)
            cash_data = {}
            if cash_data:
                cash_types = [
                    (500, "خمس مئة"),
                    (100, "مئة"),
                    (50, "خمسون"),
                    (10, "عشرة"),
                    (5, "خمسة"),
                    (1, "ريال"),
                    (0.5, "نصف"),
                    (0.25, "ربع")
                ]

                cash_rows = []
                for value, label in cash_types:
                    count = cash_data.get(str(value), '0')
                    try:
                        count_num = float(count)
                        total_value = count_num * value
                    except:
                        count_num = 0
                        total_value = 0

                    cash_rows.append([label, count, value, total_value])

                # إضافة صف الإجمالي
                cash_rows.append(['الإجمالي', '', '', totals.get('cash', 0)])

                cash_df = pd.DataFrame(cash_rows, columns=['الفئة', 'العدد', 'القيمة', 'المجموع'])
                cash_df.to_excel(writer, sheet_name='المقبوضات النقدية', index=False)

            # ورقة المبيعات الآجلة (مؤقتاً فارغ)
            credit_data = []
            if credit_data:
                credit_df = pd.DataFrame(credit_data, columns=['اسم العميل', 'مبلغ الفاتورة'])
                # إضافة صف الإجمالي
                total_row = pd.DataFrame([['الإجمالي', totals.get('credit', 0)]],
                                       columns=['اسم العميل', 'مبلغ الفاتورة'])
                credit_df = pd.concat([credit_df, total_row], ignore_index=True)
                credit_df.to_excel(writer, sheet_name='المبيعات الآجلة', index=False)

            # ورقة المقبوضات من العملاء (مؤقتاً فارغ)
            client_data = []
            if client_data:
                client_df = pd.DataFrame(client_data, columns=['اسم العميل', 'المبلغ المدفوع'])
                # إضافة صف الإجمالي
                total_row = pd.DataFrame([['الإجمالي', totals.get('client', 0)]],
                                       columns=['اسم العميل', 'المبلغ المدفوع'])
                client_df = pd.concat([client_df, total_row], ignore_index=True)
                client_df.to_excel(writer, sheet_name='المقبوضات من العملاء', index=False)

            # ورقة المرتجعات (مؤقتاً فارغ)
            return_data = []
            if return_data:
                return_df = pd.DataFrame(return_data, columns=['رقم الفاتورة', 'مبلغ المرتجع'])
                # إضافة صف الإجمالي
                total_row = pd.DataFrame([['الإجمالي', totals.get('return', 0)]],
                                       columns=['رقم الفاتورة', 'مبلغ المرتجع'])
                return_df = pd.concat([return_df, total_row], ignore_index=True)
                return_df.to_excel(writer, sheet_name='المرتجعات', index=False)

            # ورقة ملخص التصفية
            total_receipts = (
                totals.get('bank', 0) +
                totals.get('cash', 0) +
                totals.get('credit', 0) +
                totals.get('return', 0) -
                totals.get('client', 0)
            )

            try:
                system_sales_value = float(system_sales)
            except:
                system_sales_value = 0.0

            difference = total_receipts - system_sales_value

            if difference > 0:
                status = "فائض"
            elif difference < 0:
                status = "عجز"
            else:
                status = "متوازن"

            summary_data = {
                'البيان': [
                    'المقبوضات البنكية',
                    'المقبوضات النقدية',
                    'المبيعات الآجلة',
                    'المرتجعات',
                    'المقبوضات من العملاء',
                    '',
                    'إجمالي المقبوضات',
                    'مبيعات النظام',
                    '',
                    'الفارق',
                    'الحالة'
                ],
                'المبلغ (ريال)': [
                    totals.get('bank', 0),
                    totals.get('cash', 0),
                    totals.get('credit', 0),
                    totals.get('return', 0),
                    f"-{totals.get('client', 0)}",
                    '',
                    total_receipts,
                    system_sales_value,
                    '',
                    difference,
                    status
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='ملخص التصفية', index=False)

        return True

    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تصدير Excel: {e}")
        return False
