#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح طباعة الموردين
Test Suppliers Print Fix
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    from reports.html_print import generate_filter_report
    import sqlite3
    import json
    from datetime import datetime
    
    def test_print_with_real_data():
        """اختبار الطباعة مع بيانات حقيقية من قاعدة البيانات"""
        print("🖨️ اختبار الطباعة مع بيانات حقيقية...")
        print("=" * 50)
        
        try:
            db_path = BASE_DIR / "db" / "cashier_filter.db"
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # جلب تصفية تحتوي على بيانات موردين
            cursor.execute("""
                SELECT id, date, data, admin_name,
                       (SELECT name FROM cashiers WHERE id = filters.cashier_id) as cashier_name
                FROM filters 
                WHERE data IS NOT NULL 
                ORDER BY date DESC 
                LIMIT 5
            """)
            
            filters = cursor.fetchall()
            conn.close()
            
            test_successful = False
            
            for filter_id, date, data_str, admin_name, cashier_name in filters:
                try:
                    data = json.loads(data_str)
                    
                    # فحص وجود بيانات موردين
                    suppliers_transactions = data.get('suppliers_transactions', [])
                    if not suppliers_transactions:
                        details = data.get('details', {})
                        suppliers_transactions = details.get('suppliers_transactions', [])
                    
                    if suppliers_transactions:
                        print(f"\n📋 اختبار تصفية {filter_id} - {date}:")
                        print(f"   الكاشير: {cashier_name or 'غير محدد'}")
                        print(f"   المسؤول: {admin_name or 'غير محدد'}")
                        print(f"   عدد مدفوعات الموردين: {len(suppliers_transactions)}")
                        
                        for i, supplier in enumerate(suppliers_transactions, 1):
                            print(f"      {i}. {supplier.get('supplier_name', 'غير محدد')}: {supplier.get('amount', 0)} ريال")
                        
                        # تحضير البيانات للطباعة
                        filter_data_for_print = {
                            'sequence_number': f'TEST-{filter_id}',
                            'cashier_name': cashier_name or 'غير محدد',
                            'cashier_number': '001',
                            'admin_name': admin_name or 'غير محدد',
                            'date': date,
                            'credit_transactions': data.get('credit_transactions', []),
                            'client_transactions': data.get('client_transactions', []),
                            'bank_transactions': data.get('bank_transactions', []),
                            'return_transactions': data.get('return_transactions', []),
                            'suppliers_transactions': suppliers_transactions
                        }
                        
                        totals = data.get('totals', {})
                        system_sales = data.get('system_sales', 0)
                        
                        print(f"   🖨️ محاولة طباعة التصفية...")
                        
                        if generate_filter_report(filter_data_for_print, totals, system_sales):
                            print(f"   ✅ تم إنشاء التقرير بنجاح!")
                            print(f"   🔍 تحقق من المتصفح - يجب أن ترى جدول الموردين")
                            test_successful = True
                            break
                        else:
                            print(f"   ❌ فشل في إنشاء التقرير")
                    
                except Exception as e:
                    print(f"   ❌ خطأ في معالجة تصفية {filter_id}: {e}")
                    continue
            
            if not test_successful:
                print("❌ لم يتم العثور على تصفيات تحتوي على بيانات موردين للاختبار")
            
            return test_successful
            
        except Exception as e:
            print(f"❌ خطأ في اختبار البيانات الحقيقية: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_print_with_both_formats():
        """اختبار الطباعة مع كلا تنسيقي البيانات"""
        print("\n🔄 اختبار الطباعة مع تنسيقات مختلفة للبيانات...")
        print("=" * 50)
        
        try:
            # تنسيق 1: suppliers_transactions في المستوى الأعلى
            format1_data = {
                'sequence_number': 'FORMAT1-001',
                'cashier_name': 'كاشير تنسيق 1',
                'cashier_number': '001',
                'admin_name': 'مسؤول تنسيق 1',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'suppliers_transactions': [
                    {
                        'supplier_name': 'مورد تنسيق 1 - رقم 1',
                        'amount': 1500.0,
                        'payment_method': 'نقدي',
                        'notes': 'اختبار تنسيق 1'
                    },
                    {
                        'supplier_name': 'مورد تنسيق 1 - رقم 2',
                        'amount': 2500.0,
                        'payment_method': 'شيك',
                        'notes': 'اختبار تنسيق 1 - شيك'
                    }
                ]
            }
            
            # تنسيق 2: suppliers_transactions داخل details
            format2_data = {
                'sequence_number': 'FORMAT2-001',
                'cashier_name': 'كاشير تنسيق 2',
                'cashier_number': '002',
                'admin_name': 'مسؤول تنسيق 2',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'details': {
                    'suppliers_transactions': [
                        {
                            'supplier_name': 'مورد تنسيق 2 - رقم 1',
                            'amount': 3000.0,
                            'payment_method': 'تحويل بنكي',
                            'notes': 'اختبار تنسيق 2'
                        },
                        {
                            'supplier_name': 'مورد تنسيق 2 - رقم 2',
                            'amount': 1800.0,
                            'payment_method': 'نقدي',
                            'notes': 'اختبار تنسيق 2 - نقدي'
                        }
                    ]
                }
            }
            
            test_totals = {'bank': 0, 'cash': 0, 'credit': 0, 'client': 0, 'return': 0}
            test_system_sales = 0
            
            # اختبار التنسيق الأول
            print("📋 اختبار التنسيق الأول (suppliers_transactions في المستوى الأعلى):")
            if generate_filter_report(format1_data, test_totals, test_system_sales):
                print("   ✅ نجح اختبار التنسيق الأول")
            else:
                print("   ❌ فشل اختبار التنسيق الأول")
            
            # اختبار التنسيق الثاني
            print("📋 اختبار التنسيق الثاني (suppliers_transactions داخل details):")
            if generate_filter_report(format2_data, test_totals, test_system_sales):
                print("   ✅ نجح اختبار التنسيق الثاني")
            else:
                print("   ❌ فشل اختبار التنسيق الثاني")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التنسيقات: {e}")
            return False
    
    def test_empty_suppliers():
        """اختبار الطباعة بدون بيانات موردين"""
        print("\n📭 اختبار الطباعة بدون بيانات موردين...")
        print("=" * 50)
        
        try:
            empty_data = {
                'sequence_number': 'EMPTY-001',
                'cashier_name': 'كاشير بدون موردين',
                'cashier_number': '003',
                'admin_name': 'مسؤول بدون موردين',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'suppliers_transactions': []  # قائمة فارغة
            }
            
            test_totals = {'bank': 1000, 'cash': 500, 'credit': 0, 'client': 0, 'return': 0}
            test_system_sales = 0
            
            print("📋 اختبار تصفية بدون موردين:")
            if generate_filter_report(empty_data, test_totals, test_system_sales):
                print("   ✅ نجح اختبار التصفية بدون موردين")
                print("   🔍 يجب أن ترى 'لا توجد مدفوعات للموردين' في التقرير")
            else:
                print("   ❌ فشل اختبار التصفية بدون موردين")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار البيانات الفارغة: {e}")
            return False
    
    def main():
        """تشغيل جميع الاختبارات"""
        print("🧪 اختبار إصلاح طباعة الموردين")
        print("=" * 60)
        
        # اختبار مع بيانات حقيقية
        real_data_test = test_print_with_real_data()
        
        # اختبار مع تنسيقات مختلفة
        formats_test = test_print_with_both_formats()
        
        # اختبار بدون بيانات
        empty_test = test_empty_suppliers()
        
        print("\n🎯 ملخص النتائج:")
        print("=" * 60)
        
        if real_data_test:
            print("✅ اختبار البيانات الحقيقية نجح")
        else:
            print("❌ اختبار البيانات الحقيقية فشل")
        
        if formats_test:
            print("✅ اختبار التنسيقات المختلفة نجح")
        else:
            print("❌ اختبار التنسيقات المختلفة فشل")
        
        if empty_test:
            print("✅ اختبار البيانات الفارغة نجح")
        else:
            print("❌ اختبار البيانات الفارغة فشل")
        
        if real_data_test and formats_test and empty_test:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ إصلاح طباعة الموردين يعمل بشكل صحيح")
            print("🔍 تحقق من المتصفح لرؤية التقارير المُنشأة")
        else:
            print("\n⚠️ بعض الاختبارات فشلت - قد تحتاج لمراجعة إضافية")
        
        print("=" * 60)

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
