# نظام إشعارات لوحة المعلومات التفاعلية
import json
import os
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class DashboardNotifier:
    """نظام إشعارات لوحة المعلومات التفاعلية"""
    
    def __init__(self):
        self.listeners = []  # قائمة النوافذ المستمعة
        self.events_file = "dashboard_events.json"
        self.lock = threading.Lock()
        
    def register_listener(self, dashboard_window):
        """تسجيل نافذة لوحة المعلومات للاستماع للتحديثات"""
        with self.lock:
            if dashboard_window not in self.listeners:
                self.listeners.append(dashboard_window)
                print(f"✅ تم تسجيل نافذة لوحة المعلومات: {id(dashboard_window)}")
    
    def unregister_listener(self, dashboard_window):
        """إلغاء تسجيل نافذة لوحة المعلومات"""
        with self.lock:
            if dashboard_window in self.listeners:
                self.listeners.remove(dashboard_window)
                print(f"❌ تم إلغاء تسجيل نافذة لوحة المعلومات: {id(dashboard_window)}")
    
    def notify_filter_deleted(self, filter_id: int, filter_data: Dict[str, Any]):
        """إشعار بحذف تصفية"""
        event = {
            "type": "filter_deleted",
            "filter_id": filter_id,
            "filter_data": filter_data,
            "timestamp": datetime.now().isoformat(),
            "message": f"تم حذف التصفية رقم {filter_id}"
        }
        
        self._broadcast_event(event)
        self._log_event(event)
    
    def notify_filter_added(self, filter_id: int, filter_data: Dict[str, Any]):
        """إشعار بإضافة تصفية جديدة"""
        event = {
            "type": "filter_added",
            "filter_id": filter_id,
            "filter_data": filter_data,
            "timestamp": datetime.now().isoformat(),
            "message": f"تم إضافة التصفية رقم {filter_id}"
        }
        
        self._broadcast_event(event)
        self._log_event(event)
    
    def notify_filter_updated(self, filter_id: int, filter_data: Dict[str, Any]):
        """إشعار بتحديث تصفية"""
        event = {
            "type": "filter_updated",
            "filter_id": filter_id,
            "filter_data": filter_data,
            "timestamp": datetime.now().isoformat(),
            "message": f"تم تحديث التصفية رقم {filter_id}"
        }
        
        self._broadcast_event(event)
        self._log_event(event)
    
    def notify_data_changed(self, change_type: str, details: Dict[str, Any]):
        """إشعار عام بتغيير البيانات"""
        event = {
            "type": "data_changed",
            "change_type": change_type,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "message": f"تم تغيير البيانات: {change_type}"
        }
        
        self._broadcast_event(event)
        self._log_event(event)
    
    def _broadcast_event(self, event: Dict[str, Any]):
        """بث الحدث لجميع النوافذ المستمعة"""
        with self.lock:
            # إنشاء نسخة من القائمة لتجنب مشاكل التعديل أثناء التكرار
            listeners_copy = self.listeners.copy()
        
        for listener in listeners_copy:
            try:
                # التحقق من أن النافذة ما زالت موجودة
                if hasattr(listener, 'winfo_exists') and listener.winfo_exists():
                    # استخدام after للتحديث في الخيط الرئيسي
                    listener.after(0, lambda l=listener, e=event: self._update_listener(l, e))
                else:
                    # إزالة النافذة المغلقة من القائمة
                    self.unregister_listener(listener)
            except Exception as e:
                print(f"خطأ في إشعار النافذة {id(listener)}: {e}")
                # إزالة النافذة التي تسبب مشاكل
                self.unregister_listener(listener)
    
    def _update_listener(self, listener, event: Dict[str, Any]):
        """تحديث نافذة مستمعة محددة"""
        try:
            if hasattr(listener, 'handle_dashboard_event'):
                listener.handle_dashboard_event(event)
            elif hasattr(listener, 'load_dashboard_data'):
                # إذا لم تكن هناك دالة معالجة أحداث، قم بتحديث البيانات
                listener.load_dashboard_data()
        except Exception as e:
            print(f"خطأ في تحديث النافذة {id(listener)}: {e}")
    
    def _log_event(self, event: Dict[str, Any]):
        """تسجيل الحدث في ملف السجل"""
        try:
            # قراءة الأحداث الحالية
            events = []
            if os.path.exists(self.events_file):
                try:
                    with open(self.events_file, 'r', encoding='utf-8') as f:
                        events = json.load(f)
                except:
                    events = []
            
            # إضافة الحدث الجديد
            events.append(event)
            
            # الاحتفاظ بآخر 100 حدث فقط
            if len(events) > 100:
                events = events[-100:]
            
            # حفظ الأحداث
            with open(self.events_file, 'w', encoding='utf-8') as f:
                json.dump(events, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في تسجيل الحدث: {e}")
    
    def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """الحصول على الأحداث الحديثة"""
        try:
            if os.path.exists(self.events_file):
                with open(self.events_file, 'r', encoding='utf-8') as f:
                    events = json.load(f)
                    return events[-limit:] if events else []
            return []
        except Exception as e:
            print(f"خطأ في قراءة الأحداث: {e}")
            return []
    
    def clear_events(self):
        """مسح جميع الأحداث"""
        try:
            if os.path.exists(self.events_file):
                os.remove(self.events_file)
            print("✅ تم مسح جميع أحداث لوحة المعلومات")
        except Exception as e:
            print(f"خطأ في مسح الأحداث: {e}")


# إنشاء مثيل عام للنظام
dashboard_notifier = DashboardNotifier()


def notify_filter_deleted(filter_id: int, filter_data: Dict[str, Any] = None):
    """دالة مساعدة لإشعار حذف تصفية"""
    if filter_data is None:
        filter_data = {}
    dashboard_notifier.notify_filter_deleted(filter_id, filter_data)


def notify_filter_added(filter_id: int, filter_data: Dict[str, Any] = None):
    """دالة مساعدة لإشعار إضافة تصفية"""
    if filter_data is None:
        filter_data = {}
    dashboard_notifier.notify_filter_added(filter_id, filter_data)


def notify_filter_updated(filter_id: int, filter_data: Dict[str, Any] = None):
    """دالة مساعدة لإشعار تحديث تصفية"""
    if filter_data is None:
        filter_data = {}
    dashboard_notifier.notify_filter_updated(filter_id, filter_data)


def notify_data_changed(change_type: str, details: Dict[str, Any] = None):
    """دالة مساعدة لإشعار تغيير البيانات"""
    if details is None:
        details = {}
    dashboard_notifier.notify_data_changed(change_type, details)


def register_dashboard(dashboard_window):
    """دالة مساعدة لتسجيل نافذة لوحة المعلومات"""
    dashboard_notifier.register_listener(dashboard_window)


def unregister_dashboard(dashboard_window):
    """دالة مساعدة لإلغاء تسجيل نافذة لوحة المعلومات"""
    dashboard_notifier.unregister_listener(dashboard_window)


def get_recent_dashboard_events(limit: int = 10) -> List[Dict[str, Any]]:
    """دالة مساعدة للحصول على الأحداث الحديثة"""
    return dashboard_notifier.get_recent_events(limit)


def clear_dashboard_events():
    """دالة مساعدة لمسح أحداث لوحة المعلومات"""
    dashboard_notifier.clear_events()


# دالة اختبار
def test_dashboard_notifier():
    """اختبار نظام إشعارات لوحة المعلومات"""
    print("🧪 اختبار نظام إشعارات لوحة المعلومات...")
    
    # اختبار إشعار حذف تصفية
    notify_filter_deleted(123, {
        "cashier_name": "أحمد محمد",
        "admin_name": "المدير العام",
        "date": "2024-01-15",
        "total_amount": 5000
    })
    
    # اختبار إشعار إضافة تصفية
    notify_filter_added(124, {
        "cashier_name": "فاطمة أحمد",
        "admin_name": "المدير العام",
        "date": "2024-01-16",
        "total_amount": 7500
    })
    
    # عرض الأحداث الحديثة
    events = get_recent_dashboard_events(5)
    print(f"✅ تم تسجيل {len(events)} أحداث")
    
    for event in events:
        print(f"   📋 {event['message']} - {event['timestamp']}")
    
    print("🎉 اختبار نظام الإشعارات مكتمل!")


if __name__ == "__main__":
    test_dashboard_notifier()
