#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل خادم التقارير في الخلفية (بدون نافذة)
Silent Web Server Launcher - No Console Window

يشغل خادم التقارير في الخلفية بدون إظهار نافذة الأوامر
"""

import os
import sys
import socket
import subprocess
from pathlib import Path

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # إنشاء اتصال وهمي للحصول على IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 10):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def check_database():
    """التحقق من وجود قاعدة البيانات"""
    db_path = project_root / "db" / "cashier_filter.db"
    return db_path.exists()

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        project_root / "web_templates",
        project_root / "web_static" / "css",
        project_root / "web_static" / "js"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def write_server_info(port, local_ip):
    """كتابة معلومات الخادم لملف"""
    info_file = project_root / "server_info.txt"
    
    info_content = f"""خادم تقارير نظام تصفية الكاشير
========================================
🔗 الرابط المحلي: http://localhost:{port}
🔗 الرابط الشبكي: http://{local_ip}:{port}
📱 واجهة الهاتف: http://localhost:{port}/mobile

للوصول من الهاتف:
http://{local_ip}:{port}/mobile

الحالة: يعمل في الخلفية
التاريخ: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(info_content)

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من قاعدة البيانات
        if not check_database():
            # إنشاء ملف خطأ
            error_file = project_root / "server_error.txt"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write("خطأ: قاعدة البيانات غير موجودة\nيرجى تشغيل التطبيق الرئيسي أولاً")
            return False
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # البحث عن منفذ متاح
        port = find_available_port(5000)
        if not port:
            error_file = project_root / "server_error.txt"
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write("خطأ: لا يمكن العثور على منفذ متاح")
            return False
        
        # الحصول على IP المحلي
        local_ip = get_local_ip()
        
        # كتابة معلومات الخادم
        write_server_info(port, local_ip)
        
        # تشغيل الخادم
        from web_server import run_server
        run_server(host='0.0.0.0', port=port, debug=False, open_browser_auto=False)
        
        return True
        
    except Exception as e:
        # كتابة الخطأ لملف
        error_file = project_root / "server_error.txt"
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"خطأ في تشغيل الخادم: {e}")
        return False

if __name__ == "__main__":
    main()
