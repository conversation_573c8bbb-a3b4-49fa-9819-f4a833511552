@echo off
chcp 65001 > nul
title نظام تصفية الكاشير v3.5.0
echo.
echo ========================================
echo    نظام تصفية الكاشير v3.5.0
echo    تشغيل النظام الرئيسي...
echo ========================================
echo.

REM التحقق من Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تثبيت Python من: https://python.org/downloads
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo 🚀 بدء تشغيل النظام...
echo.

REM تثبيت المتطلبات إذا لزم الأمر
if exist requirements.txt (
    echo 📦 التحقق من المتطلبات...
    pip install -r requirements.txt --quiet
)

REM تشغيل النظام
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 💡 تأكد من تثبيت جميع المتطلبات
    echo.
    pause
)
