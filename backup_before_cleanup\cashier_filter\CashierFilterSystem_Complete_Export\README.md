# 🏪 نظام تصفية الكاشير المتكامل 2025

نظام متكامل لإدارة وتصفية حسابات الكاشير اليومية مع واجهة رسومية أنيقة بتصميم Neumorphic ودعم كامل للطباعة والتصدير مع ذكاء اصطناعي متطور.

## 🆕 جديد في إصدار 2025

### 🤖 ذكاء اصطناعي متطور
- تحليل ذكي للبيانات والأنماط
- توقعات مالية دقيقة
- اكتشاف الشذوذ والأخطاء تلقائياً
- تحسين الأداء بناءً على البيانات التاريخية

### 📊 لوحة معلومات تفاعلية
- عرض البيانات في الوقت الفعلي
- رسوم بيانية تفاعلية متطورة
- مؤشرات أداء رئيسية (KPIs)
- تحليلات متقدمة للاتجاهات

### 🌐 التكامل السحابي
- مزامنة البيانات عبر السحابة
- نسخ احتياطي تلقائي
- الوصول من أي مكان
- أمان متقدم للبيانات

## ✨ الميزات الرئيسية

### 🎨 واجهة مستخدم متطورة
- تصميم Neumorphic أنيق ومريح للعين
- واجهة باللغة العربية مع دعم كامل للنصوص العربية
- ألوان متدرجة وتأثيرات ظليلة احترافية
- تصميم متجاوب يتكيف مع أحجام الشاشات المختلفة

### 📊 إدارة شاملة للتصفية
- **المقبوضات البنكية**: دعم جميع أنواع البطاقات (ماستر، مدى، فيزا، إلخ)
- **المقبوضات النقدية**: حساب تلقائي لجميع فئات العملة السعودية
- **المبيعات الآجلة**: تتبع المبيعات للعملاء بالآجل
- **المقبوضات من العملاء**: إدارة المدفوعات المستلمة
- **فواتير المرتجعات**: تسجيل وإدارة المرتجعات
- **ملخص تلقائي**: حساب الفوائض والعجز تلقائياً

### 👥 إدارة المستخدمين
- **إدارة الكاشيرين**: إضافة وتعديل وحذف بيانات الكاشيرين
- **إدارة المسؤولين**: نظام مستخدمين آمن مع كلمات مرور مشفرة
- **صلاحيات متدرجة**: تحكم في الوصول حسب نوع المستخدم

### 💾 حفظ واستعادة البيانات
- حفظ التصفيات في قاعدة بيانات SQLite آمنة
- إمكانية تعديل التصفيات المحفوظة
- استعراض تاريخ التصفيات السابقة
- نسخ احتياطي تلقائي للبيانات

### 🖨️ طباعة وتصدير متقدم
- **طباعة عبر المتصفح**: تقارير HTML جميلة قابلة للطباعة
- **تصدير PDF**: تقارير احترافية بتنسيق PDF
- **تصدير Excel**: جداول بيانات منظمة لجميع الأقسام
- **تصميم طباعة محسن**: تخطيط مثالي للطباعة على الورق

## 🛠️ التقنيات المستخدمة

- **Python 3.x**: لغة البرمجة الأساسية
- **CustomTkinter**: واجهة رسومية حديثة
- **SQLite**: قاعدة بيانات محلية آمنة
- **FPDF2**: إنشاء ملفات PDF
- **Pandas & OpenPyXL**: تصدير Excel
- **ReportLab**: تقارير متقدمة

## 📋 متطلبات النظام

- Windows 10/11
- Python 3.8 أو أحدث
- 4 جيجابايت رام (الحد الأدنى)
- 100 ميجابايت مساحة تخزين

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تهيئة قاعدة البيانات
```bash
python db/init_db.py
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## 📖 دليل الاستخدام

### البدء السريع
1. **تشغيل التطبيق**: قم بتشغيل `main.py`
2. **إضافة كاشير**: اذهب إلى "إدارة الكاشير" وأضف كاشير جديد
3. **إضافة مسؤول**: اذهب إلى "إدارة المسؤولين" وأضف مسؤول جديد
4. **بدء تصفية جديدة**: اختر "بدء تصفية جديدة" من الشاشة الرئيسية

### إجراء التصفية
1. **إدخال البيانات الأساسية**:
   - اختر اسم الكاشير من القائمة المنسدلة
   - اختر اسم المسؤول
   - تأكد من التاريخ

2. **إدخال المقبوضات البنكية**:
   - اختر نوع العملية (ماستر، مدى، إلخ)
   - اختر اسم البنك
   - أدخل المبلغ واضغط "إضافة"

3. **إدخال المقبوضات النقدية**:
   - أدخل عدد الأوراق لكل فئة
   - سيتم حساب المجموع تلقائياً

4. **إدخال باقي البيانات**:
   - المبيعات الآجلة
   - المقبوضات من العملاء
   - فواتير المرتجعات

5. **مراجعة الملخص**:
   - أدخل مبيعات النظام
   - راجع الفارق والحالة النهائية

6. **حفظ التصفية**:
   - اضغط "حفظ التصفية" لحفظ البيانات
   - أو "طباعة" للطباعة المباشرة

### الطباعة والتصدير
- **الطباعة عبر المتصفح**: تقرير HTML تفاعلي
- **تصدير PDF**: ملف PDF احترافي
- **تصدير Excel**: جداول بيانات منظمة

## 🔧 الإعدادات المتقدمة

### تخصيص قاعدة البيانات
يمكن تغيير مسار قاعدة البيانات في الملفات التالية:
- `ui/daily_filter.py`
- `ui/filter_entry.py`
- `ui/manage_cashier.py`
- `ui/manage_admins.py`
- `db/init_db.py`

### إضافة بنوك جديدة
في ملف `ui/daily_filter.py`، قسم `create_bank_section()`:
```python
self.bank_name_combo = ctk.CTkComboBox(
    fields_frame,
    values=["الأهلي", "الراجحي", "سامبا", "الرياض", "البلاد", "الإنماء", "الجزيرة", "ساب", "البنك الجديد"],
    width=150
)
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في تثبيت المتطلبات**
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

**2. خطأ في قاعدة البيانات**
```bash
python db/init_db.py
```

**3. مشكلة في الخطوط العربية**
- تأكد من تثبيت خطوط عربية على النظام
- أعد تشغيل التطبيق

**4. مشكلة في الطباعة**
- تأكد من وجود متصفح ويب
- تحقق من إعدادات الطابعة

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع قسم "استكشاف الأخطاء" أعلاه
- تحقق من ملفات السجل في مجلد التطبيق
- تأكد من تحديث جميع المتطلبات

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

- فريق CustomTkinter لواجهة المستخدم الرائعة
- مجتمع Python للمكتبات المفيدة
- جميع المساهمين في تطوير هذا المشروع

## 👨‍💻 معلومات التطوير

**المطور:** محمد الكامل
**الإصدار:** 3.0.0 (إصدار 2025)
**تاريخ الإصدار:** 2025

### 🏆 الميزات المطورة:
- نظام تصفية شامل ومتكامل
- واجهة مستخدم حديثة بتقنية Neumorphic
- تقارير متقدمة مع رسوم بيانية
- نظام طباعة احترافي
- إدارة متقدمة للمستخدمين والصلاحيات
- نظام نسخ احتياطي تلقائي
- تحليلات أداء شاملة
- **🆕 ذكاء اصطناعي متطور**
- **🆕 لوحة معلومات تفاعلية**
- **🆕 تكامل سحابي متقدم**

---

**© 2025 - نظام تصفية الكاشير المتكامل 2025**
**تطوير: محمد الكامل - الإصدار 3.0.0 - جميع الحقوق محفوظة**

**تم تطوير هذا النظام بعناية لتوفير تجربة مستخدم مثالية في إدارة تصفية الكاشير اليومية.**
