# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['web_server.py'],
    pathex=[],
    binaries=[],
    datas=[('web_templates', 'web_templates'), ('web_static', 'web_static'), ('db', 'db'), ('reports', 'reports')],
    hiddenimports=['flask', 'requests', 'pandas'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='WebReportServer_v3.5.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['assets\\icon.ico'],
)
