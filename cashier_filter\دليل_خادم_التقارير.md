# 🌐 دليل خادم التقارير - نظام تصفية الكاشير

## 📋 نظرة عامة

خادم التقارير هو خدمة ويب بسيطة ومجانية تتيح لك الاطلاع على تقارير نظام تصفية الكاشير من أي مكان ومن الهاتف الذكي. يعمل الخادم على الشبكة المحلية ويوفر واجهة ويب متجاوبة وآمنة.

## ✨ الميزات الرئيسية

### 🖥️ واجهة ويب متطورة
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية
- واجهة محسنة خصيصاً للهواتف الذكية
- تحديث تلقائي للبيانات

### 📊 عرض التقارير
- عرض جميع تقارير التصفية
- تفاصيل كاملة لكل تصفية
- إحصائيات سريعة ومؤشرات أداء
- بحث وتصفية متقدم

### 📱 محسن للهاتف
- واجهة خاصة للهواتف الذكية
- تصميم سريع الاستجابة
- إمكانية إضافة للشاشة الرئيسية
- تحديث تلقائي للبيانات

## 🚀 كيفية التشغيل

### الطريقة الأولى: التشغيل السريع (Windows)
1. **انقر نقراً مزدوجاً** على ملف `تشغيل_خادم_التقارير.bat`
2. **انتظر** حتى يبدأ الخادم
3. **سيفتح المتصفح تلقائياً** على الصفحة الرئيسية

### الطريقة الثانية: التشغيل اليدوي
```bash
# في مجلد المشروع
python start_web_server.py
```

### الطريقة الثالثة: التشغيل المتقدم
```bash
# تشغيل مخصص
python web_server.py
```

## 🔗 الروابط والصفحات

### 🖥️ للكمبيوتر
- **الصفحة الرئيسية:** `http://localhost:5000`
- **التقارير:** `http://localhost:5000/reports`
- **واجهة الهاتف:** `http://localhost:5000/mobile`

### 📱 للهاتف
استبدل `*************` بعنوان IP الفعلي لجهازك:
- **الصفحة الرئيسية:** `http://*************:5000`
- **واجهة الهاتف:** `http://*************:5000/mobile`

## 📱 الاستخدام من الهاتف

### 1. الاتصال بنفس الشبكة
- تأكد من أن الهاتف والكمبيوتر متصلان بنفس شبكة WiFi

### 2. معرفة عنوان IP
عند تشغيل الخادم، سيظهر عنوان IP مثل:
```
📱 للوصول من الهاتف:
   http://*************:5000
```

### 3. فتح الرابط في الهاتف
- افتح متصفح الهاتف
- اكتب الرابط المعروض
- أو استخدم رمز QR (إذا كان متاحاً)

### 4. إضافة للشاشة الرئيسية
**في Android:**
1. افتح الرابط في Chrome
2. اضغط على القائمة (⋮)
3. اختر "إضافة إلى الشاشة الرئيسية"

**في iPhone:**
1. افتح الرابط في Safari
2. اضغط على زر المشاركة
3. اختر "إضافة إلى الشاشة الرئيسية"

## 🔧 الإعدادات المتقدمة

### تغيير المنفذ
```python
# في ملف web_server.py
run_server(host='0.0.0.0', port=8080)  # استخدام منفذ 8080
```

### تشغيل في وضع التطوير
```python
run_server(host='0.0.0.0', port=5000, debug=True)
```

### تقييد الوصول للشبكة المحلية فقط
```python
run_server(host='127.0.0.1', port=5000)  # محلي فقط
```

## 🔒 الأمان والخصوصية

### ✅ آمن افتراضياً
- يعمل على الشبكة المحلية فقط
- لا يمكن الوصول إليه من الإنترنت
- البيانات لا تغادر شبكتك المحلية

### 🛡️ إجراءات الأمان
- تشفير البيانات المرسلة
- عدم حفظ كلمات مرور
- قراءة البيانات فقط (لا يمكن التعديل)

### ⚠️ تحذيرات
- لا تشارك عنوان IP مع أشخاص غير موثوقين
- أغلق الخادم عند عدم الحاجة إليه
- تأكد من أمان شبكة WiFi

## 🛠️ استكشاف الأخطاء

### المشكلة: الخادم لا يبدأ
**الحلول:**
```bash
# تحقق من Python
python --version

# تحقق من Flask
pip install Flask

# تحقق من قاعدة البيانات
# تأكد من وجود ملف db/cashier_filter.db
```

### المشكلة: لا يمكن الوصول من الهاتف
**الحلول:**
1. تأكد من نفس الشبكة
2. تحقق من عنوان IP
3. تأكد من عدم حجب Firewall
4. جرب منفذ مختلف

### المشكلة: البيانات لا تظهر
**الحلول:**
1. تأكد من وجود تصفيات في النظام الرئيسي
2. أعد تشغيل الخادم
3. تحقق من قاعدة البيانات

### المشكلة: الخادم بطيء
**الحلول:**
1. أغلق التطبيقات الأخرى
2. استخدم منفذ مختلف
3. أعد تشغيل الراوتر

## 📊 API المتاح

### الحصول على التصفيات
```
GET /api/filters?limit=50
```

### الحصول على تصفية محددة
```
GET /api/filter/123
```

### الحصول على الإحصائيات
```
GET /api/stats
```

## 🔄 التحديث التلقائي

الخادم يدعم التحديث التلقائي للبيانات:
- **الصفحة الرئيسية:** كل 30 ثانية
- **واجهة الهاتف:** كل دقيقة
- **التقارير:** عند الطلب

## 📞 الدعم الفني

### مشاكل شائعة:
1. **"Python غير موجود"** → ثبت Python من python.org
2. **"Flask غير مثبت"** → شغل `pip install Flask`
3. **"قاعدة البيانات غير موجودة"** → شغل التطبيق الرئيسي أولاً
4. **"لا يمكن الوصول من الهاتف"** → تحقق من الشبكة والـ IP

### للمساعدة:
- راجع رسائل الخطأ في نافذة الأوامر
- تأكد من تشغيل التطبيق الرئيسي أولاً
- تحقق من إعدادات الشبكة والـ Firewall

## 🎯 نصائح للاستخدام الأمثل

### للكمبيوتر:
- استخدم الصفحة الرئيسية للنظرة العامة
- استخدم صفحة التقارير للبحث المتقدم
- اطبع التقارير مباشرة من المتصفح

### للهاتف:
- استخدم واجهة الهاتف المخصصة
- أضف الصفحة للشاشة الرئيسية
- استخدم البحث السريع للعثور على التصفيات

### للفرق:
- شارك عنوان IP مع أعضاء الفريق
- استخدم الخادم للمراجعة السريعة
- اطبع التقارير للاجتماعات

---

**تطوير:** محمد الكامل  
**الإصدار:** 1.0.0  
**التاريخ:** 2025  
**الدعم:** مجاني ومفتوح المصدر
