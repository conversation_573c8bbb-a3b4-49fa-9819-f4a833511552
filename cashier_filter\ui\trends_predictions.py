#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة الاتجاهات والتوقعات المتقدمة
Advanced Trends and Predictions Interface

تطوير: محمد الكامل - نظام تصفية الكاشير 2025
Developed by: <PERSON> - Cashier Filter System 2025
"""

import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json
import os
import threading
import time
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

# إعداد matplotlib للعربية
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'Deja<PERSON>u Sans']
plt.rcParams['axes.unicode_minus'] = False

# مسار قاعدة البيانات
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class TrendsPredictionsWindow(ctk.CTkToplevel):
    """نافذة الاتجاهات والتوقعات المتقدمة"""
    
    def __init__(self, master=None):
        super().__init__(master)
        self.title("📈 الاتجاهات والتوقعات المتقدمة")
        self.geometry("1400x900")
        self.configure(bg="#f8f9fa")
        self.resizable(True, True)
        
        # متغيرات البيانات
        self.data = {}
        self.trends_data = {}
        self.predictions_data = {}
        self.kpi_data = {}
        
        # متغيرات الواجهة
        self.analysis_running = False
        self.current_period = "30"  # آخر 30 يوم افتراضياً
        
        # إنشاء الواجهة
        self.create_widgets()
        self.load_initial_data()
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة الرئيسية"""
        
        # الشريط العلوي
        self.create_header()
        
        # شريط التحكم
        self.create_control_panel()
        
        # المحتوى الرئيسي
        self.create_main_content()
        
    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=0)
        header_frame.pack(fill="x", pady=0)
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            header_frame,
            text="📈 الاتجاهات والتوقعات المتقدمة",
            font=("Arial", 28, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="تحليل ذكي للاتجاهات المالية والتنبؤات المستقبلية مع مؤشرات الأداء الرئيسية",
            font=("Arial", 14),
            text_color="#ecf0f1"
        )
        subtitle_label.pack(pady=(0, 15))
        
    def create_control_panel(self):
        """إنشاء لوحة التحكم"""
        control_frame = ctk.CTkFrame(self, fg_color="#e8f4fd", corner_radius=15)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # عنوان لوحة التحكم
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎛️ لوحة التحكم والفلاتر",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        control_title.pack(pady=10)
        
        # إطار الخيارات
        options_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        options_frame.pack(fill="x", padx=20, pady=10)
        
        # الصف الأول من الخيارات
        row1 = ctk.CTkFrame(options_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)
        
        # فترة التحليل
        ctk.CTkLabel(row1, text="فترة التحليل:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.period_var = ctk.CTkComboBox(
            row1,
            values=["7", "15", "30", "60", "90", "180", "365"],
            width=120,
            command=self.on_period_change
        )
        self.period_var.set("30")
        self.period_var.pack(side="left", padx=10)
        
        ctk.CTkLabel(row1, text="يوم", font=("Arial", 12)).pack(side="left", padx=5)
        
        # نوع التحليل
        ctk.CTkLabel(row1, text="نوع التحليل:", font=("Arial", 14, "bold")).pack(side="left", padx=20)
        self.analysis_type = ctk.CTkComboBox(
            row1,
            values=[
                "تحليل شامل",
                "الاتجاهات فقط", 
                "التوقعات فقط",
                "مؤشرات الأداء",
                "تحليل الموسمية",
                "تحليل المخاطر"
            ],
            width=150
        )
        self.analysis_type.set("تحليل شامل")
        self.analysis_type.pack(side="left", padx=10)
        
        # الصف الثاني من الخيارات
        row2 = ctk.CTkFrame(options_frame, fg_color="transparent")
        row2.pack(fill="x", pady=5)
        
        # نوع البيانات
        ctk.CTkLabel(row2, text="نوع البيانات:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.data_type = ctk.CTkComboBox(
            row2,
            values=[
                "الإيرادات اليومية",
                "عدد المعاملات",
                "متوسط قيمة المعاملة",
                "أداء الكاشيرين",
                "توزيع العملاء"
            ],
            width=150
        )
        self.data_type.set("الإيرادات اليومية")
        self.data_type.pack(side="left", padx=10)
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(row2, fg_color="transparent")
        buttons_frame.pack(side="right", padx=20)
        
        # زر التحديث
        self.refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث البيانات",
            command=self.refresh_data,
            width=120,
            fg_color="#3498db",
            hover_color="#2980b9"
        )
        self.refresh_btn.pack(side="left", padx=5)
        
        # زر التصدير
        self.export_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير التقرير",
            command=self.export_report,
            width=120,
            fg_color="#27ae60",
            hover_color="#229954"
        )
        self.export_btn.pack(side="left", padx=5)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # تقسيم المحتوى إلى أعمدة
        main_frame.grid_columnconfigure(0, weight=2)  # العمود الأيسر للرسوم البيانية
        main_frame.grid_columnconfigure(1, weight=1)  # العمود الأيمن للمؤشرات
        main_frame.grid_rowconfigure(0, weight=1)
        
        # العمود الأيسر - الرسوم البيانية
        self.create_charts_panel(main_frame)
        
        # العمود الأيمن - مؤشرات الأداء والتوقعات
        self.create_kpi_panel(main_frame)
        
    def create_charts_panel(self, parent):
        """إنشاء لوحة الرسوم البيانية"""
        charts_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        charts_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
        
        # عنوان لوحة الرسوم البيانية
        charts_title = ctk.CTkLabel(
            charts_frame,
            text="📊 الرسوم البيانية التفاعلية",
            font=("Arial", 20, "bold"),
            text_color="#2c3e50"
        )
        charts_title.pack(pady=15)
        
        # إطار التبويبات للرسوم البيانية
        self.charts_notebook = ttk.Notebook(charts_frame)
        self.charts_notebook.pack(fill="both", expand=True, padx=20, pady=10)
        
        # تبويب الاتجاهات
        self.trends_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.trends_frame, text="📈 الاتجاهات")
        
        # تبويب التوقعات
        self.predictions_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.predictions_frame, text="🔮 التوقعات")
        
        # تبويب التحليل الموسمي
        self.seasonal_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.seasonal_frame, text="🌊 التحليل الموسمي")
        
        # تبويب المقارنات
        self.comparison_frame = ttk.Frame(self.charts_notebook)
        self.charts_notebook.add(self.comparison_frame, text="⚖️ المقارنات")
        
    def create_kpi_panel(self, parent):
        """إنشاء لوحة مؤشرات الأداء"""
        kpi_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=15)
        kpi_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 0))
        
        # عنوان لوحة المؤشرات
        kpi_title = ctk.CTkLabel(
            kpi_frame,
            text="📊 مؤشرات الأداء الرئيسية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        kpi_title.pack(pady=15)
        
        # إطار قابل للتمرير للمؤشرات
        self.kpi_scrollable = ctk.CTkScrollableFrame(kpi_frame, fg_color="transparent")
        self.kpi_scrollable.pack(fill="both", expand=True, padx=15, pady=10)
        
        # إنشاء المؤشرات
        self.create_kpi_widgets()
        
    def create_kpi_widgets(self):
        """إنشاء عناصر مؤشرات الأداء"""
        # مؤشرات الأداء الرئيسية
        self.create_main_kpis()
        
        # التوقعات السريعة
        self.create_quick_predictions()
        
        # التنبيهات والتوصيات
        self.create_alerts_recommendations()
        
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        threading.Thread(target=self._load_data_thread, daemon=True).start()
        
    def _load_data_thread(self):
        """تحميل البيانات في خيط منفصل"""
        try:
            self.load_financial_data()
            self.calculate_trends()
            self.generate_predictions()
            self.calculate_kpis()

            # تحديث الواجهة في الخيط الرئيسي
            self.after(100, self.update_interface)

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.after(100, lambda: messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}"))

    def load_financial_data(self):
        """تحميل البيانات المالية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # حساب تاريخ البداية بناءً على الفترة المحددة
            end_date = datetime.now()
            start_date = end_date - timedelta(days=int(self.current_period))

            # استعلام البيانات اليومية
            query = """
            SELECT
                DATE(created_at) as date,
                COUNT(*) as transactions_count,
                SUM(CAST(total_amount AS REAL)) as total_revenue,
                AVG(CAST(total_amount AS REAL)) as avg_transaction,
                cashier_name
            FROM filters
            WHERE created_at >= ? AND created_at <= ?
            GROUP BY DATE(created_at), cashier_name
            ORDER BY date DESC
            """

            cursor.execute(query, (start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
            results = cursor.fetchall()

            # تنظيم البيانات
            daily_data = {}
            cashier_performance = {}

            for row in results:
                date, count, revenue, avg_trans, cashier = row

                if date not in daily_data:
                    daily_data[date] = {
                        'transactions': 0,
                        'revenue': 0,
                        'avg_transaction': 0
                    }

                daily_data[date]['transactions'] += count or 0
                daily_data[date]['revenue'] += revenue or 0

                # أداء الكاشيرين
                if cashier not in cashier_performance:
                    cashier_performance[cashier] = {
                        'transactions': 0,
                        'revenue': 0,
                        'days_active': 0
                    }

                cashier_performance[cashier]['transactions'] += count or 0
                cashier_performance[cashier]['revenue'] += revenue or 0
                cashier_performance[cashier]['days_active'] += 1

            # حساب المتوسطات
            for date in daily_data:
                if daily_data[date]['transactions'] > 0:
                    daily_data[date]['avg_transaction'] = daily_data[date]['revenue'] / daily_data[date]['transactions']

            self.data = {
                'daily_data': daily_data,
                'cashier_performance': cashier_performance,
                'period_days': int(self.current_period),
                'start_date': start_date,
                'end_date': end_date
            }

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل البيانات المالية: {e}")
            self.data = {}

    def calculate_trends(self):
        """حساب الاتجاهات"""
        if not self.data or not self.data.get('daily_data'):
            self.trends_data = {}
            return

        try:
            daily_data = self.data['daily_data']
            dates = sorted(daily_data.keys())

            if len(dates) < 2:
                self.trends_data = {'trend': 'غير محدد', 'growth_rate': 0}
                return

            # حساب الاتجاه العام للإيرادات
            revenues = [daily_data[date]['revenue'] for date in dates]
            transactions = [daily_data[date]['transactions'] for date in dates]

            # حساب معدل النمو
            if len(revenues) >= 7:
                first_week = sum(revenues[:7]) / 7
                last_week = sum(revenues[-7:]) / 7
                growth_rate = ((last_week - first_week) / first_week * 100) if first_week > 0 else 0
            else:
                growth_rate = 0

            # تحديد الاتجاه
            if growth_rate > 5:
                trend = "تصاعدي قوي"
                trend_color = "#27ae60"
            elif growth_rate > 1:
                trend = "تصاعدي"
                trend_color = "#2ecc71"
            elif growth_rate > -1:
                trend = "مستقر"
                trend_color = "#f39c12"
            elif growth_rate > -5:
                trend = "تنازلي"
                trend_color = "#e74c3c"
            else:
                trend = "تنازلي قوي"
                trend_color = "#c0392b"

            # حساب التقلبات (Volatility)
            if len(revenues) > 1:
                mean_revenue = sum(revenues) / len(revenues)
                variance = sum((r - mean_revenue) ** 2 for r in revenues) / len(revenues)
                volatility = (variance ** 0.5) / mean_revenue if mean_revenue > 0 else 0
            else:
                volatility = 0

            # تحليل الموسمية (أيام الأسبوع)
            weekday_performance = {}
            for date_str in dates:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                weekday = date_obj.strftime('%A')

                if weekday not in weekday_performance:
                    weekday_performance[weekday] = []

                weekday_performance[weekday].append(daily_data[date_str]['revenue'])

            # حساب متوسط كل يوم
            weekday_averages = {}
            for day, revenues_list in weekday_performance.items():
                weekday_averages[day] = sum(revenues_list) / len(revenues_list)

            self.trends_data = {
                'trend': trend,
                'trend_color': trend_color,
                'growth_rate': growth_rate,
                'volatility': volatility,
                'weekday_performance': weekday_averages,
                'total_revenue': sum(revenues),
                'total_transactions': sum(transactions),
                'avg_daily_revenue': sum(revenues) / len(revenues) if revenues else 0,
                'avg_daily_transactions': sum(transactions) / len(transactions) if transactions else 0
            }

        except Exception as e:
            print(f"خطأ في حساب الاتجاهات: {e}")
            self.trends_data = {}

    def generate_predictions(self):
        """توليد التوقعات المستقبلية باستخدام خوارزميات متقدمة"""
        if not self.data or not self.trends_data:
            self.predictions_data = {}
            return

        try:
            daily_data = self.data['daily_data']
            dates = sorted(daily_data.keys())

            if len(dates) < 7:
                self.predictions_data = {'error': 'بيانات غير كافية للتنبؤ'}
                return

            # تحضير البيانات للتحليل المتقدم
            revenues = [daily_data[date]['revenue'] for date in dates]
            transactions = [daily_data[date]['transactions'] for date in dates]

            # خوارزمية التنبؤ المتقدمة
            predictions = self._advanced_prediction_algorithm(revenues, transactions, dates)

            # حساب مستوى الثقة المحسن
            confidence_data = self._calculate_advanced_confidence(revenues)

            # تحليل الاتجاه المستقبلي
            future_trend = self._analyze_future_trend(revenues)

            # تحليل الموسمية للتنبؤ
            seasonal_adjustment = self._calculate_seasonal_adjustment(daily_data, dates)

            # تطبيق التعديل الموسمي
            adjusted_predictions = self._apply_seasonal_adjustment(predictions, seasonal_adjustment)

            self.predictions_data = {
                'next_week': adjusted_predictions['next_week'],
                'next_month': adjusted_predictions['next_month'],
                'next_quarter': adjusted_predictions.get('next_quarter', {}),
                'confidence': confidence_data['level'],
                'confidence_score': confidence_data['score'],
                'confidence_factors': confidence_data['factors'],
                'growth_rate': future_trend['growth_rate'],
                'trend_direction': future_trend['direction'],
                'trend_strength': future_trend['strength'],
                'seasonal_factors': seasonal_adjustment,
                'prediction_method': 'متقدم - متوسط متحرك مرجح + تحليل موسمي',
                'risk_assessment': self._assess_prediction_risk(revenues, confidence_data['score'])
            }

        except Exception as e:
            print(f"خطأ في توليد التوقعات: {e}")
            self.predictions_data = {}

    def _advanced_prediction_algorithm(self, revenues, transactions, dates):
        """خوارزمية التنبؤ المتقدمة"""
        try:
            # المتوسط المتحرك المرجح (أوزان أكبر للبيانات الحديثة)
            weights = np.exp(np.linspace(-1, 0, len(revenues)))
            weights = weights / weights.sum()

            weighted_avg_revenue = np.average(revenues, weights=weights)
            weighted_avg_transactions = np.average(transactions, weights=weights)

            # حساب الاتجاه باستخدام الانحدار الخطي البسيط
            x = np.arange(len(revenues))
            revenue_trend = np.polyfit(x, revenues, 1)[0]  # الميل
            transaction_trend = np.polyfit(x, transactions, 1)[0]

            # التنبؤ للأسبوع القادم
            next_week_revenue = weighted_avg_revenue * 7 + revenue_trend * 7 * 3.5  # متوسط الأسبوع
            next_week_transactions = weighted_avg_transactions * 7 + transaction_trend * 7 * 3.5

            # التنبؤ للشهر القادم
            next_month_revenue = weighted_avg_revenue * 30 + revenue_trend * 30 * 15
            next_month_transactions = weighted_avg_transactions * 30 + transaction_trend * 30 * 15

            # التنبؤ للربع القادم (3 أشهر)
            next_quarter_revenue = weighted_avg_revenue * 90 + revenue_trend * 90 * 45
            next_quarter_transactions = weighted_avg_transactions * 90 + transaction_trend * 90 * 45

            return {
                'next_week': {
                    'revenue': max(0, next_week_revenue),
                    'transactions': max(0, next_week_transactions)
                },
                'next_month': {
                    'revenue': max(0, next_month_revenue),
                    'transactions': max(0, next_month_transactions)
                },
                'next_quarter': {
                    'revenue': max(0, next_quarter_revenue),
                    'transactions': max(0, next_quarter_transactions)
                }
            }

        except Exception as e:
            print(f"خطأ في خوارزمية التنبؤ المتقدمة: {e}")
            # العودة للطريقة البسيطة
            avg_revenue = sum(revenues[-7:]) / 7
            avg_transactions = sum(transactions[-7:]) / 7

            return {
                'next_week': {
                    'revenue': avg_revenue * 7,
                    'transactions': avg_transactions * 7
                },
                'next_month': {
                    'revenue': avg_revenue * 30,
                    'transactions': avg_transactions * 30
                }
            }

    def _calculate_advanced_confidence(self, revenues):
        """حساب مستوى الثقة المحسن"""
        try:
            if len(revenues) < 7:
                return {'level': 'منخفض جداً', 'score': 30, 'factors': {}}

            # عوامل متعددة لحساب الثقة
            factors = {}

            # 1. ثبات البيانات (معامل التباين)
            mean_revenue = np.mean(revenues)
            std_revenue = np.std(revenues)
            cv = std_revenue / mean_revenue if mean_revenue > 0 else 1

            if cv < 0.1:
                stability_score = 95
                factors['stability'] = 'ممتاز'
            elif cv < 0.2:
                stability_score = 80
                factors['stability'] = 'جيد'
            elif cv < 0.3:
                stability_score = 65
                factors['stability'] = 'متوسط'
            elif cv < 0.5:
                stability_score = 45
                factors['stability'] = 'ضعيف'
            else:
                stability_score = 25
                factors['stability'] = 'ضعيف جداً'

            # 2. كمية البيانات
            data_quantity = len(revenues)
            if data_quantity >= 60:
                quantity_score = 95
                factors['data_quantity'] = 'ممتاز'
            elif data_quantity >= 30:
                quantity_score = 80
                factors['data_quantity'] = 'جيد'
            elif data_quantity >= 14:
                quantity_score = 65
                factors['data_quantity'] = 'متوسط'
            elif data_quantity >= 7:
                quantity_score = 45
                factors['data_quantity'] = 'ضعيف'
            else:
                quantity_score = 25
                factors['data_quantity'] = 'ضعيف جداً'

            # 3. وضوح الاتجاه
            x = np.arange(len(revenues))
            correlation = np.corrcoef(x, revenues)[0, 1]
            trend_clarity = abs(correlation)

            if trend_clarity > 0.8:
                trend_score = 95
                factors['trend_clarity'] = 'واضح جداً'
            elif trend_clarity > 0.6:
                trend_score = 80
                factors['trend_clarity'] = 'واضح'
            elif trend_clarity > 0.4:
                trend_score = 65
                factors['trend_clarity'] = 'متوسط'
            elif trend_clarity > 0.2:
                trend_score = 45
                factors['trend_clarity'] = 'ضعيف'
            else:
                trend_score = 25
                factors['trend_clarity'] = 'غير واضح'

            # 4. انتظام البيانات (عدم وجود فجوات كبيرة)
            recent_data = revenues[-14:] if len(revenues) >= 14 else revenues
            gaps = [abs(recent_data[i] - recent_data[i-1]) for i in range(1, len(recent_data))]
            avg_gap = np.mean(gaps) if gaps else 0
            max_gap = max(gaps) if gaps else 0

            if max_gap < mean_revenue * 0.5:
                regularity_score = 95
                factors['regularity'] = 'منتظم'
            elif max_gap < mean_revenue:
                regularity_score = 75
                factors['regularity'] = 'منتظم نسبياً'
            else:
                regularity_score = 45
                factors['regularity'] = 'غير منتظم'

            # حساب النتيجة الإجمالية (متوسط مرجح)
            total_score = (
                stability_score * 0.4 +      # 40% للثبات
                quantity_score * 0.25 +      # 25% لكمية البيانات
                trend_score * 0.25 +         # 25% لوضوح الاتجاه
                regularity_score * 0.1       # 10% للانتظام
            )

            # تحديد مستوى الثقة
            if total_score >= 90:
                confidence_level = "عالي جداً"
            elif total_score >= 75:
                confidence_level = "عالي"
            elif total_score >= 60:
                confidence_level = "متوسط"
            elif total_score >= 45:
                confidence_level = "منخفض"
            else:
                confidence_level = "منخفض جداً"

            return {
                'level': confidence_level,
                'score': int(total_score),
                'factors': factors
            }

        except Exception as e:
            print(f"خطأ في حساب مستوى الثقة: {e}")
            return {'level': 'غير محدد', 'score': 50, 'factors': {}}

    def _analyze_future_trend(self, revenues):
        """تحليل الاتجاه المستقبلي"""
        try:
            if len(revenues) < 7:
                return {'growth_rate': 0, 'direction': 'غير محدد', 'strength': 'ضعيف'}

            # حساب معدل النمو باستخدام طرق متعددة

            # 1. مقارنة النصف الأول بالنصف الثاني
            mid_point = len(revenues) // 2
            first_half_avg = np.mean(revenues[:mid_point])
            second_half_avg = np.mean(revenues[mid_point:])

            if first_half_avg > 0:
                growth_rate_1 = ((second_half_avg - first_half_avg) / first_half_avg) * 100
            else:
                growth_rate_1 = 0

            # 2. مقارنة آخر أسبوع بالأسبوع السابق
            if len(revenues) >= 14:
                last_week_avg = np.mean(revenues[-7:])
                prev_week_avg = np.mean(revenues[-14:-7])

                if prev_week_avg > 0:
                    growth_rate_2 = ((last_week_avg - prev_week_avg) / prev_week_avg) * 100
                else:
                    growth_rate_2 = 0
            else:
                growth_rate_2 = growth_rate_1

            # 3. الانحدار الخطي
            x = np.arange(len(revenues))
            slope, intercept = np.polyfit(x, revenues, 1)
            avg_revenue = np.mean(revenues)

            if avg_revenue > 0:
                growth_rate_3 = (slope / avg_revenue) * 100 * len(revenues)
            else:
                growth_rate_3 = 0

            # متوسط مرجح للمعدلات
            final_growth_rate = (
                growth_rate_1 * 0.3 +    # 30% للمقارنة العامة
                growth_rate_2 * 0.5 +    # 50% للاتجاه الحديث
                growth_rate_3 * 0.2      # 20% للاتجاه الخطي
            )

            # تحديد الاتجاه
            if final_growth_rate > 10:
                direction = "تصاعدي قوي"
                strength = "قوي جداً"
            elif final_growth_rate > 5:
                direction = "تصاعدي"
                strength = "قوي"
            elif final_growth_rate > 2:
                direction = "تصاعدي خفيف"
                strength = "متوسط"
            elif final_growth_rate > -2:
                direction = "مستقر"
                strength = "مستقر"
            elif final_growth_rate > -5:
                direction = "تنازلي خفيف"
                strength = "متوسط"
            elif final_growth_rate > -10:
                direction = "تنازلي"
                strength = "قوي"
            else:
                direction = "تنازلي قوي"
                strength = "قوي جداً"

            return {
                'growth_rate': final_growth_rate,
                'direction': direction,
                'strength': strength
            }

        except Exception as e:
            print(f"خطأ في تحليل الاتجاه المستقبلي: {e}")
            return {'growth_rate': 0, 'direction': 'غير محدد', 'strength': 'ضعيف'}

    def _calculate_seasonal_adjustment(self, daily_data, dates):
        """حساب التعديل الموسمي"""
        try:
            weekday_data = {}

            for date_str in dates:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday

                if weekday not in weekday_data:
                    weekday_data[weekday] = []

                weekday_data[weekday].append(daily_data[date_str]['revenue'])

            # حساب متوسط كل يوم
            weekday_averages = {}
            overall_average = 0
            total_days = 0

            for weekday, revenues in weekday_data.items():
                weekday_averages[weekday] = np.mean(revenues)
                overall_average += sum(revenues)
                total_days += len(revenues)

            if total_days > 0:
                overall_average /= total_days

            # حساب عوامل التعديل الموسمي
            seasonal_factors = {}
            weekday_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']

            for weekday in range(7):
                if weekday in weekday_averages and overall_average > 0:
                    factor = weekday_averages[weekday] / overall_average
                    seasonal_factors[weekday_names[weekday]] = factor
                else:
                    seasonal_factors[weekday_names[weekday]] = 1.0

            return seasonal_factors

        except Exception as e:
            print(f"خطأ في حساب التعديل الموسمي: {e}")
            return {}

    def _apply_seasonal_adjustment(self, predictions, seasonal_factors):
        """تطبيق التعديل الموسمي على التوقعات"""
        try:
            if not seasonal_factors:
                return predictions

            # حساب متوسط العوامل الموسمية
            avg_factor = np.mean(list(seasonal_factors.values()))

            # تطبيق التعديل
            adjusted_predictions = {}

            for period, data in predictions.items():
                adjusted_predictions[period] = {}

                for key, value in data.items():
                    # تطبيق تعديل خفيف (10% من التأثير الموسمي)
                    adjustment = 1 + (avg_factor - 1) * 0.1
                    adjusted_predictions[period][key] = value * adjustment

            return adjusted_predictions

        except Exception as e:
            print(f"خطأ في تطبيق التعديل الموسمي: {e}")
            return predictions

    def _assess_prediction_risk(self, revenues, confidence_score):
        """تقييم مخاطر التنبؤ"""
        try:
            risk_factors = []
            risk_level = "منخفض"

            # تحليل التقلبات
            if len(revenues) > 1:
                volatility = np.std(revenues) / np.mean(revenues) if np.mean(revenues) > 0 else 0

                if volatility > 0.5:
                    risk_factors.append("تقلبات عالية في البيانات")
                    risk_level = "عالي"
                elif volatility > 0.3:
                    risk_factors.append("تقلبات متوسطة في البيانات")
                    if risk_level == "منخفض":
                        risk_level = "متوسط"

            # تحليل الاتجاه
            if len(revenues) >= 7:
                recent_trend = np.mean(revenues[-3:]) - np.mean(revenues[-7:-3])
                if abs(recent_trend) > np.mean(revenues) * 0.3:
                    risk_factors.append("تغيرات حادة في الاتجاه الحديث")
                    risk_level = "عالي"

            # تحليل مستوى الثقة
            if confidence_score < 50:
                risk_factors.append("مستوى ثقة منخفض")
                risk_level = "عالي"
            elif confidence_score < 70:
                risk_factors.append("مستوى ثقة متوسط")
                if risk_level == "منخفض":
                    risk_level = "متوسط"

            # تحليل كمية البيانات
            if len(revenues) < 14:
                risk_factors.append("كمية بيانات محدودة")
                if risk_level == "منخفض":
                    risk_level = "متوسط"

            if not risk_factors:
                risk_factors.append("لا توجد مخاطر واضحة")

            return {
                'level': risk_level,
                'factors': risk_factors
            }

        except Exception as e:
            print(f"خطأ في تقييم مخاطر التنبؤ: {e}")
            return {'level': 'غير محدد', 'factors': ['خطأ في التحليل']}

    def calculate_kpis(self):
        """حساب مؤشرات الأداء الرئيسية"""
        if not self.data or not self.trends_data:
            self.kpi_data = {}
            return

        try:
            # مؤشرات الأداء الأساسية
            total_revenue = self.trends_data.get('total_revenue', 0)
            total_transactions = self.trends_data.get('total_transactions', 0)
            avg_daily_revenue = self.trends_data.get('avg_daily_revenue', 0)
            avg_daily_transactions = self.trends_data.get('avg_daily_transactions', 0)

            # حساب متوسط قيمة المعاملة
            avg_transaction_value = total_revenue / total_transactions if total_transactions > 0 else 0

            # مقارنة مع الفترة السابقة (إذا توفرت البيانات)
            period_days = self.data.get('period_days', 30)

            self.kpi_data = {
                'total_revenue': total_revenue,
                'total_transactions': total_transactions,
                'avg_daily_revenue': avg_daily_revenue,
                'avg_daily_transactions': avg_daily_transactions,
                'avg_transaction_value': avg_transaction_value,
                'growth_rate': self.trends_data.get('growth_rate', 0),
                'volatility': self.trends_data.get('volatility', 0),
                'trend': self.trends_data.get('trend', 'غير محدد'),
                'trend_color': self.trends_data.get('trend_color', '#95a5a6'),
                'period_days': period_days
            }

        except Exception as e:
            print(f"خطأ في حساب مؤشرات الأداء: {e}")
            self.kpi_data = {}

    def create_main_kpis(self):
        """إنشاء المؤشرات الرئيسية"""
        # إطار المؤشرات الرئيسية
        main_kpis_frame = ctk.CTkFrame(self.kpi_scrollable, fg_color="#ffffff", corner_radius=10)
        main_kpis_frame.pack(fill="x", pady=10)

        # عنوان المؤشرات الرئيسية
        kpi_title = ctk.CTkLabel(
            main_kpis_frame,
            text="📊 المؤشرات الرئيسية",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        kpi_title.pack(pady=10)

        # إطار المؤشرات
        kpis_container = ctk.CTkFrame(main_kpis_frame, fg_color="transparent")
        kpis_container.pack(fill="x", padx=15, pady=10)

        # مؤشر الإيرادات الإجمالية
        self.revenue_kpi = self.create_kpi_widget(
            kpis_container,
            "💰 الإيرادات الإجمالية",
            "0 ريال",
            "#27ae60"
        )

        # مؤشر عدد المعاملات
        self.transactions_kpi = self.create_kpi_widget(
            kpis_container,
            "🧾 إجمالي المعاملات",
            "0",
            "#3498db"
        )

        # مؤشر متوسط قيمة المعاملة
        self.avg_transaction_kpi = self.create_kpi_widget(
            kpis_container,
            "📈 متوسط قيمة المعاملة",
            "0 ريال",
            "#9b59b6"
        )

        # مؤشر معدل النمو
        self.growth_kpi = self.create_kpi_widget(
            kpis_container,
            "📊 معدل النمو",
            "0%",
            "#f39c12"
        )

    def create_kpi_widget(self, parent, title, value, color):
        """إنشاء عنصر مؤشر أداء"""
        kpi_frame = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)
        kpi_frame.pack(fill="x", pady=5)

        # العنوان
        title_label = ctk.CTkLabel(
            kpi_frame,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(10, 5))

        # القيمة
        value_label = ctk.CTkLabel(
            kpi_frame,
            text=value,
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        value_label.pack(pady=(0, 10))

        return value_label

    def create_quick_predictions(self):
        """إنشاء التوقعات السريعة"""
        predictions_frame = ctk.CTkFrame(self.kpi_scrollable, fg_color="#e8f4fd", corner_radius=10)
        predictions_frame.pack(fill="x", pady=10)

        # عنوان التوقعات
        pred_title = ctk.CTkLabel(
            predictions_frame,
            text="🔮 التوقعات السريعة",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        pred_title.pack(pady=10)

        # إطار التوقعات
        pred_container = ctk.CTkFrame(predictions_frame, fg_color="transparent")
        pred_container.pack(fill="x", padx=15, pady=10)

        # توقع الأسبوع القادم
        next_week_frame = ctk.CTkFrame(pred_container, fg_color="#ffffff", corner_radius=8)
        next_week_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            next_week_frame,
            text="📅 الأسبوع القادم",
            font=("Arial", 12, "bold"),
            text_color="#2c3e50"
        ).pack(pady=5)

        self.next_week_revenue = ctk.CTkLabel(
            next_week_frame,
            text="الإيرادات المتوقعة: 0 ريال",
            font=("Arial", 11),
            text_color="#27ae60"
        )
        self.next_week_revenue.pack(pady=2)

        self.next_week_transactions = ctk.CTkLabel(
            next_week_frame,
            text="المعاملات المتوقعة: 0",
            font=("Arial", 11),
            text_color="#3498db"
        )
        self.next_week_transactions.pack(pady=(0, 5))

        # توقع الشهر القادم
        next_month_frame = ctk.CTkFrame(pred_container, fg_color="#ffffff", corner_radius=8)
        next_month_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            next_month_frame,
            text="📆 الشهر القادم",
            font=("Arial", 12, "bold"),
            text_color="#2c3e50"
        ).pack(pady=5)

        self.next_month_revenue = ctk.CTkLabel(
            next_month_frame,
            text="الإيرادات المتوقعة: 0 ريال",
            font=("Arial", 11),
            text_color="#27ae60"
        )
        self.next_month_revenue.pack(pady=2)

        self.next_month_transactions = ctk.CTkLabel(
            next_month_frame,
            text="المعاملات المتوقعة: 0",
            font=("Arial", 11),
            text_color="#3498db"
        )
        self.next_month_transactions.pack(pady=(0, 5))

        # مستوى الثقة
        confidence_frame = ctk.CTkFrame(pred_container, fg_color="#f8f9fa", corner_radius=8)
        confidence_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(
            confidence_frame,
            text="🎯 مستوى الثقة",
            font=("Arial", 12, "bold"),
            text_color="#2c3e50"
        ).pack(pady=5)

        self.confidence_label = ctk.CTkLabel(
            confidence_frame,
            text="غير محدد",
            font=("Arial", 14, "bold"),
            text_color="#e74c3c"
        )
        self.confidence_label.pack(pady=(0, 5))

    def create_alerts_recommendations(self):
        """إنشاء التنبيهات والتوصيات"""
        alerts_frame = ctk.CTkFrame(self.kpi_scrollable, fg_color="#fff3cd", corner_radius=10)
        alerts_frame.pack(fill="x", pady=10)

        # عنوان التنبيهات
        alerts_title = ctk.CTkLabel(
            alerts_frame,
            text="⚠️ التنبيهات والتوصيات",
            font=("Arial", 16, "bold"),
            text_color="#856404"
        )
        alerts_title.pack(pady=10)

        # إطار التنبيهات
        self.alerts_container = ctk.CTkFrame(alerts_frame, fg_color="transparent")
        self.alerts_container.pack(fill="x", padx=15, pady=10)

        # تنبيه افتراضي
        self.create_alert("📊 جاري تحميل البيانات...", "#6c757d")

    def create_alert(self, message, color="#856404"):
        """إنشاء تنبيه"""
        alert_frame = ctk.CTkFrame(self.alerts_container, fg_color="#ffffff", corner_radius=8)
        alert_frame.pack(fill="x", pady=3)

        alert_label = ctk.CTkLabel(
            alert_frame,
            text=message,
            font=("Arial", 11),
            text_color=color,
            wraplength=250
        )
        alert_label.pack(pady=8, padx=10)

    def update_interface(self):
        """تحديث الواجهة بالبيانات الجديدة"""
        try:
            # تحديث المؤشرات الرئيسية
            self.update_main_kpis()

            # تحديث التوقعات
            self.update_predictions_display()

            # تحديث التنبيهات
            self.update_alerts()

            # تحديث الرسوم البيانية
            self.update_charts()

        except Exception as e:
            print(f"خطأ في تحديث الواجهة: {e}")

    def update_main_kpis(self):
        """تحديث المؤشرات الرئيسية"""
        if not self.kpi_data:
            return

        try:
            # تحديث الإيرادات الإجمالية
            total_revenue = self.kpi_data.get('total_revenue', 0)
            self.revenue_kpi.configure(text=f"{total_revenue:,.0f} ريال")

            # تحديث عدد المعاملات
            total_transactions = self.kpi_data.get('total_transactions', 0)
            self.transactions_kpi.configure(text=f"{total_transactions:,.0f}")

            # تحديث متوسط قيمة المعاملة
            avg_transaction = self.kpi_data.get('avg_transaction_value', 0)
            self.avg_transaction_kpi.configure(text=f"{avg_transaction:,.0f} ريال")

            # تحديث معدل النمو
            growth_rate = self.kpi_data.get('growth_rate', 0)
            growth_color = self.kpi_data.get('trend_color', '#f39c12')
            self.growth_kpi.configure(text=f"{growth_rate:+.1f}%")

        except Exception as e:
            print(f"خطأ في تحديث المؤشرات الرئيسية: {e}")

    def update_predictions_display(self):
        """تحديث عرض التوقعات"""
        if not self.predictions_data:
            return

        try:
            # تحديث توقعات الأسبوع القادم
            next_week = self.predictions_data.get('next_week', {})
            next_week_revenue = next_week.get('revenue', 0)
            next_week_trans = next_week.get('transactions', 0)

            self.next_week_revenue.configure(text=f"الإيرادات المتوقعة: {next_week_revenue:,.0f} ريال")
            self.next_week_transactions.configure(text=f"المعاملات المتوقعة: {next_week_trans:,.0f}")

            # تحديث توقعات الشهر القادم
            next_month = self.predictions_data.get('next_month', {})
            next_month_revenue = next_month.get('revenue', 0)
            next_month_trans = next_month.get('transactions', 0)

            self.next_month_revenue.configure(text=f"الإيرادات المتوقعة: {next_month_revenue:,.0f} ريال")
            self.next_month_transactions.configure(text=f"المعاملات المتوقعة: {next_month_trans:,.0f}")

            # تحديث مستوى الثقة
            confidence = self.predictions_data.get('confidence', 'غير محدد')
            confidence_score = self.predictions_data.get('confidence_score', 0)

            if confidence_score >= 80:
                confidence_color = "#27ae60"
            elif confidence_score >= 60:
                confidence_color = "#f39c12"
            else:
                confidence_color = "#e74c3c"

            self.confidence_label.configure(text=f"{confidence} ({confidence_score}%)", text_color=confidence_color)

        except Exception as e:
            print(f"خطأ في تحديث التوقعات: {e}")

    def update_alerts(self):
        """تحديث التنبيهات والتوصيات الذكية"""
        try:
            # مسح التنبيهات الحالية
            for widget in self.alerts_container.winfo_children():
                widget.destroy()

            alerts = []

            # تحليل شامل للبيانات وإنشاء التنبيهات الذكية
            alerts.extend(self._analyze_performance_alerts())
            alerts.extend(self._analyze_trend_alerts())
            alerts.extend(self._analyze_prediction_alerts())
            alerts.extend(self._analyze_risk_alerts())
            alerts.extend(self._generate_recommendations())

            # عرض التنبيهات
            if not alerts:
                alerts.append(("📊 لا توجد تنبيهات حالياً - الأداء ضمن المعدل الطبيعي", "#6c757d"))

            # ترتيب التنبيهات حسب الأولوية
            alerts = self._prioritize_alerts(alerts)

            for alert_text, alert_color, priority in alerts:
                self.create_smart_alert(alert_text, alert_color, priority)

        except Exception as e:
            print(f"خطأ في تحديث التنبيهات: {e}")
            self.create_alert("❌ خطأ في تحميل التنبيهات", "#e74c3c")

    def _analyze_performance_alerts(self):
        """تحليل تنبيهات الأداء"""
        alerts = []

        if not self.kpi_data:
            return alerts

        try:
            growth_rate = self.kpi_data.get('growth_rate', 0)
            volatility = self.kpi_data.get('volatility', 0)
            total_revenue = self.kpi_data.get('total_revenue', 0)
            total_transactions = self.kpi_data.get('total_transactions', 0)

            # تنبيهات النمو المتقدمة
            if growth_rate > 15:
                alerts.append(("🚀 نمو استثنائي! معدل النمو يتجاوز 15% - فرصة للتوسع", "#27ae60", "عالي"))
            elif growth_rate > 10:
                alerts.append(("📈 نمو ممتاز! معدل النمو يتجاوز 10% - استمر على هذا المنوال", "#27ae60", "متوسط"))
            elif growth_rate > 5:
                alerts.append(("📊 نمو جيد، يمكن تحسينه بالتركيز على أيام الذروة", "#2ecc71", "منخفض"))
            elif growth_rate > 1:
                alerts.append(("📉 نمو بطيء، راجع استراتيجيات التسويق والعروض", "#f39c12", "متوسط"))
            elif growth_rate > -5:
                alerts.append(("⚠️ انخفاض طفيف، تحتاج مراجعة العمليات اليومية", "#e67e22", "متوسط"))
            elif growth_rate > -10:
                alerts.append(("🚨 انخفاض ملحوظ، مراجعة فورية للاستراتيجية مطلوبة", "#e74c3c", "عالي"))
            else:
                alerts.append(("🆘 انخفاض حاد، تدخل عاجل وخطة إنقاذ مطلوبة", "#c0392b", "حرج"))

            # تنبيهات التقلبات المتقدمة
            if volatility > 0.7:
                alerts.append(("⚡ تقلبات عالية جداً، راجع العوامل الخارجية والداخلية", "#e74c3c", "عالي"))
            elif volatility > 0.5:
                alerts.append(("📊 تقلبات عالية، حدد أسباب عدم الاستقرار", "#f39c12", "متوسط"))
            elif volatility > 0.3:
                alerts.append(("📈 تقلبات متوسطة، راقب الاتجاه العام", "#3498db", "منخفض"))
            elif volatility < 0.1:
                alerts.append(("✅ أداء مستقر ومتسق - ممتاز!", "#27ae60", "منخفض"))

            # تنبيهات حجم العمل
            if total_transactions > 0:
                avg_transaction = total_revenue / total_transactions
                if avg_transaction < 50:
                    alerts.append(("💰 متوسط قيمة المعاملة منخفض، ركز على البيع الإضافي", "#f39c12", "متوسط"))
                elif avg_transaction > 500:
                    alerts.append(("💎 متوسط قيمة المعاملة عالي - عملاء مميزون!", "#27ae60", "منخفض"))

            return alerts

        except Exception as e:
            print(f"خطأ في تحليل تنبيهات الأداء: {e}")
            return []

    def _analyze_trend_alerts(self):
        """تحليل تنبيهات الاتجاهات"""
        alerts = []

        if not self.trends_data:
            return alerts

        try:
            weekday_performance = self.trends_data.get('weekday_performance', {})

            if weekday_performance:
                # تحليل أيام الأسبوع
                sorted_days = sorted(weekday_performance.items(), key=lambda x: x[1], reverse=True)
                best_day = sorted_days[0]
                worst_day = sorted_days[-1]

                # حساب الفرق بين أفضل وأسوأ يوم
                performance_gap = (best_day[1] - worst_day[1]) / best_day[1] * 100 if best_day[1] > 0 else 0

                if performance_gap > 50:
                    alerts.append((f"📊 فجوة كبيرة في الأداء: {best_day[0]} أفضل من {worst_day[0]} بـ {performance_gap:.0f}%", "#e67e22", "متوسط"))

                alerts.append((f"🌟 أفضل أيام الأسبوع: {best_day[0]} ({best_day[1]:,.0f} ريال)", "#3498db", "منخفض"))
                alerts.append((f"📉 أضعف أيام الأسبوع: {worst_day[0]} ({worst_day[1]:,.0f} ريال)", "#95a5a6", "منخفض"))

                # تحليل نهاية الأسبوع
                weekend_days = ['الجمعة', 'السبت']
                weekend_avg = np.mean([weekday_performance.get(day, 0) for day in weekend_days])
                weekday_avg = np.mean([weekday_performance.get(day, 0) for day in weekday_performance.keys() if day not in weekend_days])

                if weekend_avg > weekday_avg * 1.2:
                    alerts.append(("🎉 أداء نهاية الأسبوع ممتاز، استغل هذا الاتجاه", "#27ae60", "منخفض"))
                elif weekend_avg < weekday_avg * 0.8:
                    alerts.append(("📅 أداء نهاية الأسبوع ضعيف، فكر في عروض خاصة", "#f39c12", "متوسط"))

            return alerts

        except Exception as e:
            print(f"خطأ في تحليل تنبيهات الاتجاهات: {e}")
            return []

    def _analyze_prediction_alerts(self):
        """تحليل تنبيهات التوقعات"""
        alerts = []

        if not self.predictions_data:
            return alerts

        try:
            confidence_score = self.predictions_data.get('confidence_score', 0)
            confidence_factors = self.predictions_data.get('confidence_factors', {})
            risk_assessment = self.predictions_data.get('risk_assessment', {})

            # تنبيهات مستوى الثقة
            if confidence_score < 40:
                alerts.append(("🔍 دقة التوقعات منخفضة جداً، احتج بيانات أكثر لتحسين الدقة", "#e74c3c", "عالي"))
            elif confidence_score < 60:
                alerts.append(("📊 دقة التوقعات متوسطة، النتائج قد تحتاج تعديل", "#f39c12", "متوسط"))
            elif confidence_score > 85:
                alerts.append(("🎯 دقة التوقعات عالية، يمكن الاعتماد على النتائج", "#27ae60", "منخفض"))

            # تحليل عوامل الثقة
            if confidence_factors:
                if confidence_factors.get('stability') == 'ضعيف جداً':
                    alerts.append(("⚡ البيانات غير مستقرة، راجع العوامل المؤثرة على الأداء", "#e74c3c", "عالي"))

                if confidence_factors.get('data_quantity') == 'ضعيف':
                    alerts.append(("📈 كمية البيانات محدودة، انتظر فترة أطول لتوقعات أدق", "#3498db", "متوسط"))

                if confidence_factors.get('trend_clarity') == 'غير واضح':
                    alerts.append(("🔄 الاتجاه غير واضح، راقب التطورات القادمة", "#f39c12", "متوسط"))

            # تحليل المخاطر
            if risk_assessment:
                risk_level = risk_assessment.get('level', 'منخفض')
                if risk_level == 'عالي':
                    alerts.append(("⚠️ مستوى مخاطر عالي في التوقعات، كن حذراً في اتخاذ القرارات", "#e74c3c", "عالي"))
                elif risk_level == 'متوسط':
                    alerts.append(("📊 مستوى مخاطر متوسط، راجع العوامل المؤثرة", "#f39c12", "متوسط"))

            return alerts

        except Exception as e:
            print(f"خطأ في تحليل تنبيهات التوقعات: {e}")
            return []

    def _analyze_risk_alerts(self):
        """تحليل تنبيهات المخاطر"""
        alerts = []

        if not self.data or not self.data.get('daily_data'):
            return alerts

        try:
            daily_data = self.data['daily_data']
            dates = sorted(daily_data.keys())
            revenues = [daily_data[date]['revenue'] for date in dates]

            # تحليل الانحرافات الشاذة
            if len(revenues) > 7:
                mean_revenue = np.mean(revenues)
                std_revenue = np.std(revenues)

                # البحث عن القيم الشاذة (أكثر من 2 انحراف معياري)
                outliers = []
                for i, revenue in enumerate(revenues):
                    if abs(revenue - mean_revenue) > 2 * std_revenue:
                        outliers.append((dates[i], revenue))

                if len(outliers) > len(revenues) * 0.1:  # أكثر من 10% قيم شاذة
                    alerts.append(("⚠️ عدد كبير من القيم الشاذة، راجع دقة البيانات", "#e74c3c", "عالي"))
                elif outliers:
                    alerts.append((f"📊 تم اكتشاف {len(outliers)} قيم شاذة، راجع الأيام المحددة", "#f39c12", "متوسط"))

            # تحليل الاتجاه المفاجئ
            if len(revenues) >= 14:
                recent_trend = np.mean(revenues[-7:]) - np.mean(revenues[-14:-7])
                overall_mean = np.mean(revenues)

                if abs(recent_trend) > overall_mean * 0.3:
                    if recent_trend > 0:
                        alerts.append(("📈 ارتفاع مفاجئ في الأداء، تأكد من استدامة هذا التحسن", "#27ae60", "متوسط"))
                    else:
                        alerts.append(("📉 انخفاض مفاجئ في الأداء، تحقق من الأسباب فوراً", "#e74c3c", "عالي"))

            # تحليل الاستقرار الأسبوعي
            if len(revenues) >= 21:  # 3 أسابيع على الأقل
                weekly_averages = []
                for i in range(0, len(revenues) - 6, 7):
                    week_avg = np.mean(revenues[i:i+7])
                    weekly_averages.append(week_avg)

                if len(weekly_averages) >= 3:
                    weekly_volatility = np.std(weekly_averages) / np.mean(weekly_averages)
                    if weekly_volatility > 0.2:
                        alerts.append(("📊 تقلبات أسبوعية عالية، راجع العوامل الموسمية", "#f39c12", "متوسط"))

            return alerts

        except Exception as e:
            print(f"خطأ في تحليل تنبيهات المخاطر: {e}")
            return []

    def _generate_recommendations(self):
        """توليد التوصيات الذكية"""
        recommendations = []

        try:
            # توصيات بناءً على الأداء
            if self.kpi_data:
                growth_rate = self.kpi_data.get('growth_rate', 0)

                if growth_rate > 10:
                    recommendations.append(("💡 فكر في التوسع أو زيادة المخزون لاستغلال النمو", "#3498db", "منخفض"))
                elif growth_rate < -5:
                    recommendations.append(("🔧 راجع استراتيجية التسعير والعروض الترويجية", "#e67e22", "متوسط"))
                    recommendations.append(("👥 قم بتدريب الكاشيرين على تقنيات البيع", "#3498db", "منخفض"))

            # توصيات بناءً على الاتجاهات
            if self.trends_data:
                weekday_performance = self.trends_data.get('weekday_performance', {})
                if weekday_performance:
                    sorted_days = sorted(weekday_performance.items(), key=lambda x: x[1])
                    worst_day = sorted_days[0]

                    recommendations.append((f"📅 ركز على تحسين أداء يوم {worst_day[0]} بعروض خاصة", "#3498db", "منخفض"))
                    recommendations.append(("📊 حلل أسباب التفاوت في الأداء بين الأيام", "#6c757d", "منخفض"))

            # توصيات بناءً على التوقعات
            if self.predictions_data:
                confidence_score = self.predictions_data.get('confidence_score', 0)

                if confidence_score < 60:
                    recommendations.append(("📈 اجمع المزيد من البيانات لتحسين دقة التوقعات", "#3498db", "منخفض"))

                trend_direction = self.predictions_data.get('trend_direction', '')
                if 'تنازلي' in trend_direction:
                    recommendations.append(("🎯 ضع خطة لعكس الاتجاه التنازلي", "#e67e22", "متوسط"))
                    recommendations.append(("💰 راجع استراتيجية التسعير والعروض", "#f39c12", "متوسط"))

            # توصيات عامة
            recommendations.append(("📊 راجع هذا التحليل أسبوعياً لمتابعة التحسن", "#6c757d", "منخفض"))
            recommendations.append(("📱 استخدم التطبيق المحمول لمتابعة الأداء", "#3498db", "منخفض"))

            return recommendations

        except Exception as e:
            print(f"خطأ في توليد التوصيات: {e}")
            return []

    def _prioritize_alerts(self, alerts):
        """ترتيب التنبيهات حسب الأولوية"""
        try:
            priority_order = {'حرج': 0, 'عالي': 1, 'متوسط': 2, 'منخفض': 3}

            # إضافة أولوية افتراضية للتنبيهات التي لا تحتوي على أولوية
            processed_alerts = []
            for alert in alerts:
                if len(alert) == 2:
                    processed_alerts.append((alert[0], alert[1], 'منخفض'))
                else:
                    processed_alerts.append(alert)

            # ترتيب حسب الأولوية
            sorted_alerts = sorted(processed_alerts, key=lambda x: priority_order.get(x[2], 3))

            return sorted_alerts

        except Exception as e:
            print(f"خطأ في ترتيب التنبيهات: {e}")
            return alerts

    def create_smart_alert(self, message, color="#856404", priority="منخفض"):
        """إنشاء تنبيه ذكي مع أولوية"""
        alert_frame = ctk.CTkFrame(self.alerts_container, fg_color="#ffffff", corner_radius=8)
        alert_frame.pack(fill="x", pady=3)

        # إطار المحتوى
        content_frame = ctk.CTkFrame(alert_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=10, pady=8)

        # أيقونة الأولوية
        priority_icons = {
            'حرج': '🆘',
            'عالي': '⚠️',
            'متوسط': '📊',
            'منخفض': '💡'
        }

        priority_icon = priority_icons.get(priority, '📊')

        # النص مع الأيقونة
        full_message = f"{priority_icon} {message}"

        alert_label = ctk.CTkLabel(
            content_frame,
            text=full_message,
            font=("Arial", 11),
            text_color=color,
            wraplength=250,
            justify="right"
        )
        alert_label.pack(side="right", fill="x", expand=True)

        # شريط الأولوية
        priority_colors = {
            'حرج': '#c0392b',
            'عالي': '#e74c3c',
            'متوسط': '#f39c12',
            'منخفض': '#95a5a6'
        }

        priority_bar = ctk.CTkFrame(
            content_frame,
            fg_color=priority_colors.get(priority, '#95a5a6'),
            width=4,
            height=20
        )
        priority_bar.pack(side="left", padx=(0, 10))

    def update_charts(self):
        """تحديث الرسوم البيانية"""
        try:
            # تحديث رسم الاتجاهات
            self.create_trends_chart()

            # تحديث رسم التوقعات
            self.create_predictions_chart()

            # تحديث رسم التحليل الموسمي
            self.create_seasonal_chart()

            # تحديث رسم المقارنات
            self.create_comparison_chart()

        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")

    def create_trends_chart(self):
        """إنشاء رسم الاتجاهات"""
        try:
            # مسح الرسم السابق
            for widget in self.trends_frame.winfo_children():
                widget.destroy()

            if not self.data or not self.data.get('daily_data'):
                no_data_label = ctk.CTkLabel(
                    self.trends_frame,
                    text="📊 لا توجد بيانات كافية لعرض الاتجاهات",
                    font=("Arial", 14),
                    text_color="#6c757d"
                )
                no_data_label.pack(expand=True)
                return

            # إنشاء الرسم البياني
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            fig.patch.set_facecolor('white')

            # تحضير البيانات
            daily_data = self.data['daily_data']
            dates = sorted(daily_data.keys())
            revenues = [daily_data[date]['revenue'] for date in dates]
            transactions = [daily_data[date]['transactions'] for date in dates]

            # تحويل التواريخ
            date_objects = [datetime.strptime(date, '%Y-%m-%d') for date in dates]

            # رسم الإيرادات
            ax1.plot(date_objects, revenues, marker='o', linewidth=2, markersize=4, color='#27ae60')
            ax1.set_title('اتجاه الإيرادات اليومية', fontsize=14, fontweight='bold', pad=20)
            ax1.set_ylabel('الإيرادات (ريال)', fontsize=12)
            ax1.grid(True, alpha=0.3)
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))

            # رسم المعاملات
            ax2.plot(date_objects, transactions, marker='s', linewidth=2, markersize=4, color='#3498db')
            ax2.set_title('اتجاه عدد المعاملات اليومية', fontsize=14, fontweight='bold', pad=20)
            ax2.set_ylabel('عدد المعاملات', fontsize=12)
            ax2.set_xlabel('التاريخ', fontsize=12)
            ax2.grid(True, alpha=0.3)
            ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.trends_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)

        except Exception as e:
            print(f"خطأ في إنشاء رسم الاتجاهات: {e}")
            error_label = ctk.CTkLabel(
                self.trends_frame,
                text="❌ خطأ في تحميل رسم الاتجاهات",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True)

    def create_predictions_chart(self):
        """إنشاء رسم التوقعات"""
        try:
            # مسح الرسم السابق
            for widget in self.predictions_frame.winfo_children():
                widget.destroy()

            if not self.predictions_data or 'error' in self.predictions_data:
                no_data_label = ctk.CTkLabel(
                    self.predictions_frame,
                    text="🔮 لا توجد بيانات كافية للتنبؤ",
                    font=("Arial", 14),
                    text_color="#6c757d"
                )
                no_data_label.pack(expand=True)
                return

            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(12, 6))
            fig.patch.set_facecolor('white')

            # البيانات التاريخية
            if self.data and self.data.get('daily_data'):
                daily_data = self.data['daily_data']
                dates = sorted(daily_data.keys())[-14:]  # آخر 14 يوم
                revenues = [daily_data[date]['revenue'] for date in dates]
                date_objects = [datetime.strptime(date, '%Y-%m-%d') for date in dates]

                # رسم البيانات التاريخية
                ax.plot(date_objects, revenues, marker='o', linewidth=2,
                       color='#3498db', label='البيانات الفعلية')

                # إضافة التوقعات
                last_date = date_objects[-1]
                next_week_dates = [last_date + timedelta(days=i) for i in range(1, 8)]

                # حساب التوقعات اليومية للأسبوع القادم
                next_week_revenue = self.predictions_data['next_week']['revenue']
                daily_prediction = next_week_revenue / 7
                predictions = [daily_prediction] * 7

                # رسم التوقعات
                ax.plot(next_week_dates, predictions, marker='s', linewidth=2,
                       linestyle='--', color='#e74c3c', label='التوقعات')

                # منطقة الثقة
                confidence_score = self.predictions_data.get('confidence_score', 50) / 100
                margin = daily_prediction * (1 - confidence_score) * 0.5

                upper_bound = [p + margin for p in predictions]
                lower_bound = [p - margin for p in predictions]

                ax.fill_between(next_week_dates, lower_bound, upper_bound,
                               alpha=0.3, color='#e74c3c', label='نطاق الثقة')

            ax.set_title('التوقعات المستقبلية للإيرادات', fontsize=14, fontweight='bold', pad=20)
            ax.set_ylabel('الإيرادات (ريال)', fontsize=12)
            ax.set_xlabel('التاريخ', fontsize=12)
            ax.legend()
            ax.grid(True, alpha=0.3)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.predictions_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)

        except Exception as e:
            print(f"خطأ في إنشاء رسم التوقعات: {e}")
            error_label = ctk.CTkLabel(
                self.predictions_frame,
                text="❌ خطأ في تحميل رسم التوقعات",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True)

    def create_seasonal_chart(self):
        """إنشاء رسم التحليل الموسمي"""
        try:
            # مسح الرسم السابق
            for widget in self.seasonal_frame.winfo_children():
                widget.destroy()

            if not self.trends_data or not self.trends_data.get('weekday_performance'):
                no_data_label = ctk.CTkLabel(
                    self.seasonal_frame,
                    text="🌊 لا توجد بيانات كافية للتحليل الموسمي",
                    font=("Arial", 14),
                    text_color="#6c757d"
                )
                no_data_label.pack(expand=True)
                return

            # إنشاء الرسم البياني
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
            fig.patch.set_facecolor('white')

            # أداء أيام الأسبوع
            weekday_performance = self.trends_data['weekday_performance']
            days = list(weekday_performance.keys())
            values = list(weekday_performance.values())

            # ترجمة أيام الأسبوع
            day_translation = {
                'Monday': 'الاثنين', 'Tuesday': 'الثلاثاء', 'Wednesday': 'الأربعاء',
                'Thursday': 'الخميس', 'Friday': 'الجمعة', 'Saturday': 'السبت', 'Sunday': 'الأحد'
            }
            arabic_days = [day_translation.get(day, day) for day in days]

            # رسم بياني عمودي
            bars = ax1.bar(arabic_days, values, color=['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6', '#1abc9c', '#34495e'])
            ax1.set_title('أداء أيام الأسبوع', fontsize=14, fontweight='bold')
            ax1.set_ylabel('متوسط الإيرادات (ريال)', fontsize=12)
            ax1.tick_params(axis='x', rotation=45)

            # إضافة القيم على الأعمدة
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                        f'{value:,.0f}', ha='center', va='bottom', fontsize=10)

            # رسم دائري للتوزيع
            ax2.pie(values, labels=arabic_days, autopct='%1.1f%%', startangle=90,
                   colors=['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6', '#1abc9c', '#34495e'])
            ax2.set_title('توزيع الإيرادات حسب أيام الأسبوع', fontsize=14, fontweight='bold')

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.seasonal_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)

        except Exception as e:
            print(f"خطأ في إنشاء رسم التحليل الموسمي: {e}")
            error_label = ctk.CTkLabel(
                self.seasonal_frame,
                text="❌ خطأ في تحميل التحليل الموسمي",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True)

    def create_comparison_chart(self):
        """إنشاء رسم المقارنات"""
        try:
            # مسح الرسم السابق
            for widget in self.comparison_frame.winfo_children():
                widget.destroy()

            if not self.data or not self.data.get('cashier_performance'):
                no_data_label = ctk.CTkLabel(
                    self.comparison_frame,
                    text="⚖️ لا توجد بيانات كافية للمقارنات",
                    font=("Arial", 14),
                    text_color="#6c757d"
                )
                no_data_label.pack(expand=True)
                return

            # إنشاء الرسم البياني
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            fig.patch.set_facecolor('white')

            # أداء الكاشيرين
            cashier_performance = self.data['cashier_performance']
            cashiers = list(cashier_performance.keys())[:10]  # أفضل 10 كاشيرين
            revenues = [cashier_performance[cashier]['revenue'] for cashier in cashiers]
            transactions = [cashier_performance[cashier]['transactions'] for cashier in cashiers]

            # رسم الإيرادات حسب الكاشير
            bars1 = ax1.barh(cashiers, revenues, color='#27ae60')
            ax1.set_title('أداء الكاشيرين - الإيرادات', fontsize=14, fontweight='bold')
            ax1.set_xlabel('الإيرادات (ريال)', fontsize=12)

            # إضافة القيم
            for i, (bar, value) in enumerate(zip(bars1, revenues)):
                ax1.text(value + max(revenues)*0.01, bar.get_y() + bar.get_height()/2,
                        f'{value:,.0f}', ha='left', va='center', fontsize=10)

            # رسم المعاملات حسب الكاشير
            bars2 = ax2.barh(cashiers, transactions, color='#3498db')
            ax2.set_title('أداء الكاشيرين - عدد المعاملات', fontsize=14, fontweight='bold')
            ax2.set_xlabel('عدد المعاملات', fontsize=12)

            # إضافة القيم
            for i, (bar, value) in enumerate(zip(bars2, transactions)):
                ax2.text(value + max(transactions)*0.01, bar.get_y() + bar.get_height()/2,
                        f'{value:,.0f}', ha='left', va='center', fontsize=10)

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, self.comparison_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True)

        except Exception as e:
            print(f"خطأ في إنشاء رسم المقارنات: {e}")
            error_label = ctk.CTkLabel(
                self.comparison_frame,
                text="❌ خطأ في تحميل رسم المقارنات",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True)

    def on_period_change(self, value):
        """عند تغيير فترة التحليل"""
        self.current_period = value
        self.refresh_data()

    def refresh_data(self):
        """تحديث البيانات"""
        if self.analysis_running:
            return

        self.analysis_running = True

        # تعطيل الأزرار
        self.refresh_btn.configure(state="disabled", text="🔄 جاري التحديث...")
        self.export_btn.configure(state="disabled")

        # تشغيل التحديث في خيط منفصل
        threading.Thread(target=self._refresh_data_thread, daemon=True).start()

    def _refresh_data_thread(self):
        """تحديث البيانات في خيط منفصل"""
        try:
            time.sleep(0.5)  # محاكاة وقت التحميل

            self.load_financial_data()
            self.calculate_trends()
            self.generate_predictions()
            self.calculate_kpis()

            # تحديث الواجهة
            self.after(100, self._refresh_complete)

        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            self.after(100, self._refresh_complete)

    def _refresh_complete(self):
        """إكمال عملية التحديث"""
        self.analysis_running = False

        # تفعيل الأزرار
        self.refresh_btn.configure(state="normal", text="🔄 تحديث البيانات")
        self.export_btn.configure(state="normal")

        # تحديث الواجهة
        self.update_interface()

    def export_report(self):
        """تصدير التقرير"""
        try:
            from tkinter import filedialog
            import json
            from datetime import datetime

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                title="حفظ تقرير الاتجاهات والتوقعات",
                defaultextension=".json",
                filetypes=[
                    ("JSON files", "*.json"),
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if not filename:
                return

            # إعداد بيانات التقرير
            report_data = {
                'report_info': {
                    'title': 'تقرير الاتجاهات والتوقعات المتقدمة',
                    'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'period_days': self.current_period,
                    'analysis_type': self.analysis_type.get(),
                    'data_type': self.data_type.get()
                },
                'kpi_data': self.kpi_data,
                'trends_data': self.trends_data,
                'predictions_data': self.predictions_data,
                'raw_data': {
                    'daily_data': self.data.get('daily_data', {}),
                    'cashier_performance': self.data.get('cashier_performance', {})
                }
            }

            # حفظ التقرير
            with open(filename, 'w', encoding='utf-8') as f:
                if filename.endswith('.json'):
                    json.dump(report_data, f, ensure_ascii=False, indent=2)
                else:
                    # تنسيق نصي
                    f.write("=" * 60 + "\n")
                    f.write("تقرير الاتجاهات والتوقعات المتقدمة\n")
                    f.write("=" * 60 + "\n\n")

                    f.write(f"تاريخ التقرير: {report_data['report_info']['generated_at']}\n")
                    f.write(f"فترة التحليل: {self.current_period} يوم\n")
                    f.write(f"نوع التحليل: {self.analysis_type.get()}\n")
                    f.write(f"نوع البيانات: {self.data_type.get()}\n\n")

                    # المؤشرات الرئيسية
                    f.write("المؤشرات الرئيسية:\n")
                    f.write("-" * 30 + "\n")
                    if self.kpi_data:
                        f.write(f"إجمالي الإيرادات: {self.kpi_data.get('total_revenue', 0):,.0f} ريال\n")
                        f.write(f"إجمالي المعاملات: {self.kpi_data.get('total_transactions', 0):,.0f}\n")
                        f.write(f"متوسط قيمة المعاملة: {self.kpi_data.get('avg_transaction_value', 0):,.0f} ريال\n")
                        f.write(f"معدل النمو: {self.kpi_data.get('growth_rate', 0):+.1f}%\n")
                        f.write(f"الاتجاه العام: {self.kpi_data.get('trend', 'غير محدد')}\n\n")

                    # التوقعات
                    f.write("التوقعات المستقبلية:\n")
                    f.write("-" * 30 + "\n")
                    if self.predictions_data:
                        next_week = self.predictions_data.get('next_week', {})
                        next_month = self.predictions_data.get('next_month', {})
                        f.write(f"توقع الأسبوع القادم: {next_week.get('revenue', 0):,.0f} ريال\n")
                        f.write(f"توقع الشهر القادم: {next_month.get('revenue', 0):,.0f} ريال\n")
                        f.write(f"مستوى الثقة: {self.predictions_data.get('confidence', 'غير محدد')}\n")

            messagebox.showinfo("نجح التصدير", f"تم حفظ التقرير بنجاح في:\n{filename}")

        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            messagebox.showerror("خطأ في التصدير", f"فشل في حفظ التقرير:\n{e}")

# دالة لفتح النافذة من الواجهة الرئيسية
def open_trends_predictions_window(master=None):
    """فتح نافذة الاتجاهات والتوقعات"""
    try:
        window = TrendsPredictionsWindow(master)
        window.focus()
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة الاتجاهات والتوقعات: {e}")
        messagebox.showerror("خطأ", f"فشل في فتح النافذة:\n{e}")
        return None

if __name__ == "__main__":
    # اختبار النافذة
    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    app = TrendsPredictionsWindow()
    app.mainloop()
