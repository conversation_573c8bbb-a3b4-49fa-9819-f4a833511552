# شاشة اختيار وتعديل التصفية المحفوظة
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json
import os
import sys
from ui.daily_filter import DailyFilterWindow

# إضافة مسار utils للوصول لنظام الإشعارات
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))

try:
    from dashboard_notifier import notify_filter_deleted, notify_data_changed
except ImportError:
    # إذا لم يكن النظام متاحاً، استخدم دوال وهمية
    def notify_filter_deleted(filter_id, filter_data=None):
        print(f"إشعار: تم حذف التصفية رقم {filter_id}")

    def notify_data_changed(change_type, details=None):
        print(f"إشعار: تم تغيير البيانات - {change_type}")

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class EditFilterWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("تعديل تصفية محفوظة")
        self.geometry("900x600")
        self.configure(bg="#f2f3f7")

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()

        self.create_widgets()
        self.load_filters()

    def create_widgets(self):
        # العنوان الرئيسي
        title = ctk.CTkLabel(
            self,
            text="إدارة التصفيات المحفوظة",
            font=("Arial", 26, "bold"),
            text_color="#2c3e50",
            bg_color="#f2f3f7"
        )
        title.pack(pady=20)

        # إطار الإحصائيات
        stats_frame = ctk.CTkFrame(self, fg_color="#34495e", corner_radius=15)
        stats_frame.pack(pady=10, padx=20, fill="x")

        # عنوان الإحصائيات
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📊 إحصائيات سريعة",
            font=("Arial", 16, "bold"),
            text_color="#ecf0f1"
        )
        stats_title.pack(pady=10)

        # إطار البطاقات
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(pady=10, padx=20, fill="x")

        # بطاقة إجمالي التصفيات
        self.total_card = ctk.CTkFrame(cards_frame, fg_color="#3498db", corner_radius=10)
        self.total_card.pack(side="left", fill="both", expand=True, padx=5)

        ctk.CTkLabel(self.total_card, text="إجمالي التصفيات",
                    font=("Arial", 12, "bold"), text_color="white").pack(pady=5)
        self.total_count_label = ctk.CTkLabel(self.total_card, text="0",
                                            font=("Arial", 20, "bold"), text_color="white")
        self.total_count_label.pack(pady=5)

        # بطاقة تصفيات اليوم
        self.today_card = ctk.CTkFrame(cards_frame, fg_color="#27ae60", corner_radius=10)
        self.today_card.pack(side="left", fill="both", expand=True, padx=5)

        ctk.CTkLabel(self.today_card, text="تصفيات اليوم",
                    font=("Arial", 12, "bold"), text_color="white").pack(pady=5)
        self.today_count_label = ctk.CTkLabel(self.today_card, text="0",
                                            font=("Arial", 20, "bold"), text_color="white")
        self.today_count_label.pack(pady=5)

        # بطاقة تصفيات هذا الأسبوع
        self.week_card = ctk.CTkFrame(cards_frame, fg_color="#e67e22", corner_radius=10)
        self.week_card.pack(side="left", fill="both", expand=True, padx=5)

        ctk.CTkLabel(self.week_card, text="تصفيات هذا الأسبوع",
                    font=("Arial", 12, "bold"), text_color="white").pack(pady=5)
        self.week_count_label = ctk.CTkLabel(self.week_card, text="0",
                                           font=("Arial", 20, "bold"), text_color="white")
        self.week_count_label.pack(pady=5)

        # إطار الجدول
        table_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        table_frame.pack(padx=20, pady=10, fill="both", expand=True)

        # عنوان الجدول
        table_title = ctk.CTkLabel(
            table_frame,
            text="📋 قائمة التصفيات المحفوظة",
            font=("Arial", 18, "bold"),
            text_color="#34495e"
        )
        table_title.pack(pady=15)

        # الجدول
        self.tree = ttk.Treeview(
            table_frame,
            columns=("id", "cashier", "admin", "date"),
            show="headings",
            height=15
        )
        self.tree.heading("id", text="رقم التصفية")
        self.tree.heading("cashier", text="اسم الكاشير")
        self.tree.heading("admin", text="اسم المسؤول")
        self.tree.heading("date", text="تاريخ التصفية")

        # تنسيق الأعمدة
        self.tree.column("id", width=100, anchor="center")
        self.tree.column("cashier", width=200, anchor="center")
        self.tree.column("admin", width=200, anchor="center")
        self.tree.column("date", width=150, anchor="center")

        self.tree.pack(padx=20, pady=10, fill="both", expand=True)
        self.tree.bind('<Double-1>', self.on_double_click)

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(self, fg_color="#ecf0f1", corner_radius=15)
        buttons_frame.pack(pady=15, padx=20, fill="x")

        # زر التعديل
        edit_btn = ctk.CTkButton(
            buttons_frame,
            text="✏️ تعديل التصفية المختارة",
            command=self.edit_selected_filter,
            fg_color="#3498db",
            hover_color="#2980b9",
            width=200,
            height=40,
            font=("Arial", 14, "bold")
        )
        edit_btn.pack(side="left", padx=10, pady=15)

        # زر الحذف
        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف التصفية المختارة",
            command=self.delete_selected_filter,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=200,
            height=40,
            font=("Arial", 14, "bold")
        )
        delete_btn.pack(side="left", padx=10, pady=15)

        # زر تحديث القائمة
        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث القائمة",
            command=self.refresh_filters,
            fg_color="#27ae60",
            hover_color="#229954",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        refresh_btn.pack(side="left", padx=10, pady=15)

        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#95a5a6",
            hover_color="#7f8c8d",
            width=100,
            height=40,
            font=("Arial", 14, "bold")
        )
        close_btn.pack(side="right", padx=10, pady=15)

    def load_filters(self):
        """تحميل التصفيات وتحديث الإحصائيات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # تحميل التصفيات
            c.execute('''SELECT f.id, ca.name, f.admin_name, f.date FROM filters f
                         LEFT JOIN cashiers ca ON f.cashier_id=ca.id
                         ORDER BY f.date DESC''')
            filters = c.fetchall()

            # إضافة التصفيات للجدول
            for row in filters:
                self.tree.insert('', 'end', values=row)

            # تحديث الإحصائيات
            self.update_statistics(c)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل التصفيات: {e}")

    def update_statistics(self, cursor):
        """تحديث الإحصائيات"""
        try:
            from datetime import datetime, timedelta

            # الحصول على التاريخ الحالي بالتنسيق الصحيح
            today = datetime.now().strftime('%Y-%m-%d')
            week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            # إجمالي التصفيات
            cursor.execute("SELECT COUNT(*) FROM filters")
            total_count = cursor.fetchone()[0]
            self.total_count_label.configure(text=str(total_count))

            # تصفيات اليوم
            cursor.execute("SELECT COUNT(*) FROM filters WHERE date = ?", (today,))
            today_count = cursor.fetchone()[0]
            self.today_count_label.configure(text=str(today_count))

            # تصفيات هذا الأسبوع
            cursor.execute("""
                SELECT COUNT(*) FROM filters
                WHERE date >= ?
            """, (week_ago,))
            week_count = cursor.fetchone()[0]
            self.week_count_label.configure(text=str(week_count))

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
            # قيم افتراضية في حالة الخطأ
            self.total_count_label.configure(text="0")
            self.today_count_label.configure(text="0")
            self.week_count_label.configure(text="0")

    def on_double_click(self, event):
        """النقر المزدوج لتعديل التصفية"""
        self.edit_selected_filter()

    def edit_selected_filter(self):
        """تعديل التصفية المختارة"""
        item = self.tree.selection()
        if not item:
            messagebox.showwarning("تحذير", "يرجى اختيار تصفية للتعديل")
            return

        filter_id = self.tree.item(item[0])["values"][0]
        filter_info = self.tree.item(item[0])["values"]

        # تأكيد التعديل
        result = messagebox.askyesno(
            "تأكيد التعديل",
            f"هل تريد تعديل التصفية رقم {filter_id}؟\n"
            f"الكاشير: {filter_info[1]}\n"
            f"التاريخ: {filter_info[3]}"
        )

        if result:
            self.edit_filter(filter_id)

    def delete_selected_filter(self):
        """حذف التصفية المختارة"""
        item = self.tree.selection()
        if not item:
            messagebox.showwarning("تحذير", "يرجى اختيار تصفية للحذف")
            return

        filter_id = self.tree.item(item[0])["values"][0]
        filter_info = self.tree.item(item[0])["values"]

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"⚠️ هل أنت متأكد من حذف التصفية رقم {filter_id}؟\n\n"
            f"الكاشير: {filter_info[1]}\n"
            f"المسؤول: {filter_info[2]}\n"
            f"التاريخ: {filter_info[3]}\n\n"
            f"⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!"
        )

        if result:
            self.delete_filter(filter_id)

    def delete_filter(self, filter_id):
        """حذف التصفية من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # الحصول على بيانات التصفية قبل الحذف لإرسال الإشعار
            c.execute("""
                SELECT f.id, f.data, f.date, f.admin_name, c.name as cashier_name
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                WHERE f.id = ?
            """, (filter_id,))
            filter_info = c.fetchone()

            # حذف التصفية
            c.execute("DELETE FROM filters WHERE id = ?", (filter_id,))

            if c.rowcount > 0:
                conn.commit()

                # إعداد بيانات التصفية المحذوفة للإشعار
                filter_data = {}
                if filter_info:
                    try:
                        # استخراج البيانات المالية من JSON
                        data_json = json.loads(filter_info[1]) if filter_info[1] else {}
                        totals = data_json.get('totals', {})

                        filter_data = {
                            "filter_id": filter_info[0],
                            "cashier_name": filter_info[4] or "غير محدد",
                            "admin_name": filter_info[3] or "غير محدد",
                            "date": filter_info[2],
                            "total_amount": sum(totals.values()) if totals else 0,
                            "bank_amount": totals.get('bank', 0),
                            "cash_amount": totals.get('cash', 0),
                            "credit_amount": totals.get('credit', 0),
                            "return_amount": totals.get('return', 0),
                            "client_amount": totals.get('client', 0)
                        }
                    except (json.JSONDecodeError, KeyError) as e:
                        print(f"خطأ في استخراج بيانات التصفية: {e}")
                        filter_data = {
                            "filter_id": filter_id,
                            "cashier_name": "غير محدد",
                            "admin_name": "غير محدد",
                            "date": "غير محدد",
                            "total_amount": 0
                        }

                # إرسال إشعار لوحة المعلومات
                try:
                    notify_filter_deleted(filter_id, filter_data)
                    notify_data_changed("filter_deleted", {
                        "filter_id": filter_id,
                        "impact": "dashboard_update_required",
                        "affected_metrics": ["total_filters", "revenue", "cashier_performance"]
                    })
                    print(f"✅ تم إرسال إشعار حذف التصفية رقم {filter_id} للوحة المعلومات")
                except Exception as e:
                    print(f"⚠️ خطأ في إرسال إشعار لوحة المعلومات: {e}")

                messagebox.showinfo("نجح", f"تم حذف التصفية رقم {filter_id} بنجاح!\n\n📊 سيتم تحديث لوحة المعلومات التفاعلية تلقائياً")

                # تحديث القائمة
                self.refresh_filters()
            else:
                messagebox.showerror("خطأ", "لم يتم العثور على التصفية المحددة")

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف التصفية: {e}")

    def refresh_filters(self):
        """تحديث قائمة التصفيات والإحصائيات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # إعادة تحميل البيانات
            self.load_filters()

            messagebox.showinfo("تم", "تم تحديث قائمة التصفيات والإحصائيات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث القائمة: {e}")

    def edit_filter(self, filter_id):
        # جلب بيانات التصفية
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute("""
            SELECT f.date, ca.name, ca.number, f.admin_name, f.data FROM filters f
            LEFT JOIN cashiers ca ON f.cashier_id=ca.id
            WHERE f.id=?
        """, (filter_id,))
        row = c.fetchone()
        conn.close()
        if row:
            filter_data = {
                "date": row[0],
                "cashier_name": row[1],
                "cashier_id": row[2],
                "admin_name": row[3]
            }
            details_json = row[4]  # البيانات كـ JSON string
            # فتح شاشة التصفية اليومية مع البيانات القديمة
            DailyFilterWindow(self.master, filter_data=filter_data, old_details=details_json, filter_id=filter_id)
            self.destroy()
