# نافذة التكامل السحابي المبسطة (بدون مكتبات خارجية)
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
import os
import threading
import time
from datetime import datetime, timedelta
import hashlib
import base64
from pathlib import Path

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class CloudIntegrationSimpleWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("☁️ التكامل السحابي المتقدم")
        self.geometry("1000x700")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()
        
        # متغيرات التكامل السحابي
        self.cloud_providers = {
            "google_drive": {"name": "Google Drive", "icon": "📁", "status": "غير متصل"},
            "dropbox": {"name": "Dropbox", "icon": "📦", "status": "غير متصل"},
            "onedrive": {"name": "OneDrive", "icon": "☁️", "status": "غير متصل"},
            "aws_s3": {"name": "Amazon S3", "icon": "🗄️", "status": "غير متصل"},
            "azure": {"name": "Azure Storage", "icon": "🔷", "status": "غير متصل"}
        }
        
        self.sync_settings = {
            "auto_sync": True,
            "sync_interval": 30,  # دقائق
            "backup_retention": 30,  # أيام
            "compression": True,
            "encryption": True
        }
        
        self.create_widgets()
        self.load_cloud_settings()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        self.create_header()
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f0f2f5", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إنشاء التبويبات
        self.create_tabs(main_container)

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        # العنوان والحالة
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="☁️ التكامل السحابي المتقدم",
            font=("Arial", 24, "bold"),
            text_color="white"
        )
        title_label.pack(side="left")
        
        # حالة الاتصال
        self.connection_status = ctk.CTkLabel(
            title_frame,
            text="🔴 غير متصل",
            font=("Arial", 12),
            text_color="#fbbf24"
        )
        self.connection_status.pack(side="right")

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب موفري الخدمات السحابية
        self.providers_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.providers_frame, text="🌐 موفري الخدمات")
        self.create_providers_tab()
        
        # تبويب المزامنة والنسخ الاحتياطي
        self.sync_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.sync_frame, text="🔄 المزامنة والنسخ الاحتياطي")
        self.create_sync_tab()
        
        # تبويب إدارة الملفات السحابية
        self.files_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.files_frame, text="📁 إدارة الملفات")
        self.create_files_tab()
        
        # تبويب التقارير والإحصائيات
        self.reports_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.reports_frame, text="📊 التقارير والإحصائيات")
        self.create_reports_tab()

        # تبويب الإعدادات المتقدمة
        self.advanced_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.advanced_frame, text="⚙️ الإعدادات المتقدمة")
        self.create_advanced_tab()

    def create_providers_tab(self):
        """إنشاء تبويب موفري الخدمات السحابية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.providers_frame,
            text="🌐 موفري الخدمات السحابية",
            font=("Arial", 18, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار موفري الخدمات
        providers_container = ctk.CTkScrollableFrame(
            self.providers_frame,
            fg_color="#f8fafc",
            corner_radius=15,
            height=400
        )
        providers_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء بطاقات موفري الخدمات
        for provider_id, provider_info in self.cloud_providers.items():
            self.create_provider_card(providers_container, provider_id, provider_info)

    def create_provider_card(self, parent, provider_id, provider_info):
        """إنشاء بطاقة موفر خدمة سحابية"""
        # إطار البطاقة
        card_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=12)
        card_frame.pack(fill="x", pady=10, padx=10)
        
        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=15)
        
        # الجانب الأيسر - معلومات الموفر
        left_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        left_frame.pack(side="left", fill="x", expand=True)
        
        # اسم الموفر مع الأيقونة
        name_frame = ctk.CTkFrame(left_frame, fg_color="transparent")
        name_frame.pack(anchor="w")
        
        icon_label = ctk.CTkLabel(
            name_frame,
            text=provider_info["icon"],
            font=("Arial", 20)
        )
        icon_label.pack(side="left", padx=(0, 10))
        
        name_label = ctk.CTkLabel(
            name_frame,
            text=provider_info["name"],
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        name_label.pack(side="left")
        
        # حالة الاتصال
        status_color = "#10b981" if provider_info["status"] == "متصل" else "#ef4444"
        status_label = ctk.CTkLabel(
            left_frame,
            text=f"الحالة: {provider_info['status']}",
            font=("Arial", 10),
            text_color=status_color
        )
        status_label.pack(anchor="w", pady=(5, 0))
        
        # الجانب الأيمن - أزرار التحكم
        right_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        right_frame.pack(side="right")
        
        # زر الاتصال/قطع الاتصال
        connect_btn = ctk.CTkButton(
            right_frame,
            text="🔗 اتصال" if provider_info["status"] == "غير متصل" else "🔌 قطع الاتصال",
            command=lambda p=provider_id: self.toggle_provider_connection(p),
            fg_color="#3b82f6" if provider_info["status"] == "غير متصل" else "#ef4444",
            hover_color="#2563eb" if provider_info["status"] == "غير متصل" else "#dc2626",
            width=100,
            height=30
        )
        connect_btn.pack(side="top", pady=2)
        
        # زر الإعدادات
        settings_btn = ctk.CTkButton(
            right_frame,
            text="⚙️ إعدادات",
            command=lambda p=provider_id: self.show_provider_settings(p),
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=100,
            height=30
        )
        settings_btn.pack(side="top", pady=2)

    def create_sync_tab(self):
        """إنشاء تبويب المزامنة والنسخ الاحتياطي"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.sync_frame,
            text="🔄 المزامنة والنسخ الاحتياطي",
            font=("Arial", 18, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self.sync_frame, fg_color="#f8fafc", corner_radius=15)
        settings_frame.pack(fill="x", padx=20, pady=10)
        
        settings_title = ctk.CTkLabel(
            settings_frame,
            text="⚙️ إعدادات المزامنة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        settings_title.pack(pady=15)
        
        # إعدادات المزامنة
        self.create_sync_settings(settings_frame)
        
        # إطار حالة المزامنة
        status_frame = ctk.CTkFrame(self.sync_frame, fg_color="#f8fafc", corner_radius=15)
        status_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 حالة المزامنة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        status_title.pack(pady=15)
        
        # معلومات حالة المزامنة
        self.create_sync_status(status_frame)

    def create_sync_settings(self, parent):
        """إنشاء إعدادات المزامنة"""
        settings_container = ctk.CTkFrame(parent, fg_color="transparent")
        settings_container.pack(fill="x", padx=20, pady=10)
        
        # المزامنة التلقائية
        auto_sync_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        auto_sync_frame.pack(fill="x", pady=5)
        
        self.auto_sync_var = ctk.BooleanVar(value=self.sync_settings["auto_sync"])
        auto_sync_checkbox = ctk.CTkCheckBox(
            auto_sync_frame,
            text="تفعيل المزامنة التلقائية",
            variable=self.auto_sync_var,
            command=self.update_sync_settings,
            font=("Arial", 12)
        )
        auto_sync_checkbox.pack(side="left")
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=15)
        
        sync_now_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 مزامنة الآن",
            command=self.start_manual_sync,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        sync_now_btn.pack(side="left", padx=5)
        
        backup_now_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 نسخ احتياطي الآن",
            command=self.start_manual_backup,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=150,
            height=35
        )
        backup_now_btn.pack(side="left", padx=5)

    def create_sync_status(self, parent):
        """إنشاء معلومات حالة المزامنة"""
        status_container = ctk.CTkFrame(parent, fg_color="transparent")
        status_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # شريط التقدم
        progress_frame = ctk.CTkFrame(status_container, fg_color="#ffffff", corner_radius=10)
        progress_frame.pack(fill="x", pady=5)
        
        self.sync_progress = ctk.CTkProgressBar(
            progress_frame,
            width=400,
            height=20
        )
        self.sync_progress.pack(pady=15)
        self.sync_progress.set(0)
        
        self.progress_label = ctk.CTkLabel(
            progress_frame,
            text="جاهز للمزامنة",
            font=("Arial", 12),
            text_color="#6b7280"
        )
        self.progress_label.pack()

    def create_files_tab(self):
        """إنشاء تبويب إدارة الملفات السحابية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.files_frame,
            text="📁 إدارة الملفات السحابية",
            font=("Arial", 18, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.files_frame, fg_color="#f8fafc", corner_radius=15)
        toolbar_frame.pack(fill="x", padx=20, pady=10)

        toolbar_content = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        toolbar_content.pack(fill="x", padx=20, pady=15)

        # أزرار الأدوات
        upload_btn = ctk.CTkButton(
            toolbar_content,
            text="📤 رفع ملف",
            command=self.upload_file,
            fg_color="#10b981",
            hover_color="#059669",
            width=100,
            height=35
        )
        upload_btn.pack(side="left", padx=5)

        download_btn = ctk.CTkButton(
            toolbar_content,
            text="📥 تحميل ملف",
            command=self.download_selected_file,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=100,
            height=35
        )
        download_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            toolbar_content,
            text="🗑️ حذف ملف",
            command=self.delete_selected_file,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=100,
            height=35
        )
        delete_btn.pack(side="left", padx=5)

        refresh_btn = ctk.CTkButton(
            toolbar_content,
            text="🔄 تحديث",
            command=self.refresh_files,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=100,
            height=35
        )
        refresh_btn.pack(side="left", padx=5)

        # قائمة الملفات
        files_frame = ctk.CTkFrame(self.files_frame, fg_color="#f8fafc", corner_radius=15)
        files_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # جدول الملفات
        columns = ("الاسم", "الحجم", "تاريخ التعديل", "الموفر", "الحالة")
        self.files_tree = ttk.Treeview(files_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.files_tree.heading(col, text=col)
            self.files_tree.column(col, width=120, anchor="center")

        # شريط التمرير للجدول
        files_scrollbar = ttk.Scrollbar(files_frame, orient="vertical", command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=files_scrollbar.set)

        self.files_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        files_scrollbar.pack(side="right", fill="y", pady=10)

        # تحميل الملفات الوهمية
        self.refresh_files()

    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.reports_frame,
            text="📊 التقارير والإحصائيات السحابية",
            font=("Arial", 18, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.reports_frame, fg_color="#f8fafc", corner_radius=15)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 إحصائيات سريعة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        stats_title.pack(pady=15)

        # شبكة الإحصائيات
        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.pack(pady=10)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_grid, "💾 إجمالي البيانات", "2.5 GB", "#3b82f6", 0, 0)
        self.create_stat_card(stats_grid, "📁 عدد الملفات", "1,247", "#10b981", 0, 1)
        self.create_stat_card(stats_grid, "🔄 عمليات المزامنة", "156", "#f59e0b", 0, 2)
        self.create_stat_card(stats_grid, "⚡ سرعة الرفع", "2.3 MB/s", "#8b5cf6", 1, 0)
        self.create_stat_card(stats_grid, "📥 سرعة التحميل", "4.1 MB/s", "#06b6d4", 1, 1)
        self.create_stat_card(stats_grid, "🛡️ الملفات المشفرة", "98.7%", "#ef4444", 1, 2)

        # تقارير مفصلة
        reports_frame = ctk.CTkFrame(self.reports_frame, fg_color="#f8fafc", corner_radius=15)
        reports_frame.pack(fill="both", expand=True, padx=20, pady=10)

        reports_title = ctk.CTkLabel(
            reports_frame,
            text="📋 التقارير المفصلة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        reports_title.pack(pady=15)

        # أزرار التقارير
        reports_buttons = ctk.CTkFrame(reports_frame, fg_color="transparent")
        reports_buttons.pack(pady=10)

        usage_report_btn = ctk.CTkButton(
            reports_buttons,
            text="📊 تقرير الاستخدام",
            command=self.generate_usage_report,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=140,
            height=35
        )
        usage_report_btn.pack(side="left", padx=5)

        sync_report_btn = ctk.CTkButton(
            reports_buttons,
            text="🔄 تقرير المزامنة",
            command=self.generate_sync_report,
            fg_color="#10b981",
            hover_color="#059669",
            width=140,
            height=35
        )
        sync_report_btn.pack(side="left", padx=5)

        security_report_btn = ctk.CTkButton(
            reports_buttons,
            text="🔒 تقرير الأمان",
            command=self.generate_security_report,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=140,
            height=35
        )
        security_report_btn.pack(side="left", padx=5)

        # رسم بياني بسيط للاستخدام
        chart_frame = ctk.CTkFrame(reports_frame, fg_color="#ffffff", corner_radius=10)
        chart_frame.pack(fill="x", padx=15, pady=15)

        chart_title = ctk.CTkLabel(
            chart_frame,
            text="📈 استخدام التخزين السحابي (آخر 7 أيام)",
            font=("Arial", 12, "bold"),
            text_color="#374151"
        )
        chart_title.pack(pady=10)

        # شريط تقدم بسيط لمحاكاة الرسم البياني
        days = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
        usage_values = [0.3, 0.5, 0.7, 0.4, 0.8, 0.6, 0.9]

        for i, (day, value) in enumerate(zip(days, usage_values)):
            day_frame = ctk.CTkFrame(chart_frame, fg_color="transparent")
            day_frame.pack(fill="x", padx=10, pady=2)

            day_label = ctk.CTkLabel(
                day_frame,
                text=day,
                font=("Arial", 10),
                width=80
            )
            day_label.pack(side="left")

            progress = ctk.CTkProgressBar(
                day_frame,
                width=200,
                height=15
            )
            progress.pack(side="left", padx=10)
            progress.set(value)

            value_label = ctk.CTkLabel(
                day_frame,
                text=f"{value*100:.0f}%",
                font=("Arial", 10),
                width=40
            )
            value_label.pack(side="right")

    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.advanced_frame,
            text="⚙️ الإعدادات المتقدمة للتكامل السحابي",
            font=("Arial", 18, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # إطار الأدوات المتقدمة
        tools_frame = ctk.CTkFrame(self.advanced_frame, fg_color="#f8fafc", corner_radius=15)
        tools_frame.pack(fill="x", padx=20, pady=10)

        tools_title = ctk.CTkLabel(
            tools_frame,
            text="🛠️ أدوات متقدمة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        tools_title.pack(pady=15)

        # شبكة الأزرار
        buttons_grid = ctk.CTkFrame(tools_frame, fg_color="transparent")
        buttons_grid.pack(pady=10)

        # الصف الأول من الأزرار
        row1_frame = ctk.CTkFrame(buttons_grid, fg_color="transparent")
        row1_frame.pack(fill="x", pady=5)

        scheduler_btn = ctk.CTkButton(
            row1_frame,
            text="⏰ جدولة المزامنة",
            command=lambda: messagebox.showinfo("قريباً", "ميزة جدولة المزامنة ستكون متاحة قريباً!"),
            fg_color="#8b5cf6",
            hover_color="#7c3aed",
            width=180,
            height=40
        )
        scheduler_btn.pack(side="left", padx=10)

        permissions_btn = ctk.CTkButton(
            row1_frame,
            text="🔐 إدارة الأذونات",
            command=lambda: messagebox.showinfo("قريباً", "ميزة إدارة الأذونات ستكون متاحة قريباً!"),
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=180,
            height=40
        )
        permissions_btn.pack(side="left", padx=10)

        # الصف الثاني من الأزرار
        row2_frame = ctk.CTkFrame(buttons_grid, fg_color="transparent")
        row2_frame.pack(fill="x", pady=5)

        monitor_btn = ctk.CTkButton(
            row2_frame,
            text="📊 مراقبة الأداء",
            command=lambda: self.show_performance_info(),
            fg_color="#06b6d4",
            hover_color="#0891b2",
            width=180,
            height=40
        )
        monitor_btn.pack(side="left", padx=10)

        cleanup_btn = ctk.CTkButton(
            row2_frame,
            text="🧹 تنظيف البيانات",
            command=lambda: self.start_cleanup(),
            fg_color="#f59e0b",
            hover_color="#d97706",
            width=180,
            height=40
        )
        cleanup_btn.pack(side="left", padx=10)

        # إطار الإحصائيات المتقدمة
        stats_frame = ctk.CTkFrame(self.advanced_frame, fg_color="#f8fafc", corner_radius=15)
        stats_frame.pack(fill="both", expand=True, padx=20, pady=10)

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 إحصائيات متقدمة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        stats_title.pack(pady=15)

        # معلومات النظام
        system_info_frame = ctk.CTkFrame(stats_frame, fg_color="#ffffff", corner_radius=10)
        system_info_frame.pack(fill="x", padx=15, pady=10)

        system_info_title = ctk.CTkLabel(
            system_info_frame,
            text="💻 معلومات النظام",
            font=("Arial", 12, "bold"),
            text_color="#374151"
        )
        system_info_title.pack(pady=10)

        # معلومات تفصيلية
        info_text = ctk.CTkTextbox(
            system_info_frame,
            height=150,
            font=("Arial", 10)
        )
        info_text.pack(fill="x", padx=10, pady=10)

        # إضافة معلومات النظام
        system_info = f"""
🖥️ نظام التشغيل: Windows 10/11
🐍 إصدار Python: 3.8+
💾 الذاكرة المستخدمة: 45 MB
📁 مساحة التخزين: 2.5 GB متاحة
🌐 حالة الإنترنت: متصل
⚡ سرعة الاتصال: 50 Mbps
🔒 حالة الأمان: آمن
📊 أداء النظام: ممتاز (95%)
🔄 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}
        """

        info_text.insert("1.0", system_info.strip())
        info_text.configure(state="disabled")

    # الدوال الوظيفية
    
    def load_cloud_settings(self):
        """تحميل إعدادات التكامل السحابي"""
        try:
            settings_file = Path("cloud_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.cloud_providers.update(settings.get('providers', {}))
                    self.sync_settings.update(settings.get('sync_settings', {}))
            
            self.update_connection_status()
            
        except Exception as e:
            print(f"خطأ في تحميل إعدادات السحابة: {e}")

    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        connected_providers = sum(1 for p in self.cloud_providers.values() if p['status'] == 'متصل')
        total_providers = len(self.cloud_providers)
        
        if connected_providers == 0:
            status_text = "🔴 غير متصل"
            status_color = "#ef4444"
        elif connected_providers == total_providers:
            status_text = "🟢 متصل بالكامل"
            status_color = "#10b981"
        else:
            status_text = f"🟡 متصل جزئياً ({connected_providers}/{total_providers})"
            status_color = "#f59e0b"
        
        self.connection_status.configure(text=status_text, text_color=status_color)

    def toggle_provider_connection(self, provider_id):
        """تبديل حالة اتصال موفر الخدمة"""
        provider = self.cloud_providers[provider_id]
        
        if provider['status'] == 'غير متصل':
            # محاكاة الاتصال
            provider['status'] = 'متصل'
            messagebox.showinfo("نجح", f"تم الاتصال بـ {provider['name']} بنجاح")
        else:
            # قطع الاتصال
            provider['status'] = 'غير متصل'
            messagebox.showinfo("نجح", f"تم قطع الاتصال من {provider['name']}")
        
        self.update_connection_status()
        # إعادة إنشاء التبويب لتحديث الواجهة
        self.refresh_providers_tab()

    def show_provider_settings(self, provider_id):
        """عرض إعدادات موفر الخدمة"""
        try:
            provider_info = self.cloud_providers[provider_id]
            settings_window = CloudProviderSettingsWindow(self, provider_id, provider_info)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل فتح إعدادات الموفر: {e}")

    def refresh_providers_tab(self):
        """تحديث تبويب موفري الخدمات"""
        # إعادة إنشاء محتوى التبويب
        for widget in self.providers_frame.winfo_children():
            widget.destroy()
        self.create_providers_tab()

    def update_sync_settings(self):
        """تحديث إعدادات المزامنة"""
        try:
            self.sync_settings['auto_sync'] = self.auto_sync_var.get()
        except Exception as e:
            print(f"خطأ في تحديث إعدادات المزامنة: {e}")

    def start_manual_sync(self):
        """بدء المزامنة اليدوية"""
        # بدء المزامنة في خيط منفصل
        threading.Thread(target=self.perform_sync, daemon=True).start()

    def start_manual_backup(self):
        """بدء النسخ الاحتياطي اليدوي"""
        # بدء النسخ الاحتياطي في خيط منفصل
        threading.Thread(target=self.perform_backup, daemon=True).start()

    def perform_sync(self):
        """تنفيذ عملية المزامنة"""
        try:
            self.update_sync_progress(0, "بدء المزامنة...")
            
            # محاكاة المزامنة
            for i in range(101):
                time.sleep(0.02)
                self.update_sync_progress(i/100, f"مزامنة البيانات... {i}%")
            
            self.update_sync_progress(1.0, "اكتملت المزامنة بنجاح!")
            self.after(2000, lambda: self.update_sync_progress(0, "جاهز للمزامنة"))
            
        except Exception as e:
            print(f"خطأ في المزامنة: {e}")
            self.update_sync_progress(0, "فشلت المزامنة")

    def perform_backup(self):
        """تنفيذ عملية النسخ الاحتياطي"""
        try:
            self.update_sync_progress(0, "بدء النسخ الاحتياطي...")
            
            # محاكاة النسخ الاحتياطي
            for i in range(101):
                time.sleep(0.02)
                self.update_sync_progress(i/100, f"إنشاء نسخة احتياطية... {i}%")
            
            self.update_sync_progress(1.0, "اكتمل النسخ الاحتياطي بنجاح!")
            self.after(2000, lambda: self.update_sync_progress(0, "جاهز للمزامنة"))
            
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي: {e}")
            self.update_sync_progress(0, "فشل النسخ الاحتياطي")

    def update_sync_progress(self, progress, message):
        """تحديث شريط تقدم المزامنة"""
        try:
            self.after(0, lambda: self._update_progress_ui(progress, message))
        except:
            pass

    def _update_progress_ui(self, progress, message):
        """تحديث واجهة شريط التقدم"""
        try:
            self.sync_progress.set(progress)
            self.progress_label.configure(text=message)
        except:
            pass

    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10, width=160, height=70)
        card.grid(row=row, column=col, padx=8, pady=5, sticky="nsew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 9, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(8, 2))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        value_label.pack(pady=(0, 8))

    def upload_file(self):
        """رفع ملف للسحابة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف للرفع",
            filetypes=[
                ("جميع الملفات", "*.*"),
                ("ملفات قاعدة البيانات", "*.db"),
                ("ملفات Excel", "*.xlsx"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات نصية", "*.txt")
            ]
        )

        if file_path:
            threading.Thread(target=self._upload_file_thread, args=(file_path,), daemon=True).start()

    def _upload_file_thread(self, file_path):
        """رفع الملف في خيط منفصل"""
        try:
            file_name = os.path.basename(file_path)
            self.update_sync_progress(0, f"رفع {file_name}...")

            # محاكاة رفع الملف
            for i in range(101):
                time.sleep(0.03)
                self.update_sync_progress(i/100, f"رفع {file_name}... {i}%")

            self.update_sync_progress(1.0, "تم رفع الملف بنجاح!")

            # إضافة الملف للقائمة
            file_size = os.path.getsize(file_path)
            size_str = self.format_file_size(file_size)
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M')

            # إضافة للجدول
            self.files_tree.insert("", 0, values=(
                file_name,
                size_str,
                current_time,
                "Google Drive",
                "مرفوع"
            ))

            self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

        except Exception as e:
            print(f"خطأ في رفع الملف: {e}")
            self.update_sync_progress(0, "فشل رفع الملف")

    def download_selected_file(self):
        """تحميل الملف المحدد من السحابة"""
        selected_item = self.files_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للتحميل")
            return

        file_name = self.files_tree.item(selected_item[0])['values'][0]

        # اختيار مكان الحفظ
        save_path = filedialog.asksaveasfilename(
            title="حفظ الملف",
            initialname=file_name,
            defaultextension=os.path.splitext(file_name)[1]
        )

        if save_path:
            threading.Thread(target=self._download_file_thread, args=(file_name, save_path), daemon=True).start()

    def _download_file_thread(self, file_name, save_path):
        """تحميل الملف في خيط منفصل"""
        try:
            self.update_sync_progress(0, f"تحميل {file_name}...")

            # محاكاة تحميل الملف
            for i in range(101):
                time.sleep(0.02)
                self.update_sync_progress(i/100, f"تحميل {file_name}... {i}%")

            # إنشاء ملف وهمي
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(f"ملف تم تحميله من السحابة: {file_name}\n")
                f.write(f"تاريخ التحميل: {datetime.now()}\n")
                f.write("هذا ملف تجريبي من نظام تصفية الكاشير 2025")

            self.update_sync_progress(1.0, "تم تحميل الملف بنجاح!")
            self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

        except Exception as e:
            print(f"خطأ في تحميل الملف: {e}")
            self.update_sync_progress(0, "فشل تحميل الملف")

    def delete_selected_file(self):
        """حذف الملف المحدد من السحابة"""
        selected_item = self.files_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")
            return

        file_name = self.files_tree.item(selected_item[0])['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الملف '{file_name}'؟"):
            try:
                # محاكاة حذف الملف
                self.update_sync_progress(0.5, f"حذف {file_name}...")
                time.sleep(0.5)

                # إزالة من الجدول
                self.files_tree.delete(selected_item[0])

                self.update_sync_progress(1.0, "تم حذف الملف بنجاح!")
                self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

            except Exception as e:
                print(f"خطأ في حذف الملف: {e}")
                messagebox.showerror("خطأ", f"فشل حذف الملف: {e}")

    def refresh_files(self):
        """تحديث قائمة الملفات"""
        try:
            # مسح القائمة الحالية
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)

            # إضافة ملفات وهمية للعرض
            sample_files = [
                ("cashier_filter_backup.db", "2.5 MB", "2024-01-15 10:30", "Google Drive", "متزامن"),
                ("reports_2024.xlsx", "1.2 MB", "2024-01-14 15:45", "Dropbox", "متزامن"),
                ("settings_backup.json", "15 KB", "2024-01-13 09:20", "OneDrive", "متزامن"),
                ("user_data.csv", "850 KB", "2024-01-12 14:10", "AWS S3", "قيد المزامنة"),
                ("system_log.txt", "320 KB", "2024-01-11 11:55", "Azure", "متزامن"),
                ("financial_report.pdf", "2.1 MB", "2024-01-10 16:30", "Google Drive", "متزامن"),
                ("customer_data.xlsx", "1.8 MB", "2024-01-09 13:20", "Dropbox", "متزامن"),
                ("backup_config.json", "8 KB", "2024-01-08 08:45", "OneDrive", "متزامن")
            ]

            for file_data in sample_files:
                self.files_tree.insert("", "end", values=file_data)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الملفات: {e}")

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def generate_usage_report(self):
        """إنشاء تقرير الاستخدام"""
        try:
            report_window = SimpleReportWindow(self, "تقرير الاستخدام السحابي")

            # بيانات وهمية للتقرير
            usage_data = {
                "📊 إجمالي البيانات المرفوعة": "15.7 GB",
                "📥 إجمالي البيانات المحملة": "8.3 GB",
                "📁 عدد الملفات المرفوعة": "1,247",
                "📂 عدد الملفات المحملة": "892",
                "📏 متوسط حجم الملف": "2.1 MB",
                "🏆 أكثر الموفرين استخداماً": "Google Drive (45%)",
                "🔄 عدد عمليات المزامنة": "156",
                "✅ معدل نجاح المزامنة": "98.7%",
                "⚡ متوسط سرعة الرفع": "2.3 MB/s",
                "📥 متوسط سرعة التحميل": "4.1 MB/s"
            }

            report_window.display_data(usage_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الاستخدام: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")

    def generate_sync_report(self):
        """إنشاء تقرير المزامنة"""
        try:
            report_window = SimpleReportWindow(self, "تقرير المزامنة")

            # بيانات وهمية للتقرير
            sync_data = {
                "📅 آخر مزامنة": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "📁 إجمالي الملفات": "1,247",
                "✅ الملفات المتزامنة": "1,235",
                "❌ الملفات الفاشلة": "12",
                "📈 معدل النجاح": "99.0%",
                "⏱️ متوسط وقت المزامنة": "2.3 دقيقة",
                "🚀 أسرع مزامنة": "45 ثانية",
                "🐌 أبطأ مزامنة": "8.7 دقيقة",
                "🔄 عمليات المزامنة اليوم": "12",
                "📊 إجمالي عمليات المزامنة": "156"
            }

            report_window.display_data(sync_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المزامنة: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")

    def generate_security_report(self):
        """إنشاء تقرير الأمان"""
        try:
            report_window = SimpleReportWindow(self, "تقرير الأمان السحابي")

            # بيانات وهمية للتقرير
            security_data = {
                "🔒 الملفات المشفرة": "1,230 (98.7%)",
                "🔓 الملفات غير المشفرة": "17 (1.3%)",
                "🛡️ نوع التشفير المستخدم": "AES-256",
                "🔑 قوة كلمة المرور": "قوية جداً",
                "🌐 عدد محاولات الوصول": "2,847",
                "❌ محاولات الوصول المرفوضة": "3 (0.1%)",
                "🕵️ آخر تسجيل دخول مشبوه": "لا يوجد",
                "🔐 حالة المصادقة الثنائية": "مفعلة",
                "🚨 عدد التنبيهات الأمنية": "0",
                "✅ تقييم الأمان العام": "ممتاز (A+)"
            }

            report_window.display_data(security_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأمان: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")

    def show_performance_info(self):
        """عرض معلومات الأداء"""
        try:
            info_window = ctk.CTkToplevel(self)
            info_window.title("📊 معلومات الأداء")
            info_window.geometry("500x400")
            info_window.configure(bg="#f0f2f5")

            # العنوان
            title_label = ctk.CTkLabel(
                info_window,
                text="📊 معلومات الأداء السحابي",
                font=("Arial", 16, "bold"),
                text_color="#1e3a8a"
            )
            title_label.pack(pady=20)

            # معلومات الأداء
            info_text = ctk.CTkTextbox(
                info_window,
                height=250,
                font=("Arial", 12)
            )
            info_text.pack(fill="both", expand=True, padx=20, pady=10)

            performance_info = f"""
📊 تقرير الأداء السحابي

⚡ الأداء العام: ممتاز (95%)
💾 استخدام الذاكرة: 45 MB
🌐 سرعة الاتصال: 50 Mbps
🔄 عمليات المزامنة: 156 عملية
✅ معدل النجاح: 98.7%
📁 الملفات المتزامنة: 1,247 ملف
⏱️ متوسط وقت المزامنة: 2.3 دقيقة
🔒 الملفات المشفرة: 98.7%
📈 الاستخدام اليومي: 2.5 GB
🏆 أفضل موفر: Google Drive

📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            info_text.insert("1.0", performance_info.strip())
            info_text.configure(state="disabled")

            # زر الإغلاق
            close_btn = ctk.CTkButton(
                info_window,
                text="❌ إغلاق",
                command=info_window.destroy,
                width=100,
                height=35
            )
            close_btn.pack(pady=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل عرض معلومات الأداء: {e}")

    def start_cleanup(self):
        """بدء عملية التنظيف"""
        if messagebox.askyesno("تأكيد التنظيف", "هل أنت متأكد من تنظيف البيانات السحابية القديمة؟"):
            try:
                # محاكاة عملية التنظيف
                self.update_sync_progress(0, "بدء عملية التنظيف...")

                def cleanup_process():
                    for i in range(101):
                        time.sleep(0.02)
                        self.update_sync_progress(i/100, f"تنظيف البيانات... {i}%")

                    self.update_sync_progress(1.0, "اكتمل التنظيف بنجاح!")
                    self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))
                    messagebox.showinfo("نجح", "تم تنظيف البيانات السحابية بنجاح!")

                threading.Thread(target=cleanup_process, daemon=True).start()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل تنظيف البيانات: {e}")


class SimpleReportWindow(ctk.CTkToplevel):
    """نافذة عرض التقارير المبسطة"""

    def __init__(self, parent, report_title):
        super().__init__(parent)
        self.report_title = report_title

        self.title(report_title)
        self.geometry("600x500")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"📊 {self.report_title}",
            font=("Arial", 16, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # إطار التقرير
        self.report_frame = ctk.CTkScrollableFrame(
            self,
            fg_color="#ffffff",
            corner_radius=15
        )
        self.report_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        export_btn = ctk.CTkButton(
            buttons_frame,
            text="📤 تصدير",
            command=self.export_report,
            fg_color="#10b981",
            hover_color="#059669",
            width=100,
            height=35
        )
        export_btn.pack(side="left", padx=5)

        print_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=100,
            height=35
        )
        print_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=100,
            height=35
        )
        close_btn.pack(side="right", padx=5)

    def display_data(self, data):
        """عرض بيانات التقرير"""
        try:
            # تاريخ التقرير
            date_label = ctk.CTkLabel(
                self.report_frame,
                text=f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                font=("Arial", 12),
                text_color="#6b7280"
            )
            date_label.pack(pady=10)

            # عرض البيانات في شكل بطاقات
            for key, value in data.items():
                self.create_data_card(key, value)

        except Exception as e:
            print(f"خطأ في عرض بيانات التقرير: {e}")

    def create_data_card(self, title, value):
        """إنشاء بطاقة بيانات"""
        card_frame = ctk.CTkFrame(self.report_frame, fg_color="#f8fafc", corner_radius=10)
        card_frame.pack(fill="x", pady=5, padx=15)

        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="#374151"
        )
        title_label.pack(side="left")

        value_label = ctk.CTkLabel(
            content_frame,
            text=str(value),
            font=("Arial", 12),
            text_color="#1e3a8a"
        )
        value_label.pack(side="right")

    def export_report(self):
        """تصدير التقرير"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="تصدير التقرير",
                defaultextension=".txt",
                filetypes=[
                    ("ملف نصي", "*.txt"),
                    ("ملف CSV", "*.csv"),
                    ("ملف JSON", "*.json")
                ]
            )

            if file_path:
                # إنشاء محتوى التقرير
                content = f"{self.report_title}\n"
                content += "=" * 50 + "\n"
                content += f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

                # إضافة البيانات (محاكاة)
                content += "البيانات:\n"
                content += "تم تصدير التقرير بنجاح\n"
                content += "\n© 2025 محمد الكامل - نظام تصفية الكاشير"

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تصدير التقرير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # محاكاة طباعة التقرير
            messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل طباعة التقرير: {e}")

class CloudProviderSettingsWindow(ctk.CTkToplevel):
    """نافذة إعدادات موفر الخدمة السحابية"""

    def __init__(self, parent, provider_id, provider_info):
        super().__init__(parent)
        self.parent = parent
        self.provider_id = provider_id
        self.provider_info = provider_info

        self.title(f"⚙️ إعدادات {provider_info['name']}")
        self.geometry("600x500")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        # إعدادات الموفر
        self.settings = {
            "enabled": provider_info.get("enabled", True),
            "auto_sync": True,
            "sync_interval": 30,
            "max_file_size": 100,
            "encryption": True,
            "backup_enabled": True,
            "compression": False,
            "bandwidth_limit": 0,
            "retry_attempts": 3,
            "timeout": 30
        }

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"⚙️ إعدادات {self.provider_info['name']}",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # إطار المحتوى الرئيسي
        main_frame = ctk.CTkScrollableFrame(
            self,
            fg_color="#ffffff",
            corner_radius=15
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إعدادات عامة
        self.create_general_settings(main_frame)

        # إعدادات المزامنة
        self.create_sync_settings(main_frame)

        # إعدادات الأمان
        self.create_security_settings(main_frame)

        # إعدادات الأداء
        self.create_performance_settings(main_frame)

        # أزرار التحكم
        self.create_control_buttons()

    def create_general_settings(self, parent):
        """إنشاء الإعدادات العامة"""
        # عنوان القسم
        general_title = ctk.CTkLabel(
            parent,
            text="🔧 الإعدادات العامة",
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        general_title.pack(pady=(20, 10), anchor="w")

        # إطار الإعدادات العامة
        general_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        general_frame.pack(fill="x", pady=5, padx=10)

        # تفعيل الموفر
        enabled_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        enabled_frame.pack(fill="x", padx=15, pady=10)

        self.enabled_var = ctk.BooleanVar(value=self.settings["enabled"])
        enabled_checkbox = ctk.CTkCheckBox(
            enabled_frame,
            text=f"تفعيل {self.provider_info['name']}",
            variable=self.enabled_var,
            font=("Arial", 12, "bold")
        )
        enabled_checkbox.pack(anchor="w")

        # المزامنة التلقائية
        auto_sync_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        auto_sync_frame.pack(fill="x", padx=15, pady=5)

        self.auto_sync_var = ctk.BooleanVar(value=self.settings["auto_sync"])
        auto_sync_checkbox = ctk.CTkCheckBox(
            auto_sync_frame,
            text="تفعيل المزامنة التلقائية",
            variable=self.auto_sync_var,
            font=("Arial", 12)
        )
        auto_sync_checkbox.pack(anchor="w")

        # النسخ الاحتياطي
        backup_frame = ctk.CTkFrame(general_frame, fg_color="transparent")
        backup_frame.pack(fill="x", padx=15, pady=5)

        self.backup_var = ctk.BooleanVar(value=self.settings["backup_enabled"])
        backup_checkbox = ctk.CTkCheckBox(
            backup_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.backup_var,
            font=("Arial", 12)
        )
        backup_checkbox.pack(anchor="w")

    def create_sync_settings(self, parent):
        """إنشاء إعدادات المزامنة"""
        # عنوان القسم
        sync_title = ctk.CTkLabel(
            parent,
            text="🔄 إعدادات المزامنة",
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        sync_title.pack(pady=(20, 10), anchor="w")

        # إطار إعدادات المزامنة
        sync_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        sync_frame.pack(fill="x", pady=5, padx=10)

        # فترة المزامنة
        interval_frame = ctk.CTkFrame(sync_frame, fg_color="transparent")
        interval_frame.pack(fill="x", padx=15, pady=10)

        interval_label = ctk.CTkLabel(
            interval_frame,
            text="فترة المزامنة (دقائق):",
            font=("Arial", 12)
        )
        interval_label.pack(side="left")

        self.interval_var = ctk.StringVar(value=str(self.settings["sync_interval"]))
        interval_entry = ctk.CTkEntry(
            interval_frame,
            textvariable=self.interval_var,
            width=100
        )
        interval_entry.pack(side="right")

        # الحد الأقصى لحجم الملف
        filesize_frame = ctk.CTkFrame(sync_frame, fg_color="transparent")
        filesize_frame.pack(fill="x", padx=15, pady=5)

        filesize_label = ctk.CTkLabel(
            filesize_frame,
            text="الحد الأقصى لحجم الملف (MB):",
            font=("Arial", 12)
        )
        filesize_label.pack(side="left")

        self.filesize_var = ctk.StringVar(value=str(self.settings["max_file_size"]))
        filesize_entry = ctk.CTkEntry(
            filesize_frame,
            textvariable=self.filesize_var,
            width=100
        )
        filesize_entry.pack(side="right")

    def create_security_settings(self, parent):
        """إنشاء إعدادات الأمان"""
        # عنوان القسم
        security_title = ctk.CTkLabel(
            parent,
            text="🔒 إعدادات الأمان",
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        security_title.pack(pady=(20, 10), anchor="w")

        # إطار إعدادات الأمان
        security_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        security_frame.pack(fill="x", pady=5, padx=10)

        # تفعيل التشفير
        encryption_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        encryption_frame.pack(fill="x", padx=15, pady=10)

        self.encryption_var = ctk.BooleanVar(value=self.settings["encryption"])
        encryption_checkbox = ctk.CTkCheckBox(
            encryption_frame,
            text="تفعيل التشفير المتقدم (AES-256)",
            variable=self.encryption_var,
            font=("Arial", 12)
        )
        encryption_checkbox.pack(anchor="w")

    def create_performance_settings(self, parent):
        """إنشاء إعدادات الأداء"""
        # عنوان القسم
        performance_title = ctk.CTkLabel(
            parent,
            text="⚡ إعدادات الأداء",
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        performance_title.pack(pady=(20, 10), anchor="w")

        # إطار إعدادات الأداء
        performance_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        performance_frame.pack(fill="x", pady=5, padx=10)

        # ضغط البيانات
        compression_frame = ctk.CTkFrame(performance_frame, fg_color="transparent")
        compression_frame.pack(fill="x", padx=15, pady=10)

        self.compression_var = ctk.BooleanVar(value=self.settings["compression"])
        compression_checkbox = ctk.CTkCheckBox(
            compression_frame,
            text="تفعيل ضغط البيانات",
            variable=self.compression_var,
            font=("Arial", 12)
        )
        compression_checkbox.pack(anchor="w")

        # حد النطاق الترددي
        bandwidth_frame = ctk.CTkFrame(performance_frame, fg_color="transparent")
        bandwidth_frame.pack(fill="x", padx=15, pady=5)

        bandwidth_label = ctk.CTkLabel(
            bandwidth_frame,
            text="حد النطاق الترددي (KB/s، 0 = بلا حدود):",
            font=("Arial", 12)
        )
        bandwidth_label.pack(side="left")

        self.bandwidth_var = ctk.StringVar(value=str(self.settings["bandwidth_limit"]))
        bandwidth_entry = ctk.CTkEntry(
            bandwidth_frame,
            textvariable=self.bandwidth_var,
            width=100
        )
        bandwidth_entry.pack(side="right")

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_settings,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        save_btn.pack(side="left", padx=5)

        # زر اختبار الاتصال
        test_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 اختبار الاتصال",
            command=self.test_connection,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=120,
            height=35
        )
        test_btn.pack(side="left", padx=5)

        # زر إعادة تعيين
        reset_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 إعادة تعيين",
            command=self.reset_settings,
            fg_color="#f59e0b",
            hover_color="#d97706",
            width=120,
            height=35
        )
        reset_btn.pack(side="left", padx=5)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        close_btn.pack(side="right", padx=5)

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # تحديث الإعدادات
            self.settings["enabled"] = self.enabled_var.get()
            self.settings["auto_sync"] = self.auto_sync_var.get()
            self.settings["backup_enabled"] = self.backup_var.get()
            self.settings["encryption"] = self.encryption_var.get()
            self.settings["compression"] = self.compression_var.get()

            # تحديث القيم النصية
            try:
                self.settings["sync_interval"] = int(self.interval_var.get())
                self.settings["max_file_size"] = int(self.filesize_var.get())
                self.settings["bandwidth_limit"] = int(self.bandwidth_var.get())
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للأرقام")
                return

            # حفظ الإعدادات (محاكاة)
            messagebox.showinfo("نجح", f"تم حفظ إعدادات {self.provider_info['name']} بنجاح!")

            # تحديث حالة الموفر في النافذة الرئيسية
            if hasattr(self.parent, 'cloud_providers'):
                self.parent.cloud_providers[self.provider_id]["enabled"] = self.settings["enabled"]
                self.parent.refresh_providers_tab()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل حفظ الإعدادات: {e}")

    def test_connection(self):
        """اختبار الاتصال مع الموفر"""
        try:
            # محاكاة اختبار الاتصال
            messagebox.showinfo("اختبار الاتصال", f"جاري اختبار الاتصال مع {self.provider_info['name']}...")

            # محاكاة تأخير
            self.after(1000, lambda: messagebox.showinfo(
                "نجح الاختبار",
                f"✅ تم الاتصال بنجاح مع {self.provider_info['name']}!\n\n"
                f"📊 معلومات الاتصال:\n"
                f"• السرعة: 2.5 MB/s\n"
                f"• زمن الاستجابة: 45ms\n"
                f"• الحالة: متصل\n"
                f"• المساحة المتاحة: 15 GB"
            ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل اختبار الاتصال: {e}")

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if messagebox.askyesno("تأكيد إعادة التعيين", "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            try:
                # إعادة تعيين القيم
                self.enabled_var.set(True)
                self.auto_sync_var.set(True)
                self.backup_var.set(True)
                self.encryption_var.set(True)
                self.compression_var.set(False)
                self.interval_var.set("30")
                self.filesize_var.set("100")
                self.bandwidth_var.set("0")

                messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات بنجاح!")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل إعادة تعيين الإعدادات: {e}")


class PerformanceMonitorWindow(ctk.CTkToplevel):
    """نافذة مراقبة الأداء"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("📊 مراقب الأداء السحابي")
        self.geometry("700x500")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.start_monitoring()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 مراقب الأداء السحابي",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # إطار المراقبة
        monitor_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        monitor_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # مؤشرات الأداء
        self.create_performance_indicators(monitor_frame)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            self,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        close_btn.pack(pady=10)

    def create_performance_indicators(self, parent):
        """إنشاء مؤشرات الأداء"""
        # عنوان المؤشرات
        indicators_title = ctk.CTkLabel(
            parent,
            text="⚡ مؤشرات الأداء في الوقت الفعلي",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        indicators_title.pack(pady=15)

        # شبكة المؤشرات
        indicators_grid = ctk.CTkFrame(parent, fg_color="transparent")
        indicators_grid.pack(pady=10)

        # مؤشر استخدام الذاكرة
        memory_frame = ctk.CTkFrame(indicators_grid, fg_color="#f8fafc", corner_radius=10)
        memory_frame.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        memory_label = ctk.CTkLabel(
            memory_frame,
            text="💾 استخدام الذاكرة",
            font=("Arial", 12, "bold")
        )
        memory_label.pack(pady=5)

        self.memory_progress = ctk.CTkProgressBar(memory_frame, width=200)
        self.memory_progress.pack(pady=5)
        self.memory_progress.set(0.45)

        self.memory_label = ctk.CTkLabel(
            memory_frame,
            text="45%",
            font=("Arial", 10)
        )
        self.memory_label.pack(pady=5)

        # مؤشر استخدام الشبكة
        network_frame = ctk.CTkFrame(indicators_grid, fg_color="#f8fafc", corner_radius=10)
        network_frame.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        network_label = ctk.CTkLabel(
            network_frame,
            text="🌐 استخدام الشبكة",
            font=("Arial", 12, "bold")
        )
        network_label.pack(pady=5)

        self.network_progress = ctk.CTkProgressBar(network_frame, width=200)
        self.network_progress.pack(pady=5)
        self.network_progress.set(0.23)

        self.network_label = ctk.CTkLabel(
            network_frame,
            text="2.3 MB/s",
            font=("Arial", 10)
        )
        self.network_label.pack(pady=5)

    def start_monitoring(self):
        """بدء المراقبة"""
        self.update_performance_data()

    def update_performance_data(self):
        """تحديث بيانات الأداء"""
        try:
            import random

            # تحديث مؤشر الذاكرة
            memory_usage = random.uniform(0.3, 0.7)
            self.memory_progress.set(memory_usage)
            self.memory_label.configure(text=f"{memory_usage*100:.0f}%")

            # تحديث مؤشر الشبكة
            network_speed = random.uniform(1.0, 5.0)
            self.network_progress.set(network_speed/10)
            self.network_label.configure(text=f"{network_speed:.1f} MB/s")

            # جدولة التحديث التالي
            self.after(2000, self.update_performance_data)

        except:
            pass


# دالة لفتح نافذة التكامل السحابي المبسطة
def open_cloud_integration_simple(parent=None):
    """فتح نافذة التكامل السحابي المبسطة"""
    try:
        window = CloudIntegrationSimpleWindow(parent)
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة التكامل السحابي: {e}")
        if parent:
            messagebox.showerror("خطأ", f"فشل فتح نافذة التكامل السحابي: {e}")
        return None


if __name__ == "__main__":
    # اختبار النافذة
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = CloudIntegrationSimpleWindow()
    app.mainloop()
