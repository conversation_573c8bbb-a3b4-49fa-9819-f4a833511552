# نافذة إدارة المسؤولين
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import hashlib

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class ManageAdminsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("إدارة المسؤولين")
        self.geometry("850x650")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()

        self.create_widgets()
        self.load_admins()

    def create_widgets(self):
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        title_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="🧑‍💼 إدارة المسؤولين",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)

        # إطار إضافة مسؤول جديد
        add_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        add_frame.pack(pady=10, padx=20, fill="x")
        
        add_title = ctk.CTkLabel(
            add_frame,
            text="إضافة مسؤول جديد",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        add_title.pack(pady=10)

        # حقول الإدخال
        input_frame = ctk.CTkFrame(add_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=20, fill="x")

        # اسم المسؤول
        name_label = ctk.CTkLabel(input_frame, text="اسم المسؤول:", font=("Arial", 14))
        name_label.grid(row=0, column=0, padx=10, pady=10, sticky="e")
        
        self.name_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل اسم المسؤول",
            width=200,
            font=("Arial", 12)
        )
        self.name_entry.grid(row=0, column=1, padx=10, pady=10)

        # اسم المستخدم
        username_label = ctk.CTkLabel(input_frame, text="اسم المستخدم:", font=("Arial", 14))
        username_label.grid(row=1, column=0, padx=10, pady=10, sticky="e")
        
        self.username_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل اسم المستخدم",
            width=200,
            font=("Arial", 12)
        )
        self.username_entry.grid(row=1, column=1, padx=10, pady=10)

        # كلمة المرور
        password_label = ctk.CTkLabel(input_frame, text="كلمة المرور:", font=("Arial", 14))
        password_label.grid(row=2, column=0, padx=10, pady=10, sticky="e")
        
        self.password_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل كلمة المرور",
            width=200,
            font=("Arial", 12),
            show="*"
        )
        self.password_entry.grid(row=2, column=1, padx=10, pady=10)

        # تأكيد كلمة المرور
        confirm_label = ctk.CTkLabel(input_frame, text="تأكيد كلمة المرور:", font=("Arial", 14))
        confirm_label.grid(row=3, column=0, padx=10, pady=10, sticky="e")
        
        self.confirm_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أعد إدخال كلمة المرور",
            width=200,
            font=("Arial", 12),
            show="*"
        )
        self.confirm_entry.grid(row=3, column=1, padx=10, pady=10)

        # أزرار العمليات
        btn_frame = ctk.CTkFrame(add_frame, fg_color="#e0e5ec", corner_radius=0)
        btn_frame.pack(pady=10)

        add_btn = ctk.CTkButton(
            btn_frame,
            text="➕ إضافة",
            command=self.add_admin,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=100,
            font=("Arial", 12, "bold")
        )
        add_btn.pack(side="left", padx=5)

        update_btn = ctk.CTkButton(
            btn_frame,
            text="🔄 تحديث",
            command=self.update_admin,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=100,
            font=("Arial", 12, "bold")
        )
        update_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            btn_frame,
            text="🗑️ حذف",
            command=self.delete_admin,
            fg_color="#f44336",
            hover_color="#d32f2f",
            width=100,
            font=("Arial", 12, "bold")
        )
        delete_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            btn_frame,
            text="🔑 إعادة تعيين كلمة المرور",
            command=self.reset_password,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=180,
            font=("Arial", 12, "bold")
        )
        reset_btn.pack(side="left", padx=5)

        # جدول المسؤولين
        table_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        table_frame.pack(pady=10, padx=20, fill="both", expand=True)

        table_title = ctk.CTkLabel(
            table_frame,
            text="قائمة المسؤولين",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        table_title.pack(pady=10)

        # إنشاء الجدول
        columns = ("ID", "الاسم", "اسم المستخدم")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=200, anchor="center")

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # ربط النقر المزدوج
        self.tree.bind("<Double-1>", self.on_select)

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def load_admins(self):
        """تحميل قائمة المسؤولين من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("SELECT id, name, username FROM admins ORDER BY name")
            
            for row in c.fetchall():
                self.tree.insert("", "end", values=row)
                
            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {e}")

    def add_admin(self):
        """إضافة مسؤول جديد"""
        name = self.name_entry.get().strip()
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        confirm = self.confirm_entry.get()

        if not name or not username or not password:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        if password != confirm:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return

        if len(password) < 6:
            messagebox.showwarning("تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            hashed_password = self.hash_password(password)
            c.execute("INSERT INTO admins (name, username, password) VALUES (?, ?, ?)", 
                     (name, username, hashed_password))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة المسؤول بنجاح!")
            self.clear_entries()
            self.load_admins()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً!")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def update_admin(self):
        """تحديث بيانات المسؤول المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مسؤول للتحديث")
            return

        name = self.name_entry.get().strip()
        username = self.username_entry.get().strip()

        if not name or not username:
            messagebox.showwarning("تحذير", "يرجى ملء الاسم واسم المستخدم")
            return

        admin_id = self.tree.item(selected[0])["values"][0]

        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("UPDATE admins SET name=?, username=? WHERE id=?", (name, username, admin_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تحديث بيانات المسؤول بنجاح!")
            self.clear_entries()
            self.load_admins()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المستخدم موجود مسبقاً!")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def delete_admin(self):
        """حذف المسؤول المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مسؤول للحذف")
            return

        admin_name = self.tree.item(selected[0])["values"][1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المسؤول '{admin_name}'؟"):
            admin_id = self.tree.item(selected[0])["values"][0]

            try:
                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                c.execute("DELETE FROM admins WHERE id=?", (admin_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف المسؤول بنجاح!")
                self.clear_entries()
                self.load_admins()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def reset_password(self):
        """إعادة تعيين كلمة مرور المسؤول المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مسؤول لإعادة تعيين كلمة المرور")
            return

        password = self.password_entry.get()
        confirm = self.confirm_entry.get()

        if not password:
            messagebox.showwarning("تحذير", "يرجى إدخال كلمة المرور الجديدة")
            return

        if password != confirm:
            messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقتين")
            return

        if len(password) < 6:
            messagebox.showwarning("تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        admin_name = self.tree.item(selected[0])["values"][1]
        
        if messagebox.askyesno("تأكيد إعادة التعيين", f"هل أنت متأكد من إعادة تعيين كلمة مرور '{admin_name}'؟"):
            admin_id = self.tree.item(selected[0])["values"][0]

            try:
                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                hashed_password = self.hash_password(password)
                c.execute("UPDATE admins SET password=? WHERE id=?", (hashed_password, admin_id))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم إعادة تعيين كلمة المرور بنجاح!")
                self.clear_entries()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def on_select(self, event):
        """عند النقر المزدوج على عنصر في الجدول"""
        selected = self.tree.selection()
        if selected:
            values = self.tree.item(selected[0])["values"]
            self.name_entry.delete(0, "end")
            self.name_entry.insert(0, values[1])
            self.username_entry.delete(0, "end")
            self.username_entry.insert(0, values[2])
            # مسح حقول كلمة المرور
            self.password_entry.delete(0, "end")
            self.confirm_entry.delete(0, "end")

    def clear_entries(self):
        """مسح حقول الإدخال"""
        self.name_entry.delete(0, "end")
        self.username_entry.delete(0, "end")
        self.password_entry.delete(0, "end")
        self.confirm_entry.delete(0, "end")
