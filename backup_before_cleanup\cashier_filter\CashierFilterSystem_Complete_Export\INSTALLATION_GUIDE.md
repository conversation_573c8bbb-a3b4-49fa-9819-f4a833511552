# 🚀 دليل التثبيت والتشغيل السريع

## 📦 نظام تصفية الكاشير - الإصدار الكامل v3.5.0

### 🎉 الميزات الجديدة في هذا الإصدار:
- ✅ **طريقة الدفع** في مقبوضات العملاء (نقدي/شبكة)
- ✅ **رقم المرجع** للمعاملات البنكية
- ✅ **جدول الموردين** منفصل عن الحسابات
- ✅ **أسماء العملاء** في جميع التقارير
- ✅ **حساب الفارق** الدقيق في التصفية
- ✅ **التقرير الشامل** المحسن على الويب
- ✅ **الوصول العالمي** عبر الإنترنت

---

## ⚡ التشغيل السريع (30 ثانية)

### 🖥️ على Windows:
```
1. فك الضغط عن الملف
2. انقر نقراً مزدوجاً على: تشغيل_النظام.bat
3. انتظر تحميل النظام
4. سجل الدخول: admin / 123456
5. ابدأ الاستخدام فوراً!
```

### 🐧 على Linux/Mac:
```bash
1. فك الضغط عن الملف
2. افتح Terminal في المجلد
3. نفذ: chmod +x تشغيل_النظام.sh
4. نفذ: ./تشغيل_النظام.sh
5. سجل الدخول: admin / 123456
```

---

## 🔧 التثبيت التفصيلي

### 1. متطلبات النظام:
- **Python 3.8+** (يُنصح بـ 3.9 أو أحدث)
- **نظام التشغيل:** Windows 10+, Linux, macOS
- **الذاكرة:** 4GB RAM كحد أدنى
- **المساحة:** 500MB مساحة فارغة

### 2. تثبيت Python (إذا لم يكن مثبتاً):

#### على Windows:
```
1. اذهب إلى: https://python.org/downloads
2. حمل Python 3.9+ 
3. ثبته مع تفعيل "Add to PATH"
4. أعد تشغيل الكمبيوتر
```

#### على Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### على macOS:
```bash
# باستخدام Homebrew
brew install python3
```

### 3. تثبيت المتطلبات:
```bash
# في مجلد النظام
pip install -r requirements.txt
```

أو تثبيت يدوي:
```bash
pip install customtkinter flask requests pandas fpdf2
```

---

## 🚀 طرق التشغيل

### 🎯 الطريقة الأسهل - ملفات التشغيل:

#### على Windows:
- **`تشغيل_النظام.bat`** - تشغيل التطبيق الرئيسي
- **`تشغيل_خادم_التقارير.bat`** - تشغيل خادم التقارير
- **`وصول_عالمي_فوري.bat`** - تشغيل الوصول العالمي

#### على Linux/Mac:
- **`تشغيل_النظام.sh`** - تشغيل التطبيق الرئيسي
- **`run.sh`** - تشغيل بديل

### 🐍 الطريقة اليدوية - Python:
```bash
# التطبيق الرئيسي
python main.py

# خادم التقارير
python web_server.py

# الوصول العالمي
python setup_global_access.py
```

---

## 👤 بيانات تسجيل الدخول الافتراضية

### 🔐 المدير الرئيسي:
```
اسم المستخدم: admin
كلمة المرور: 123456
```

### 👨‍💼 إضافة مديرين جدد:
1. سجل الدخول كـ admin
2. اذهب إلى "إدارة المسؤولين"
3. أضف مدير جديد
4. حدد الصلاحيات

### 👨‍💻 إضافة كاشيرين:
1. سجل الدخول كمدير
2. اذهب إلى "إدارة الكاشيرين"
3. أضف كاشير جديد
4. حدد رقم الكاشير

---

## 🌟 الميزات الجديدة - دليل سريع

### 💳 طريقة الدفع في مقبوضات العملاء:
```
1. في قسم "مقبوضات العملاء"
2. اختر طريقة الدفع: نقدي أو شبكة
3. أدخل رقم المرجع للشبكة (اختياري)
4. احفظ - ستظهر في التقارير
```

### 🏭 جدول الموردين:
```
1. في قسم "الموردين" الجديد
2. أدخل اسم المورد والمبلغ
3. اختر طريقة الدفع (نقدي/شيك/تحويل بنكي)
4. أضف ملاحظات
5. لا يؤثر على حسابات التصفية
```

### 📊 التقرير الشامل:
```
1. شغل خادم التقارير
2. اذهب إلى: http://localhost:5000
3. اختر تصفية
4. انقر "التقرير الشامل"
5. استمتع بالتفاصيل الكاملة
```

### 🌐 الوصول العالمي:
```
1. شغل: وصول_عالمي_فوري.bat
2. انتظر إنشاء الرابط العالمي
3. انسخ الرابط واستخدمه من أي مكان
4. شارك مع الفريق للوصول عن بُعد
```

---

## 🔍 استكشاف الأخطاء

### ❓ خطأ "Python not found":
```
الحل: ثبت Python من python.org
تأكد من تفعيل "Add to PATH"
```

### ❓ خطأ "Module not found":
```bash
الحل: ثبت المتطلبات
pip install -r requirements.txt
```

### ❓ لا يفتح التطبيق:
```
1. تأكد من Python 3.8+
2. ثبت المتطلبات
3. شغل من Terminal: python main.py
4. راجع رسائل الخطأ
```

### ❓ خادم التقارير لا يعمل:
```
1. تأكد من المنفذ 5000 غير مستخدم
2. شغل: python web_server.py
3. اذهب إلى: http://localhost:5000
```

### ❓ الوصول العالمي لا يعمل:
```
1. تأكد من اتصال الإنترنت
2. شغل: python setup_global_access.py
3. اتبع التعليمات على الشاشة
```

---

## 📞 الدعم والمساعدة

### 📚 الأدلة المتاحة:
- **`دليل_الميزات_الجديدة.md`** - شرح الميزات الجديدة
- **`دليل_التقارير_المحسنة.md`** - التقارير والطباعة
- **`دليل_الوصول_العالمي.md`** - الوصول عن بُعد
- **`USER_GUIDE.md`** - دليل المستخدم الشامل

### 🧪 ملفات الاختبار:
```bash
# اختبار الميزات الجديدة
python test_enhanced_reports.py

# اختبار أسماء العملاء
python test_customer_names_fix.py

# اختبار الفارق
python test_variance_feature.py
```

---

## 🎊 استمتع بالنظام!

### ✅ الآن لديك:
- **نظام تصفية متكامل** مع جميع الميزات
- **تقارير احترافية** مع تفاصيل كاملة
- **وصول عالمي** من أي مكان في العالم
- **واجهة محسنة** سهلة الاستخدام
- **دعم كامل** للعربية والإنجليزية

### 🚀 ابدأ الآن:
1. **شغل النظام:** `تشغيل_النظام.bat`
2. **سجل الدخول:** admin / 123456
3. **ابدأ تصفية جديدة**
4. **جرب الميزات الجديدة**
5. **استمتع بالتقارير المحسنة**

**مرحباً بك في نظام تصفية الكاشير المحسن!** 🎉✨

---

**الإصدار:** 3.5.0  
**تاريخ البناء:** 2025-07-09 14:05:55  
**المطور:** محمد الكامل  
**الحالة:** ✅ جاهز للاستخدام الفوري
