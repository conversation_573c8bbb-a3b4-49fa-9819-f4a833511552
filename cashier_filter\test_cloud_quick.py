#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لنافذة التكامل السحابي
"""

import sys
import os
import customtkinter as ctk

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cloud_window():
    """اختبار سريع لنافذة التكامل السحابي"""
    print("🧪 اختبار سريع لنافذة التكامل السحابي...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # محاولة استيراد النافذة المبسطة
        from ui.cloud_integration_simple import CloudIntegrationSimpleWindow
        print("✅ تم استيراد النافذة المبسطة بنجاح")
        
        # إنشاء النافذة
        window = CloudIntegrationSimpleWindow()
        print("✅ تم إنشاء النافذة بنجاح")
        
        # إغلاق تلقائي بعد 3 ثوان
        window.after(3000, window.destroy)
        
        print("🚀 تشغيل النافذة...")
        window.mainloop()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cloud_window()
    if success:
        print("🎉 الاختبار نجح! النافذة تعمل بشكل صحيح")
    else:
        print("💥 الاختبار فشل! يوجد مشكلة في النافذة")
