# متطلبات نظام تصفية الكاشير
# Cashier Filter System Requirements

# واجهة المستخدم الرسومية
customtkinter>=5.2.0

# معالجة PDF
fpdf2>=2.7.0
reportlab>=4.0.0

# معالجة البيانات
pandas>=1.5.0
openpyxl>=3.1.0
numpy>=1.24.0

# الرسوم البيانية (اختياري)
matplotlib>=3.5.0

# مكتبات اختيارية للميزات المتقدمة
# weasyprint>=60.0   # لتصدير PDF المتقدم (اختياري)
# pillow>=9.0.0      # لمعالجة الصور (اختياري)

# التكامل السحابي
requests>=2.31.0
urllib3>=2.0.0

# خادم الويب
Flask>=2.3.0
Werkzeug>=2.3.0

# نسخ النصوص
pyperclip>=1.8.0

# مكتبات التكامل السحابي المتقدم (اختيارية)
google-api-python-client>=2.0.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.5.0
dropbox>=11.0.0
onedrive-sdk-python>=1.1.8
boto3>=1.26.0
azure-storage-blob>=12.14.0

# الأمان والتشفير
cryptography>=41.0.0
pycryptodome>=3.15.0

# معالجة الصور
Pillow>=10.0.0

# مكتبات إضافية للميزات المتقدمة
schedule>=1.2.0
psutil>=5.9.0
certifi>=2022.12.7

# مكتبات الشبكة والاتصال المتقدم
httpx>=0.24.0
aiohttp>=3.8.0

# مكتبات الضغط والأرشفة
py7zr>=0.20.0

# مكتبات النظام (مدمجة في Python)
# لا تحتاج تثبيت منفصل:
# - sqlite3, datetime, json, threading
# - os, sys, pathlib, math, tempfile
# - webbrowser, hashlib, shutil, re
