Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pyinstaller in c:\users\<USER>\appdata\roaming\python\python313\site-packages (6.14.2)
Requirement already satisfied: setuptools>=42.0.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (80.9.0)
Requirement already satisfied: altgraph in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (0.17.4)
Requirement already satisfied: pefile!=2024.8.26,>=2022.5.30 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (2023.2.7)
Requirement already satisfied: pywin32-ctypes>=0.2.1 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (0.2.3)
Requirement already satisfied: pyinstaller-hooks-contrib>=2025.5 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (2025.5)
Requirement already satisfied: packaging>=22.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pyinstaller) (25.0)
