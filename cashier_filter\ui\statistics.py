# نافذة الإحصائيات والتحليلات المتقدمة
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json
from datetime import datetime, timedelta
import calendar
from collections import defaultdict

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class StatisticsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("الإحصائيات والتحليلات")
        self.geometry("1200x800")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()

        self.create_widgets()
        self.load_statistics()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""

        # العنوان الرئيسي
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            header_frame,
            text="📊 الإحصائيات والتحليلات المتقدمة",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # إطار الفلاتر
        filters_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        filters_frame.pack(pady=10, padx=20, fill="x")

        filters_title = ctk.CTkLabel(
            filters_frame,
            text="🔍 فلاتر البحث",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        filters_title.pack(pady=10)

        # حقول الفلتر
        filter_controls = ctk.CTkFrame(filters_frame, fg_color="#f2f3f7", corner_radius=10)
        filter_controls.pack(pady=10, padx=20, fill="x")

        # الصف الأول - التواريخ
        date_row = ctk.CTkFrame(filter_controls, fg_color="#f2f3f7", corner_radius=0)
        date_row.pack(fill="x", pady=5)

        ctk.CTkLabel(date_row, text="من تاريخ:", font=("Arial", 12, "bold")).pack(side="left", padx=10)
        self.start_date_entry = ctk.CTkEntry(date_row, width=120, placeholder_text="YYYY-MM-DD")
        self.start_date_entry.pack(side="left", padx=5)

        ctk.CTkLabel(date_row, text="إلى تاريخ:", font=("Arial", 12, "bold")).pack(side="left", padx=10)
        self.end_date_entry = ctk.CTkEntry(date_row, width=120, placeholder_text="YYYY-MM-DD")
        self.end_date_entry.pack(side="left", padx=5)

        # الصف الثاني - الكاشير والمسؤول
        filter_row = ctk.CTkFrame(filter_controls, fg_color="#f2f3f7", corner_radius=0)
        filter_row.pack(fill="x", pady=5)

        ctk.CTkLabel(filter_row, text="الكاشير:", font=("Arial", 12, "bold")).pack(side="left", padx=10)
        self.cashier_combo = ctk.CTkComboBox(filter_row, width=150, values=["الكل"])
        self.cashier_combo.pack(side="left", padx=5)

        ctk.CTkLabel(filter_row, text="المسؤول:", font=("Arial", 12, "bold")).pack(side="left", padx=10)
        self.admin_combo = ctk.CTkComboBox(filter_row, width=150, values=["الكل"])
        self.admin_combo.pack(side="left", padx=5)

        # أزرار الفلتر
        filter_buttons = ctk.CTkFrame(filter_controls, fg_color="#f2f3f7", corner_radius=0)
        filter_buttons.pack(fill="x", pady=10)

        apply_filter_btn = ctk.CTkButton(
            filter_buttons,
            text="🔍 تطبيق الفلتر",
            command=self.apply_filters,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=120,
            height=35
        )
        apply_filter_btn.pack(side="left", padx=10)

        reset_filter_btn = ctk.CTkButton(
            filter_buttons,
            text="🔄 إعادة تعيين",
            command=self.reset_filters,
            fg_color="#607D8B",
            hover_color="#455A64",
            width=120,
            height=35
        )
        reset_filter_btn.pack(side="left", padx=5)

        # إطار الإحصائيات الرئيسية
        main_stats_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        main_stats_frame.pack(pady=10, padx=20, fill="x")

        stats_title = ctk.CTkLabel(
            main_stats_frame,
            text="📈 الإحصائيات العامة",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        stats_title.pack(pady=10)

        # شبكة الإحصائيات
        stats_grid = ctk.CTkFrame(main_stats_frame, fg_color="#f2f3f7", corner_radius=10)
        stats_grid.pack(pady=10, padx=20, fill="x")

        # إنشاء بطاقات الإحصائيات
        self.create_stat_cards(stats_grid)

        # إطار التحليلات التفصيلية
        details_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        details_frame.pack(pady=10, padx=20, fill="both", expand=True)

        details_title = ctk.CTkLabel(
            details_frame,
            text="📋 التحليلات التفصيلية",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        details_title.pack(pady=10)

        # دفتر التبويبات للتحليلات
        self.notebook = ttk.Notebook(details_frame)
        self.notebook.pack(pady=10, padx=20, fill="both", expand=True)

        # تبويب التحليل الشهري
        self.monthly_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.monthly_frame, text="التحليل الشهري")

        # تبويب تحليل الكاشيرين
        self.cashiers_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")

        # تبويب الإحصائيات التسلسلية
        self.sequence_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.cashiers_frame, text="تحليل الكاشيرين")
        self.notebook.add(self.sequence_frame, text="الإحصائيات التسلسلية")

        # تبويب تحليل المقبوضات
        self.receipts_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.receipts_frame, text="تحليل المقبوضات")

        # إنشاء محتوى التبويبات
        self.create_monthly_analysis()
        self.create_cashiers_analysis()
        self.create_sequence_analysis()
        self.create_receipts_analysis()

    def create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""

        # الصف الأول
        row1 = ctk.CTkFrame(parent, fg_color="#f2f3f7", corner_radius=0)
        row1.pack(fill="x", pady=5)

        self.total_filters_card = self.create_stat_card(row1, "إجمالي التصفيات", "0", "#4CAF50")
        self.total_amount_card = self.create_stat_card(row1, "إجمالي المبالغ", "0.00 ريال", "#2196F3")
        self.avg_amount_card = self.create_stat_card(row1, "متوسط المبلغ", "0.00 ريال", "#FF9800")

        # الصف الثاني
        row2 = ctk.CTkFrame(parent, fg_color="#f2f3f7", corner_radius=0)
        row2.pack(fill="x", pady=5)

        self.surplus_count_card = self.create_stat_card(row2, "عدد الفوائض", "0", "#27ae60")
        self.deficit_count_card = self.create_stat_card(row2, "عدد العجز", "0", "#e74c3c")
        self.balanced_count_card = self.create_stat_card(row2, "عدد المتوازن", "0", "#f39c12")

    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=15, width=180, height=100)
        card.pack(side="left", padx=10, pady=10)
        card.pack_propagate(False)

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 16, "bold"),
            text_color="white"
        )
        value_label.pack()

        return value_label

    def create_monthly_analysis(self):
        """إنشاء تحليل شهري"""
        # جدول التحليل الشهري
        columns = ("الشهر", "عدد التصفيات", "إجمالي المبالغ", "متوسط المبلغ", "الفوائض", "العجز")
        self.monthly_tree = ttk.Treeview(self.monthly_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=150, anchor="center")

        scrollbar_monthly = ttk.Scrollbar(self.monthly_frame, orient="vertical", command=self.monthly_tree.yview)
        self.monthly_tree.configure(yscrollcommand=scrollbar_monthly.set)

        self.monthly_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_monthly.pack(side="right", fill="y", pady=10)

    def create_cashiers_analysis(self):
        """إنشاء تحليل الكاشيرين"""
        # جدول تحليل الكاشيرين
        columns = ("الكاشير", "عدد التصفيات", "إجمالي المبالغ", "متوسط المبلغ", "آخر تصفية")
        self.cashiers_tree = ttk.Treeview(self.cashiers_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.cashiers_tree.heading(col, text=col)
            self.cashiers_tree.column(col, width=150, anchor="center")

        scrollbar_cashiers = ttk.Scrollbar(self.cashiers_frame, orient="vertical", command=self.cashiers_tree.yview)
        self.cashiers_tree.configure(yscrollcommand=scrollbar_cashiers.set)

        self.cashiers_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_cashiers.pack(side="right", fill="y", pady=10)

    def create_receipts_analysis(self):
        """إنشاء تحليل المقبوضات"""
        # جدول تحليل المقبوضات
        columns = ("نوع المقبوض", "العدد", "إجمالي المبلغ", "متوسط المبلغ", "النسبة %")
        self.receipts_tree = ttk.Treeview(self.receipts_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.receipts_tree.heading(col, text=col)
            self.receipts_tree.column(col, width=150, anchor="center")

        scrollbar_receipts = ttk.Scrollbar(self.receipts_frame, orient="vertical", command=self.receipts_tree.yview)
        self.receipts_tree.configure(yscrollcommand=scrollbar_receipts.set)

        self.receipts_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_receipts.pack(side="right", fill="y", pady=10)

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # تحميل قوائم الكاشيرين والمسؤولين
            self.load_filter_options()

            # تحميل الإحصائيات العامة
            self.load_general_stats()

            # تحميل التحليلات التفصيلية
            self.load_monthly_analysis()
            self.load_cashiers_analysis()
            self.load_receipts_analysis()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإحصائيات: {e}")

    def load_filter_options(self):
        """تحميل خيارات الفلاتر"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # تحميل الكاشيرين
            c.execute("SELECT DISTINCT name FROM cashiers ORDER BY name")
            cashiers = ["الكل"] + [row[0] for row in c.fetchall()]
            self.cashier_combo.configure(values=cashiers)
            self.cashier_combo.set("الكل")

            # تحميل المسؤولين
            c.execute("SELECT DISTINCT name FROM admins ORDER BY name")
            admins = ["الكل"] + [row[0] for row in c.fetchall()]
            self.admin_combo.configure(values=admins)
            self.admin_combo.set("الكل")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل خيارات الفلاتر: {e}")

    def load_general_stats(self):
        """تحميل الإحصائيات العامة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # إجمالي التصفيات
            c.execute("SELECT COUNT(*) FROM filters")
            total_filters = c.fetchone()[0]
            self.total_filters_card.configure(text=str(total_filters))

            # إحصائيات المبالغ والحالات
            c.execute("SELECT data FROM filters")
            filters_data = c.fetchall()

            total_amount = 0
            surplus_count = 0
            deficit_count = 0
            balanced_count = 0

            for row in filters_data:
                try:
                    data = json.loads(row[0])
                    totals = data.get('totals', {})

                    # حساب إجمالي المقبوضات
                    receipts_total = (
                        totals.get('bank', 0) +
                        totals.get('cash', 0) +
                        totals.get('credit', 0) +
                        totals.get('return', 0) -
                        totals.get('client', 0)
                    )

                    total_amount += receipts_total

                    # حساب الفارق
                    system_sales = float(data.get('system_sales', 0) or 0)
                    difference = receipts_total - system_sales

                    if difference > 0:
                        surplus_count += 1
                    elif difference < 0:
                        deficit_count += 1
                    else:
                        balanced_count += 1

                except Exception:
                    continue

            # تحديث البطاقات
            self.total_amount_card.configure(text=f"{total_amount:.2f} ريال")

            if total_filters > 0:
                avg_amount = total_amount / total_filters
                self.avg_amount_card.configure(text=f"{avg_amount:.2f} ريال")
            else:
                self.avg_amount_card.configure(text="0.00 ريال")

            self.surplus_count_card.configure(text=str(surplus_count))
            self.deficit_count_card.configure(text=str(deficit_count))
            self.balanced_count_card.configure(text=str(balanced_count))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات العامة: {e}")

    def load_monthly_analysis(self):
        """تحميل التحليل الشهري"""
        try:
            # مسح البيانات الحالية
            for item in self.monthly_tree.get_children():
                self.monthly_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            c.execute("SELECT date, data FROM filters ORDER BY date")
            filters_data = c.fetchall()

            # تجميع البيانات حسب الشهر
            monthly_data = defaultdict(lambda: {
                'count': 0,
                'total_amount': 0,
                'surplus': 0,
                'deficit': 0
            })

            for date_str, data_str in filters_data:
                try:
                    # استخراج الشهر
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    month_key = date_obj.strftime("%Y-%m")
                    month_name = f"{date_obj.year}-{calendar.month_name[date_obj.month]}"

                    data = json.loads(data_str)
                    totals = data.get('totals', {})

                    # حساب إجمالي المقبوضات
                    receipts_total = (
                        totals.get('bank', 0) +
                        totals.get('cash', 0) +
                        totals.get('credit', 0) +
                        totals.get('return', 0) -
                        totals.get('client', 0)
                    )

                    monthly_data[month_key]['count'] += 1
                    monthly_data[month_key]['total_amount'] += receipts_total
                    monthly_data[month_key]['month_name'] = month_name

                    # حساب الفارق
                    system_sales = float(data.get('system_sales', 0) or 0)
                    difference = receipts_total - system_sales

                    if difference > 0:
                        monthly_data[month_key]['surplus'] += 1
                    elif difference < 0:
                        monthly_data[month_key]['deficit'] += 1

                except Exception:
                    continue

            # إدراج البيانات في الجدول
            for month_key in sorted(monthly_data.keys(), reverse=True):
                data = monthly_data[month_key]
                avg_amount = data['total_amount'] / data['count'] if data['count'] > 0 else 0

                self.monthly_tree.insert("", "end", values=(
                    data['month_name'],
                    data['count'],
                    f"{data['total_amount']:.2f}",
                    f"{avg_amount:.2f}",
                    data['surplus'],
                    data['deficit']
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل التحليل الشهري: {e}")

    def load_cashiers_analysis(self):
        """تحميل تحليل الكاشيرين"""
        try:
            # مسح البيانات الحالية
            for item in self.cashiers_tree.get_children():
                self.cashiers_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # الحصول على بيانات التصفيات مع أسماء الكاشيرين
            c.execute("""
                SELECT c.name, f.date, f.data
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                ORDER BY f.date DESC
            """)

            filters_data = c.fetchall()

            # تجميع البيانات حسب الكاشير
            cashiers_data = defaultdict(lambda: {
                'count': 0,
                'total_amount': 0,
                'last_filter': None
            })

            for cashier_name, date_str, data_str in filters_data:
                if not cashier_name:
                    cashier_name = "غير محدد"

                try:
                    data = json.loads(data_str)
                    totals = data.get('totals', {})

                    # حساب إجمالي المقبوضات
                    receipts_total = (
                        totals.get('bank', 0) +
                        totals.get('cash', 0) +
                        totals.get('credit', 0) +
                        totals.get('return', 0) -
                        totals.get('client', 0)
                    )

                    cashiers_data[cashier_name]['count'] += 1
                    cashiers_data[cashier_name]['total_amount'] += receipts_total

                    if not cashiers_data[cashier_name]['last_filter'] or date_str > cashiers_data[cashier_name]['last_filter']:
                        cashiers_data[cashier_name]['last_filter'] = date_str

                except Exception:
                    continue

            # إدراج البيانات في الجدول
            for cashier_name, data in cashiers_data.items():
                avg_amount = data['total_amount'] / data['count'] if data['count'] > 0 else 0

                self.cashiers_tree.insert("", "end", values=(
                    cashier_name,
                    data['count'],
                    f"{data['total_amount']:.2f}",
                    f"{avg_amount:.2f}",
                    data['last_filter'] or "غير محدد"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تحليل الكاشيرين: {e}")

    def load_receipts_analysis(self):
        """تحميل تحليل المقبوضات"""
        try:
            # مسح البيانات الحالية
            for item in self.receipts_tree.get_children():
                self.receipts_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            c.execute("SELECT data FROM filters")
            filters_data = c.fetchall()

            # تجميع البيانات حسب نوع المقبوض
            receipts_data = {
                'المقبوضات البنكية': {'count': 0, 'total': 0},
                'المقبوضات النقدية': {'count': 0, 'total': 0},
                'المبيعات الآجلة': {'count': 0, 'total': 0},
                'المقبوضات من العملاء': {'count': 0, 'total': 0},
                'المرتجعات': {'count': 0, 'total': 0}
            }

            total_all = 0

            for row in filters_data:
                try:
                    data = json.loads(row[0])
                    totals = data.get('totals', {})

                    bank_total = totals.get('bank', 0)
                    cash_total = totals.get('cash', 0)
                    credit_total = totals.get('credit', 0)
                    client_total = totals.get('client', 0)
                    return_total = totals.get('return', 0)

                    if bank_total > 0:
                        receipts_data['المقبوضات البنكية']['count'] += 1
                        receipts_data['المقبوضات البنكية']['total'] += bank_total

                    if cash_total > 0:
                        receipts_data['المقبوضات النقدية']['count'] += 1
                        receipts_data['المقبوضات النقدية']['total'] += cash_total

                    if credit_total > 0:
                        receipts_data['المبيعات الآجلة']['count'] += 1
                        receipts_data['المبيعات الآجلة']['total'] += credit_total

                    if client_total > 0:
                        receipts_data['المقبوضات من العملاء']['count'] += 1
                        receipts_data['المقبوضات من العملاء']['total'] += client_total

                    if return_total > 0:
                        receipts_data['المرتجعات']['count'] += 1
                        receipts_data['المرتجعات']['total'] += return_total

                    total_all += bank_total + cash_total + credit_total + return_total

                except Exception:
                    continue

            # إدراج البيانات في الجدول
            for receipt_type, data in receipts_data.items():
                avg_amount = data['total'] / data['count'] if data['count'] > 0 else 0
                percentage = (data['total'] / total_all * 100) if total_all > 0 else 0

                self.receipts_tree.insert("", "end", values=(
                    receipt_type,
                    data['count'],
                    f"{data['total']:.2f}",
                    f"{avg_amount:.2f}",
                    f"{percentage:.1f}%"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تحليل المقبوضات: {e}")

    def create_sequence_analysis(self):
        """إنشاء تحليل الإحصائيات التسلسلية"""

        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.sequence_frame,
            text="🔢 تحليل الأرقام التسلسلية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # إطار الإحصائيات الأساسية
        basic_stats_frame = ctk.CTkFrame(self.sequence_frame, fg_color="#e0e5ec", corner_radius=15)
        basic_stats_frame.pack(pady=10, padx=20, fill="x")

        basic_title = ctk.CTkLabel(
            basic_stats_frame,
            text="📊 الإحصائيات الأساسية",
            font=("Arial", 14, "bold"),
            text_color="#34495e"
        )
        basic_title.pack(pady=10)

        # شبكة الإحصائيات الأساسية
        basic_grid = ctk.CTkFrame(basic_stats_frame, fg_color="#f2f3f7", corner_radius=10)
        basic_grid.pack(pady=10, padx=20, fill="x")

        # بطاقات الإحصائيات التسلسلية
        self.sequence_cards = {}
        cards_data = [
            ("total_filters", "إجمالي التصفيات", "0", "#3498db"),
            ("last_sequence", "آخر رقم تسلسلي", "0", "#e74c3c"),
            ("avg_daily", "متوسط التصفيات اليومية", "0", "#27ae60"),
            ("sequence_gaps", "الفجوات في الترقيم", "0", "#f39c12")
        ]

        for i, (key, title, value, color) in enumerate(cards_data):
            card = self.create_sequence_stat_card(basic_grid, title, value, color)
            self.sequence_cards[key] = card
            card.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")

        # تكوين الشبكة
        basic_grid.grid_columnconfigure(0, weight=1)
        basic_grid.grid_columnconfigure(1, weight=1)

        # إطار التحليل التفصيلي
        detailed_frame = ctk.CTkFrame(self.sequence_frame, fg_color="#e0e5ec", corner_radius=15)
        detailed_frame.pack(pady=10, padx=20, fill="both", expand=True)

        detailed_title = ctk.CTkLabel(
            detailed_frame,
            text="📋 التحليل التفصيلي",
            font=("Arial", 14, "bold"),
            text_color="#34495e"
        )
        detailed_title.pack(pady=10)

        # جدول التحليل التفصيلي
        columns = ("الرقم التسلسلي", "تاريخ الإنشاء", "الكاشير", "المسؤول", "المبلغ الإجمالي")
        self.sequence_tree = ttk.Treeview(detailed_frame, columns=columns, show="headings", height=10)

        for col in columns:
            self.sequence_tree.heading(col, text=col)
            self.sequence_tree.column(col, width=120, anchor="center")

        # شريط التمرير للجدول
        sequence_scrollbar = ttk.Scrollbar(detailed_frame, orient="vertical", command=self.sequence_tree.yview)
        self.sequence_tree.configure(yscrollcommand=sequence_scrollbar.set)

        self.sequence_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        sequence_scrollbar.pack(side="right", fill="y", pady=10)

        # تحميل البيانات
        self.load_sequence_analysis()

    def create_sequence_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية للأرقام التسلسلية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=12)

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(15, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 20, "bold"),
            text_color="white"
        )
        value_label.pack(pady=(0, 15))

        return {"title": title_label, "value": value_label}

    def load_sequence_analysis(self):
        """تحميل تحليل الإحصائيات التسلسلية"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # إجمالي التصفيات
            c.execute("SELECT COUNT(*) FROM filters")
            total_filters = c.fetchone()[0]
            self.sequence_cards["total_filters"]["value"].configure(text=str(total_filters))

            # آخر رقم تسلسلي
            c.execute("SELECT MAX(sequence_number) FROM filters")
            last_sequence = c.fetchone()[0] or 0
            self.sequence_cards["last_sequence"]["value"].configure(text=str(last_sequence))

            # متوسط التصفيات اليومية
            c.execute("""
                SELECT COUNT(DISTINCT date) FROM filters
                WHERE date IS NOT NULL AND date != ''
            """)
            unique_days = c.fetchone()[0] or 1
            avg_daily = total_filters / unique_days if unique_days > 0 else 0
            self.sequence_cards["avg_daily"]["value"].configure(text=f"{avg_daily:.1f}")

            # الفجوات في الترقيم
            if last_sequence > 0:
                expected_count = last_sequence
                actual_count = total_filters
                gaps = expected_count - actual_count
                self.sequence_cards["sequence_gaps"]["value"].configure(text=str(max(0, gaps)))
            else:
                self.sequence_cards["sequence_gaps"]["value"].configure(text="0")

            # تحميل البيانات التفصيلية
            for item in self.sequence_tree.get_children():
                self.sequence_tree.delete(item)

            c.execute("""
                SELECT f.sequence_number, f.date,
                       COALESCE(c.name, 'غير محدد') as cashier_name,
                       COALESCE(f.admin_name, 'غير محدد') as admin_name,
                       CASE
                           WHEN json_extract(f.data, '$.totals') IS NOT NULL
                           THEN (
                               COALESCE(json_extract(f.data, '$.totals.bank'), 0) +
                               COALESCE(json_extract(f.data, '$.totals.cash'), 0) +
                               COALESCE(json_extract(f.data, '$.totals.credit'), 0) +
                               COALESCE(json_extract(f.data, '$.totals.return'), 0) -
                               COALESCE(json_extract(f.data, '$.totals.client'), 0)
                           )
                           ELSE 0
                       END as total_amount
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                ORDER BY f.sequence_number DESC
                LIMIT 50
            """)

            for row in c.fetchall():
                sequence_number, date, cashier_name, admin_name, total_amount = row
                self.sequence_tree.insert("", "end", values=(
                    sequence_number or "غير محدد",
                    date or "غير محدد",
                    cashier_name,
                    admin_name,
                    f"{total_amount:,.0f} ريال" if total_amount else "0 ريال"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل تحليل الإحصائيات التسلسلية: {e}")

    def apply_filters(self):
        """تطبيق الفلاتر"""
        # يمكن تطوير هذه الدالة لاحقاً لتطبيق فلاتر متقدمة
        self.load_statistics()
        messagebox.showinfo("تم", "تم تطبيق الفلاتر وتحديث الإحصائيات")

    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.start_date_entry.delete(0, "end")
        self.end_date_entry.delete(0, "end")
        self.cashier_combo.set("الكل")
        self.admin_combo.set("الكل")
        self.load_statistics()