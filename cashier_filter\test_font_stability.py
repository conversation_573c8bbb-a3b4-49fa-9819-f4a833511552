#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استقرار حجم الخط في النظام
Font Stability Test for Cashier Filter System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_font_manager():
    """اختبار مدير الخطوط"""
    print("🧪 اختبار مدير الخطوط...")
    
    try:
        from utils.font_manager import font_manager, get_stable_font, get_stable_font_size
        
        # اختبار الحصول على حجم خط مستقر
        test_sizes = [8, 10, 12, 14, 16, 18, 20, 25, 30]
        print("   📏 اختبار أحجام الخطوط:")
        
        for size in test_sizes:
            stable_size = get_stable_font_size(size)
            status = "✅" if 12 <= stable_size <= 18 else "❌"
            print(f"      {status} الحجم {size} → {stable_size}")
        
        # اختبار أنواع الخطوط المختلفة
        font_types = ["small", "normal", "medium", "large", "button", "title"]
        print("   🎨 اختبار أنواع الخطوط:")
        
        for font_type in font_types:
            font = get_stable_font(font_type, "bold")
            print(f"      ✅ {font_type}: {font}")
        
        print("   ✅ مدير الخطوط يعمل بشكل صحيح")
        return True
        
    except ImportError as e:
        print(f"   ❌ فشل في استيراد مدير الخطوط: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في مدير الخطوط: {e}")
        return False

def test_settings_font():
    """اختبار حجم الخط في ملف الإعدادات"""
    print("\n⚙️ اختبار إعدادات الخط...")
    
    try:
        import json
        settings_file = "settings.json"
        
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            font_size = settings.get("ui", {}).get("font_size", 14)
            
            if 12 <= font_size <= 18:
                print(f"   ✅ حجم الخط في الإعدادات مستقر: {font_size}")
                return True
            else:
                print(f"   ⚠️ حجم الخط في الإعدادات خارج النطاق: {font_size}")
                return False
        else:
            print("   ⚠️ ملف الإعدادات غير موجود")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في قراءة إعدادات الخط: {e}")
        return False

def test_ui_font_stability():
    """اختبار استقرار الخط في الواجهة"""
    print("\n🖥️ اختبار استقرار الخط في الواجهة...")
    
    try:
        # محاولة استيراد الواجهة الرئيسية
        from ui.main_window import load_and_apply_settings, ensure_stable_font_size
        
        # اختبار تحميل الإعدادات
        ui_settings = load_and_apply_settings()
        font_size = ui_settings.get("font_size", 14)
        
        if 12 <= font_size <= 18:
            print(f"   ✅ حجم الخط المحمل مستقر: {font_size}")
        else:
            print(f"   ⚠️ حجم الخط المحمل غير مستقر: {font_size}")
        
        # اختبار دالة ضمان الاستقرار
        test_values = [8, 10, 12, 14, 16, 18, 20, 25]
        print("   📐 اختبار دالة ضمان الاستقرار:")
        
        all_stable = True
        for value in test_values:
            stable = ensure_stable_font_size(value)
            is_stable = 12 <= stable <= 18
            status = "✅" if is_stable else "❌"
            print(f"      {status} {value} → {stable}")
            if not is_stable:
                all_stable = False
        
        if all_stable:
            print("   ✅ جميع القيم مستقرة")
            return True
        else:
            print("   ❌ بعض القيم غير مستقرة")
            return False
            
    except ImportError as e:
        print(f"   ❌ فشل في استيراد الواجهة: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_config_font():
    """اختبار إعدادات الخط في config.py"""
    print("\n📋 اختبار إعدادات الخط في config.py...")
    
    try:
        from config import UI_CONFIG
        
        font_size = UI_CONFIG.get("font_size", 14)
        min_font_size = UI_CONFIG.get("min_font_size", 12)
        max_font_size = UI_CONFIG.get("max_font_size", 18)
        
        print(f"   📏 حجم الخط الافتراضي: {font_size}")
        print(f"   📏 الحد الأدنى: {min_font_size}")
        print(f"   📏 الحد الأقصى: {max_font_size}")
        
        if min_font_size <= font_size <= max_font_size:
            print("   ✅ إعدادات الخط في config.py مستقرة")
            return True
        else:
            print("   ❌ إعدادات الخط في config.py غير مستقرة")
            return False
            
    except ImportError as e:
        print(f"   ❌ فشل في استيراد config.py: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في config.py: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🔤 اختبار استقرار حجم الخط - نظام تصفية الكاشير 2025")
    print("=" * 60)
    
    tests = [
        ("مدير الخطوط", test_font_manager),
        ("إعدادات الخط", test_settings_font),
        ("استقرار الخط في الواجهة", test_ui_font_stability),
        ("إعدادات config.py", test_config_font)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed}/{total}")
    print(f"   ❌ فشل: {total - passed}/{total}")
    print(f"   📈 معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 جميع اختبارات استقرار الخط نجحت!")
        print("✅ مشكلة حجم الخط تم حلها بنجاح")
    else:
        print("⚠️ بعض اختبارات استقرار الخط فشلت")
        print("🔧 يحتاج المزيد من التحسينات")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
