#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة الاتجاهات والتوقعات المتقدمة
Test for Advanced Trends and Predictions Interface

تطوير: محمد الكامل - نظام تصفية الكاشير 2025
Developed by: <PERSON> - Cashier Filter System 2025
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_trends_predictions_window():
    """اختبار نافذة الاتجاهات والتوقعات"""
    
    print("🧪 بدء اختبار واجهة الاتجاهات والتوقعات...")
    
    try:
        # التحقق من المكتبات المطلوبة
        print("📦 التحقق من المكتبات المطلوبة...")
        
        required_modules = [
            'customtkinter',
            'matplotlib',
            'numpy', 
            'pandas',
            'seaborn'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} - متوفر")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module} - غير متوفر")
        
        if missing_modules:
            print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
            print("لتثبيت المكتبات المفقودة، قم بتشغيل:")
            print(f"pip install {' '.join(missing_modules)}")
            return False
        
        # اختبار استيراد الواجهة
        print("\n🔧 اختبار استيراد الواجهة...")
        from ui.trends_predictions import TrendsPredictionsWindow, open_trends_predictions_window
        print("✅ تم استيراد الواجهة بنجاح")
        
        # اختبار قاعدة البيانات
        print("\n🗄️ اختبار قاعدة البيانات...")
        db_path = project_root / "db" / "cashier_filter.db"
        if db_path.exists():
            print("✅ قاعدة البيانات موجودة")
        else:
            print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها")
            
        # اختبار إنشاء النافذة
        print("\n🖥️ اختبار إنشاء النافذة...")
        
        import customtkinter as ctk
        
        # إنشاء نافذة جذر مخفية
        root = ctk.CTk()
        root.withdraw()
        
        # إنشاء نافذة الاختبار
        test_window = TrendsPredictionsWindow(root)
        
        print("✅ تم إنشاء النافذة بنجاح")
        
        # اختبار الوظائف الأساسية
        print("\n⚙️ اختبار الوظائف الأساسية...")
        
        # اختبار تحميل البيانات
        try:
            test_window.load_financial_data()
            print("✅ تحميل البيانات المالية")
        except Exception as e:
            print(f"⚠️ تحميل البيانات المالية: {e}")
        
        # اختبار حساب الاتجاهات
        try:
            test_window.calculate_trends()
            print("✅ حساب الاتجاهات")
        except Exception as e:
            print(f"⚠️ حساب الاتجاهات: {e}")
        
        # اختبار توليد التوقعات
        try:
            test_window.generate_predictions()
            print("✅ توليد التوقعات")
        except Exception as e:
            print(f"⚠️ توليد التوقعات: {e}")
        
        # اختبار حساب مؤشرات الأداء
        try:
            test_window.calculate_kpis()
            print("✅ حساب مؤشرات الأداء")
        except Exception as e:
            print(f"⚠️ حساب مؤشرات الأداء: {e}")
        
        # اختبار تحديث الواجهة
        try:
            test_window.update_interface()
            print("✅ تحديث الواجهة")
        except Exception as e:
            print(f"⚠️ تحديث الواجهة: {e}")
        
        print("\n🎉 اكتمل الاختبار بنجاح!")
        print("\n📋 ملخص النتائج:")
        print("✅ جميع المكتبات متوفرة")
        print("✅ الواجهة تعمل بشكل صحيح")
        print("✅ الوظائف الأساسية تعمل")
        
        # عرض النافذة للاختبار اليدوي
        print("\n🖥️ عرض النافذة للاختبار اليدوي...")
        print("أغلق النافذة لإنهاء الاختبار")
        
        test_window.mainloop()
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_data_generation():
    """اختبار توليد بيانات تجريبية"""
    
    print("\n🔧 اختبار توليد بيانات تجريبية...")
    
    try:
        import sqlite3
        from datetime import datetime, timedelta
        import random
        
        db_path = project_root / "db" / "cashier_filter.db"
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        if not db_path.exists():
            print("📝 إنشاء قاعدة بيانات تجريبية...")
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول التصفيات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS filters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    cashier_name TEXT NOT NULL,
                    customer_name TEXT,
                    total_amount TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إضافة بيانات تجريبية
            print("📊 إضافة بيانات تجريبية...")
            
            cashiers = ["أحمد محمد", "فاطمة علي", "محمد سالم", "نورا أحمد", "خالد يوسف"]
            customers = ["عميل 1", "عميل 2", "عميل 3", "عميل 4", "عميل 5"]
            
            # إضافة بيانات لآخر 60 يوم
            end_date = datetime.now()
            
            for i in range(60):
                date = end_date - timedelta(days=i)
                
                # عدد المعاملات اليومية (5-20 معاملة)
                daily_transactions = random.randint(5, 20)
                
                for j in range(daily_transactions):
                    cashier = random.choice(cashiers)
                    customer = random.choice(customers)
                    amount = random.randint(50, 1000)  # مبلغ بين 50-1000 ريال
                    
                    # إضافة بعض التنويع في الأوقات
                    transaction_time = date.replace(
                        hour=random.randint(8, 22),
                        minute=random.randint(0, 59),
                        second=random.randint(0, 59)
                    )
                    
                    cursor.execute('''
                        INSERT INTO filters (cashier_name, customer_name, total_amount, created_at)
                        VALUES (?, ?, ?, ?)
                    ''', (cashier, customer, str(amount), transaction_time))
            
            conn.commit()
            conn.close()
            
            print("✅ تم إنشاء البيانات التجريبية بنجاح")
            
        else:
            print("✅ قاعدة البيانات موجودة بالفعل")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في توليد البيانات التجريبية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    
    print("=" * 60)
    print("🧪 اختبار واجهة الاتجاهات والتوقعات المتقدمة")
    print("=" * 60)
    
    # اختبار توليد البيانات التجريبية
    if not test_data_generation():
        print("❌ فشل في توليد البيانات التجريبية")
        return
    
    # اختبار الواجهة
    if test_trends_predictions_window():
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n❌ فشل في بعض الاختبارات")

if __name__ == "__main__":
    main()
