@echo off
title Global Access - Cashier Filter Reports

echo.
echo ========================================
echo Global Access - Cashier Filter Reports
echo ========================================
echo.

cd /d "%~dp0"

REM Check if local server is running
echo [1/3] Checking local server...
curl -s http://localhost:5000 > nul 2>&1
if errorlevel 1 (
    echo Local server not running. Starting...
    start "Reports Server" python web_server.py
    echo Waiting 5 seconds for server to start...
    timeout /t 5 > nul
) else (
    echo Local server is running
)

echo.
echo [2/3] Checking cloudflared...
if not exist "cloudflared.exe" (
    echo cloudflared.exe not found
    echo.
    echo DOWNLOAD INSTRUCTIONS:
    echo 1. Go to: https://github.com/cloudflare/cloudflared/releases
    echo 2. Download: cloudflared-windows-amd64.exe
    echo 3. Rename it to: cloudflared.exe
    echo 4. Place it in this folder
    echo 5. Run this file again
    echo.
    pause
    exit /b 1
) else (
    echo cloudflared.exe found
)

echo.
echo [3/3] Starting global tunnel...
echo.
echo ========================================
echo COPY THE HTTPS URL BELOW:
echo This URL works from anywhere in the world
echo.
echo For mobile: Add /mobile to the end
echo Example: https://abc123.trycloudflare.com/mobile
echo ========================================
echo.

cloudflared.exe tunnel --url http://localhost:5000

echo.
echo Tunnel ended
pause
