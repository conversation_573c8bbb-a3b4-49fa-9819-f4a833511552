#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إعداد وتثبيت نظام تصفية الكاشير المتكامل 2025
Setup and Installation Script for Cashier Filter System 2025

تطوير: محمد الكامل - الإصدار 3.0.0
Developed by: <PERSON> - Version 3.0.0
© 2025 - جميع الحقوق محفوظة
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def print_header():
    """طباعة رأس الإعداد"""
    print("=" * 70)
    print("🏪 نظام تصفية الكاشير المتكامل 2025 - إعداد التطبيق")
    print("Cashier Filter System 2025 - Application Setup")
    print("الإصدار 3.0.0 - مع ذكاء اصطناعي متطور")
    print("=" * 70)
    print()

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ تم تثبيت جميع المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات...")
    
    directories = [
        "db",
        "reports/generated",
        "assets",
        "backups",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        # تشغيل سكريبت إنشاء قاعدة البيانات
        from db.init_db import init_db
        init_db()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # إضافة بيانات تجريبية
        add_sample_data()
        return True
        
    except Exception as e:
        print(f"❌ فشل في إعداد قاعدة البيانات: {e}")
        return False

def add_sample_data():
    """إضافة بيانات تجريبية"""
    print("📝 إضافة بيانات تجريبية...")
    
    db_path = "db/cashier_filter.db"
    
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # إضافة كاشير تجريبي
        c.execute("""
            INSERT OR IGNORE INTO cashiers (name, number) 
            VALUES ('أحمد محمد', '001')
        """)
        
        c.execute("""
            INSERT OR IGNORE INTO cashiers (name, number) 
            VALUES ('فاطمة علي', '002')
        """)
        
        # إضافة مسؤول تجريبي
        import hashlib
        password_hash = hashlib.sha256("123456".encode()).hexdigest()
        
        c.execute("""
            INSERT OR IGNORE INTO admins (name, username, password) 
            VALUES ('المدير العام', 'admin', ?)
        """, (password_hash,))
        
        c.execute("""
            INSERT OR IGNORE INTO admins (name, username, password) 
            VALUES ('محاسب رئيسي', 'accountant', ?)
        """, (password_hash,))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة البيانات التجريبية:")
        print("   - كاشيرين: أحمد محمد (001), فاطمة علي (002)")
        print("   - مسؤولين: admin, accountant (كلمة المرور: 123456)")
        
    except Exception as e:
        print(f"⚠️ تحذير: فشل في إضافة البيانات التجريبية: {e}")

def test_application():
    """اختبار التطبيق"""
    print("\n🧪 اختبار التطبيق...")
    
    try:
        # اختبار استيراد الوحدات الرئيسية
        from ui.main_window import MainWindow
        from ui.daily_filter import DailyFilterWindow
        from ui.manage_cashier import ManageCashierWindow
        from ui.manage_admins import ManageAdminsWindow
        
        print("✅ جميع الوحدات تعمل بشكل صحيح")
        return True
        
    except ImportError as e:
        print(f"❌ فشل في اختبار التطبيق: {e}")
        return False

def create_shortcuts():
    """إنشاء اختصارات التشغيل"""
    print("\n🔗 إنشاء اختصارات التشغيل...")
    
    # إنشاء ملف تشغيل مبسط
    if os.name == 'nt':  # Windows
        with open("تشغيل_النظام.bat", "w", encoding="utf-8") as f:
            f.write("""@echo off
chcp 65001 > nul
cd /d "%~dp0"
python main.py
pause
""")
        print("✅ تم إنشاء ملف: تشغيل_النظام.bat")
    
    else:  # Linux/Mac
        with open("تشغيل_النظام.sh", "w", encoding="utf-8") as f:
            f.write("""#!/bin/bash
cd "$(dirname "$0")"
python3 main.py
read -p "اضغط Enter للمتابعة..."
""")
        os.chmod("تشغيل_النظام.sh", 0o755)
        print("✅ تم إنشاء ملف: تشغيل_النظام.sh")

def print_completion_message():
    """طباعة رسالة الإكمال"""
    print("\n" + "=" * 60)
    print("🎉 تم إعداد نظام تصفية الكاشير المتكامل 2025 بنجاح!")
    print("تطوير: محمد الكامل - الإصدار 3.0.0")
    print("=" * 70)
    print()
    print("📋 خطوات التشغيل:")
    print("1. تشغيل التطبيق: python main.py")
    if os.name == 'nt':
        print("   أو النقر المزدوج على: تشغيل_النظام.bat")
    else:
        print("   أو تشغيل: ./تشغيل_النظام.sh")
    print()
    print("👤 بيانات تجريبية:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: 123456")
    print()
    print("📖 للمزيد من المعلومات، راجع ملف README.md")
    print()

def main():
    """الدالة الرئيسية للإعداد"""
    print_header()
    
    # التحقق من إصدار Python
    if not check_python_version():
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("\n❌ فشل الإعداد: لا يمكن تثبيت المتطلبات")
        sys.exit(1)
    
    # إنشاء المجلدات
    create_directories()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("\n❌ فشل الإعداد: لا يمكن إعداد قاعدة البيانات")
        sys.exit(1)
    
    # اختبار التطبيق
    if not test_application():
        print("\n❌ فشل الإعداد: التطبيق لا يعمل بشكل صحيح")
        sys.exit(1)
    
    # إنشاء اختصارات التشغيل
    create_shortcuts()
    
    # طباعة رسالة الإكمال
    print_completion_message()

if __name__ == "__main__":
    main()
