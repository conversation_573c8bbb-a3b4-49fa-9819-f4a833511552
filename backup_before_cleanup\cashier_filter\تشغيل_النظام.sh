#!/bin/bash
# -*- coding: utf-8 -*-

# تعيين ترميز UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8
export PYTHONIOENCODING=utf-8

# مسح الشاشة
clear

# طباعة شعار النظام
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║                                                                      ║"
echo "║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║"
echo "║         Cashier Filter System 2025                                   ║"
echo "║                                                                      ║"
echo "║    الإصدار 3.0.0 - مع ذكاء اصطناعي متطور                          ║"
echo "║    Version 3.0.0 - With Advanced AI                                 ║"
echo "║                                                                      ║"
echo "║    تطوير: محمد الكامل | Developed by: <PERSON>              ║"
echo "║    البريد: <EMAIL>                                     ║"
echo "║                                                                      ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo ""

# الانتقال لمجلد التطبيق
cd "$(dirname "$0")"

# دالة للتحقق من وجود الأمر
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# التحقق من وجود Python
echo "🔍 التحقق من Python..."
if command_exists python3; then
    PYTHON_CMD="python3"
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    echo "✅ Python $PYTHON_VERSION متاح"
elif command_exists python; then
    PYTHON_CMD="python"
    PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    echo "✅ Python $PYTHON_VERSION متاح"
else
    echo "❌ خطأ: Python غير مثبت"
    echo ""
    echo "📥 يرجى تثبيت Python:"
    echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "   macOS: brew install python3"
    echo "   أو تحميل من: https://python.org"
    echo ""
    read -p "اضغط Enter للخروج..."
    exit 1
fi

# التحقق من إصدار Python
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "❌ خطأ: يتطلب Python 3.8 أو أحدث"
    echo "   الإصدار الحالي: $PYTHON_VERSION"
    echo ""
    read -p "اضغط Enter للخروج..."
    exit 1
fi

# التحقق من وجود الملف الرئيسي
echo ""
echo "📁 التحقق من ملفات النظام..."
if [ ! -f "main.py" ]; then
    echo "❌ خطأ: ملف main.py غير موجود"
    echo ""
    echo "💡 تأكد من وجود جميع ملفات النظام في نفس المجلد"
    echo ""
    read -p "اضغط Enter للخروج..."
    exit 1
fi
echo "✅ الملف الرئيسي موجود"

# التحقق من قاعدة البيانات
if [ ! -f "db/cashier_filter.db" ]; then
    echo "⚠️ تحذير: قاعدة البيانات غير موجودة"
    echo "🔧 محاولة إنشاء قاعدة البيانات..."
    
    if [ -f "setup.py" ]; then
        $PYTHON_CMD setup.py
        if [ $? -ne 0 ]; then
            echo "❌ فشل في إنشاء قاعدة البيانات"
            echo ""
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        echo "✅ تم إنشاء قاعدة البيانات"
    else
        echo "❌ ملف الإعداد غير موجود"
        echo ""
        read -p "اضغط Enter للخروج..."
        exit 1
    fi
else
    echo "✅ قاعدة البيانات موجودة"
fi

# التحقق من pip
echo ""
echo "📦 التحقق من pip..."
if ! $PYTHON_CMD -m pip --version >/dev/null 2>&1; then
    echo "❌ خطأ: pip غير متاح"
    echo ""
    echo "📥 يرجى تثبيت pip:"
    echo "   Ubuntu/Debian: sudo apt install python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3-pip"
    echo "   macOS: python3 -m ensurepip --upgrade"
    echo ""
    read -p "اضغط Enter للخروج..."
    exit 1
fi
echo "✅ pip متاح"

# التحقق من المتطلبات
echo ""
echo "📦 التحقق من المتطلبات..."
if ! $PYTHON_CMD -c "import customtkinter" >/dev/null 2>&1; then
    echo "⚠️ تحذير: customtkinter غير مثبت"
    echo "🔧 محاولة تثبيت المتطلبات..."
    
    if [ -f "requirements.txt" ]; then
        $PYTHON_CMD -m pip install -r requirements.txt --user
        if [ $? -ne 0 ]; then
            echo "❌ فشل في تثبيت المتطلبات"
            echo ""
            echo "💡 جرب تشغيل الأمر التالي يدوياً:"
            echo "   $PYTHON_CMD -m pip install customtkinter --user"
            echo ""
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        echo "✅ تم تثبيت المتطلبات"
    else
        echo "🔧 تثبيت المتطلبات الأساسية..."
        $PYTHON_CMD -m pip install customtkinter --user
        if [ $? -ne 0 ]; then
            echo "❌ فشل في تثبيت customtkinter"
            echo ""
            read -p "اضغط Enter للخروج..."
            exit 1
        fi
        echo "✅ تم تثبيت المتطلبات الأساسية"
    fi
else
    echo "✅ جميع المتطلبات متاحة"
fi

# عرض معلومات الدخول
echo ""
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║                        معلومات تسجيل الدخول                        ║"
echo "║                                                                      ║"
echo "║    👤 اسم المستخدم: admin                                           ║"
echo "║    🔐 كلمة المرور: 123456                                            ║"
echo "║                                                                      ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo ""

# تشغيل النظام
echo "🚀 جاري تشغيل نظام تصفية الكاشير..."
echo ""

$PYTHON_CMD main.py

# التحقق من حالة الخروج
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ حدث خطأ في تشغيل النظام"
    echo ""
    echo "🔧 خطوات حل المشكلة:"
    echo "1. تأكد من تثبيت Python بشكل صحيح"
    echo "2. تأكد من وجود جميع ملفات النظام"
    echo "3. جرب تشغيل: $PYTHON_CMD main.py"
    echo "4. راجع ملف README.md للمزيد من المعلومات"
    echo ""
    echo "💬 للدعم الفني: <EMAIL>"
    echo ""
else
    echo ""
    echo "✅ تم إغلاق النظام بنجاح"
    echo ""
    echo "👋 شكراً لاستخدام نظام تصفية الكاشير المتكامل 2025"
    echo "    تطوير: محمد الكامل"
    echo ""
fi

read -p "اضغط Enter للمتابعة..."
