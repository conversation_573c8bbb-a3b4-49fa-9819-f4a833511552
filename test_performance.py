#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأداء والاستقرار - نظام تصفية الكاشير
Performance and Stability Test - Cashier Filter System
"""

import sys
import os
import time
import psutil
import threading
import sqlite3
import json
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent / "cashier_filter"
sys.path.insert(0, str(project_root))

class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.start_time = time.time()
        self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory
        self.monitoring = False
        
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        self.monitoring = False
        
    def _monitor_loop(self):
        """حلقة مراقبة الأداء"""
        while self.monitoring:
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            if current_memory > self.peak_memory:
                self.peak_memory = current_memory
            time.sleep(0.1)
    
    def get_stats(self):
        """الحصول على إحصائيات الأداء"""
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        elapsed_time = time.time() - self.start_time
        
        return {
            'elapsed_time': elapsed_time,
            'initial_memory': self.initial_memory,
            'current_memory': current_memory,
            'peak_memory': self.peak_memory,
            'memory_increase': current_memory - self.initial_memory,
            'cpu_percent': psutil.Process().cpu_percent()
        }

def test_database_performance():
    """اختبار أداء قاعدة البيانات"""
    print("🗄️ اختبار أداء قاعدة البيانات...")
    
    db_path = "cashier_filter/db/cashier_filter.db"
    
    if not os.path.exists(db_path):
        print("  ❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        # اختبار سرعة الاتصال
        start_time = time.time()
        conn = sqlite3.connect(db_path)
        connection_time = (time.time() - start_time) * 1000
        
        print(f"  ⚡ زمن الاتصال: {connection_time:.2f} مللي ثانية")
        
        cursor = conn.cursor()
        
        # اختبار استعلام بسيط
        start_time = time.time()
        cursor.execute("SELECT COUNT(*) FROM filters")
        filters_count = cursor.fetchone()[0]
        query_time = (time.time() - start_time) * 1000
        
        print(f"  📊 عدد التصفيات: {filters_count}")
        print(f"  ⚡ زمن الاستعلام البسيط: {query_time:.2f} مللي ثانية")
        
        # اختبار استعلام معقد
        start_time = time.time()
        cursor.execute("""
            SELECT f.*, c.name as cashier_name, a.name as admin_name
            FROM filters f
            LEFT JOIN cashiers c ON f.cashier_id = c.id
            LEFT JOIN admins a ON f.admin_id = a.id
            ORDER BY f.id DESC
            LIMIT 10
        """)
        results = cursor.fetchall()
        complex_query_time = (time.time() - start_time) * 1000
        
        print(f"  ⚡ زمن الاستعلام المعقد: {complex_query_time:.2f} مللي ثانية")
        print(f"  📋 عدد النتائج: {len(results)}")
        
        # اختبار معالجة JSON
        if results:
            start_time = time.time()
            for row in results:
                if row[4]:  # data column
                    try:
                        json.loads(row[4])
                    except:
                        pass
            json_processing_time = (time.time() - start_time) * 1000
            print(f"  ⚡ زمن معالجة JSON: {json_processing_time:.2f} مللي ثانية")
        
        conn.close()
        
        # تقييم الأداء
        if connection_time < 10 and query_time < 50 and complex_query_time < 100:
            print("  ✅ أداء قاعدة البيانات ممتاز")
            return True
        elif connection_time < 50 and query_time < 200 and complex_query_time < 500:
            print("  ⚠️ أداء قاعدة البيانات مقبول")
            return True
        else:
            print("  ❌ أداء قاعدة البيانات ضعيف")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui_import_performance():
    """اختبار أداء استيراد واجهة المستخدم"""
    print("\n🖥️ اختبار أداء استيراد واجهة المستخدم...")
    
    monitor = PerformanceMonitor()
    monitor.start_monitoring()
    
    ui_modules = [
        "ui.main_window",
        "ui.login", 
        "ui.dashboard",
        "ui.filter_entry",
        "ui.reports",
        "ui.settings"
    ]
    
    import_times = {}
    
    for module in ui_modules:
        try:
            start_time = time.time()
            __import__(module)
            import_time = (time.time() - start_time) * 1000
            import_times[module] = import_time
            print(f"  ⚡ {module}: {import_time:.2f} مللي ثانية")
        except Exception as e:
            print(f"  ❌ {module}: خطأ - {e}")
            import_times[module] = -1
    
    monitor.stop_monitoring()
    stats = monitor.get_stats()
    
    total_import_time = sum(t for t in import_times.values() if t > 0)
    print(f"\n  📊 إجمالي زمن الاستيراد: {total_import_time:.2f} مللي ثانية")
    print(f"  💾 استهلاك الذاكرة: {stats['memory_increase']:.2f} MB")
    
    # تقييم الأداء
    if total_import_time < 2000 and stats['memory_increase'] < 50:
        print("  ✅ أداء استيراد الواجهة ممتاز")
        return True
    elif total_import_time < 5000 and stats['memory_increase'] < 100:
        print("  ⚠️ أداء استيراد الواجهة مقبول")
        return True
    else:
        print("  ❌ أداء استيراد الواجهة ضعيف")
        return False

def test_memory_usage():
    """اختبار استهلاك الذاكرة"""
    print("\n💾 اختبار استهلاك الذاكرة...")
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024
    
    print(f"  📊 الذاكرة الأولية: {initial_memory:.2f} MB")
    
    # محاكاة عمليات مكثفة
    test_data = []
    
    # إنشاء بيانات اختبار
    for i in range(1000):
        test_data.append({
            'id': i,
            'data': json.dumps({
                'totals': {'bank': i * 100, 'cash': i * 50},
                'transactions': [{'amount': j * 10} for j in range(10)]
            }),
            'timestamp': datetime.now().isoformat()
        })
    
    current_memory = process.memory_info().rss / 1024 / 1024
    memory_increase = current_memory - initial_memory
    
    print(f"  📊 الذاكرة بعد إنشاء البيانات: {current_memory:.2f} MB")
    print(f"  📈 زيادة الذاكرة: {memory_increase:.2f} MB")
    
    # تنظيف البيانات
    del test_data
    
    # انتظار garbage collection
    import gc
    gc.collect()
    time.sleep(1)
    
    final_memory = process.memory_info().rss / 1024 / 1024
    memory_recovered = current_memory - final_memory
    
    print(f"  📊 الذاكرة بعد التنظيف: {final_memory:.2f} MB")
    print(f"  ♻️ الذاكرة المستردة: {memory_recovered:.2f} MB")
    
    # تقييم إدارة الذاكرة
    if memory_increase < 50 and memory_recovered > memory_increase * 0.8:
        print("  ✅ إدارة الذاكرة ممتازة")
        return True
    elif memory_increase < 100 and memory_recovered > memory_increase * 0.5:
        print("  ⚠️ إدارة الذاكرة مقبولة")
        return True
    else:
        print("  ❌ مشكلة في إدارة الذاكرة")
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("\n📁 اختبار عمليات الملفات...")
    
    test_dir = Path("test_performance_files")
    test_dir.mkdir(exist_ok=True)
    
    try:
        # اختبار كتابة الملفات
        start_time = time.time()
        for i in range(100):
            test_file = test_dir / f"test_{i}.json"
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump({'id': i, 'data': f'test data {i}'}, f, ensure_ascii=False)
        write_time = (time.time() - start_time) * 1000
        
        print(f"  ✍️ زمن كتابة 100 ملف: {write_time:.2f} مللي ثانية")
        
        # اختبار قراءة الملفات
        start_time = time.time()
        for i in range(100):
            test_file = test_dir / f"test_{i}.json"
            with open(test_file, 'r', encoding='utf-8') as f:
                json.load(f)
        read_time = (time.time() - start_time) * 1000
        
        print(f"  📖 زمن قراءة 100 ملف: {read_time:.2f} مللي ثانية")
        
        # تنظيف الملفات
        import shutil
        shutil.rmtree(test_dir)
        
        # تقييم الأداء
        if write_time < 1000 and read_time < 500:
            print("  ✅ أداء عمليات الملفات ممتاز")
            return True
        elif write_time < 3000 and read_time < 1500:
            print("  ⚠️ أداء عمليات الملفات مقبول")
            return True
        else:
            print("  ❌ أداء عمليات الملفات ضعيف")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار عمليات الملفات: {e}")
        # تنظيف في حالة الخطأ
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
        return False

def test_system_resources():
    """اختبار موارد النظام"""
    print("\n🖥️ اختبار موارد النظام...")
    
    # معلومات النظام
    cpu_count = psutil.cpu_count()
    memory_info = psutil.virtual_memory()
    disk_info = psutil.disk_usage('.')
    
    print(f"  🔧 عدد المعالجات: {cpu_count}")
    print(f"  💾 إجمالي الذاكرة: {memory_info.total / 1024 / 1024 / 1024:.2f} GB")
    print(f"  💾 الذاكرة المتاحة: {memory_info.available / 1024 / 1024 / 1024:.2f} GB")
    print(f"  💾 استخدام الذاكرة: {memory_info.percent}%")
    print(f"  💽 مساحة القرص المتاحة: {disk_info.free / 1024 / 1024 / 1024:.2f} GB")
    
    # اختبار استخدام المعالج
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"  🔧 استخدام المعالج: {cpu_percent}%")
    
    # تقييم الموارد
    if (memory_info.percent < 80 and 
        cpu_percent < 70 and 
        disk_info.free > 1024 * 1024 * 1024):  # 1GB
        print("  ✅ موارد النظام كافية")
        return True
    elif (memory_info.percent < 90 and 
          cpu_percent < 85 and 
          disk_info.free > 512 * 1024 * 1024):  # 512MB
        print("  ⚠️ موارد النظام مقبولة")
        return True
    else:
        print("  ❌ موارد النظام غير كافية")
        return False

def run_performance_tests():
    """تشغيل جميع اختبارات الأداء"""
    print("🚀 بدء اختبارات الأداء والاستقرار...")
    print("=" * 50)
    
    tests = [
        ("أداء قاعدة البيانات", test_database_performance),
        ("أداء استيراد الواجهة", test_ui_import_performance),
        ("استهلاك الذاكرة", test_memory_usage),
        ("عمليات الملفات", test_file_operations),
        ("موارد النظام", test_system_resources)
    ]
    
    results = []
    overall_start = time.time()
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    overall_time = time.time() - overall_start
    
    # ملخص النتائج
    print("\n📋 ملخص نتائج اختبارات الأداء:")
    print("=" * 40)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ ممتاز" if result else "❌ يحتاج تحسين"
        print(f"  {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 النتيجة النهائية:")
    print(f"  ✅ نجح: {passed}")
    print(f"  ❌ فشل: {failed}")
    print(f"  ⏱️ إجمالي وقت الاختبار: {overall_time:.2f} ثانية")
    print(f"  📈 معدل النجاح: {(passed / len(results)) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع اختبارات الأداء نجحت!")
        print("💪 النظام يعمل بكفاءة عالية")
    elif failed <= 2:
        print(f"\n⚠️ {failed} اختبار يحتاج تحسين")
        print("🔧 الأداء العام مقبول مع إمكانية التحسين")
    else:
        print(f"\n❌ {failed} اختبار فشل")
        print("🚨 النظام يحتاج تحسينات جوهرية")

if __name__ == "__main__":
    run_performance_tests()
