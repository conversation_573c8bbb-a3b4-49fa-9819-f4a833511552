#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة الفارق وأسماء العملاء
Test Variance and Customer Names Feature
"""

import sys
import os
from pathlib import Path
import traceback

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_variance_calculation():
    """اختبار حساب الفارق"""
    try:
        print("🔍 اختبار حساب الفارق...")
        
        # استيراد WebReportServer
        from web_server import WebReportServer
        print("✅ تم استيراد WebReportServer")
        
        # إنشاء خادم التقارير
        web_server = WebReportServer()
        print("✅ تم إنشاء WebReportServer")
        
        # جلب بيانات التصفية
        filter_id = 22
        filter_data = web_server.get_filter_by_id(filter_id)
        print(f"📊 بيانات التصفية {filter_id}: {filter_data is not None}")
        
        if not filter_data:
            print(f"❌ التصفية رقم {filter_id} غير موجودة")
            
            # جرب تصفيات أخرى
            all_filters = web_server.get_all_filters(10)
            print(f"📋 عدد التصفيات المتاحة: {len(all_filters)}")
            
            if all_filters:
                filter_data = all_filters[0]
                filter_id = filter_data['id']
                print(f"🔄 جرب التصفية {filter_id}")
            else:
                print("❌ لا توجد تصفيات في قاعدة البيانات")
                return False
        
        # فحص التفاصيل
        details = filter_data.get('details', {})
        print(f"\n💰 التفاصيل المالية:")
        print(f"   بنكي: {details.get('bank_total', 0)}")
        print(f"   نقدي: {details.get('cash_total', 0)}")
        print(f"   آجل: {details.get('credit_total', 0)}")
        print(f"   عملاء: {details.get('client_total', 0)}")
        print(f"   مرتجعات: {details.get('return_total', 0)}")
        print(f"   المجموع: {details.get('grand_total', 0)}")
        
        # فحص الفارق
        if 'variance' in details:
            variance = details['variance']
            print(f"\n⚖️ تحليل الفارق:")
            print(f"   مبيعات النظام: {variance.get('system_sales', 0)}")
            print(f"   المجموع الفعلي: {variance.get('actual_total', 0)}")
            print(f"   الفارق: {variance.get('difference', 0)}")
            print(f"   النسبة: {variance.get('percentage', 0)}%")
            print(f"   الحالة: {variance.get('status', 'غير محدد')}")
        else:
            print("⚠️ لا توجد بيانات الفارق")
        
        # فحص تفاصيل العملاء
        print(f"\n👥 تفاصيل العملاء:")
        
        # المبيعات الآجلة
        credit_details = details.get('credit_details', [])
        print(f"   المبيعات الآجلة: {len(credit_details)} عميل")
        for i, credit in enumerate(credit_details[:3], 1):  # أول 3 فقط
            print(f"     {i}. {credit.get('customer_name', 'غير محدد')} - {credit.get('amount', 0)} ريال")
        
        # مقبوضات العملاء
        client_details = details.get('client_details', [])
        print(f"   مقبوضات العملاء: {len(client_details)} عميل")
        for i, client in enumerate(client_details[:3], 1):  # أول 3 فقط
            print(f"     {i}. {client.get('customer_name', 'غير محدد')} - {client.get('amount', 0)} ريال")
        
        # المرتجعات
        return_details = details.get('return_details', [])
        print(f"   المرتجعات: {len(return_details)} عميل")
        for i, return_item in enumerate(return_details[:3], 1):  # أول 3 فقط
            print(f"     {i}. {return_item.get('customer_name', 'غير محدد')} - {return_item.get('amount', 0)} ريال")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفارق: {e}")
        traceback.print_exc()
        return False

def test_template_rendering():
    """اختبار عرض القالب"""
    try:
        print("\n🎨 اختبار عرض القالب...")
        
        from flask import Flask, render_template
        from web_server import WebReportServer
        
        app = Flask(__name__, template_folder='web_templates')
        web_server = WebReportServer()
        
        # جلب بيانات التصفية
        filter_data = web_server.get_filter_by_id(22)
        if not filter_data:
            all_filters = web_server.get_all_filters(1)
            if all_filters:
                filter_data = all_filters[0]
            else:
                print("❌ لا توجد تصفيات للاختبار")
                return False
        
        with app.app_context():
            try:
                html_content = render_template('comprehensive_report.html', filter=filter_data)
                print("✅ تم عرض القالب بنجاح!")
                print(f"📄 طول المحتوى: {len(html_content)} حرف")
                
                # فحص وجود عناصر مهمة
                checks = [
                    ('التقرير الشامل', 'العنوان'),
                    ('الملخص التنفيذي', 'الملخص التنفيذي'),
                    ('تحليل الفارق', 'قسم الفارق'),
                    ('تفاصيل المبيعات الآجلة', 'المبيعات الآجلة'),
                    ('تفاصيل المقبوضات من العملاء', 'مقبوضات العملاء'),
                    ('تفاصيل فواتير المرتجعات', 'المرتجعات')
                ]
                
                for text, description in checks:
                    if text in html_content:
                        print(f"✅ {description} موجود")
                    else:
                        print(f"⚠️ {description} مفقود")
                
                return True
                
            except Exception as e:
                print(f"❌ خطأ في عرض القالب: {e}")
                traceback.print_exc()
                return False
    
    except Exception as e:
        print(f"❌ خطأ عام في اختبار القالب: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار ميزة الفارق وأسماء العملاء")
    print("=" * 60)
    
    # التحقق من المجلد الحالي
    if not Path('web_server.py').exists():
        print("❌ يجب تشغيل هذا الملف من مجلد cashier_filter")
        return
    
    # اختبار حساب الفارق
    variance_success = test_variance_calculation()
    
    # اختبار عرض القالب
    template_success = test_template_rendering()
    
    print("\n" + "=" * 60)
    if variance_success and template_success:
        print("🎉 جميع الاختبارات نجحت!")
        print("💡 الميزات الجديدة تعمل بشكل صحيح")
        print("🔗 جرب: http://localhost:5000/filter/22/comprehensive")
    else:
        print("❌ بعض الاختبارات فشلت!")
        print("💡 راجع رسائل الخطأ أعلاه لحل المشاكل")

if __name__ == "__main__":
    main()
