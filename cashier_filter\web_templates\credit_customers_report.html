{% extends "base.html" %}

{% block title %}تقرير العملاء الآجل - نظام تصفية الكاشير{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="card-title mb-2">
                            <i class="fas fa-users"></i>
                            تقرير العملاء الآجل الشامل
                        </h1>
                        <p class="card-text mb-0">
                            عرض شامل لجميع العملاء الآجل من كافة التصفيات مع إمكانية الطباعة والتصدير
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-light" onclick="printReport()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button type="button" class="btn btn-light" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h3 class="card-title text-primary">{{ credit_data.total_customers }}</h3>
                <p class="card-text">إجمالي العملاء الآجل</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
                <h3 class="card-title text-success">{{ "{:,.2f}".format(credit_data.total_amount) }}</h3>
                <p class="card-text">إجمالي المبلغ (ريال)</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h5 class="card-title text-info">{{ credit_data.last_update }}</h5>
                <p class="card-text">آخر تحديث</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلترة البيانات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="customer-search" class="form-label">البحث بالعميل:</label>
                        <input type="text" class="form-control" id="customer-search" placeholder="اسم العميل..." onkeyup="filterTable()">
                    </div>
                    <div class="col-md-3">
                        <label for="cashier-search" class="form-label">البحث بالكاشير:</label>
                        <input type="text" class="form-control" id="cashier-search" placeholder="اسم الكاشير..." onkeyup="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label for="date-from" class="form-label">من تاريخ:</label>
                        <input type="date" class="form-control" id="date-from" onchange="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label for="date-to" class="form-label">إلى تاريخ:</label>
                        <input type="date" class="form-control" id="date-to" onchange="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Credit Customers Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> جدول العملاء الآجل</h5>
                <span class="badge bg-primary" id="results-count">
                    {{ credit_data.total_customers }} عميل
                </span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped" id="credit-customers-table">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>رقم الفاتورة</th>
                                <th>المبلغ (ريال)</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الكاشير</th>
                                <th>المسؤول</th>
                                <th>تاريخ التصفية</th>
                                <th>الملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="credit-customers-tbody">
                            {% for customer in credit_data.customers %}
                            <tr data-customer-name="{{ customer.customer_name or '' }}" 
                                data-cashier="{{ customer.cashier_name or '' }}" 
                                data-filter-date="{{ customer.filter_date or '' }}">
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong class="text-primary">{{ customer.customer_name or 'عميل غير محدد' }}</strong>
                                </td>
                                <td>
                                    {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}" class="text-success">
                                            <i class="fas fa-phone"></i> {{ customer.phone }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">غير متاح</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ customer.invoice_number or 'غير محدد' }}</span>
                                </td>
                                <td class="text-end">
                                    <strong class="text-success">{{ "{:,.2f}".format(customer.amount or 0) }}</strong>
                                </td>
                                <td>{{ customer.due_date or 'غير محدد' }}</td>
                                <td>{{ customer.cashier_name or 'غير محدد' }}</td>
                                <td>{{ customer.admin_name or 'غير محدد' }}</td>
                                <td>{{ customer.filter_date or 'غير محدد' }}</td>
                                <td>
                                    {% if customer.notes %}
                                        <span class="text-muted">{{ customer.notes }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/filter/{{ customer.filter_id }}" class="btn btn-outline-primary btn-sm" title="عرض التصفية">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/filter/{{ customer.filter_id }}/comprehensive" class="btn btn-outline-info btn-sm" title="التقرير الشامل">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="11" class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                                    <br>لا توجد بيانات عملاء آجل متاحة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        {% if credit_data.customers %}
                        <tfoot class="table-success">
                            <tr>
                                <th colspan="4" class="text-end">الإجمالي:</th>
                                <th class="text-end">{{ "{:,.2f}".format(credit_data.total_amount) }} ريال</th>
                                <th colspan="6"></th>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// فلترة الجدول
function filterTable() {
    const customerSearch = document.getElementById('customer-search').value.toLowerCase();
    const cashierSearch = document.getElementById('cashier-search').value.toLowerCase();
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    
    const rows = document.querySelectorAll('#credit-customers-tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const customerName = row.getAttribute('data-customer-name').toLowerCase();
        const cashierName = row.getAttribute('data-cashier').toLowerCase();
        const filterDate = row.getAttribute('data-filter-date');
        
        let showRow = true;
        
        // فلترة بالعميل
        if (customerSearch && !customerName.includes(customerSearch)) {
            showRow = false;
        }
        
        // فلترة بالكاشير
        if (cashierSearch && !cashierName.includes(cashierSearch)) {
            showRow = false;
        }
        
        // فلترة بالتاريخ
        if (dateFrom && filterDate < dateFrom) {
            showRow = false;
        }
        
        if (dateTo && filterDate > dateTo) {
            showRow = false;
        }
        
        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    document.getElementById('results-count').textContent = `${visibleCount} عميل`;
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('customer-search').value = '';
    document.getElementById('cashier-search').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    
    const rows = document.querySelectorAll('#credit-customers-tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
    
    document.getElementById('results-count').textContent = `{{ credit_data.total_customers }} عميل`;
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تصدير إلى Excel
function exportToExcel() {
    // جمع البيانات المرئية فقط
    const visibleRows = Array.from(document.querySelectorAll('#credit-customers-tbody tr'))
        .filter(row => row.style.display !== 'none');
    
    if (visibleRows.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // إنشاء CSV
    let csvContent = "اسم العميل,رقم الهاتف,رقم الفاتورة,المبلغ,تاريخ الاستحقاق,الكاشير,المسؤول,تاريخ التصفية,الملاحظات\n";
    
    visibleRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) { // تجاهل الصفوف الفارغة
            const rowData = [
                cells[1].textContent.trim(), // اسم العميل
                cells[2].textContent.trim(), // رقم الهاتف
                cells[3].textContent.trim(), // رقم الفاتورة
                cells[4].textContent.trim(), // المبلغ
                cells[5].textContent.trim(), // تاريخ الاستحقاق
                cells[6].textContent.trim(), // الكاشير
                cells[7].textContent.trim(), // المسؤول
                cells[8].textContent.trim(), // تاريخ التصفية
                cells[9].textContent.trim()  // الملاحظات
            ];
            csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
        }
    });
    
    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `تقرير_العملاء_الآجل_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث تلقائي كل 5 دقائق
setInterval(() => {
    location.reload();
}, 300000);
</script>

<style>
@media print {
    .btn, .card-header .btn-group, .filters-section {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th, .table td {
        padding: 4px !important;
    }
}

.table th {
    white-space: nowrap;
}

.badge {
    font-size: 0.8em;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
{% endblock %}
