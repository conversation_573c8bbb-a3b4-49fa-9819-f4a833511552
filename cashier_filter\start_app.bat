@echo off
chcp 65001 > nul
title Cashier Filter System 2025

echo ========================================
echo    Cashier Filter System 2025
echo    نظام تصفية الكاشير المتكامل
echo ========================================
echo.

echo Starting system...
echo جاري تشغيل النظام...
echo.

REM Check Python installation
python --version > nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed
    echo خطأ: Python غير مثبت على النظام
    pause
    exit /b 1
)

REM Check requirements
echo Checking requirements...
echo فحص المتطلبات...
pip show customtkinter > nul 2>&1
if errorlevel 1 (
    echo Installing requirements...
    echo تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Failed to install requirements
        echo فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

REM Initialize database
echo Initializing database...
echo تهيئة قاعدة البيانات...
python db/init_db.py

echo Starting main application...
echo تشغيل التطبيق الرئيسي...
echo.
echo ============================================================
echo LOGIN CREDENTIALS / بيانات تسجيل الدخول:
echo Username / اسم المستخدم: admin
echo Password / كلمة المرور: 123456
echo ============================================================
echo.

REM Start main application
python run.py

if errorlevel 1 (
    echo.
    echo Error starting application
    echo خطأ في تشغيل التطبيق
    pause
)
