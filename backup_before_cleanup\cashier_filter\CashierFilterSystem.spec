# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# تحديد الملفات الإضافية المطلوبة
added_files = [
    ('ui', 'ui'),
    ('db', 'db'),
    ('reports', 'reports'),
    ('utils', 'utils'),
    ('web_templates', 'web_templates'),
    ('web_static', 'web_static'),
    ('assets', 'assets'),
    ('config.py', '.'),
    ('settings.json', '.'),
    ('version_info.txt', '.'),
]

# تحديد الوحدات المخفية المطلوبة
hidden_imports = [
    'customtkinter',
    'flask',
    'requests',
    'pandas',
    'fpdf2',
    'PIL',
    'sqlite3',
    'json',
    'datetime',
    'threading',
    'webbrowser',
    'subprocess',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CashierFilterSystem_v3.5.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
