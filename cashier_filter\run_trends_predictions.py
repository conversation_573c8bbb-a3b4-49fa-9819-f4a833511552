#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل واجهة الاتجاهات والتوقعات المتقدمة
Run Advanced Trends and Predictions Interface

تطوير: محمد الكامل - نظام تصفية الكاشير 2025
Developed by: <PERSON> - Cashier Filter System 2025
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """تشغيل واجهة الاتجاهات والتوقعات"""
    
    print("🚀 تشغيل واجهة الاتجاهات والتوقعات المتقدمة...")
    
    try:
        # التحقق من المكتبات المطلوبة
        required_modules = ['customtkinter', 'matplotlib', 'numpy', 'pandas', 'seaborn']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            print(f"❌ المكتبات المفقودة: {', '.join(missing_modules)}")
            print(f"لتثبيت المكتبات: pip install {' '.join(missing_modules)}")
            return
        
        # استيراد وتشغيل الواجهة
        from ui.trends_predictions import TrendsPredictionsWindow
        import customtkinter as ctk
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء النافذة الرئيسية
        root = ctk.CTk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء نافذة الاتجاهات والتوقعات
        app = TrendsPredictionsWindow(root)
        
        print("✅ تم تشغيل الواجهة بنجاح!")
        
        # تشغيل التطبيق
        app.mainloop()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("تأكد من تثبيت جميع المكتبات المطلوبة")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

if __name__ == "__main__":
    main()
