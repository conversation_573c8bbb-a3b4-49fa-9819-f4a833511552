#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار route التقرير الشامل
Test Comprehensive Report Route
"""

from flask import Flask, render_template
import sys
import os
from pathlib import Path

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

app = Flask(__name__, template_folder='web_templates')

# استيراد DatabaseManager
try:
    from web_server import DatabaseManager
    print("✅ تم استيراد DatabaseManager بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد DatabaseManager: {e}")
    sys.exit(1)

@app.route('/')
def index():
    return "خادم اختبار التقرير الشامل يعمل!"

@app.route('/test')
def test():
    return "اختبار ناجح!"

@app.route('/filter/<int:filter_id>')
def filter_detail(filter_id):
    """عرض تفاصيل تصفية محددة"""
    try:
        db_manager = DatabaseManager()
        filter_data = db_manager.get_filter_by_id(filter_id)
        if not filter_data:
            return f"التصفية رقم {filter_id} غير موجودة", 404
        
        return f"تفاصيل التصفية رقم {filter_id} موجودة"
    except Exception as e:
        return f"خطأ: {e}", 500

@app.route('/filter/<int:filter_id>/comprehensive')
def comprehensive_report(filter_id):
    """عرض التقرير الشامل لتصفية محددة"""
    try:
        print(f"🔍 طلب التقرير الشامل للتصفية رقم: {filter_id}")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        print("✅ تم إنشاء DatabaseManager")
        
        # جلب بيانات التصفية
        filter_data = db_manager.get_filter_by_id(filter_id)
        print(f"📊 بيانات التصفية: {filter_data is not None}")
        
        if not filter_data:
            print(f"❌ التصفية رقم {filter_id} غير موجودة")
            return f"التصفية رقم {filter_id} غير موجودة", 404
        
        # التحقق من وجود القالب
        template_path = project_root / "web_templates" / "comprehensive_report.html"
        if not template_path.exists():
            print(f"❌ القالب غير موجود: {template_path}")
            return "قالب التقرير الشامل غير موجود", 500
        
        print("✅ القالب موجود")
        print(f"📋 عرض التقرير الشامل للتصفية رقم {filter_id}")
        
        # عرض القالب
        return render_template('comprehensive_report.html', filter=filter_data)
        
    except Exception as e:
        print(f"❌ خطأ في التقرير الشامل: {e}")
        import traceback
        traceback.print_exc()
        return f"خطأ في عرض التقرير الشامل: {e}", 500

if __name__ == "__main__":
    print("🚀 بدء خادم اختبار التقرير الشامل...")
    print("📋 Routes المتاحة:")
    print("   / - الصفحة الرئيسية")
    print("   /test - اختبار بسيط")
    print("   /filter/<id> - تفاصيل التصفية")
    print("   /filter/<id>/comprehensive - التقرير الشامل")
    print()
    print("🔗 للاختبار:")
    print("   http://localhost:5001/filter/1/comprehensive")
    print()
    
    app.run(host='0.0.0.0', port=5001, debug=True)
