#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة تصدير كاملة للاستخدام الفوري
Create Complete Export Package for Immediate Use
"""

import os
import shutil
import zipfile
import json
from datetime import datetime
from pathlib import Path

def create_complete_package():
    """إنشاء حزمة تصدير كاملة"""
    print("📦 إنشاء حزمة التصدير الكاملة...")
    print("=" * 60)
    
    # إنشاء مجلد التصدير
    export_dir = Path("CashierFilterSystem_Complete_Export")
    if export_dir.exists():
        shutil.rmtree(export_dir)
    export_dir.mkdir()
    
    print(f"📁 تم إنشاء مجلد التصدير: {export_dir}")
    
    # قائمة الملفات والمجلدات المطلوبة
    essential_files = [
        # الملفات الأساسية
        "main.py",
        "config.py",
        "requirements.txt",
        "requirements_complete.txt",
        "README.md",
        "README_COMPLETE.md",
        "USER_GUIDE.md",

        # ملفات التشغيل الأساسية
        "run.py",
        "run.bat",
        "run.sh",
        "تشغيل_النظام.bat",
        "تشغيل_النظام.sh",

        # ملفات التشغيل المحسنة الجديدة
        "تشغيل_النظام_الكامل.bat",
        "تشغيل_خادم_التقارير_الكامل.bat",
        "وصول_عالمي_كامل.bat",
        
        # خادم التقارير
        "web_server.py",
        "start_web_server.py",
        "start_web_server_silent.py",
        "تشغيل_خادم_التقارير.bat",
        "تشغيل_خادم_التقارير_صامت.bat",
        
        # الوصول العالمي
        "setup_global_access.py",
        "start_global_access.py",
        "cloudflare_tunnel.py",
        "global_access.bat",
        "simple_global_access.bat",
        "وصول_عالمي_سريع.bat",
        "وصول_عالمي_فوري.bat",
        
        # الأدلة والتوثيق
        "دليل_الميزات_الجديدة.md",
        "دليل_التقارير_المحسنة.md",
        "إصلاح_أسماء_العملاء.md",
        "دليل_أسماء_العملاء_والفارق.md",
        "دليل_التقرير_الشامل.md",
        "دليل_الوصول_العالمي.md",
        "الوصول_العالمي_README.md",
        "WEB_SERVER_README.md",
        "تعليمات_التوزيع_السريع.txt",
        
        # ملفات الاختبار
        "test_enhanced_reports.py",
        "test_customer_names_fix.py",
        "test_variance_feature.py",
        "debug_customer_names.py",
        
        # الأصول
        "assets/icon.ico",
        
        # الإعدادات
        "settings.json",
        "version_info.txt",
    ]
    
    essential_dirs = [
        "ui",
        "db", 
        "reports",
        "utils",
        "web_templates",
        "web_static",
    ]
    
    # نسخ الملفات الأساسية
    print("\n📄 نسخ الملفات الأساسية...")
    copied_files = 0
    for file_path in essential_files:
        src = Path(file_path)
        if src.exists():
            dst = export_dir / file_path
            dst.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src, dst)
            copied_files += 1
            print(f"   ✅ {file_path}")
        else:
            print(f"   ⚠️ {file_path} (غير موجود)")
    
    # نسخ المجلدات الأساسية
    print(f"\n📁 نسخ المجلدات الأساسية...")
    copied_dirs = 0
    for dir_path in essential_dirs:
        src = Path(dir_path)
        if src.exists():
            dst = export_dir / dir_path
            shutil.copytree(src, dst, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
            copied_dirs += 1
            print(f"   ✅ {dir_path}/")
        else:
            print(f"   ⚠️ {dir_path}/ (غير موجود)")
    
    # إنشاء قاعدة بيانات فارغة
    print(f"\n🗄️ إنشاء قاعدة بيانات فارغة...")
    db_dir = export_dir / "db"
    db_dir.mkdir(exist_ok=True)
    
    # نسخ ملف إنشاء قاعدة البيانات
    if Path("db/init_db.py").exists():
        shutil.copy2("db/init_db.py", db_dir / "init_db.py")
        print("   ✅ init_db.py")
    
    # إنشاء قاعدة بيانات فارغة
    try:
        import sys
        sys.path.insert(0, str(export_dir))
        from db.init_db import create_database
        create_database(str(db_dir / "cashier_filter.db"))
        print("   ✅ cashier_filter.db (قاعدة بيانات فارغة)")
    except Exception as e:
        print(f"   ⚠️ خطأ في إنشاء قاعدة البيانات: {e}")
    
    # إنشاء ملف معلومات الحزمة
    print(f"\n📋 إنشاء ملف معلومات الحزمة...")
    package_info = {
        "name": "نظام تصفية الكاشير - الإصدار الكامل",
        "version": "3.5.0",
        "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "features": [
            "طريقة الدفع في مقبوضات العملاء (نقدي/شبكة)",
            "رقم المرجع للمعاملات البنكية", 
            "جدول الموردين منفصل عن الحسابات",
            "أسماء العملاء في التقارير",
            "حساب الفارق في التصفية",
            "التقرير الشامل المحسن",
            "خادم التقارير المحسن",
            "الوصول العالمي عبر Cloudflare",
            "تقارير HTML للطباعة",
            "واجهة محسنة مع تحسينات بصرية"
        ],
        "requirements": [
            "Python 3.8+",
            "customtkinter",
            "flask",
            "sqlite3",
            "requests",
            "pandas (اختياري للتصدير)",
            "fpdf2 (اختياري لـ PDF)"
        ],
        "files_count": copied_files,
        "dirs_count": copied_dirs
    }
    
    with open(export_dir / "PACKAGE_INFO.json", 'w', encoding='utf-8') as f:
        json.dump(package_info, f, ensure_ascii=False, indent=2)
    print("   ✅ PACKAGE_INFO.json")
    
    return export_dir, package_info

def create_installation_guide(export_dir):
    """إنشاء دليل التثبيت والتشغيل"""
    print(f"\n📖 إنشاء دليل التثبيت والتشغيل...")
    
    installation_guide = """# 🚀 دليل التثبيت والتشغيل السريع

## 📦 نظام تصفية الكاشير - الإصدار الكامل v3.5.0

### 🎉 الميزات الجديدة في هذا الإصدار:
- ✅ **طريقة الدفع** في مقبوضات العملاء (نقدي/شبكة)
- ✅ **رقم المرجع** للمعاملات البنكية
- ✅ **جدول الموردين** منفصل عن الحسابات
- ✅ **أسماء العملاء** في جميع التقارير
- ✅ **حساب الفارق** الدقيق في التصفية
- ✅ **التقرير الشامل** المحسن على الويب
- ✅ **الوصول العالمي** عبر الإنترنت

---

## ⚡ التشغيل السريع (30 ثانية)

### 🖥️ على Windows:
```
1. فك الضغط عن الملف
2. انقر نقراً مزدوجاً على: تشغيل_النظام.bat
3. انتظر تحميل النظام
4. سجل الدخول: admin / 123456
5. ابدأ الاستخدام فوراً!
```

### 🐧 على Linux/Mac:
```bash
1. فك الضغط عن الملف
2. افتح Terminal في المجلد
3. نفذ: chmod +x تشغيل_النظام.sh
4. نفذ: ./تشغيل_النظام.sh
5. سجل الدخول: admin / 123456
```

---

## 🔧 التثبيت التفصيلي

### 1. متطلبات النظام:
- **Python 3.8+** (يُنصح بـ 3.9 أو أحدث)
- **نظام التشغيل:** Windows 10+, Linux, macOS
- **الذاكرة:** 4GB RAM كحد أدنى
- **المساحة:** 500MB مساحة فارغة

### 2. تثبيت Python (إذا لم يكن مثبتاً):

#### على Windows:
```
1. اذهب إلى: https://python.org/downloads
2. حمل Python 3.9+ 
3. ثبته مع تفعيل "Add to PATH"
4. أعد تشغيل الكمبيوتر
```

#### على Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### على macOS:
```bash
# باستخدام Homebrew
brew install python3
```

### 3. تثبيت المتطلبات:
```bash
# في مجلد النظام
pip install -r requirements.txt
```

أو تثبيت يدوي:
```bash
pip install customtkinter flask requests pandas fpdf2
```

---

## 🚀 طرق التشغيل

### 🎯 الطريقة الأسهل - ملفات التشغيل:

#### على Windows:
- **`تشغيل_النظام.bat`** - تشغيل التطبيق الرئيسي
- **`تشغيل_خادم_التقارير.bat`** - تشغيل خادم التقارير
- **`وصول_عالمي_فوري.bat`** - تشغيل الوصول العالمي

#### على Linux/Mac:
- **`تشغيل_النظام.sh`** - تشغيل التطبيق الرئيسي
- **`run.sh`** - تشغيل بديل

### 🐍 الطريقة اليدوية - Python:
```bash
# التطبيق الرئيسي
python main.py

# خادم التقارير
python web_server.py

# الوصول العالمي
python setup_global_access.py
```

---

## 👤 بيانات تسجيل الدخول الافتراضية

### 🔐 المدير الرئيسي:
```
اسم المستخدم: admin
كلمة المرور: 123456
```

### 👨‍💼 إضافة مديرين جدد:
1. سجل الدخول كـ admin
2. اذهب إلى "إدارة المسؤولين"
3. أضف مدير جديد
4. حدد الصلاحيات

### 👨‍💻 إضافة كاشيرين:
1. سجل الدخول كمدير
2. اذهب إلى "إدارة الكاشيرين"
3. أضف كاشير جديد
4. حدد رقم الكاشير

---

## 🌟 الميزات الجديدة - دليل سريع

### 💳 طريقة الدفع في مقبوضات العملاء:
```
1. في قسم "مقبوضات العملاء"
2. اختر طريقة الدفع: نقدي أو شبكة
3. أدخل رقم المرجع للشبكة (اختياري)
4. احفظ - ستظهر في التقارير
```

### 🏭 جدول الموردين:
```
1. في قسم "الموردين" الجديد
2. أدخل اسم المورد والمبلغ
3. اختر طريقة الدفع (نقدي/شيك/تحويل بنكي)
4. أضف ملاحظات
5. لا يؤثر على حسابات التصفية
```

### 📊 التقرير الشامل:
```
1. شغل خادم التقارير
2. اذهب إلى: http://localhost:5000
3. اختر تصفية
4. انقر "التقرير الشامل"
5. استمتع بالتفاصيل الكاملة
```

### 🌐 الوصول العالمي:
```
1. شغل: وصول_عالمي_فوري.bat
2. انتظر إنشاء الرابط العالمي
3. انسخ الرابط واستخدمه من أي مكان
4. شارك مع الفريق للوصول عن بُعد
```

---

## 🔍 استكشاف الأخطاء

### ❓ خطأ "Python not found":
```
الحل: ثبت Python من python.org
تأكد من تفعيل "Add to PATH"
```

### ❓ خطأ "Module not found":
```bash
الحل: ثبت المتطلبات
pip install -r requirements.txt
```

### ❓ لا يفتح التطبيق:
```
1. تأكد من Python 3.8+
2. ثبت المتطلبات
3. شغل من Terminal: python main.py
4. راجع رسائل الخطأ
```

### ❓ خادم التقارير لا يعمل:
```
1. تأكد من المنفذ 5000 غير مستخدم
2. شغل: python web_server.py
3. اذهب إلى: http://localhost:5000
```

### ❓ الوصول العالمي لا يعمل:
```
1. تأكد من اتصال الإنترنت
2. شغل: python setup_global_access.py
3. اتبع التعليمات على الشاشة
```

---

## 📞 الدعم والمساعدة

### 📚 الأدلة المتاحة:
- **`دليل_الميزات_الجديدة.md`** - شرح الميزات الجديدة
- **`دليل_التقارير_المحسنة.md`** - التقارير والطباعة
- **`دليل_الوصول_العالمي.md`** - الوصول عن بُعد
- **`USER_GUIDE.md`** - دليل المستخدم الشامل

### 🧪 ملفات الاختبار:
```bash
# اختبار الميزات الجديدة
python test_enhanced_reports.py

# اختبار أسماء العملاء
python test_customer_names_fix.py

# اختبار الفارق
python test_variance_feature.py
```

---

## 🎊 استمتع بالنظام!

### ✅ الآن لديك:
- **نظام تصفية متكامل** مع جميع الميزات
- **تقارير احترافية** مع تفاصيل كاملة
- **وصول عالمي** من أي مكان في العالم
- **واجهة محسنة** سهلة الاستخدام
- **دعم كامل** للعربية والإنجليزية

### 🚀 ابدأ الآن:
1. **شغل النظام:** `تشغيل_النظام.bat`
2. **سجل الدخول:** admin / 123456
3. **ابدأ تصفية جديدة**
4. **جرب الميزات الجديدة**
5. **استمتع بالتقارير المحسنة**

**مرحباً بك في نظام تصفية الكاشير المحسن!** 🎉✨

---

**الإصدار:** 3.5.0  
**تاريخ البناء:** {build_date}  
**المطور:** محمد الكامل  
**الحالة:** ✅ جاهز للاستخدام الفوري
"""
    
    with open(export_dir / "INSTALLATION_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(installation_guide.format(build_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    print("   ✅ INSTALLATION_GUIDE.md")

def create_batch_files(export_dir):
    """إنشاء ملفات التشغيل المحسنة"""
    print(f"\n⚡ إنشاء ملفات التشغيل المحسنة...")
    
    # ملف تشغيل النظام الرئيسي
    main_bat = """@echo off
chcp 65001 > nul
title نظام تصفية الكاشير v3.5.0
echo.
echo ========================================
echo    نظام تصفية الكاشير v3.5.0
echo    تشغيل النظام الرئيسي...
echo ========================================
echo.

REM التحقق من Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تثبيت Python من: https://python.org/downloads
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo 🚀 بدء تشغيل النظام...
echo.

REM تثبيت المتطلبات إذا لزم الأمر
if exist requirements.txt (
    echo 📦 التحقق من المتطلبات...
    pip install -r requirements.txt --quiet
)

REM تشغيل النظام
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 💡 تأكد من تثبيت جميع المتطلبات
    echo.
    pause
)
"""
    
    with open(export_dir / "تشغيل_النظام_المحسن.bat", 'w', encoding='utf-8') as f:
        f.write(main_bat)
    print("   ✅ تشغيل_النظام_المحسن.bat")
    
    # ملف تشغيل خادم التقارير
    web_bat = """@echo off
chcp 65001 > nul
title خادم التقارير v3.5.0
echo.
echo ========================================
echo    خادم التقارير المحسن v3.5.0
echo    مع الميزات الجديدة
echo ========================================
echo.

python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    pause
    exit /b 1
)

echo ✅ بدء تشغيل خادم التقارير...
echo 🌐 سيكون متاح على: http://localhost:5000
echo 📊 يحتوي على جميع الميزات الجديدة:
echo    💳 طريقة الدفع في مقبوضات العملاء
echo    🏭 جدول الموردين منفصل
echo    📄 رقم المرجع للمعاملات البنكية
echo    👥 أسماء العملاء في التقارير
echo.

python web_server.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل خادم التقارير
    pause
)
"""
    
    with open(export_dir / "تشغيل_خادم_التقارير_المحسن.bat", 'w', encoding='utf-8') as f:
        f.write(web_bat)
    print("   ✅ تشغيل_خادم_التقارير_المحسن.bat")
    
    # ملف الوصول العالمي السريع
    global_bat = """@echo off
chcp 65001 > nul
title الوصول العالمي v3.5.0
echo.
echo ========================================
echo    الوصول العالمي السريع v3.5.0
echo    الوصول من أي مكان في العالم
echo ========================================
echo.

python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    pause
    exit /b 1
)

echo 🌐 إعداد الوصول العالمي...
echo 🚀 سيتم إنشاء رابط للوصول من أي مكان
echo.

python setup_global_access.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في إعداد الوصول العالمي
    echo 💡 تأكد من اتصال الإنترنت
    pause
)
"""
    
    with open(export_dir / "وصول_عالمي_محسن.bat", 'w', encoding='utf-8') as f:
        f.write(global_bat)
    print("   ✅ وصول_عالمي_محسن.bat")

def create_zip_package(export_dir, package_info):
    """إنشاء ملف ZIP للتوزيع"""
    print(f"\n📦 إنشاء ملف ZIP للتوزيع...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"CashierFilterSystem_v3.5.0_Complete_{timestamp}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(export_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(export_dir.parent)
                zipf.write(file_path, arc_path)
    
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    print(f"   ✅ {zip_filename} ({zip_size:.1f} MB)")
    
    return zip_filename, zip_size

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء حزمة التصدير الكاملة للاستخدام الفوري")
    print("=" * 70)
    print("📦 يحتوي على جميع الميزات الجديدة:")
    print("   💳 طريقة الدفع في مقبوضات العملاء")
    print("   🏭 جدول الموردين منفصل")
    print("   📄 رقم المرجع للمعاملات البنكية")
    print("   👥 أسماء العملاء في التقارير")
    print("   ⚖️ حساب الفارق الدقيق")
    print("   📊 التقرير الشامل المحسن")
    print("   🌐 الوصول العالمي")
    print("=" * 70)
    
    try:
        # إنشاء الحزمة الأساسية
        export_dir, package_info = create_complete_package()
        
        # إنشاء دليل التثبيت
        create_installation_guide(export_dir)
        
        # إنشاء ملفات التشغيل المحسنة
        create_batch_files(export_dir)
        
        # إنشاء ملف ZIP
        zip_filename, zip_size = create_zip_package(export_dir, package_info)
        
        print(f"\n🎉 تم إنشاء الحزمة الكاملة بنجاح!")
        print("=" * 70)
        print(f"📦 اسم الحزمة: {zip_filename}")
        print(f"📏 حجم الحزمة: {zip_size:.1f} MB")
        print(f"📁 عدد الملفات: {package_info['files_count']}")
        print(f"📂 عدد المجلدات: {package_info['dirs_count']}")
        print(f"🗓️ تاريخ البناء: {package_info['build_date']}")
        
        print(f"\n✅ الحزمة جاهزة للتوزيع والاستخدام الفوري!")
        print("🚀 يمكن تشغيلها على أي كمبيوتر بـ Python")
        print("📖 راجع INSTALLATION_GUIDE.md للتعليمات")
        
        print(f"\n🎯 للاستخدام السريع:")
        print("1. فك الضغط عن الملف")
        print("2. شغل: تشغيل_النظام_المحسن.bat")
        print("3. سجل الدخول: admin / 123456")
        print("4. استمتع بجميع الميزات الجديدة!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحزمة: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
