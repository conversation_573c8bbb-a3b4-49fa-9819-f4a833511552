@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🔨 بناء النسخة الكاملة لنظام تصفية الكاشير                      ║
echo ║         Building COMPLETE Version of Cashier Filter System          ║
echo ║                                                                      ║
echo ║    الإصدار 3.5.0 - النسخة الكاملة مع جميع الميزات                  ║
echo ║    Version 3.5.0 - Complete with ALL Features                       ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📁 الانتقال إلى مجلد المشروع...
cd /d "%~dp0"
echo    المجلد الحالي: %CD%
echo.

echo 🐍 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo    يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo    ✅ Python متوفر
echo.

echo 📦 التحقق من PyInstaller...
python -c "import PyInstaller; print('PyInstaller version:', PyInstaller.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ PyInstaller غير مثبت، جاري التثبيت...
    python -m pip install pyinstaller>=5.13.0
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo    ✅ تم تثبيت PyInstaller
) else (
    echo    ✅ PyInstaller متوفر
)
echo.

echo 🔍 التحقق من الملفات المطلوبة...
if not exist "main.py" (
    echo ❌ ملف main.py غير موجود
    pause
    exit /b 1
)
if not exist "CashierFilterSystem_Complete.spec" (
    echo ❌ ملف CashierFilterSystem_Complete.spec غير موجود
    pause
    exit /b 1
)
if not exist "pyinstaller_utils.py" (
    echo ❌ ملف pyinstaller_utils.py غير موجود
    pause
    exit /b 1
)
echo    ✅ جميع الملفات المطلوبة موجودة
echo.

echo 🧹 تنظيف البناءات السابقة...
if exist "build" rmdir /s /q "build" 2>nul
if exist "dist" rmdir /s /q "dist" 2>nul
echo    ✅ تم تنظيف البناءات السابقة
echo.

echo 🚀 بدء بناء النسخة الكاملة...
echo    هذا سيستغرق 5-15 دقيقة حسب سرعة جهازك...
echo    سيتم عرض التقدم أدناه:
echo.

python build_complete_exe.py

if %errorlevel% equ 0 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════╗
    echo ║                                                                      ║
    echo ║    🎉 تم بناء النسخة الكاملة بنجاح!                                 ║
    echo ║         COMPLETE Version Built Successfully!                        ║
    echo ║                                                                      ║
    echo ╚══════════════════════════════════════════════════════════════════════╝
    echo.
    echo 📦 ستجد النسخة الكاملة في:
    echo    CashierFilterSystem_Complete_Distribution\
    echo.
    echo 🎯 الميزات المشمولة:
    echo    ✅ نظام تصفية كاشير كامل
    echo    ✅ تقارير متقدمة مع جميع البيانات
    echo    ✅ طباعة احترافية مع بيانات الموردين
    echo    ✅ تقارير ويب تفاعلية
    echo    ✅ تحليل ذكي بالذكاء الاصطناعي
    echo    ✅ تكامل سحابي
    echo    ✅ أمان وتشفير متقدم
    echo    ✅ واجهة ويب متجاوبة
    echo.
    echo 🚀 جاهز للاستخدام الإنتاجي والتوزيع!
    echo.
    
    if exist "CashierFilterSystem_Complete_Distribution" (
        echo 📂 فتح مجلد التوزيع الكامل...
        explorer "CashierFilterSystem_Complete_Distribution"
    )
) else (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════╗
    echo ║                                                                      ║
    echo ║    ❌ فشل في بناء النسخة الكاملة                                    ║
    echo ║         COMPLETE Build Failed                                       ║
    echo ║                                                                      ║
    echo ╚══════════════════════════════════════════════════════════════════════╝
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تأكد من تثبيت جميع المتطلبات
    echo    2. شغل Command Prompt كمسؤول
    echo    3. تحديث Python و PyInstaller
    echo    4. تحقق من مساحة القرص الصلب
    echo.
    echo 📧 للدعم: <EMAIL>
)

echo.
pause
