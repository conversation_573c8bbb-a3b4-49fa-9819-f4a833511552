#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من سلامة حزمة الإصدار التنفيذي
Verify EXE Package Integrity
"""

import os
import zipfile
import json
from pathlib import Path

def verify_exe_package():
    """التحقق من سلامة حزمة الإصدار التنفيذي"""
    print("🔍 التحقق من سلامة حزمة الإصدار التنفيذي...")
    print("=" * 70)
    
    # البحث عن ملف ZIP للإصدار التنفيذي
    zip_files = list(Path(".").glob("CashierFilterSystem_v3.5.0_EXE_*.zip"))
    
    if not zip_files:
        print("❌ لم يتم العثور على ملف ZIP للإصدار التنفيذي")
        return False
    
    # اختيار أحدث ملف
    zip_file = max(zip_files, key=lambda f: f.stat().st_mtime)
    print(f"📦 فحص الحزمة: {zip_file.name}")
    
    try:
        with zipfile.ZipFile(zip_file, 'r') as zf:
            # فحص سلامة الملف
            print("🔍 فحص سلامة ملف ZIP...")
            bad_file = zf.testzip()
            if bad_file:
                print(f"❌ ملف تالف: {bad_file}")
                return False
            print("✅ ملف ZIP سليم")
            
            # قائمة الملفات
            file_list = zf.namelist()
            print(f"📁 عدد الملفات: {len(file_list)}")
            
            # فحص الملفات التنفيذية الأساسية
            essential_exe_files = [
                "CashierFilterSystem_v3.5.0.exe",
                "WebReportServer_v3.5.0.exe"
            ]
            
            print("\n🔍 فحص الملفات التنفيذية:")
            missing_exe_files = []
            for file_name in essential_exe_files:
                found = any(file_name in path for path in file_list)
                if found:
                    print(f"   ✅ {file_name}")
                    # البحث عن حجم الملف
                    for path in file_list:
                        if file_name in path:
                            try:
                                file_info = zf.getinfo(path)
                                size_mb = file_info.file_size / (1024 * 1024)
                                print(f"      📏 الحجم: {size_mb:.1f} MB")
                            except:
                                pass
                            break
                else:
                    print(f"   ❌ {file_name} (مفقود)")
                    missing_exe_files.append(file_name)
            
            # فحص ملفات التشغيل
            batch_files = [
                "تشغيل_النظام.bat",
                "تشغيل_خادم_التقارير.bat"
            ]
            
            print("\n🔍 فحص ملفات التشغيل:")
            missing_batch_files = []
            for file_name in batch_files:
                found = any(file_name in path for path in file_list)
                if found:
                    print(f"   ✅ {file_name}")
                else:
                    print(f"   ❌ {file_name} (مفقود)")
                    missing_batch_files.append(file_name)
            
            # فحص الأدلة
            guide_files = [
                "دليل_الاستخدام_الفوري.md",
                "README_COMPLETE.md",
                "دليل_الميزات_الجديدة.md"
            ]
            
            print("\n🔍 فحص الأدلة:")
            missing_guides = []
            for file_name in guide_files:
                found = any(file_name in path for path in file_list)
                if found:
                    print(f"   ✅ {file_name}")
                else:
                    print(f"   ⚠️ {file_name} (مفقود)")
                    missing_guides.append(file_name)
            
            # فحص قاعدة البيانات
            print("\n🔍 فحص قاعدة البيانات:")
            db_found = any("db/" in path and "cashier_filter.db" in path for path in file_list)
            if db_found:
                print("   ✅ قاعدة البيانات موجودة")
            else:
                print("   ⚠️ قاعدة البيانات غير موجودة")
            
            # فحص ملف معلومات الحزمة
            package_info_path = None
            for path in file_list:
                if "PACKAGE_INFO_EXE.json" in path:
                    package_info_path = path
                    break
            
            if package_info_path:
                print("\n📋 فحص معلومات الحزمة:")
                try:
                    with zf.open(package_info_path) as f:
                        package_info = json.load(f)
                    
                    print(f"   ✅ اسم الحزمة: {package_info.get('name', 'غير محدد')}")
                    print(f"   ✅ الإصدار: {package_info.get('version', 'غير محدد')}")
                    print(f"   ✅ تاريخ البناء: {package_info.get('build_date', 'غير محدد')}")
                    print(f"   ✅ حجم ZIP: {package_info.get('zip_size_mb', 0)} MB")
                    print(f"   ✅ حجم EXE: {package_info.get('exe_size_mb', 0)} MB")
                    print(f"   ✅ يحتاج Python: {package_info.get('requires_python', True)}")
                    
                    # فحص الميزات
                    features = package_info.get('features', [])
                    print(f"   ✅ عدد الميزات: {len(features)}")
                    
                    # فحص متطلبات النظام
                    requirements = package_info.get('system_requirements', [])
                    print(f"   ✅ متطلبات النظام: {len(requirements)} عنصر")
                    
                except Exception as e:
                    print(f"   ⚠️ خطأ في قراءة معلومات الحزمة: {e}")
            else:
                print("\n⚠️ ملف معلومات الحزمة غير موجود")
            
            # النتيجة النهائية
            print("\n" + "=" * 70)
            if not missing_exe_files and not missing_batch_files:
                print("🎉 حزمة الإصدار التنفيذي سليمة وجاهزة للتوزيع!")
                print("✅ جميع الملفات التنفيذية موجودة")
                print("✅ ملفات التشغيل متاحة")
                print("✅ ملف ZIP سليم وغير تالف")
                
                # حساب حجم الحزمة
                size_mb = zip_file.stat().st_size / (1024 * 1024)
                print(f"📏 حجم الحزمة: {size_mb:.1f} MB")
                
                print(f"\n🚀 الحزمة جاهزة للتوزيع:")
                print(f"   📦 الملف: {zip_file.name}")
                print(f"   📁 الملفات: {len(file_list)}")
                print(f"   📏 الحجم: {size_mb:.1f} MB")
                print(f"   ⚡ النوع: ملف تنفيذي مستقل")
                
                return True
            else:
                print("❌ حزمة الإصدار التنفيذي غير مكتملة!")
                if missing_exe_files:
                    print(f"   💾 ملفات تنفيذية مفقودة: {len(missing_exe_files)}")
                    for file in missing_exe_files:
                        print(f"      • {file}")
                if missing_batch_files:
                    print(f"   ⚡ ملفات تشغيل مفقودة: {len(missing_batch_files)}")
                    for file in missing_batch_files:
                        print(f"      • {file}")
                return False
                
    except zipfile.BadZipFile:
        print("❌ ملف ZIP تالف أو غير صالح")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص الحزمة: {e}")
        return False

def test_exe_files():
    """اختبار وجود الملفات التنفيذية في المجلد"""
    print("\n🧪 اختبار الملفات التنفيذية المحلية...")
    
    exe_package_dir = Path("CashierFilterSystem_EXE_Package")
    if not exe_package_dir.exists():
        print("❌ مجلد الحزمة التنفيذية غير موجود")
        return False
    
    # فحص الملفات التنفيذية
    main_exe = exe_package_dir / "CashierFilterSystem_v3.5.0.exe"
    web_exe = exe_package_dir / "WebReportServer_v3.5.0.exe"
    
    print("🔍 فحص الملفات التنفيذية:")
    
    if main_exe.exists():
        size_mb = main_exe.stat().st_size / (1024 * 1024)
        print(f"   ✅ CashierFilterSystem_v3.5.0.exe ({size_mb:.1f} MB)")
    else:
        print("   ❌ CashierFilterSystem_v3.5.0.exe (مفقود)")
        return False
    
    if web_exe.exists():
        size_mb = web_exe.stat().st_size / (1024 * 1024)
        print(f"   ✅ WebReportServer_v3.5.0.exe ({size_mb:.1f} MB)")
    else:
        print("   ⚠️ WebReportServer_v3.5.0.exe (مفقود - اختياري)")
    
    # فحص ملفات التشغيل
    batch_files = [
        "تشغيل_النظام.bat",
        "تشغيل_خادم_التقارير.bat"
    ]
    
    print("\n🔍 فحص ملفات التشغيل:")
    for batch_file in batch_files:
        file_path = exe_package_dir / batch_file
        if file_path.exists():
            print(f"   ✅ {batch_file}")
        else:
            print(f"   ❌ {batch_file} (مفقود)")
    
    # فحص قاعدة البيانات
    db_file = exe_package_dir / "db" / "cashier_filter.db"
    if db_file.exists():
        size_kb = db_file.stat().st_size / 1024
        print(f"\n✅ قاعدة البيانات: {size_kb:.1f} KB")
    else:
        print("\n⚠️ قاعدة البيانات غير موجودة")
    
    return True

def create_exe_distribution_info():
    """إنشاء ملف معلومات التوزيع للإصدار التنفيذي"""
    print("\n📋 إنشاء ملف معلومات التوزيع...")
    
    # البحث عن ملف ZIP
    zip_files = list(Path(".").glob("CashierFilterSystem_v3.5.0_EXE_*.zip"))
    if not zip_files:
        return
    
    zip_file = max(zip_files, key=lambda f: f.stat().st_mtime)
    size_mb = zip_file.stat().st_size / (1024 * 1024)
    
    # حساب حجم الملف التنفيذي
    exe_package_dir = Path("CashierFilterSystem_EXE_Package")
    main_exe = exe_package_dir / "CashierFilterSystem_v3.5.0.exe"
    exe_size = 0
    if main_exe.exists():
        exe_size = main_exe.stat().st_size / (1024 * 1024)
    
    distribution_info = f"""# 📦 معلومات التوزيع - نظام تصفية الكاشير v3.5.0 EXE

## 📊 تفاصيل الحزمة التنفيذية
- **اسم الملف:** {zip_file.name}
- **حجم الحزمة:** {size_mb:.1f} MB
- **حجم الملف التنفيذي:** {exe_size:.1f} MB
- **تاريخ الإنشاء:** {zip_file.stat().st_mtime}
- **النوع:** ملف تنفيذي مستقل
- **الحالة:** ✅ جاهز للتوزيع

## ⚡ للاستخدام الفوري (10 ثوانٍ)
1. فك الضغط عن الملف
2. انقر مزدوجاً على: `تشغيل_النظام.bat`
3. سجل الدخول: admin / 123456
4. استمتع بجميع الميزات الجديدة!

## 🌟 المزايا الرئيسية
- ⚡ **لا يحتاج Python** - يعمل فوراً على أي كمبيوتر
- 🔧 **لا يحتاج تثبيت** مكتبات أو dependencies
- 💻 **متوافق مع جميع إصدارات Windows** (7/8/10/11)
- 🔒 **آمن ومستقل** - لا يرسل بيانات لأي خادم
- 📱 **محمول تماماً** - يعمل من USB أو أي مجلد

## 🎯 الميزات المدمجة
- 💳 طريقة الدفع في مقبوضات العملاء (نقدي/شبكة)
- 📄 رقم المرجع للمعاملات البنكية
- 🏭 جدول الموردين منفصل عن الحسابات
- 👥 أسماء العملاء في جميع التقارير
- ⚖️ حساب الفارق الدقيق في التصفية
- 📊 التقرير الشامل المحسن على الويب
- 🌐 الوصول العالمي عبر الإنترنت

## 🔧 متطلبات النظام
- Windows 7/8/10/11 (32-bit أو 64-bit)
- 4GB RAM كحد أدنى
- 500MB مساحة فارغة
- لا يحتاج Python أو مكتبات إضافية

## 📁 محتويات الحزمة
- `CashierFilterSystem_v3.5.0.exe` - التطبيق الرئيسي
- `WebReportServer_v3.5.0.exe` - خادم التقارير
- `تشغيل_النظام.bat` - ملف التشغيل السريع
- `دليل_الاستخدام_الفوري.md` - دليل الاستخدام

## ✅ تم التحقق من سلامة الحزمة
- جميع الملفات التنفيذية موجودة وسليمة
- ملفات التشغيل متاحة ومحدثة
- ملف ZIP سليم وغير تالف
- قاعدة البيانات جاهزة للاستخدام
- جاهز للتوزيع والاستخدام الفوري

---
**المطور:** محمد الكامل  
**الإصدار:** 3.5.0 EXE  
**التاريخ:** 9 يوليو 2025  
**الحالة:** ✅ جاهز للإنتاج  
**النوع:** ملف تنفيذي مستقل
"""
    
    with open("EXE_DISTRIBUTION_INFO.md", 'w', encoding='utf-8') as f:
        f.write(distribution_info)
    
    print("✅ تم إنشاء EXE_DISTRIBUTION_INFO.md")

def main():
    """الدالة الرئيسية"""
    print("🔍 التحقق من سلامة حزمة الإصدار التنفيذي")
    print("=" * 80)
    
    # اختبار الملفات المحلية
    local_test = test_exe_files()
    
    # التحقق من سلامة الحزمة
    is_valid = verify_exe_package()
    
    if is_valid and local_test:
        # إنشاء ملف معلومات التوزيع
        create_exe_distribution_info()
        
        print("\n🎉 حزمة الإصدار التنفيذي جاهزة للتوزيع!")
        print("⚡ لا يحتاج Python - يعمل على أي كمبيوتر Windows فوراً!")
        print("📦 يمكن توزيعها واستخدامها على أي عدد من الأجهزة")
        print("🚀 للاستخدام الفوري: فك الضغط وانقر مزدوجاً على تشغيل_النظام.bat")
    else:
        print("\n❌ الحزمة تحتاج إصلاح قبل التوزيع")
        print("💡 أعد تشغيل create_exe_package.py")

if __name__ == "__main__":
    main()
