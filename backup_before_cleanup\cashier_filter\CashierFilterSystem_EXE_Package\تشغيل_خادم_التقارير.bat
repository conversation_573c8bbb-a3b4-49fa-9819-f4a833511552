@echo off
chcp 65001 > nul
title خادم التقارير المحسن v3.5.0
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           🌐 خادم التقارير المحسن v3.5.0                   ██
echo ██                                                            ██
echo ██  📊 الميزات الجديدة في التقارير:                          ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  📄 رقم المرجع للمعاملات البنكية                         ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء الحقيقية                               ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██                                                            ██
echo ██  ⚡ لا يحتاج Python - يعمل فوراً!                        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 بدء تشغيل خادم التقارير...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🌐 خادم التقارير سيكون متاحاً على:                        │
echo │                                                             │
echo │     🔗 http://localhost:5000                               │
echo │                                                             │
echo │  📊 الميزات المتاحة:                                        │
echo │     • قائمة جميع التصفيات                                   │
echo │     • التقرير الشامل لكل تصفية                             │
echo │     • طباعة التقارير                                       │
echo │     • تصدير البيانات                                       │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

WebReportServer_v3.5.0.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل خادم التقارير
    pause
) else (
    echo.
    echo ✅ تم إيقاف خادم التقارير بنجاح
)

pause
