<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الشامل - تصفية #{{ filter.sequence_number or filter.id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .amount {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        .section-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            margin-top: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        .financial-summary {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        @media print {
            .no-print { display: none !important; }
            .print-break { page-break-before: always; }
            body { background: white !important; }
        }
        .badge-custom {
            font-size: 0.9em;
            padding: 8px 12px;
        }
        .highlight-amount {
            background: #fff3cd;
            padding: 5px 10px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }
        .text-brown {
            color: #795548 !important;
        }
        .table-warning th {
            background-color: #fff3cd !important;
            color: #856404 !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Header -->
        <div class="print-header">
            <h1 class="text-primary">
                <i class="fas fa-file-invoice-dollar"></i>
                التقرير الشامل لتصفية الكاشير
            </h1>
            <h2>تصفية رقم: {{ filter.sequence_number or filter.id }}</h2>
            <p class="text-muted">تاريخ التصفية: {{ filter.date }} | الكاشير: {{ filter.cashier_name or 'غير محدد' }}</p>
        </div>

        <!-- Navigation -->
        <div class="row no-print mb-3">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="/reports">التقارير</a></li>
                        <li class="breadcrumb-item active">التقرير الشامل</li>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="financial-summary">
            <div class="row">
                <div class="col-md-8">
                    <h3><i class="fas fa-chart-line"></i> الملخص التنفيذي</h3>
                    {% set total_income = (filter.details.bank_total or 0) + (filter.details.cash_total or 0) + (filter.details.credit_total or 0) + (filter.details.client_total or 0) %}
                    {% set total_deductions = (filter.details.return_total or 0) + (filter.details.expenses | sum(attribute='amount') or 0) %}
                    {% set total_adjustments = filter.details.adjustments | sum(attribute='amount') or 0 %}
                    {% set final_total = total_income - total_deductions + total_adjustments %}
                    
                    <div class="row mt-3">
                        <div class="col-4">
                            <h5>إجمالي الإيرادات</h5>
                            <h2 class="amount">{{ "{:,.2f}".format(total_income) }} ريال</h2>
                        </div>
                        <div class="col-4">
                            <h5>المبلغ النهائي</h5>
                            <h2 class="amount">{{ "{:,.2f}".format(final_total) }} ريال</h2>
                        </div>
                        <div class="col-4">
                            {% if filter.details.variance %}
                            <h5>حالة التصفية</h5>
                            <h2 class="{% if filter.details.variance.status_type == 'balanced' %}text-success{% elif filter.details.variance.status_type == 'surplus' %}text-info{% else %}text-warning{% endif %}">
                                {{ filter.details.variance.status_icon }} {{ filter.details.variance.status }}
                            </h2>
                            <small class="text-muted">
                                {% if filter.details.variance.abs_difference > 0.01 %}
                                {{ "{:+,.2f}".format(filter.details.variance.difference) }} ريال
                                {% else %}
                                متوازن تماماً
                                {% endif %}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="bg-white text-dark rounded p-3">
                        <h6>إحصائيات سريعة</h6>
                        <p class="mb-1">المعاملات البنكية: <strong>{{ filter.details.bank_transactions | length }}</strong></p>
                        <p class="mb-1">فئات النقدي: <strong>{{ filter.details.cash_details | length }}</strong></p>
                        <p class="mb-1">إجمالي المعاملات: <strong>{{ filter.details.total_transactions or 0 }}</strong></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Financial Breakdown -->
        <div class="detail-table">
            <div class="section-header">
                <h4><i class="fas fa-calculator"></i> التفصيل المالي الشامل</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-primary">
                        <tr>
                            <th width="40%">البيان</th>
                            <th width="20%" class="text-center">العدد</th>
                            <th width="20%" class="text-center">المبلغ</th>
                            <th width="20%" class="text-center">النسبة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-credit-card text-primary"></i> المقبوضات البنكية</td>
                            <td class="text-center">{{ filter.details.bank_transactions | length }}</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.bank_total or 0) }}</td>
                            <td class="text-center">{{ "{:.1f}%".format((filter.details.bank_total or 0) / total_income * 100 if total_income > 0 else 0) }}</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-money-bill text-success"></i> المقبوضات النقدية</td>
                            <td class="text-center">{{ filter.details.cash_details | length }}</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.cash_total or 0) }}</td>
                            <td class="text-center">{{ "{:.1f}%".format((filter.details.cash_total or 0) / total_income * 100 if total_income > 0 else 0) }}</td>
                        </tr>
                        {% if filter.details.credit_total and filter.details.credit_total > 0 %}
                        <tr>
                            <td><i class="fas fa-clock text-warning"></i> المبيعات الآجلة</td>
                            <td class="text-center">{{ filter.details.credit_details | length if filter.details.credit_details else 1 }}</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.credit_total or 0) }}</td>
                            <td class="text-center">{{ "{:.1f}%".format((filter.details.credit_total or 0) / total_income * 100 if total_income > 0 else 0) }}</td>
                        </tr>
                        {% endif %}
                        {% if filter.details.client_total and filter.details.client_total > 0 %}
                        <tr>
                            <td><i class="fas fa-users text-info"></i> مقبوضات العملاء</td>
                            <td class="text-center">{{ filter.details.client_details | length if filter.details.client_details else 1 }}</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.client_total or 0) }}</td>
                            <td class="text-center">{{ "{:.1f}%".format((filter.details.client_total or 0) / total_income * 100 if total_income > 0 else 0) }}</td>
                        </tr>
                        {% endif %}
                        {% if filter.details.return_total and filter.details.return_total > 0 %}
                        <tr class="table-danger">
                            <td><i class="fas fa-undo text-danger"></i> فواتير المرتجعات</td>
                            <td class="text-center">{{ filter.details.return_details | length if filter.details.return_details else 1 }}</td>
                            <td class="amount text-center text-danger">-{{ "{:,.2f}".format(filter.details.return_total or 0) }}</td>
                            <td class="text-center">{{ "{:.1f}%".format((filter.details.return_total or 0) / total_income * 100 if total_income > 0 else 0) }}</td>
                        </tr>
                        {% endif %}
                    </tbody>
                    <tfoot class="table-success">
                        <tr>
                            <th>إجمالي الإيرادات</th>
                            <th class="text-center">{{ filter.details.total_transactions or 0 }}</th>
                            <th class="amount text-center">{{ "{:,.2f}".format(total_income) }}</th>
                            <th class="text-center">100.0%</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- Variance Analysis -->
        {% if filter.details.variance %}
        <div class="detail-table">
            <div class="section-header" style="background: linear-gradient(135deg,
                {% if filter.details.variance.status_type == 'balanced' %}#28a745, #20c997{% elif filter.details.variance.status_type == 'surplus' %}#17a2b8, #138496{% else %}#ffc107, #e0a800{% endif %});">
                <h4>{{ filter.details.variance.status_icon }} تحليل الفارق في التصفية - {{ filter.details.variance.status }}</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-primary">
                        <tr>
                            <th width="40%">البيان</th>
                            <th width="30%" class="text-center">المبلغ</th>
                            <th width="30%" class="text-center">ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-desktop text-info"></i> مبيعات النظام المتوقعة</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.variance.system_sales or 0) }} ريال</td>
                            <td class="text-center">حسب النظام</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-calculator text-success"></i> إجمالي المقبوضات الفعلية</td>
                            <td class="amount text-center">{{ "{:,.2f}".format(filter.details.variance.actual_total or 0) }} ريال</td>
                            <td class="text-center">حسب التصفية</td>
                        </tr>
                        <tr class="{% if filter.details.variance.status_type == 'balanced' %}table-success{% elif filter.details.variance.status_type == 'surplus' %}table-info{% else %}table-warning{% endif %}">
                            <td><strong>{{ filter.details.variance.status_icon }} الفارق ({{ filter.details.variance.status }})</strong></td>
                            <td class="amount text-center">
                                <strong class="{% if filter.details.variance.status_type == 'balanced' %}text-success{% elif filter.details.variance.status_type == 'surplus' %}text-info{% else %}text-warning{% endif %}">
                                    {{ "{:+,.2f}".format(filter.details.variance.difference or 0) }} ريال
                                </strong>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-{% if filter.details.variance.status_type == 'balanced' %}success{% elif filter.details.variance.status_type == 'surplus' %}info{% else %}warning{% endif %}">
                                    {{ filter.details.variance.status }}
                                </span>
                                <br>
                                <small class="text-muted">{{ filter.details.variance.severity }}</small>
                            </td>
                        </tr>
                        {% if filter.details.variance.percentage != 0 %}
                        <tr>
                            <td><i class="fas fa-percentage"></i> نسبة الفارق</td>
                            <td class="amount text-center">
                                <strong class="{% if filter.details.variance.percentage > 0 %}text-success{% elif filter.details.variance.percentage < 0 %}text-danger{% else %}text-warning{% endif %}">
                                    {{ "{:+.2f}".format(filter.details.variance.percentage or 0) }}%
                                </strong>
                            </td>
                            <td class="text-center">
                                {% set abs_percentage = filter.details.variance.percentage if filter.details.variance.percentage >= 0 else -filter.details.variance.percentage %}
                        {% if abs_percentage < 1 %}
                                <small class="text-muted">فارق طبيعي</small>
                                {% elif abs_percentage < 5 %}
                                <small class="text-warning">فارق مقبول</small>
                                {% else %}
                                <small class="text-danger">فارق كبير - يحتاج مراجعة</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <!-- Variance Explanation -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">{{ filter.details.variance.status_icon }} تفسير الفارق:</h6>

                    {% if filter.details.variance.status_type == 'balanced' %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>⚖️ التصفية متوازنة تماماً!</strong>
                        <br>لا يوجد فارق بين مبيعات النظام والمقبوضات الفعلية.
                        <br><small class="text-muted">هذا يعني أن جميع المبيعات تم تحصيلها بدقة.</small>
                    </div>

                    {% elif filter.details.variance.status_type == 'surplus' %}
                    <div class="alert alert-info">
                        <i class="fas fa-trending-up"></i>
                        <strong>📈 يوجد فائض في المقبوضات!</strong>
                        <br>المقبوضات الفعلية أكبر من مبيعات النظام بمقدار <strong>{{ "{:,.2f}".format(filter.details.variance.abs_difference) }} ريال</strong>
                        <br><strong>النسبة:</strong> +{{ "{:.2f}".format(filter.details.variance.abs_percentage) }}%

                        <hr class="my-2">
                        <h6><i class="fas fa-lightbulb text-warning"></i> الأسباب المحتملة للفائض:</h6>
                        <ul class="mb-0">
                            <li><strong>مقبوضات من عملاء:</strong> سداد فواتير آجلة سابقة</li>
                            <li><strong>تعديلات إيجابية:</strong> إضافات أو تصحيحات</li>
                            <li><strong>مبيعات غير مسجلة:</strong> معاملات لم تُدخل في النظام</li>
                            <li><strong>أخطاء في الإدخال:</strong> مبالغ مدخلة بالخطأ</li>
                        </ul>
                    </div>

                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-trending-down"></i>
                        <strong>📉 يوجد عجز في المقبوضات!</strong>
                        <br>المقبوضات الفعلية أقل من مبيعات النظام بمقدار <strong>{{ "{:,.2f}".format(filter.details.variance.abs_difference) }} ريال</strong>
                        <br><strong>النسبة:</strong> -{{ "{:.2f}".format(filter.details.variance.abs_percentage) }}%

                        <hr class="my-2">
                        <h6><i class="fas fa-lightbulb text-warning"></i> الأسباب المحتملة للعجز:</h6>
                        <ul class="mb-0">
                            <li><strong>مبيعات آجلة:</strong> فواتير لم تُحصل نقداً بعد</li>
                            <li><strong>فواتير مرتجعات:</strong> إرجاع بضائع أو إلغاء فواتير</li>
                            <li><strong>خصومات ممنوحة:</strong> تخفيضات للعملاء</li>
                            <li><strong>أخطاء في العد:</strong> نقص في النقدي أو البنكي</li>
                            <li><strong>مصروفات:</strong> مبالغ مدفوعة من الصندوق</li>
                        </ul>
                    </div>
                    {% endif %}

                    <!-- Severity Warning -->
                    {% if filter.details.variance.severity_level == 'high' %}
                    <div class="alert alert-danger mt-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> الفارق كبير ({{ "{:.1f}".format(filter.details.variance.abs_percentage) }}%) ويحتاج مراجعة فورية!
                    </div>
                    {% elif filter.details.variance.severity_level == 'medium' %}
                    <div class="alert alert-info mt-2">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> الفارق مقبول ({{ "{:.1f}".format(filter.details.variance.abs_percentage) }}%) لكن يُنصح بالمراجعة.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Bank Transactions Detail -->
        {% if filter.details.bank_transactions %}
        <div class="print-break"></div>
        <div class="detail-table">
            <div class="section-header">
                <h4><i class="fas fa-university"></i> تفاصيل المعاملات البنكية ({{ filter.details.bank_transactions | length }} معاملة)</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-primary">
                        <tr>
                            <th>#</th>
                            <th>نوع البطاقة</th>
                            <th>آخر 4 أرقام</th>
                            <th>رقم المرجع</th>
                            <th>المبلغ</th>
                            <th>الوقت</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in filter.details.bank_transactions %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                <span class="badge badge-custom bg-primary">
                                    {{ transaction.card_type or 'غير محدد' }}
                                </span>
                            </td>
                            <td class="amount">****{{ transaction.last_four or '0000' }}</td>
                            <td class="amount">{{ transaction.reference or 'غير متاح' }}</td>
                            <td class="amount highlight-amount">{{ "{:,.2f}".format(transaction.amount or 0) }} ريال</td>
                            <td>{{ transaction.time or 'غير محدد' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-primary">
                        <tr>
                            <th colspan="4">إجمالي المقبوضات البنكية</th>
                            <th class="amount">{{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال</th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Cash Details -->
        {% if filter.details.cash_details %}
        <div class="detail-table">
            <div class="section-header">
                <h4><i class="fas fa-coins"></i> تفاصيل المقبوضات النقدية</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-success">
                        <tr>
                            <th>فئة الورقة النقدية</th>
                            <th class="text-center">العدد</th>
                            <th class="text-center">المبلغ الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for denomination, details in filter.details.cash_details.items() %}
                        <tr>
                            <td><strong>{{ denomination }} ريال</strong></td>
                            <td class="text-center amount">{{ details.count or 0 }}</td>
                            <td class="amount text-center highlight-amount">{{ "{:,.2f}".format(details.total or 0) }} ريال</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot class="table-success">
                        <tr>
                            <th>إجمالي المقبوضات النقدية</th>
                            <th class="text-center">{{ filter.details.cash_details.values() | sum(attribute='count') or 0 }}</th>
                            <th class="amount text-center">{{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Credit Sales Details -->
        {% if filter.details.credit_total and filter.details.credit_total > 0 %}
        <div class="print-break"></div>
        <div class="detail-table">
            <div class="section-header" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                <h4><i class="fas fa-clock"></i> تفاصيل المبيعات الآجلة ({{ filter.details.credit_details | length if filter.details.credit_details else 1 }} فاتورة)</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-warning">
                        <tr>
                            <th>#</th>
                            <th>رقم الفاتورة</th>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>المبلغ</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if filter.details.credit_details %}
                        {% for credit in filter.details.credit_details %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td><span class="badge bg-warning text-dark">{{ credit.invoice_number or 'غير محدد' }}</span></td>
                            <td><strong class="text-primary">{{ credit.customer_name or 'عميل غير محدد' }}</strong></td>
                            <td>{{ credit.phone or 'غير متاح' }}</td>
                            <td class="amount highlight-amount">{{ "{:,.2f}".format(credit.amount or 0) }} ريال</td>
                            <td>{{ credit.due_date or 'غير محدد' }}</td>
                            <td>{{ credit.notes or '-' }}</td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i>
                                إجمالي المبيعات الآجلة: <strong>{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</strong>
                                <br><small>لا توجد تفاصيل العملاء متاحة</small>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                    <tfoot class="table-warning">
                        <tr>
                            <th colspan="4">إجمالي المبيعات الآجلة</th>
                            <th class="amount">{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</th>
                            <th colspan="2">{{ filter.details.credit_details | length if filter.details.credit_details else 0 }} عميل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Client Collections Details -->
        {% if filter.details.client_total and filter.details.client_total > 0 %}
        <div class="detail-table">
            <div class="section-header" style="background: linear-gradient(135deg, #17a2b8, #138496);">
                <h4><i class="fas fa-users"></i> تفاصيل المقبوضات من العملاء ({{ filter.details.client_details | length if filter.details.client_details else 1 }} معاملة)</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-info">
                        <tr>
                            <th>#</th>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>رقم الفاتورة المسددة</th>
                            <th>المبلغ المسدد</th>
                            <th>طريقة الدفع</th>
                            <th>رقم المرجع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if filter.details.client_details %}
                        {% for client in filter.details.client_details %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td><strong class="text-success">{{ client.customer_name or 'عميل غير محدد' }}</strong></td>
                            <td>{{ client.phone or 'غير متاح' }}</td>
                            <td><span class="badge bg-info">{{ client.invoice_number or 'غير محدد' }}</span></td>
                            <td class="amount highlight-amount">{{ "{:,.2f}".format(client.amount or 0) }} ريال</td>
                            <td>
                                {% if client.payment_method == 'نقدي' %}
                                <span class="badge bg-success"><i class="fas fa-money-bill"></i> نقدي</span>
                                {% elif client.payment_method == 'شبكة' %}
                                <span class="badge bg-primary"><i class="fas fa-credit-card"></i> شبكة</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ client.payment_method or 'غير محدد' }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if client.ref %}
                                <span class="badge bg-info">{{ client.ref }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ client.notes or '-' }}</td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i>
                                إجمالي المقبوضات من العملاء: <strong>{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</strong>
                                <br><small>لا توجد تفاصيل العملاء متاحة</small>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                    <tfoot class="table-info">
                        <tr>
                            <th colspan="4">إجمالي المقبوضات من العملاء</th>
                            <th class="amount">{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</th>
                            <th colspan="3">{{ filter.details.client_details | length if filter.details.client_details else 0 }} عميل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Return Details -->
        {% if filter.details.return_total and filter.details.return_total > 0 %}
        <div class="detail-table">
            <div class="section-header bg-danger">
                <h4><i class="fas fa-undo"></i> تفاصيل فواتير المرتجعات ({{ filter.details.return_details | length if filter.details.return_details else 1 }} فاتورة)</h4>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-danger">
                        <tr>
                            <th>#</th>
                            <th>رقم فاتورة الإرجاع</th>
                            <th>الفاتورة الأصلية</th>
                            <th>اسم العميل</th>
                            <th>سبب الإرجاع</th>
                            <th>المبلغ</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if filter.details.return_details %}
                        {% for return_item in filter.details.return_details %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td><span class="badge bg-danger">{{ return_item.return_invoice or 'غير محدد' }}</span></td>
                            <td><span class="badge bg-secondary">{{ return_item.original_invoice or 'غير محدد' }}</span></td>
                            <td><strong class="text-danger">{{ return_item.customer_name or 'عميل غير محدد' }}</strong></td>
                            <td>{{ return_item.reason or 'غير محدد' }}</td>
                            <td class="amount text-danger">{{ "{:,.2f}".format(return_item.amount or 0) }} ريال</td>
                            <td>{{ return_item.date or 'غير محدد' }}</td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">
                                <i class="fas fa-exclamation-triangle"></i>
                                إجمالي فواتير المرتجعات: <strong class="text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</strong>
                                <br><small>لا توجد تفاصيل العملاء متاحة</small>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                    <tfoot class="table-danger">
                        <tr>
                            <th colspan="5">إجمالي المرتجعات</th>
                            <th class="amount text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</th>
                            <th>{{ filter.details.return_details | length if filter.details.return_details else 0 }} عميل</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Suppliers Details (For Tracking Only) -->
        {% if filter.details.suppliers_total and filter.details.suppliers_total > 0 %}
        <div class="detail-table">
            <div class="section-header" style="background: linear-gradient(135deg, #795548, #5d4037);">
                <h4><i class="fas fa-industry"></i> تفاصيل الموردين - للمتابعة فقط ({{ filter.details.suppliers_details | length if filter.details.suppliers_details else 1 }} مورد)</h4>
                <small class="text-warning"><i class="fas fa-exclamation-triangle"></i> لا يؤثر على حسابات التصفية</small>
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-warning">
                        <tr>
                            <th>#</th>
                            <th>اسم المورد</th>
                            <th>المبلغ المسلم</th>
                            <th>طريقة الدفع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if filter.details.suppliers_details %}
                        {% for supplier in filter.details.suppliers_details %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td><strong class="text-brown">{{ supplier.supplier_name or 'مورد غير محدد' }}</strong></td>
                            <td class="amount highlight-amount">{{ "{:,.2f}".format(supplier.amount or 0) }} ريال</td>
                            <td>
                                {% if supplier.payment_method == 'نقدي' %}
                                <span class="badge bg-success"><i class="fas fa-money-bill"></i> نقدي</span>
                                {% elif supplier.payment_method == 'شيك' %}
                                <span class="badge bg-warning"><i class="fas fa-money-check"></i> شيك</span>
                                {% elif supplier.payment_method == 'تحويل بنكي' %}
                                <span class="badge bg-primary"><i class="fas fa-university"></i> تحويل بنكي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ supplier.payment_method or 'غير محدد' }}</span>
                                {% endif %}
                            </td>
                            <td>{{ supplier.notes or '-' }}</td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                <i class="fas fa-info-circle"></i>
                                إجمالي المدفوعات للموردين: <strong>{{ "{:,.2f}".format(filter.details.suppliers_total or 0) }} ريال</strong>
                                <br><small>لا توجد تفاصيل الموردين متاحة</small>
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                    <tfoot class="table-warning">
                        <tr>
                            <th colspan="2">إجمالي المدفوعات للموردين</th>
                            <th class="amount">{{ "{:,.2f}".format(filter.details.suppliers_total or 0) }} ريال</th>
                            <th colspan="2">
                                <small class="text-muted">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    {{ filter.details.suppliers_details | length if filter.details.suppliers_details else 0 }} مورد - لا يؤثر على الحسابات
                                </small>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Print Actions -->
        <div class="row no-print mt-4 mb-5">
            <div class="col-12 text-center">
                <button onclick="window.print()" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
                <a href="/filter/{{ filter.id }}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-eye"></i> العرض العادي
                </a>
                <a href="/reports" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-arrow-right"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
