@echo off
chcp 65001 > nul
title تقرير العملاء الآجل - نظام تصفية الكاشير

echo ========================================
echo    📋 تقرير العملاء الآجل الشامل
echo ========================================
echo.

echo 🔄 جاري تشغيل خادم التقارير...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المتطلبات
echo 📦 التحقق من المتطلبات...
pip show flask > nul 2>&1
if errorlevel 1 (
    echo 🔧 تثبيت المتطلبات...
    pip install flask
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

REM تهيئة قاعدة البيانات
echo 🗄️ تهيئة قاعدة البيانات...
python db/init_db.py

REM إضافة بيانات عينة إذا لم تكن موجودة
echo 📊 التحقق من وجود بيانات العينة...
echo 1 | python test_credit_customers_data.py > nul 2>&1

REM تشغيل خادم الويب
echo ✅ تشغيل خادم التقارير...
echo.
echo ============================================================
echo 🌐 خادم تقارير العملاء الآجل
echo ============================================================
echo 🔗 الرابط المحلي: http://localhost:5000/credit-customers
echo 📱 واجهة الهاتف: http://localhost:5000/mobile
echo 🏠 الصفحة الرئيسية: http://localhost:5000/
echo ============================================================
echo 💡 للوصول من الهاتف:
echo    1. تأكد من أن الهاتف والكمبيوتر على نفس الشبكة
echo    2. استخدم عنوان IP الكمبيوتر بدلاً من localhost
echo ============================================================
echo ⏹️  للإيقاف: اضغط Ctrl+C
echo ============================================================
echo.

REM فتح المتصفح تلقائياً
start http://localhost:5000/credit-customers

REM تشغيل الخادم
python web_server.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل خادم التقارير
    pause
)
