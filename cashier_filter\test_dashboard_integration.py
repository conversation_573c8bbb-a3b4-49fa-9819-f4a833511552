#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تكامل حذف التقارير مع لوحة المعلومات التفاعلية
"""

import sys
import os
import customtkinter as ctk
import time
import threading

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_notifier():
    """اختبار نظام إشعارات لوحة المعلومات"""
    print("🧪 اختبار نظام إشعارات لوحة المعلومات...")
    
    try:
        from utils.dashboard_notifier import test_dashboard_notifier
        test_dashboard_notifier()
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الإشعارات: {e}")
        return False

def test_dashboard_window():
    """اختبار نافذة لوحة المعلومات مع الإشعارات"""
    print("\n🧪 اختبار نافذة لوحة المعلومات...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد النافذة
        from ui.dashboard import DashboardWindow
        print("✅ تم استيراد نافذة لوحة المعلومات بنجاح")
        
        # إنشاء النافذة
        dashboard = DashboardWindow()
        print("✅ تم إنشاء نافذة لوحة المعلومات بنجاح")
        
        # اختبار إرسال إشعار
        def send_test_notification():
            time.sleep(2)  # انتظار حتى تظهر النافذة
            try:
                from utils.dashboard_notifier import notify_filter_deleted
                
                # إرسال إشعار تجريبي
                notify_filter_deleted(999, {
                    "cashier_name": "كاشير تجريبي",
                    "admin_name": "مدير تجريبي",
                    "date": "2024-01-15",
                    "total_amount": 5000
                })
                print("✅ تم إرسال إشعار تجريبي")
                
            except Exception as e:
                print(f"❌ خطأ في إرسال الإشعار التجريبي: {e}")
        
        # إرسال إشعار في خيط منفصل
        threading.Thread(target=send_test_notification, daemon=True).start()
        
        # إغلاق تلقائي بعد 5 ثوان
        dashboard.after(5000, dashboard.destroy)
        
        print("🚀 تشغيل نافذة لوحة المعلومات...")
        dashboard.mainloop()
        
        print("✅ تم إغلاق نافذة لوحة المعلومات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة لوحة المعلومات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edit_filter_integration():
    """اختبار تكامل نافذة تعديل التصفية مع الإشعارات"""
    print("\n🧪 اختبار تكامل نافذة تعديل التصفية...")
    
    try:
        # اختبار استيراد النافذة
        from ui.edit_filter import EditFilterWindow
        print("✅ تم استيراد نافذة تعديل التصفية بنجاح")
        
        # اختبار استيراد دوال الإشعارات
        from utils.dashboard_notifier import notify_filter_deleted, notify_data_changed
        print("✅ تم استيراد دوال الإشعارات بنجاح")
        
        # اختبار إرسال إشعار
        notify_filter_deleted(123, {
            "cashier_name": "أحمد محمد",
            "admin_name": "المدير العام",
            "date": "2024-01-15",
            "total_amount": 7500
        })
        print("✅ تم اختبار إرسال إشعار حذف التصفية")
        
        notify_data_changed("filter_deleted", {
            "filter_id": 123,
            "impact": "dashboard_update_required"
        })
        print("✅ تم اختبار إرسال إشعار تغيير البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل نافذة تعديل التصفية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_integration():
    """اختبار التكامل الكامل"""
    print("\n🧪 اختبار التكامل الكامل...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # إنشاء نافذة لوحة المعلومات
        from ui.dashboard import DashboardWindow
        dashboard = DashboardWindow()
        print("✅ تم إنشاء نافذة لوحة المعلومات")
        
        # محاكاة حذف تصفية
        def simulate_filter_deletion():
            time.sleep(2)
            try:
                from utils.dashboard_notifier import notify_filter_deleted, notify_data_changed
                
                # محاكاة حذف تصفية
                filter_data = {
                    "filter_id": 456,
                    "cashier_name": "فاطمة أحمد",
                    "admin_name": "المدير العام",
                    "date": "2024-01-16",
                    "total_amount": 12500,
                    "bank_amount": 8000,
                    "cash_amount": 4500
                }
                
                notify_filter_deleted(456, filter_data)
                notify_data_changed("filter_deleted", {
                    "filter_id": 456,
                    "impact": "dashboard_update_required",
                    "affected_metrics": ["total_filters", "revenue", "cashier_performance"]
                })
                
                print("✅ تم محاكاة حذف التصفية وإرسال الإشعارات")
                
            except Exception as e:
                print(f"❌ خطأ في محاكاة حذف التصفية: {e}")
        
        # تشغيل المحاكاة في خيط منفصل
        threading.Thread(target=simulate_filter_deletion, daemon=True).start()
        
        # إغلاق تلقائي بعد 7 ثوان
        dashboard.after(7000, dashboard.destroy)
        
        print("🚀 تشغيل اختبار التكامل الكامل...")
        dashboard.mainloop()
        
        print("✅ اكتمل اختبار التكامل الكامل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل الكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار تكامل حذف التقارير مع لوحة المعلومات التفاعلية")
    print("=" * 80)
    
    tests = [
        ("نظام إشعارات لوحة المعلومات", test_dashboard_notifier),
        ("تكامل نافذة تعديل التصفية", test_edit_filter_integration),
        ("نافذة لوحة المعلومات", test_dashboard_window),
        ("التكامل الكامل", test_full_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 60)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ نجح اختبار: {test_name}")
            else:
                print(f"❌ فشل اختبار: {test_name}")
                
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
        
        # فترة انتظار بين الاختبارات
        time.sleep(1)
    
    # تقرير النتائج النهائية
    print("\n" + "=" * 80)
    print("📊 تقرير النتائج النهائية")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print("-" * 80)
    print(f"📈 إجمالي الاختبارات: {len(results)}")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📊 معدل النجاح: {(passed/len(results)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 تهانينا! جميع الاختبارات نجحت!")
        print("🚀 نظام ربط حذف التقارير مع لوحة المعلومات يعمل بشكل مثالي!")
    else:
        print(f"\n⚠️ يوجد {failed} اختبار فاشل يحتاج إلى مراجعة")
    
    print("\n© 2025 محمد الكامل - نظام تصفية الكاشير")
    print("=" * 80)

if __name__ == "__main__":
    try:
        run_all_tests()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n💥 خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 شكراً لاستخدام نظام تصفية الكاشير 2025!")
