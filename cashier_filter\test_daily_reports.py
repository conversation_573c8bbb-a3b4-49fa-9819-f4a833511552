#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة التقارير اليومية
Test Daily Reports Window
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_daily_reports_import():
    """اختبار استيراد نافذة التقارير اليومية"""
    print("🧪 اختبار استيراد نافذة التقارير اليومية...")
    
    try:
        from ui.daily_reports import DailyReportsWindow
        print("✅ تم استيراد نافذة التقارير اليومية بنجاح")
        
        # فحص الدوال المطلوبة
        required_methods = [
            'create_widgets',
            'create_date_selection_frame',
            'create_daily_stats_frame',
            'create_tables_frame',
            'load_daily_data',
            'load_daily_statistics',
            'load_daily_filters',
            'load_user_activity',
            'set_quick_date',
            'export_report'
        ]
        
        available_methods = []
        for method in required_methods:
            if hasattr(DailyReportsWindow, method):
                available_methods.append(method)
        
        print(f"✅ الدوال المتاحة: {len(available_methods)}/{len(required_methods)}")
        for method in available_methods:
            print(f"   ✅ {method}")
        
        missing_methods = set(required_methods) - set(available_methods)
        if missing_methods:
            print("❌ الدوال المفقودة:")
            for method in missing_methods:
                print(f"   ❌ {method}")
        
        return len(available_methods) == len(required_methods)
        
    except Exception as e:
        print(f"❌ خطأ في استيراد نافذة التقارير اليومية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_daily_reports():
    """اختبار دالة التقارير اليومية في الواجهة الرئيسية"""
    print("\n🧪 اختبار دالة التقارير اليومية في الواجهة الرئيسية...")
    
    try:
        from ui.main_window import MainWindow
        
        # فحص وجود الدالة
        if hasattr(MainWindow, 'show_daily_reports'):
            print("✅ دالة show_daily_reports موجودة")
            
            # فحص محتوى الدالة
            import inspect
            source = inspect.getsource(MainWindow.show_daily_reports)
            
            if 'DailyReportsWindow' in source:
                print("✅ الدالة تستدعي DailyReportsWindow")
            elif 'ReportsWindow' in source:
                print("✅ الدالة تستدعي ReportsWindow كبديل")
            else:
                print("❌ الدالة لا تستدعي نافذة التقارير الصحيحة")
                return False
            
            if 'DailyFilterWindow' in source:
                print("❌ الدالة ما زالت تستدعي DailyFilterWindow (خطأ)")
                return False
            else:
                print("✅ الدالة لا تستدعي DailyFilterWindow")
            
            return True
        else:
            print("❌ دالة show_daily_reports غير موجودة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة الرئيسية: {e}")
        return False

def test_database_tables():
    """اختبار وجود جداول قاعدة البيانات"""
    print("\n🧪 اختبار جداول قاعدة البيانات...")
    
    try:
        import sqlite3
        
        # تحديد مسار قاعدة البيانات
        if os.path.exists("db/cashier_filter.db"):
            db_path = "db/cashier_filter.db"
        else:
            db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
        
        if not os.path.exists(db_path):
            print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها عند الحاجة")
            return True
        
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # فحص الجداول المطلوبة
        required_tables = ['filters', 'operations_log']
        existing_tables = []
        
        for table in required_tables:
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if c.fetchone():
                existing_tables.append(table)
                print(f"✅ جدول {table} موجود")
            else:
                print(f"⚠️ جدول {table} غير موجود")
        
        # فحص بيانات العينة
        if 'operations_log' in existing_tables:
            c.execute("SELECT COUNT(*) FROM operations_log")
            count = c.fetchone()[0]
            print(f"✅ عدد العمليات المسجلة: {count}")
        
        if 'filters' in existing_tables:
            c.execute("SELECT COUNT(*) FROM filters")
            count = c.fetchone()[0]
            print(f"✅ عدد التصفيات: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def create_sample_data():
    """إنشاء بيانات عينة للاختبار"""
    print("\n🧪 إنشاء بيانات عينة للاختبار...")
    
    try:
        import sqlite3
        from datetime import datetime
        
        # تحديد مسار قاعدة البيانات
        if os.path.exists("db/cashier_filter.db"):
            db_path = "db/cashier_filter.db"
        else:
            db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # إنشاء جدول التصفيات إذا لم يكن موجوداً
        c.execute('''
            CREATE TABLE IF NOT EXISTS filters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cashier_name TEXT,
                total_amount REAL,
                status TEXT DEFAULT 'completed',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # إضافة بيانات عينة للتصفيات
        today = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        sample_filters = [
            ('كاشير 1', 1500.50, 'completed', today),
            ('كاشير 2', 2300.75, 'completed', today),
            ('كاشير 3', 980.25, 'completed', today),
        ]
        
        for cashier, amount, status, created in sample_filters:
            c.execute('''
                INSERT OR IGNORE INTO filters (cashier_name, total_amount, status, created_at)
                VALUES (?, ?, ?, ?)
            ''', (cashier, amount, status, created))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء بيانات العينة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء بيانات العينة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نافذة التقارير اليومية")
    print("=" * 50)
    
    # تشغيل الاختبارات
    tests = [
        ("استيراد نافذة التقارير اليومية", test_daily_reports_import),
        ("دالة التقارير اليومية في الواجهة الرئيسية", test_main_window_daily_reports),
        ("جداول قاعدة البيانات", test_database_tables),
        ("إنشاء بيانات العينة", create_sample_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print(f"\n📊 نتائج الاختبارات:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 الإجمالي: {success_count}/{total_count} اختبار نجح")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! زر التقرير اليومي جاهز!")
        print("\n🚀 الآن يمكنك:")
        print("   • الضغط على زر '📈 تقرير يومي' في الواجهة الرئيسية")
        print("   • عرض التقارير اليومية بدلاً من بدء تصفية جديدة")
        print("   • اختيار تواريخ مختلفة (اليوم، أمس، آخر 7 أيام، آخر 30 يوم)")
        print("   • عرض إحصائيات يومية شاملة")
        print("   • تصدير التقارير اليومية")
    else:
        print("⚠️ بعض الاختبارات فشلت، لكن الوظيفة قد تعمل جزئياً")

if __name__ == "__main__":
    main()
