@echo off
chcp 65001 > nul
title Global Access - Cashier Filter Reports

echo.
echo ========================================
echo Global Access - Cashier Filter Reports
echo ========================================
echo.

cd /d "%~dp0"

echo Checking local server...
curl -s http://localhost:5000 > nul 2>&1
if errorlevel 1 (
    echo Starting local server...
    start "Reports Server" /min python web_server.py
    echo Waiting for server to start...
    timeout /t 5 > nul
)

echo Local server is ready

echo.
echo Downloading Cloudflare Tunnel...
if not exist "cloudflared.exe" (
    echo Downloading cloudflared.exe...
    curl -L -o cloudflared.exe https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe
    if errorlevel 1 (
        echo Download failed, try manual method:
        echo 1. Go to: https://github.com/cloudflare/cloudflared/releases
        echo 2. Download cloudflared-windows-amd64.exe
        echo 3. Place it in the project folder
        pause
        exit /b 1
    )
    echo Download completed successfully
) else (
    echo cloudflared.exe already exists
)

echo.
echo Starting global tunnel...
echo ========================================
echo Global URL will appear below:
echo.

cloudflared.exe tunnel --url http://localhost:5000

echo.
echo Tunnel ended
pause
