
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير تصفية الكاشير</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Tajawal', Arial, sans-serif;
                background: #f8f9fa;
                color: #2c3e50;
                line-height: 1.6;
                padding: 20px;
            }

            .container {
                max-width: 900px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }

            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                font-weight: 700;
            }

            .header .subtitle {
                font-size: 1.2em;
                opacity: 0.9;
            }

            .info-section {
                background: #ecf0f1;
                padding: 25px;
                border-bottom: 3px solid #3498db;
            }

            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 15px;
            }

            .info-item {
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .info-label {
                font-weight: bold;
                color: #34495e;
                margin-bottom: 5px;
            }

            .info-value {
                color: #2c3e50;
                font-size: 1.1em;
            }

            .section {
                padding: 25px;
                border-bottom: 1px solid #ecf0f1;
            }

            .section:last-child {
                border-bottom: none;
            }

            .section-title {
                background: linear-gradient(45deg, #3498db, #2980b9);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                font-size: 1.3em;
                font-weight: bold;
                display: inline-block;
            }

            .table-container {
                overflow-x: auto;
                margin: 15px 0;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            }

            th {
                background: #34495e;
                color: white;
                padding: 15px 12px;
                text-align: center;
                font-weight: bold;
            }

            td {
                padding: 12px;
                text-align: center;
                border-bottom: 1px solid #ecf0f1;
            }

            tr:nth-child(even) {
                background: #f8f9fa;
            }

            tr:hover {
                background: #e3f2fd;
            }

            .cash-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
                margin: 20px 0;
            }

            .cash-item {
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                text-align: center;
            }

            .cash-denomination {
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 8px;
            }

            .cash-count {
                font-size: 1.2em;
                color: #27ae60;
            }

            .summary-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }

            .summary-title {
                font-size: 2em;
                margin-bottom: 25px;
                font-weight: bold;
            }

            .summary-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                margin-bottom: 25px;
            }

            .summary-item {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                backdrop-filter: blur(10px);
            }

            .summary-label {
                font-size: 1.1em;
                margin-bottom: 8px;
                opacity: 0.9;
            }

            .summary-value {
                font-size: 1.5em;
                font-weight: bold;
            }

            .final-result {
                background: rgba(255,255,255,0.2);
                padding: 25px;
                border-radius: 15px;
                margin-top: 20px;
            }

            .difference {
                font-size: 2.2em;
                font-weight: bold;
                margin-bottom: 10px;
            }

            .status {
                font-size: 1.4em;
                font-weight: bold;
            }

            .status.surplus {
                color: #2ecc71;
            }

            .status.deficit {
                color: #e74c3c;
            }

            .status.balanced {
                color: #f39c12;
            }

            .footer {
                background: #2c3e50;
                color: white;
                padding: 20px;
                text-align: center;
            }

            @media print {
                body {
                    background: white;
                    padding: 0;
                }

                .container {
                    box-shadow: none;
                    border-radius: 0;
                }

                .section {
                    page-break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📊 تقرير تصفية الكاشير</h1>
                <div class="subtitle">نظام إدارة التصفية اليومية</div>
            </div>

            <div class="info-section">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">👤 اسم الكاشير:</div>
                        <div class="info-value">جلال</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">🆔 رقم الكاشير:</div>
                        <div class="info-value">101</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">🧑‍💼 المسؤول:</div>
                        <div class="info-value">محمد الكامل</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">📅 تاريخ التصفية:</div>
                        <div class="info-value">2025-07-04</div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">🕐 وقت إنشاء التقرير:</div>
                    <div class="info-value">2025-07-05 00:36:04</div>
                </div>
            </div>
    
            <div class="summary-section">
                <div class="summary-title">📊 ملخص التصفية</div>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">إجمالي المقبوضات</div>
                        <div class="summary-value">4182.87 ريال</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">مبيعات النظام</div>
                        <div class="summary-value">4174.97 ريال</div>
                    </div>
                </div>
                <div class="final-result">
                    <div class="difference">7.90 ريال</div>
                    <div class="status surplus">✅ فائض</div>
                </div>
            </div>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير</p>
                <p>© 2024 - جميع الحقوق محفوظة</p>
            </div>
        </div>

        <script>
            window.onload = function() {
                // إضافة تأخير قصير للتأكد من تحميل الخطوط
                setTimeout(function() {
                    window.print();
                }, 1000);
            };
        </script>
    </body>
    </html>
    