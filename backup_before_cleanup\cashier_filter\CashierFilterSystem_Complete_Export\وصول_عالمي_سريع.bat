@echo off
chcp 65001 > nul
title الوصول العالمي لتقارير نظام تصفية الكاشير

echo.
echo ========================================
echo 🌍 الوصول العالمي للتقارير
echo ========================================
echo.

cd /d "%~dp0"

echo 🔍 التحقق من تشغيل الخادم المحلي...
curl -s http://localhost:5000 > nul 2>&1
if errorlevel 1 (
    echo ❌ الخادم المحلي لا يعمل
    echo 🚀 بدء تشغيل الخادم...
    start "خادم التقارير" python web_server.py
    echo ⏳ انتظار 5 ثوان لبدء الخادم...
    timeout /t 5 > nul
)

echo ✅ الخادم المحلي يعمل

echo.
echo 🌐 اختر طريقة الوصول العالمي:
echo ========================================
echo 1. ngrok (الأسرع - يحتاج تسجيل مجاني)
echo 2. Cloudflare Tunnel (مجاني تماماً)
echo 3. LocalTunnel (مجاني - يحتاج Node.js)
echo 4. Serveo (مجاني - SSH)
echo ========================================
echo.

set /p choice="اختر الرقم (1-4): "

if "%choice%"=="1" goto ngrok
if "%choice%"=="2" goto cloudflare
if "%choice%"=="3" goto localtunnel
if "%choice%"=="4" goto serveo

echo ❌ اختيار غير صحيح
pause
exit /b 1

:ngrok
echo.
echo 🔗 إعداد ngrok...
echo ========================================
echo 📋 خطوات التثبيت:
echo 1. اذهب إلى: https://ngrok.com/download
echo 2. سجل حساب مجاني
echo 3. حمل ngrok.exe
echo 4. ضعه في مجلد المشروع
echo 5. شغل: ngrok config add-authtoken YOUR_TOKEN
echo.
echo 🚀 بدء النفق...

if not exist "ngrok.exe" (
    echo ❌ ngrok.exe غير موجود
    echo 📥 تحميل ngrok...
    curl -L -o ngrok.zip https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-windows-amd64.zip
    if exist ngrok.zip (
        echo 📦 فك الضغط...
        powershell -command "Expand-Archive -Path ngrok.zip -DestinationPath . -Force"
        del ngrok.zip
    )
)

if exist "ngrok.exe" (
    echo ✅ بدء نفق ngrok...
    ngrok.exe http 5000
) else (
    echo ❌ فشل في تحميل ngrok
    echo يرجى التحميل يدوياً من: https://ngrok.com/download
)
goto end

:cloudflare
echo.
echo ☁️ إعداد Cloudflare Tunnel...
echo ========================================

if not exist "cloudflared.exe" (
    echo 📥 تحميل cloudflared...
    curl -L -o cloudflared.exe https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe
)

if exist "cloudflared.exe" (
    echo ✅ بدء نفق Cloudflare...
    echo 🔗 الرابط العالمي سيظهر أدناه:
    echo.
    cloudflared.exe tunnel --url http://localhost:5000
) else (
    echo ❌ فشل في تحميل cloudflared
)
goto end

:localtunnel
echo.
echo 🌐 إعداد LocalTunnel...
echo ========================================

echo 🔍 التحقق من Node.js...
node --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تثبيت Node.js من: https://nodejs.org
    echo ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)

echo ✅ Node.js متاح
echo 📦 تثبيت localtunnel...
npm install -g localtunnel

echo ✅ بدء نفق LocalTunnel...
echo 🔗 الرابط العالمي سيظهر أدناه:
echo.
lt --port 5000 --subdomain cashier-reports-%random%
goto end

:serveo
echo.
echo 🔐 إعداد Serveo (SSH Tunnel)...
echo ========================================

echo 🔍 التحقق من SSH...
ssh -V > nul 2>&1
if errorlevel 1 (
    echo ❌ SSH غير متاح
    echo 📥 يرجى تفعيل OpenSSH في Windows
    pause
    exit /b 1
)

echo ✅ SSH متاح
echo 🚀 بدء نفق Serveo...
echo 🔗 الرابط العالمي سيظهر أدناه:
echo.
ssh -R 80:localhost:5000 serveo.net
goto end

:end
echo.
echo 📱 للاستخدام من الهاتف:
echo   - انسخ الرابط المعروض أعلاه
echo   - افتحه في متصفح الهاتف
echo   - أضف /mobile في نهاية الرابط للواجهة المحسنة
echo.
echo 🔒 تذكير أمني:
echo   - لا تشارك الرابط مع غير الموثوقين
echo   - أغلق النفق عند الانتهاء
echo.
pause
