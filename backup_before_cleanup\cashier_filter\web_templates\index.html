{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام تصفية الكاشير{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <h1 class="card-title">
                    <i class="fas fa-chart-line text-primary"></i>
                    مرحباً بك في نظام تقارير تصفية الكاشير
                </h1>
                <p class="card-text lead">
                    اطلع على تقارير التصفية من أي مكان ومن هاتفك بسهولة
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <i class="fas fa-file-alt fa-2x mb-2"></i>
            <h3>{{ stats.total_filters or 0 }}</h3>
            <p>إجمالي التصفيات</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(45deg, #2196F3, #1976D2);">
            <i class="fas fa-calendar-month fa-2x mb-2"></i>
            <h3>{{ stats.monthly_filters or 0 }}</h3>
            <p>تصفيات هذا الشهر</p>
        </div>
    </div>
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(45deg, #FF9800, #F57C00);">
            <i class="fas fa-coins fa-2x mb-2"></i>
            <h3>{{ "{:,.2f}".format(stats.total_amount or 0) }} ريال</h3>
            <p>إجمالي المبالغ</p>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="/reports" class="btn btn-primary">
                            <i class="fas fa-list"></i> جميع التقارير
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/mobile" class="btn btn-success">
                            <i class="fas fa-mobile-alt"></i> واجهة الهاتف
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-info" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> تحديث البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning" onclick="exportData()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Filters -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clock"></i> آخر التصفيات</h5>
                <a href="/reports" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if filters %}
                    <div id="filters-container">
                        {% for filter in filters %}
                        <div class="filter-card card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="cashier-name mb-1">
                                            <i class="fas fa-user"></i>
                                            {{ filter.cashier_name or 'غير محدد' }}
                                        </h6>
                                        <small class="date">
                                            <i class="fas fa-calendar"></i>
                                            {{ filter.date }}
                                        </small>
                                        {% if filter.details.variance %}
                                        <div class="mt-1">
                                            <span class="badge bg-{% if filter.details.variance.status_type == 'balanced' %}success{% elif filter.details.variance.status_type == 'surplus' %}info{% else %}warning{% endif %}">
                                                {{ filter.details.variance.status_icon }} {{ filter.details.variance.status }}
                                            </span>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <p class="mb-1">
                                            <strong>المسؤول:</strong> {{ filter.admin_name or 'غير محدد' }}
                                        </p>
                                        {% if filter.sequence_number %}
                                        <small class="text-muted">
                                            رقم التسلسل: {{ filter.sequence_number }}
                                        </small>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        {% if filter.details %}
                                        <p class="amount mb-1">
                                            <i class="fas fa-credit-card"></i>
                                            بنكي: {{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال
                                        </p>
                                        <p class="amount mb-1">
                                            <i class="fas fa-money-bill"></i>
                                            نقدي: {{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال
                                        </p>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <div class="btn-group" role="group">
                                            <a href="/filter/{{ filter.id }}" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="/filter/{{ filter.id }}/comprehensive" class="btn btn-sm btn-success" title="التقرير الشامل">
                                                <i class="fas fa-file-invoice-dollar"></i> شامل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تصفيات متاحة</h5>
                        <p class="text-muted">ابدأ بإنشاء تصفية جديدة من التطبيق الرئيسي</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Last Update Info -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <small class="text-muted">
                    <i class="fas fa-sync-alt"></i>
                    آخر تحديث: {{ stats.last_update or 'غير متاح' }}
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث البيانات
    function refreshData() {
        showLoading(document.getElementById('filters-container'));
        
        fetch('/api/filters?limit=10')
            .then(response => response.json())
            .then(data => {
                updateFiltersDisplay(data);
            })
            .catch(error => {
                console.error('خطأ في تحديث البيانات:', error);
                alert('حدث خطأ في تحديث البيانات');
            });
    }
    
    // تحديث عرض التصفيات
    function updateFiltersDisplay(filters) {
        const container = document.getElementById('filters-container');
        
        if (filters.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تصفيات متاحة</h5>
                </div>
            `;
            return;
        }
        
        let html = '';
        filters.forEach(filter => {
            const details = filter.details || {};
            html += `
                <div class="filter-card card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="cashier-name mb-1">
                                    <i class="fas fa-user"></i>
                                    ${filter.cashier_name || 'غير محدد'}
                                </h6>
                                <small class="date">
                                    <i class="fas fa-calendar"></i>
                                    ${filter.date}
                                </small>
                                ${filter.details.variance ? `
                                <div class="mt-1">
                                    <span class="badge bg-${filter.details.variance.status_type === 'balanced' ? 'success' : filter.details.variance.status_type === 'surplus' ? 'info' : 'warning'}">
                                        ${filter.details.variance.status_icon} ${filter.details.variance.status}
                                    </span>
                                </div>
                                ` : ''}
                            </div>
                            <div class="col-md-3">
                                <p class="mb-1">
                                    <strong>المسؤول:</strong> ${filter.admin_name || 'غير محدد'}
                                </p>
                                ${filter.sequence_number ? `<small class="text-muted">رقم التسلسل: ${filter.sequence_number}</small>` : ''}
                            </div>
                            <div class="col-md-3">
                                <p class="amount mb-1">
                                    <i class="fas fa-credit-card"></i>
                                    بنكي: ${formatArabicNumber(details.bank_total || 0)} ريال
                                </p>
                                <p class="amount mb-1">
                                    <i class="fas fa-money-bill"></i>
                                    نقدي: ${formatArabicNumber(details.cash_total || 0)} ريال
                                </p>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group" role="group">
                                    <a href="/filter/${filter.id}" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="/filter/${filter.id}/comprehensive" class="btn btn-sm btn-success" title="التقرير الشامل">
                                        <i class="fas fa-file-invoice-dollar"></i> شامل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // تصدير البيانات
    function exportData() {
        alert('ميزة التصدير ستكون متاحة قريباً');
    }
</script>
{% endblock %}
