@echo off
chcp 65001 > nul
title نظام تصفية الكاشير v3.5.0 - الإصدار الكامل
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🏪 نظام تصفية الكاشير v3.5.0 - الإصدار الكامل        ██
echo ██                                                            ██
echo ██  🎉 جميع الميزات الجديدة:                                  ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء في التقارير                            ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██  📊 التقرير الشامل المحسن                                ██
echo ██  🌐 الوصول العالمي                                       ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
echo 🔍 التحقق من متطلبات النظام...
python --version > nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Python غير مثبت على هذا الكمبيوتر!
    echo.
    echo 💡 يرجى تثبيت Python أولاً:
    echo    1. اذهب إلى: https://python.org/downloads
    echo    2. حمل Python 3.9 أو أحدث
    echo    3. ثبته مع تفعيل "Add Python to PATH"
    echo    4. أعد تشغيل الكمبيوتر
    echo    5. شغل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    الإصدار: %PYTHON_VERSION%

REM التحقق من المتطلبات وتثبيتها
echo.
echo 📦 التحقق من المتطلبات...

if exist requirements_complete.txt (
    echo    تثبيت المتطلبات من requirements_complete.txt...
    pip install -r requirements_complete.txt --quiet --disable-pip-version-check
    if errorlevel 1 (
        echo    ⚠️ فشل في تثبيت بعض المتطلبات، محاولة التثبيت الأساسي...
        pip install customtkinter flask requests pandas fpdf2 Pillow --quiet --disable-pip-version-check
    )
) else if exist requirements.txt (
    echo    تثبيت المتطلبات من requirements.txt...
    pip install -r requirements.txt --quiet --disable-pip-version-check
) else (
    echo    تثبيت المتطلبات الأساسية...
    pip install customtkinter flask requests pandas fpdf2 Pillow --quiet --disable-pip-version-check
)

echo ✅ تم تثبيت المتطلبات

REM التحقق من قاعدة البيانات
echo.
echo 🗄️ التحقق من قاعدة البيانات...
if not exist "db" mkdir db
if not exist "db\cashier_filter.db" (
    echo    إنشاء قاعدة بيانات جديدة...
    python -c "from db.init_db import create_database; create_database('db/cashier_filter.db')" 2>nul
    if errorlevel 1 (
        echo    ⚠️ سيتم إنشاء قاعدة البيانات عند أول تشغيل
    ) else (
        echo    ✅ تم إنشاء قاعدة البيانات
    )
) else (
    echo ✅ قاعدة البيانات موجودة
)

REM إنشاء المجلدات المطلوبة
echo.
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
echo ✅ تم إنشاء المجلدات

echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🎯 بيانات تسجيل الدخول الافتراضية:                        │
echo │                                                             │
echo │     👤 اسم المستخدم: admin                                │
echo │     🔐 كلمة المرور: 123456                                │
echo │                                                             │
echo │  💡 يمكنك تغيير كلمة المرور من الإعدادات                  │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM تشغيل النظام
python main.py

REM التحقق من حالة الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo.
    echo 🔍 خطوات استكشاف الأخطاء:
    echo    1. تأكد من تثبيت Python 3.8 أو أحدث
    echo    2. تأكد من تثبيت جميع المتطلبات
    echo    3. تحقق من وجود ملف main.py
    echo    4. راجع ملف logs/error.log إن وجد
    echo.
    echo 💡 للحصول على مساعدة:
    echo    - راجع INSTALLATION_GUIDE.md
    echo    - شغل test_enhanced_reports.py للاختبار
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo 👋 شكراً لاستخدام نظام تصفية الكاشير!
    echo.
)

echo 🔄 يمكنك تشغيل النظام مرة أخرى بالنقر على هذا الملف
pause
