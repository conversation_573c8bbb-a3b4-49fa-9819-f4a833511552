#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل قاعدة البيانات - نظام تصفية الكاشير
Database Issues Fix - Cashier Filter System
"""

import sqlite3
import json
from datetime import datetime

def fix_database_issues():
    """إصلاح جميع مشاكل قاعدة البيانات"""
    
    db_path = 'cashier_filter/db/cashier_filter.db'
    
    print("🔧 بدء إصلاح مشاكل قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. فحص السجلات المعطلة
        print("\n1️⃣ فحص السجلات المعطلة...")
        
        cursor.execute("SELECT id, admin_id, admin_name FROM filters WHERE admin_id IS NULL")
        broken_records = cursor.fetchall()
        
        print(f"   📊 عدد السجلات المعطلة: {len(broken_records)}")
        
        if broken_records:
            for record in broken_records:
                filter_id, admin_id, admin_name = record
                print(f"   🔍 التصفية رقم {filter_id}: admin_id={admin_id}, admin_name='{admin_name}'")
        
        # 2. إصلاح السجلات المعطلة
        if broken_records:
            print("\n2️⃣ إصلاح السجلات المعطلة...")
            
            # الحصول على قائمة المديرين المتاحين
            cursor.execute("SELECT id, name FROM admins")
            admins = cursor.fetchall()
            
            print(f"   👥 المديرين المتاحين:")
            for admin in admins:
                print(f"      - ID: {admin[0]}, الاسم: {admin[1]}")
            
            # إصلاح كل سجل معطل
            fixed_count = 0
            for record in broken_records:
                filter_id, admin_id, admin_name = record
                
                # محاولة العثور على المدير بالاسم
                matching_admin = None
                if admin_name:
                    for admin in admins:
                        if admin[1] == admin_name:
                            matching_admin = admin
                            break
                
                if matching_admin:
                    # إصلاح بربط المدير الصحيح
                    cursor.execute("""
                        UPDATE filters 
                        SET admin_id = ? 
                        WHERE id = ?
                    """, (matching_admin[0], filter_id))
                    
                    print(f"   ✅ تم إصلاح التصفية {filter_id}: ربط بالمدير {matching_admin[1]} (ID: {matching_admin[0]})")
                    fixed_count += 1
                    
                else:
                    # إذا لم نجد مطابقة، نربط بالمدير الافتراضي (أول مدير)
                    if admins:
                        default_admin = admins[0]
                        cursor.execute("""
                            UPDATE filters 
                            SET admin_id = ?, admin_name = ? 
                            WHERE id = ?
                        """, (default_admin[0], default_admin[1], filter_id))
                        
                        print(f"   ⚠️ تم إصلاح التصفية {filter_id}: ربط بالمدير الافتراضي {default_admin[1]} (ID: {default_admin[0]})")
                        fixed_count += 1
            
            print(f"   📊 تم إصلاح {fixed_count} سجل")
        
        # 3. فحص المراجع الخارجية المعطلة
        print("\n3️⃣ فحص المراجع الخارجية المعطلة...")
        
        # فحص مراجع الكاشير المعطلة
        cursor.execute("""
            SELECT f.id, f.cashier_id 
            FROM filters f 
            LEFT JOIN cashiers c ON f.cashier_id = c.id 
            WHERE f.cashier_id IS NOT NULL AND c.id IS NULL
        """)
        orphaned_cashier_refs = cursor.fetchall()
        
        print(f"   📊 مراجع كاشير معطلة: {len(orphaned_cashier_refs)}")
        
        if orphaned_cashier_refs:
            # الحصول على قائمة الكاشيرين المتاحين
            cursor.execute("SELECT id, name FROM cashiers")
            cashiers = cursor.fetchall()
            
            if cashiers:
                default_cashier = cashiers[0]
                for filter_id, broken_cashier_id in orphaned_cashier_refs:
                    cursor.execute("""
                        UPDATE filters 
                        SET cashier_id = ? 
                        WHERE id = ?
                    """, (default_cashier[0], filter_id))
                    
                    print(f"   ✅ تم إصلاح مرجع الكاشير للتصفية {filter_id}: ربط بـ {default_cashier[1]}")
        
        # فحص مراجع المدير المعطلة (بعد الإصلاح)
        cursor.execute("""
            SELECT f.id, f.admin_id 
            FROM filters f 
            LEFT JOIN admins a ON f.admin_id = a.id 
            WHERE f.admin_id IS NOT NULL AND a.id IS NULL
        """)
        orphaned_admin_refs = cursor.fetchall()
        
        print(f"   📊 مراجع مدير معطلة: {len(orphaned_admin_refs)}")
        
        # 4. فحص وإصلاح بيانات JSON
        print("\n4️⃣ فحص وإصلاح بيانات JSON...")
        
        cursor.execute("SELECT id, data FROM filters WHERE data IS NOT NULL AND data != ''")
        filters_data = cursor.fetchall()
        
        invalid_json_count = 0
        fixed_json_count = 0
        
        for filter_id, data in filters_data:
            try:
                json.loads(data)
            except json.JSONDecodeError as e:
                invalid_json_count += 1
                print(f"   ❌ JSON غير صحيح في التصفية {filter_id}: {e}")
                
                # محاولة إصلاح JSON البسيط
                try:
                    # إزالة الأحرف غير الصحيحة الشائعة
                    fixed_data = data.replace("'", '"').replace('True', 'true').replace('False', 'false').replace('None', 'null')
                    json.loads(fixed_data)  # اختبار الإصلاح
                    
                    cursor.execute("UPDATE filters SET data = ? WHERE id = ?", (fixed_data, filter_id))
                    print(f"   ✅ تم إصلاح JSON للتصفية {filter_id}")
                    fixed_json_count += 1
                    
                except:
                    # إذا فشل الإصلاح، نضع JSON فارغ
                    cursor.execute("UPDATE filters SET data = '{}' WHERE id = ?", (filter_id,))
                    print(f"   ⚠️ تم استبدال JSON للتصفية {filter_id} بـ JSON فارغ")
                    fixed_json_count += 1
        
        print(f"   📊 JSON غير صحيح: {invalid_json_count}")
        print(f"   📊 تم إصلاح: {fixed_json_count}")
        
        # 5. تحسين الفهارس
        print("\n5️⃣ تحسين الفهارس...")
        
        # إنشاء فهارس إضافية لتحسين الأداء
        indexes_to_create = [
            ("idx_filters_date", "CREATE INDEX IF NOT EXISTS idx_filters_date ON filters(date)"),
            ("idx_filters_cashier_date", "CREATE INDEX IF NOT EXISTS idx_filters_cashier_date ON filters(cashier_id, date)"),
            ("idx_filters_admin_date", "CREATE INDEX IF NOT EXISTS idx_filters_admin_date ON filters(admin_id, date)"),
            ("idx_operations_timestamp", "CREATE INDEX IF NOT EXISTS idx_operations_timestamp ON operations_log(timestamp)"),
        ]
        
        for index_name, create_sql in indexes_to_create:
            try:
                cursor.execute(create_sql)
                print(f"   ✅ تم إنشاء الفهرس: {index_name}")
            except Exception as e:
                print(f"   ⚠️ فشل في إنشاء الفهرس {index_name}: {e}")
        
        # 6. تنظيف البيانات
        print("\n6️⃣ تنظيف البيانات...")
        
        # إزالة السجلات الفارغة أو غير المكتملة
        cursor.execute("DELETE FROM filters WHERE date IS NULL OR date = ''")
        deleted_empty = cursor.rowcount
        print(f"   🗑️ تم حذف {deleted_empty} سجل فارغ")
        
        # تحديث إحصائيات قاعدة البيانات
        cursor.execute("ANALYZE")
        print("   📊 تم تحديث إحصائيات قاعدة البيانات")
        
        # حفظ التغييرات
        conn.commit()
        
        # 7. فحص نهائي
        print("\n7️⃣ فحص نهائي...")
        
        # إعادة فحص المشاكل
        cursor.execute("SELECT COUNT(*) FROM filters WHERE admin_id IS NULL")
        remaining_null_admin = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM filters WHERE cashier_id IS NULL")
        remaining_null_cashier = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM filters WHERE date IS NULL OR date = ''")
        remaining_null_date = cursor.fetchone()[0]
        
        print(f"   📊 المشاكل المتبقية:")
        print(f"      - تصفيات بدون مدير: {remaining_null_admin}")
        print(f"      - تصفيات بدون كاشير: {remaining_null_cashier}")
        print(f"      - تصفيات بدون تاريخ: {remaining_null_date}")
        
        conn.close()
        
        # النتيجة النهائية
        total_remaining_issues = remaining_null_admin + remaining_null_cashier + remaining_null_date
        
        if total_remaining_issues == 0:
            print("\n🎉 تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!")
            return True
        else:
            print(f"\n⚠️ تبقى {total_remaining_issues} مشكلة تحتاج مراجعة يدوية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def create_backup_before_fix():
    """إنشاء نسخة احتياطية قبل الإصلاح"""
    import shutil
    from datetime import datetime
    
    db_path = 'cashier_filter/db/cashier_filter.db'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f'cashier_filter/db/cashier_filter.db.backup_before_fix_{timestamp}'
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل قاعدة البيانات")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية
    if not create_backup_before_fix():
        print("❌ تم إلغاء العملية بسبب فشل النسخة الاحتياطية")
        return
    
    # إصلاح المشاكل
    success = fix_database_issues()
    
    if success:
        print("\n✅ تم إصلاح قاعدة البيانات بنجاح!")
        print("💡 يمكنك الآن تشغيل التطبيق بدون مشاكل")
    else:
        print("\n⚠️ تم إصلاح معظم المشاكل، لكن قد تحتاج مراجعة يدوية")

if __name__ == "__main__":
    main()
