#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل خادم التقارير المحدث مع التقرير الشامل
Start Updated Web Server with Comprehensive Report
"""

import sys
import os
from pathlib import Path
import subprocess
import time

def kill_existing_servers():
    """إيقاف الخوادم الموجودة"""
    try:
        # إيقاف العمليات على المنفذ 5000
        subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                      capture_output=True, text=True)
        time.sleep(2)
        print("✅ تم إيقاف الخوادم الموجودة")
    except:
        print("⚠️ لم يتم العثور على خوادم للإيقاف")

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'web_server.py',
        'web_templates/comprehensive_report.html',
        'web_templates/filter_detail.html',
        'web_templates/reports.html',
        'web_templates/index.html'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def start_server():
    """تشغيل الخادم"""
    try:
        print("🚀 بدء تشغيل خادم التقارير المحدث...")
        
        # تشغيل الخادم
        process = subprocess.Popen([
            sys.executable, 'web_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # انتظار قصير للتأكد من بدء التشغيل
        time.sleep(3)
        
        # التحقق من أن العملية لا تزال تعمل
        if process.poll() is None:
            print("✅ تم تشغيل الخادم بنجاح!")
            print("🔗 الروابط المتاحة:")
            print("   http://localhost:5000 - الصفحة الرئيسية")
            print("   http://localhost:5000/reports - التقارير")
            print("   http://localhost:5000/filter/[ID]/comprehensive - التقرير الشامل")
            print()
            print("📋 لاختبار التقرير الشامل:")
            print("   1. افتح http://localhost:5000")
            print("   2. انقر على زر 'شامل' بجانب أي تصفية")
            print("   3. أو اذهب مباشرة لـ http://localhost:5000/filter/22/comprehensive")
            print()
            print("⏹️ لإيقاف الخادم: اضغط Ctrl+C")
            
            # انتظار إنهاء العملية
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 تم إيقاف الخادم")
                process.terminate()
        else:
            print("❌ فشل في تشغيل الخادم")
            # قراءة رسائل الخطأ
            output, _ = process.communicate()
            if output:
                print("رسائل الخطأ:")
                print(output)
    
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 إعداد خادم التقارير المحدث...")
    print("=" * 50)
    
    # التحقق من المجلد الحالي
    if not Path('web_server.py').exists():
        print("❌ يجب تشغيل هذا الملف من مجلد cashier_filter")
        return
    
    # إيقاف الخوادم الموجودة
    kill_existing_servers()
    
    # التحقق من الملفات
    if not check_files():
        print("❌ لا يمكن تشغيل الخادم بسبب ملفات مفقودة")
        return
    
    # تشغيل الخادم
    start_server()

if __name__ == "__main__":
    main()
