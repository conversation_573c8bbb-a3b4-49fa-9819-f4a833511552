# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for Cashier Filter System
Fixed version with proper path handling and all dependencies
"""

import os
import sys
from pathlib import Path

block_cipher = None

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define data files to include
added_files = [
    # Core directories
    (os.path.join(current_dir, 'ui'), 'ui'),
    (os.path.join(current_dir, 'db'), 'db'),
    (os.path.join(current_dir, 'reports'), 'reports'),
    (os.path.join(current_dir, 'utils'), 'utils'),
    (os.path.join(current_dir, 'web_templates'), 'web_templates'),
    (os.path.join(current_dir, 'web_static'), 'web_static'),
    (os.path.join(current_dir, 'assets'), 'assets'),
    
    # Configuration files
    (os.path.join(current_dir, 'config.py'), '.'),
    (os.path.join(current_dir, 'pyinstaller_utils.py'), '.'),
    (os.path.join(current_dir, 'settings.json'), '.'),
    (os.path.join(current_dir, 'version_info.txt'), '.'),
    
    # Requirements file for reference
    (os.path.join(current_dir, 'requirements.txt'), '.'),
]

# Add files only if they exist
filtered_files = []
for src, dst in added_files:
    if os.path.exists(src):
        filtered_files.append((src, dst))
        print(f"Adding: {src} -> {dst}")
    else:
        print(f"Warning: File not found: {src}")

# Hidden imports - all required modules
hidden_imports = [
    # GUI Framework
    'customtkinter',
    'customtkinter.windows',
    'customtkinter.widgets',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.font',
    
    # Web Framework
    'flask',
    'flask.templating',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'jinja2',
    'jinja2.ext',
    
    # Data Processing
    'pandas',
    'numpy',
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    
    # PDF Generation
    'fpdf2',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.lib',
    
    # Image Processing
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageFont',
    'PIL.ImageDraw',
    
    # Network and HTTP
    'requests',
    'urllib3',
    'httpx',
    'aiohttp',
    
    # Cryptography
    'cryptography',
    'cryptography.fernet',
    'pycryptodome',
    
    # System and OS
    'psutil',
    'schedule',
    'pyperclip',
    
    # Cloud Integration
    'google.api_core',
    'google.auth',
    'google_api_python_client',
    'dropbox',
    'boto3',
    'azure.storage.blob',
    
    # Compression
    'py7zr',
    
    # Standard library modules that might need explicit inclusion
    'sqlite3',
    'json',
    'datetime',
    'threading',
    'webbrowser',
    'subprocess',
    'pathlib',
    'tempfile',
    'shutil',
    'hashlib',
    'base64',
    'uuid',
    'logging',
    'email',
    'email.mime',
    'email.mime.text',
    'email.mime.multipart',
    'smtplib',
    'ftplib',
    'zipfile',
    'tarfile',
    'gzip',
    'pickle',
    'csv',
    'xml',
    'xml.etree',
    'xml.etree.ElementTree',
    'html',
    'html.parser',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'http.server',
    'socketserver',
    'socket',
    'ssl',
    'certifi',
    'platform',
    'getpass',
    'locale',
    'calendar',
    'time',
    'math',
    'statistics',
    'random',
    'secrets',
    'collections',
    'itertools',
    'functools',
    'operator',
    'copy',
    'gc',
    'atexit',
    'signal',
    'traceback',
    'warnings',
    'contextlib',
    'weakref',
    'types',
    'inspect',
    'ast',
    'dis',
    'code',
    'codeop',
    'keyword',
    'token',
    'tokenize',
    'parser',
    'symbol',
    'bdb',
    'pdb',
    'profile',
    'pstats',
    'timeit',
    'trace',
    'doctest',
    'unittest',
    'test',
    'argparse',
    'optparse',
    'getopt',
    'configparser',
    'fileinput',
    'linecache',
    'glob',
    'fnmatch',
    'tempfile',
    'shutil',
    'macpath',
    'ntpath',
    'posixpath',
    'genericpath',
    'stat',
    'filecmp',
    'tarfile',
    'zipfile',
    'gzip',
    'bz2',
    'lzma',
    'zlib',
    'binascii',
    'base64',
    'binhex',
    'uu',
    'quopri',
    'encodings',
    'encodings.utf_8',
    'encodings.latin_1',
    'encodings.ascii',
    'encodings.cp1252',
    'codecs',
    'unicodedata',
    'stringprep',
    'readline',
    'rlcompleter',
]

# Binaries to include (if any)
binaries = []

# Modules to exclude (to reduce size and avoid conflicts)
excludes = [
    'matplotlib.tests',
    'numpy.tests',
    'pandas.tests',
    'PIL.tests',
    'test',
    'tests',
    'testing',
    'distutils',
    'setuptools',
    'pip',
    'wheel',
    'pkg_resources',
    '_distutils_hack',
    'distutils.command',
    'distutils.util',
    'distutils.version',
    'distutils.spawn',
    'distutils.errors',
    'distutils.log',
    'distutils.dep_util',
    'distutils.dir_util',
    'distutils.file_util',
    'distutils.archive_util',
    'distutils.text_file',
    'distutils.fancy_getopt',
    'distutils.debug',
    'distutils.msvccompiler',
    'distutils.bcppcompiler',
    'distutils.cygwinccompiler',
    'distutils.emxccompiler',
    'distutils.unixccompiler',
    'distutils.extension',
    'distutils.core',
    'distutils.dist',
    'distutils.cmd',
    'distutils.config',
    'distutils.filelist',
    'distutils.sysconfig',
]

a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=binaries,
    datas=filtered_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Remove duplicate files
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CashierFilterSystem_v3.5.0_Fixed',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(current_dir, 'assets', 'icon.ico') if os.path.exists(os.path.join(current_dir, 'assets', 'icon.ico')) else None,
    version='version_info.txt' if os.path.exists(os.path.join(current_dir, 'version_info.txt')) else None,
)
