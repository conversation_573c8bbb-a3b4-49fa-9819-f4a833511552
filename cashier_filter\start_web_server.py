#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل سريع لخادم تقارير نظام تصفية الكاشير
Quick Launcher for Cashier Filter Reports Web Server
"""

import os
import sys
import socket
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # إنشاء اتصال وهمي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def check_port_available(port):
    """التحقق من توفر المنفذ"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind(('', port))
        s.close()
        return True
    except:
        return False

def find_available_port(start_port=5000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None

def print_startup_info(host, port, local_ip):
    """طباعة معلومات بدء التشغيل"""
    print("=" * 70)
    print("🌐 خادم تقارير نظام تصفية الكاشير المتكامل 2025")
    print("=" * 70)
    print(f"🖥️  الرابط المحلي:")
    print(f"   http://localhost:{port}")
    print(f"   http://127.0.0.1:{port}")
    print()
    print(f"📱 للوصول من الهاتف:")
    print(f"   http://{local_ip}:{port}")
    print(f"   http://{local_ip}:{port}/mobile")
    print()
    print("📋 الصفحات المتاحة:")
    print(f"   🏠 الرئيسية: http://{local_ip}:{port}/")
    print(f"   📊 التقارير: http://{local_ip}:{port}/reports")
    print(f"   📱 الهاتف: http://{local_ip}:{port}/mobile")
    print()
    print("💡 تعليمات الاستخدام:")
    print("   1. تأكد من أن الهاتف والكمبيوتر على نفس الشبكة")
    print("   2. استخدم عنوان IP أعلاه للوصول من الهاتف")
    print("   3. أضف الصفحة للشاشة الرئيسية في الهاتف للوصول السريع")
    print()
    print("🔒 الأمان:")
    print("   - الخادم يعمل على الشبكة المحلية فقط")
    print("   - لا يمكن الوصول إليه من الإنترنت")
    print("   - البيانات محمية ومشفرة")
    print()
    print("⏹️  للإيقاف: اضغط Ctrl+C")
    print("=" * 70)

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من وجود قاعدة البيانات
        db_path = project_root / "db" / "cashier_filter.db"
        if not db_path.exists():
            print("❌ خطأ: قاعدة البيانات غير موجودة")
            print("   يرجى تشغيل التطبيق الرئيسي أولاً لإنشاء قاعدة البيانات")
            input("اضغط Enter للخروج...")
            return
        
        # الحصول على عنوان IP المحلي
        local_ip = get_local_ip()
        
        # البحث عن منفذ متاح
        port = find_available_port(5000)
        if not port:
            print("❌ خطأ: لا يمكن العثور على منفذ متاح")
            input("اضغط Enter للخروج...")
            return
        
        # طباعة معلومات بدء التشغيل
        print_startup_info('0.0.0.0', port, local_ip)
        
        # تشغيل الخادم
        from web_server import run_server
        run_server(host='0.0.0.0', port=port, debug=False, open_browser_auto=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف الخادم بواسطة المستخدم")
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("   يرجى تثبيت Flask: pip install Flask")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
