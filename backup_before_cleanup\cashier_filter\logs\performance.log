2025-07-05 00:12:22,754 - ERROR - خطأ في البحث المتقدم: no such column: f.admin_name
2025-07-05 00:12:32,955 - ERROR - خطأ في البحث المتقدم: no such column: f.admin_name
2025-07-05 00:12:35,091 - ERROR - خطأ في البحث المتقدم: no such column: f.admin_name
2025-07-05 00:38:23,334 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 00:38:25,822 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 00:38:34,952 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 00:39:13,224 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 02:44:53,373 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 02:47:31,751 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 02:47:31,752 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-05 02:50:49,038 - INFO - تم تصدير 3 سجل إلى C:/Users/<USER>/Desktop/تقارير/45.xlsx
2025-07-05 02:50:49,038 - WARNING - عملية بطيئة: export_to_excel استغرقت 3.97 ثانية
2025-07-05 14:10:48,577 - INFO - تم تصدير 2 سجل إلى C:/Users/<USER>/Desktop/تقارير/202.xlsx
2025-07-05 14:10:48,577 - WARNING - عملية بطيئة: export_to_excel استغرقت 2.01 ثانية
2025-07-06 14:47:06,595 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:47:07,948 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:47:25,658 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:47:28,080 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:47:28,287 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:47:28,498 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:49:36,518 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-06 14:49:36,521 - ERROR - خطأ في البحث المتقدم: no such column: f.created_at
2025-07-07 00:51:21,795 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 00:51:21,798 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 00:51:21,798 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 00:51:33,908 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 00:51:33,909 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 00:51:33,910 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:11:36,274 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:11:36,276 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:11:36,277 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:35,877 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:35,878 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:35,878 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:43,964 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:43,964 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:15:43,965 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:16:02,797 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:16:02,798 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
2025-07-07 02:16:02,799 - INFO - Using categorical units to plot a list of strings that are all parsable as floats or dates. If these strings should be plotted as numbers, cast to the appropriate data type before plotting.
