# واجهة التقارير المخصصة المبسطة (للاختبار)
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
from datetime import datetime, timedelta
from collections import defaultdict
import webbrowser
import os
from pathlib import Path

# استخدام مسار نسبي مع معالجة الأخطاء
try:
    BASE_DIR = Path(__file__).parent.parent
    DB_PATH = BASE_DIR / "db" / "cashier_filter.db"
    
    # التحقق من وجود قاعدة البيانات
    if not DB_PATH.exists():
        # محاولة المسار المطلق كبديل
        DB_PATH = Path("c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db")
        
except Exception as e:
    print(f"خطأ في تحديد مسار قاعدة البيانات: {e}")
    # استخدام المسار المطلق كبديل
    DB_PATH = Path("c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db")

class CustomReportsSimpleWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        try:
            super().__init__(master)
            self.title("🎯 التقارير المخصصة (نسخة مبسطة)")
            self.geometry("1200x800")
            self.configure(bg="#f0f2f5")
            self.resizable(True, True)
            
            # متغيرات البيانات
            self.filters_data = []
            self.current_report_data = {}
            
            # إنشاء الواجهة
            self.create_widgets()
            
            # تحميل البيانات
            self.load_data()
            
        except Exception as e:
            print(f"خطأ في إنشاء نافذة التقارير المخصصة: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التقارير المخصصة:\n{e}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة المبسطة"""
        
        # الشريط العلوي
        header_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=0, height=80)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎯 التقارير المخصصة المتطورة",
            font=("Arial", 24, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self, fg_color="#34495e", corner_radius=0, height=100)
        toolbar_frame.pack(fill="x", padx=0, pady=0)
        toolbar_frame.pack_propagate(False)
        
        # الفلاتر
        filters_container = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        filters_container.pack(fill="x", padx=20, pady=10)
        
        # فلتر نوع التقرير
        ctk.CTkLabel(filters_container, text="📊 نوع التقرير:", 
                    font=("Arial", 12, "bold"), text_color="#ffffff").pack(side="left", padx=10)
        
        self.report_type_combo = ctk.CTkComboBox(
            filters_container,
            values=[
                "تقرير شامل متقدم",
                "تحليل أداء الكاشيرين", 
                "تحليل المقبوضات المفصل",
                "تقرير الاتجاهات والتوقعات"
            ],
            width=200
        )
        self.report_type_combo.set("تقرير شامل متقدم")
        self.report_type_combo.pack(side="left", padx=5)
        
        # فلتر الكاشير
        ctk.CTkLabel(filters_container, text="👤 الكاشير:", 
                    font=("Arial", 12, "bold"), text_color="#ffffff").pack(side="left", padx=10)
        
        self.cashier_combo = ctk.CTkComboBox(
            filters_container,
            values=["جميع الكاشيرين"],
            width=150
        )
        self.cashier_combo.set("جميع الكاشيرين")
        self.cashier_combo.pack(side="left", padx=5)
        
        # أزرار الإجراءات
        actions_container = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        actions_container.pack(fill="x", padx=20, pady=5)
        
        generate_btn = ctk.CTkButton(
            actions_container,
            text="🚀 إنشاء التقرير",
            command=self.generate_report,
            fg_color="#27ae60",
            hover_color="#229954",
            width=140,
            height=35,
            font=("Arial", 12, "bold")
        )
        generate_btn.pack(side="left", padx=5)
        
        export_btn = ctk.CTkButton(
            actions_container,
            text="📄 تصدير PDF",
            command=self.export_pdf,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=120,
            height=35
        )
        export_btn.pack(side="left", padx=5)
        
        print_btn = ctk.CTkButton(
            actions_container,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color="#9b59b6",
            hover_color="#8e44ad",
            width=100,
            height=35
        )
        print_btn.pack(side="left", padx=5)
        
        # المحتوى الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="#ffffff")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # بطاقات الإحصائيات
        self.create_stats_cards(main_frame)
        
        # جدول البيانات
        self.create_data_table(main_frame)
        
        # شريط الحالة
        status_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=0, height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="🟢 جاهز - اختر نوع التقرير وانقر على 'إنشاء التقرير'",
            font=("Arial", 10),
            text_color="#ffffff"
        )
        self.status_label.pack(side="left", padx=20, pady=5)

    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        
        stats_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=10)
        stats_frame.pack(fill="x", padx=10, pady=10)
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="⚡ الملخص السريع",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        stats_title.pack(pady=10)
        
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(fill="x", padx=10, pady=10)
        
        # بطاقة إجمالي التصفيات
        self.total_card = self.create_stat_card(
            cards_frame, "📊 إجمالي التصفيات", "0", "#3498db"
        )
        self.total_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        # بطاقة إجمالي المبالغ
        self.amount_card = self.create_stat_card(
            cards_frame, "💰 إجمالي المبالغ", "0.00 ريال", "#27ae60"
        )
        self.amount_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        # بطاقة متوسط التصفية
        self.avg_card = self.create_stat_card(
            cards_frame, "📈 متوسط التصفية", "0.00 ريال", "#f39c12"
        )
        self.avg_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية واحدة"""
        
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 11, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(pady=(10, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 16, "bold"),
            text_color="#ffffff"
        )
        value_label.pack(pady=(0, 10))
        
        return card

    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        
        table_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        table_title = ctk.CTkLabel(
            table_frame,
            text="📋 البيانات التفصيلية",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        table_title.pack(pady=10)
        
        # إنشاء الجدول
        columns = ("التاريخ", "الكاشير", "المقبوضات البنكية", "المقبوضات النقدية", "الإجمالي")
        
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=scrollbar.set)
        
        self.data_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            if not DB_PATH.exists():
                print(f"قاعدة البيانات غير موجودة: {DB_PATH}")
                self.status_label.configure(text="⚠️ قاعدة البيانات غير موجودة - وضع تجريبي")
                return
            
            conn = sqlite3.connect(str(DB_PATH))
            c = conn.cursor()
            
            # تحميل بيانات التصفيات مع JOIN للحصول على اسم الكاشير
            try:
                c.execute("""
                    SELECT f.date, f.data, COALESCE(c.name, f.admin_name, 'غير محدد') as cashier_name
                    FROM filters f
                    LEFT JOIN cashiers c ON f.cashier_id = c.id
                    ORDER BY f.date DESC
                    LIMIT 100
                """)
                self.filters_data = c.fetchall()
            except sqlite3.OperationalError:
                # محاولة بديلة بدون JOIN
                c.execute("SELECT date, data, admin_name FROM filters ORDER BY date DESC LIMIT 100")
                self.filters_data = c.fetchall()
            
            # تحميل بيانات الكاشيرين
            c.execute("SELECT name FROM cashiers")
            cashiers = c.fetchall()
            
            conn.close()
            
            # تحديث قائمة الكاشيرين
            cashier_names = ["جميع الكاشيرين"] + [name[0] for name in cashiers if name[0]]
            self.cashier_combo.configure(values=cashier_names)
            
            # تحديث الإحصائيات
            self.update_stats()
            
            self.status_label.configure(text=f"✅ تم تحميل {len(self.filters_data)} تصفية")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_label.configure(text="❌ خطأ في تحميل البيانات")

    def update_stats(self):
        """تحديث الإحصائيات"""
        try:
            total_filters = len(self.filters_data)
            total_amount = 0
            
            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})
                    
                    amount = (
                        float(totals.get('bank', 0)) + 
                        float(totals.get('cash', 0)) + 
                        float(totals.get('credit', 0))
                    )
                    total_amount += amount
                    
                except Exception:
                    continue
            
            avg_amount = total_amount / total_filters if total_filters > 0 else 0
            
            # تحديث البطاقات
            self.update_card_value(self.total_card, str(total_filters))
            self.update_card_value(self.amount_card, f"{total_amount:.2f} ريال")
            self.update_card_value(self.avg_card, f"{avg_amount:.2f} ريال")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_card_value(self, card, new_value):
        """تحديث قيمة البطاقة"""
        try:
            # البحث عن label القيمة وتحديثه
            for child in card.winfo_children():
                if isinstance(child, ctk.CTkLabel):
                    current_text = child.cget("text")
                    if any(char.isdigit() for char in current_text):
                        child.configure(text=new_value)
                        break
        except Exception as e:
            print(f"خطأ في تحديث البطاقة: {e}")

    def generate_report(self):
        """إنشاء التقرير"""
        try:
            self.status_label.configure(text="🔄 جاري إنشاء التقرير...")
            
            # مسح الجدول الحالي
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)
            
            # ملء الجدول بالبيانات
            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})
                    
                    bank_total = float(totals.get('bank', 0))
                    cash_total = float(totals.get('cash', 0))
                    total_amount = bank_total + cash_total
                    
                    self.data_tree.insert("", "end", values=(
                        date_str or "غير محدد",
                        cashier_name or "غير محدد",
                        f"{bank_total:.2f}",
                        f"{cash_total:.2f}",
                        f"{total_amount:.2f}"
                    ))
                    
                except Exception:
                    continue
            
            self.status_label.configure(text="✅ تم إنشاء التقرير بنجاح")
            
        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            self.status_label.configure(text="❌ فشل في إنشاء التقرير")

    def export_pdf(self):
        """تصدير التقرير كـ PDF"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ التقرير كـ PDF"
            )
            if filename:
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            messagebox.showinfo("طباعة", "سيتم فتح التقرير في المتصفح للطباعة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")
