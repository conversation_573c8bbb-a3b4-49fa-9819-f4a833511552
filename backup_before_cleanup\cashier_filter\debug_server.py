#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشاكل الخادم
Debug Server Issues
"""

import requests
import sys
from pathlib import Path

def test_server():
    """اختبار الخادم"""
    base_url = "http://localhost:5000"
    
    print("🔍 اختبار خادم التقارير...")
    print("=" * 40)
    
    # اختبار الصفحة الرئيسية
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ الصفحة الرئيسية: {response.status_code}")
    except Exception as e:
        print(f"❌ الصفحة الرئيسية: {e}")
        print("💡 الخادم غير متاح. تأكد من تشغيله أولاً.")
        return False
    
    # اختبار صفحة التقارير
    try:
        response = requests.get(f"{base_url}/reports", timeout=5)
        print(f"✅ صفحة التقارير: {response.status_code}")
    except Exception as e:
        print(f"❌ صفحة التقارير: {e}")
    
    # اختبار تفاصيل تصفية موجودة
    try:
        response = requests.get(f"{base_url}/filter/22", timeout=5)
        print(f"✅ تفاصيل التصفية 22: {response.status_code}")
    except Exception as e:
        print(f"❌ تفاصيل التصفية 22: {e}")
    
    # اختبار التقرير الشامل
    try:
        response = requests.get(f"{base_url}/filter/22/comprehensive", timeout=5)
        if response.status_code == 200:
            print(f"✅ التقرير الشامل: {response.status_code}")
            print("🎉 التقرير الشامل يعمل بنجاح!")
        else:
            print(f"❌ التقرير الشامل: {response.status_code}")
            print(f"📄 المحتوى: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ التقرير الشامل: {e}")
    
    return True

def check_files():
    """التحقق من الملفات"""
    print("\n📁 التحقق من الملفات...")
    print("=" * 40)
    
    files_to_check = [
        "web_server.py",
        "web_templates/comprehensive_report.html",
        "web_templates/filter_detail.html",
        "web_templates/reports.html",
        "web_templates/index.html"
    ]
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود!")

def main():
    """الدالة الرئيسية"""
    print("🔧 تشخيص خادم التقارير")
    print("=" * 50)
    
    # التحقق من الملفات
    check_files()
    
    # اختبار الخادم
    test_server()
    
    print("\n💡 نصائح:")
    print("1. تأكد من تشغيل الخادم: python web_server.py")
    print("2. تأكد من أن المنفذ 5000 غير مستخدم")
    print("3. جرب الرابط: http://localhost:5000/filter/22/comprehensive")

if __name__ == "__main__":
    main()
