#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إحصائيات العمليات
Operations Statistics Window
"""

import customtkinter as ctk
from tkinter import messagebox, ttk
import tkinter as tk
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.operations_logger import operations_logger
    from utils.font_manager import get_stable_font
except ImportError:
    def get_stable_font(size_type="normal", weight="normal"):
        sizes = {"small": 12, "normal": 14, "medium": 16, "large": 18}
        size = sizes.get(size_type, 14)
        return ("Arial", size, weight)

class OperationsStatisticsWindow(ctk.CTkToplevel):
    """نافذة إحصائيات العمليات"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title("📊 إحصائيات العمليات")
        self.geometry("900x600")
        self.transient(parent)
        self.grab_set()
        
        # تعيين أيقونة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
        
        self.create_widgets()
        self.load_statistics()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="#f8f9fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="📊 إحصائيات العمليات والأنشطة",
            font=get_stable_font("large", "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)
        
        # إطار الفلاتر
        self.create_filters_frame(main_frame)
        
        # إطار الإحصائيات العامة
        self.create_general_stats_frame(main_frame)
        
        # إطار الرسوم البيانية والجداول
        self.create_charts_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_filters_frame(self, parent):
        """إنشاء إطار الفلاتر"""
        filters_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        filters_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الفلاتر
        filters_title = ctk.CTkLabel(
            filters_frame,
            text="📅 فترة الإحصائيات",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        filters_title.pack(pady=(10, 5))
        
        # إطار الفلاتر الأفقي
        filters_container = ctk.CTkFrame(filters_frame, fg_color="transparent")
        filters_container.pack(fill="x", padx=20, pady=(0, 15))
        
        # فلتر الفترة
        period_label = ctk.CTkLabel(filters_container, text="📅 الفترة:", font=get_stable_font("normal"))
        period_label.pack(side="left", padx=(0, 10))
        
        self.period_var = ctk.StringVar(value="آخر 30 يوم")
        self.period_combo = ctk.CTkComboBox(
            filters_container, variable=self.period_var, width=200,
            values=["آخر 7 أيام", "آخر 30 يوم", "آخر 90 يوم", "آخر سنة", "الكل"]
        )
        self.period_combo.pack(side="left", padx=(0, 20))
        
        # زر التحديث
        refresh_btn = ctk.CTkButton(
            filters_container, text="🔄 تحديث الإحصائيات", width=150,
            command=self.load_statistics,
            fg_color="#007bff", hover_color="#0056b3"
        )
        refresh_btn.pack(side="left")
    
    def create_general_stats_frame(self, parent):
        """إنشاء إطار الإحصائيات العامة"""
        stats_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        stats_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الإحصائيات
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 الإحصائيات العامة",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        stats_title.pack(pady=(10, 5))
        
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # بطاقات الإحصائيات
        self.total_operations_card = self.create_stat_card(cards_frame, "📝", "إجمالي العمليات", "0", "#007bff")
        self.successful_operations_card = self.create_stat_card(cards_frame, "✅", "العمليات الناجحة", "0", "#28a745")
        self.failed_operations_card = self.create_stat_card(cards_frame, "❌", "العمليات الفاشلة", "0", "#dc3545")
        self.avg_daily_operations_card = self.create_stat_card(cards_frame, "📊", "متوسط يومي", "0", "#6f42c1")
    
    def create_stat_card(self, parent, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=15, width=180, height=100)
        card.pack(side="left", padx=8, fill="y")
        card.pack_propagate(False)
        
        # الأيقونة
        icon_label = ctk.CTkLabel(card, text=icon, font=("Arial", 20), text_color="white")
        icon_label.pack(pady=(8, 0))
        
        # القيمة
        value_label = ctk.CTkLabel(card, text=value, font=get_stable_font("medium", "bold"), text_color="white")
        value_label.pack()
        
        # العنوان
        title_label = ctk.CTkLabel(card, text=title, font=get_stable_font("small"), text_color="white")
        title_label.pack(pady=(0, 8))
        
        return {"card": card, "value": value_label, "title": title_label}
    
    def create_charts_frame(self, parent):
        """إنشاء إطار الرسوم البيانية والجداول"""
        charts_frame = ctk.CTkFrame(parent, fg_color="transparent")
        charts_frame.pack(fill="both", expand=True, padx=10)
        
        # تكوين الشبكة
        charts_frame.grid_columnconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(1, weight=1)
        charts_frame.grid_rowconfigure(0, weight=1)
        charts_frame.grid_rowconfigure(1, weight=1)
        
        # جدول العمليات حسب النوع
        self.create_operations_by_type_table(charts_frame)
        
        # جدول العمليات حسب المستخدم
        self.create_operations_by_user_table(charts_frame)
        
        # جدول العمليات حسب اليوم
        self.create_operations_by_day_table(charts_frame)
        
        # جدول العمليات حسب الساعة
        self.create_operations_by_hour_table(charts_frame)
    
    def create_operations_by_type_table(self, parent):
        """إنشاء جدول العمليات حسب النوع"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="🔧 العمليات حسب النوع", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("نوع العملية", "العدد", "النسبة")
        self.operations_by_type_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.operations_by_type_tree.heading("نوع العملية", text="🔧 نوع العملية")
        self.operations_by_type_tree.heading("العدد", text="📊 العدد")
        self.operations_by_type_tree.heading("النسبة", text="📈 النسبة")
        
        self.operations_by_type_tree.column("نوع العملية", width=120, anchor="center")
        self.operations_by_type_tree.column("العدد", width=80, anchor="center")
        self.operations_by_type_tree.column("النسبة", width=80, anchor="center")
        
        scrollbar1 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.operations_by_type_tree.yview)
        self.operations_by_type_tree.configure(yscrollcommand=scrollbar1.set)
        
        self.operations_by_type_tree.pack(side="left", fill="both", expand=True)
        scrollbar1.pack(side="right", fill="y")
    
    def create_operations_by_user_table(self, parent):
        """إنشاء جدول العمليات حسب المستخدم"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="👤 العمليات حسب المستخدم", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("المستخدم", "العدد", "النسبة")
        self.operations_by_user_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.operations_by_user_tree.heading("المستخدم", text="👤 المستخدم")
        self.operations_by_user_tree.heading("العدد", text="📊 العدد")
        self.operations_by_user_tree.heading("النسبة", text="📈 النسبة")
        
        self.operations_by_user_tree.column("المستخدم", width=120, anchor="center")
        self.operations_by_user_tree.column("العدد", width=80, anchor="center")
        self.operations_by_user_tree.column("النسبة", width=80, anchor="center")
        
        scrollbar2 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.operations_by_user_tree.yview)
        self.operations_by_user_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.operations_by_user_tree.pack(side="left", fill="both", expand=True)
        scrollbar2.pack(side="right", fill="y")
    
    def create_operations_by_day_table(self, parent):
        """إنشاء جدول العمليات حسب اليوم"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="📅 العمليات حسب اليوم", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("التاريخ", "العدد", "الناجحة", "الفاشلة")
        self.operations_by_day_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.operations_by_day_tree.heading("التاريخ", text="📅 التاريخ")
        self.operations_by_day_tree.heading("العدد", text="📊 العدد")
        self.operations_by_day_tree.heading("الناجحة", text="✅ الناجحة")
        self.operations_by_day_tree.heading("الفاشلة", text="❌ الفاشلة")
        
        self.operations_by_day_tree.column("التاريخ", width=100, anchor="center")
        self.operations_by_day_tree.column("العدد", width=70, anchor="center")
        self.operations_by_day_tree.column("الناجحة", width=70, anchor="center")
        self.operations_by_day_tree.column("الفاشلة", width=70, anchor="center")
        
        scrollbar3 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.operations_by_day_tree.yview)
        self.operations_by_day_tree.configure(yscrollcommand=scrollbar3.set)
        
        self.operations_by_day_tree.pack(side="left", fill="both", expand=True)
        scrollbar3.pack(side="right", fill="y")
    
    def create_operations_by_hour_table(self, parent):
        """إنشاء جدول العمليات حسب الساعة"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="🕐 العمليات حسب الساعة", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("الساعة", "العدد", "النسبة")
        self.operations_by_hour_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.operations_by_hour_tree.heading("الساعة", text="🕐 الساعة")
        self.operations_by_hour_tree.heading("العدد", text="📊 العدد")
        self.operations_by_hour_tree.heading("النسبة", text="📈 النسبة")
        
        self.operations_by_hour_tree.column("الساعة", width=80, anchor="center")
        self.operations_by_hour_tree.column("العدد", width=80, anchor="center")
        self.operations_by_hour_tree.column("النسبة", width=80, anchor="center")
        
        scrollbar4 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.operations_by_hour_tree.yview)
        self.operations_by_hour_tree.configure(yscrollcommand=scrollbar4.set)
        
        self.operations_by_hour_tree.pack(side="left", fill="both", expand=True)
        scrollbar4.pack(side="right", fill="y")
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=10)
        
        # زر تصدير
        export_btn = ctk.CTkButton(
            buttons_frame, text="📤 تصدير الإحصائيات", width=150,
            command=self.export_statistics,
            fg_color="#17a2b8", hover_color="#138496"
        )
        export_btn.pack(side="left", padx=5)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame, text="❌ إغلاق", width=120,
            command=self.destroy,
            fg_color="#dc3545", hover_color="#c82333"
        )
        close_btn.pack(side="right", padx=5)

    def get_period_days(self):
        """الحصول على عدد الأيام حسب الفترة المختارة"""
        period = self.period_var.get()
        if period == "آخر 7 أيام":
            return 7
        elif period == "آخر 30 يوم":
            return 30
        elif period == "آخر 90 يوم":
            return 90
        elif period == "آخر سنة":
            return 365
        else:  # الكل
            return None

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            days = self.get_period_days()

            # تحميل الإحصائيات العامة
            self.load_general_statistics(days)

            # تحميل جداول البيانات
            self.load_operations_by_type(days)
            self.load_operations_by_user(days)
            self.load_operations_by_day(days)
            self.load_operations_by_hour(days)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإحصائيات: {e}")

    def load_general_statistics(self, days):
        """تحميل الإحصائيات العامة"""
        try:
            # الحصول على الإحصائيات من operations_logger
            stats = operations_logger.get_operations_statistics(days if days else 365)

            # تحديث البطاقات
            self.total_operations_card["value"].configure(text=str(stats.get('total_operations', 0)))
            self.successful_operations_card["value"].configure(text=str(stats.get('successful_operations', 0)))
            self.failed_operations_card["value"].configure(text=str(stats.get('failed_operations', 0)))

            # حساب المتوسط اليومي
            total_ops = stats.get('total_operations', 0)
            period_days = days if days else 365
            avg_daily = total_ops / period_days if period_days > 0 else 0
            self.avg_daily_operations_card["value"].configure(text=f"{avg_daily:.1f}")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات العامة: {e}")

    def load_operations_by_type(self, days):
        """تحميل العمليات حسب النوع"""
        try:
            # مسح الجدول
            for item in self.operations_by_type_tree.get_children():
                self.operations_by_type_tree.delete(item)

            # الحصول على الإحصائيات
            stats = operations_logger.get_operations_statistics(days if days else 365)
            operations_by_type = stats.get('most_common_operations', [])

            # حساب المجموع
            total_operations = sum(count for _, count in operations_by_type)

            # ترجمة أنواع العمليات
            operation_names = {
                'AUTH': 'المصادقة',
                'FILTER': 'التصفية',
                'REPORT': 'التقارير',
                'ADMIN': 'الإدارة',
                'SYSTEM': 'النظام'
            }

            for operation_type, count in operations_by_type:
                percentage = (count / total_operations * 100) if total_operations > 0 else 0
                operation_name = operation_names.get(operation_type, operation_type)

                self.operations_by_type_tree.insert("", "end", values=(
                    operation_name, count, f"{percentage:.1f}%"
                ))

        except Exception as e:
            print(f"خطأ في تحميل العمليات حسب النوع: {e}")

    def load_operations_by_user(self, days):
        """تحميل العمليات حسب المستخدم"""
        try:
            # مسح الجدول
            for item in self.operations_by_user_tree.get_children():
                self.operations_by_user_tree.delete(item)

            # الحصول على الإحصائيات
            stats = operations_logger.get_operations_statistics(days if days else 365)
            operations_by_user = stats.get('most_active_users', [])

            # حساب المجموع
            total_operations = sum(count for _, count in operations_by_user)

            for username, count in operations_by_user:
                percentage = (count / total_operations * 100) if total_operations > 0 else 0

                self.operations_by_user_tree.insert("", "end", values=(
                    username, count, f"{percentage:.1f}%"
                ))

        except Exception as e:
            print(f"خطأ في تحميل العمليات حسب المستخدم: {e}")

    def load_operations_by_day(self, days):
        """تحميل العمليات حسب اليوم"""
        try:
            # مسح الجدول
            for item in self.operations_by_day_tree.get_children():
                self.operations_by_day_tree.delete(item)

            # الحصول على الإحصائيات
            stats = operations_logger.get_operations_statistics(days if days else 365)
            operations_by_day = stats.get('operations_by_day', [])

            for date, count in operations_by_day:
                # للحصول على تفاصيل أكثر، نحتاج استعلام إضافي
                # هنا نعرض العدد الإجمالي فقط
                self.operations_by_day_tree.insert("", "end", values=(
                    date, count, count, 0  # نفترض أن جميع العمليات ناجحة مؤقتاً
                ))

        except Exception as e:
            print(f"خطأ في تحميل العمليات حسب اليوم: {e}")

    def load_operations_by_hour(self, days):
        """تحميل العمليات حسب الساعة"""
        try:
            # مسح الجدول
            for item in self.operations_by_hour_tree.get_children():
                self.operations_by_hour_tree.delete(item)

            # الحصول على الإحصائيات
            stats = operations_logger.get_operations_statistics(days if days else 365)
            operations_by_hour = stats.get('operations_by_hour', [])

            # حساب المجموع
            total_operations = sum(count for _, count in operations_by_hour)

            for hour, count in operations_by_hour:
                percentage = (count / total_operations * 100) if total_operations > 0 else 0
                hour_display = f"{hour}:00"

                self.operations_by_hour_tree.insert("", "end", values=(
                    hour_display, count, f"{percentage:.1f}%"
                ))

        except Exception as e:
            print(f"خطأ في تحميل العمليات حسب الساعة: {e}")

    def export_statistics(self):
        """تصدير الإحصائيات"""
        try:
            from tkinter import filedialog
            import csv

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ إحصائيات العمليات"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة الإحصائيات العامة
                    writer.writerow(['إحصائيات العمليات'])
                    writer.writerow(['الفترة:', self.period_var.get()])
                    writer.writerow(['تاريخ التصدير:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                    writer.writerow([])

                    # الإحصائيات العامة
                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي العمليات', self.total_operations_card["value"].cget("text")])
                    writer.writerow(['العمليات الناجحة', self.successful_operations_card["value"].cget("text")])
                    writer.writerow(['العمليات الفاشلة', self.failed_operations_card["value"].cget("text")])
                    writer.writerow(['المتوسط اليومي', self.avg_daily_operations_card["value"].cget("text")])
                    writer.writerow([])

                    # العمليات حسب النوع
                    writer.writerow(['العمليات حسب النوع'])
                    writer.writerow(['نوع العملية', 'العدد', 'النسبة'])
                    for item in self.operations_by_type_tree.get_children():
                        values = self.operations_by_type_tree.item(item)['values']
                        writer.writerow(values)
                    writer.writerow([])

                    # العمليات حسب المستخدم
                    writer.writerow(['العمليات حسب المستخدم'])
                    writer.writerow(['المستخدم', 'العدد', 'النسبة'])
                    for item in self.operations_by_user_tree.get_children():
                        values = self.operations_by_user_tree.item(item)['values']
                        writer.writerow(values)
                    writer.writerow([])

                    # العمليات حسب اليوم
                    writer.writerow(['العمليات حسب اليوم'])
                    writer.writerow(['التاريخ', 'العدد', 'الناجحة', 'الفاشلة'])
                    for item in self.operations_by_day_tree.get_children():
                        values = self.operations_by_day_tree.item(item)['values']
                        writer.writerow(values)
                    writer.writerow([])

                    # العمليات حسب الساعة
                    writer.writerow(['العمليات حسب الساعة'])
                    writer.writerow(['الساعة', 'العدد', 'النسبة'])
                    for item in self.operations_by_hour_tree.get_children():
                        values = self.operations_by_hour_tree.item(item)['values']
                        writer.writerow(values)

                messagebox.showinfo("نجح", "تم تصدير إحصائيات العمليات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الإحصائيات: {e}")
