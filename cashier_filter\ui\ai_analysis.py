# نافذة التحليل الذكي بالذكاء الاصطناعي
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json
from datetime import datetime, timedelta
import threading
import time
import random

import os
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class AIAnalysisWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("🤖 التحليل الذكي بالذكاء الاصطناعي")
        self.geometry("1200x800")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)
        
        # متغيرات التحليل
        self.analysis_running = False
        self.analysis_progress = 0
        
        self.create_widgets()
        self.load_initial_data()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        header_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="🤖 التحليل الذكي بالذكاء الاصطناعي",
            font=("Arial", 26, "bold"),
            text_color="white"
        )
        title_label.pack(pady=20)
        
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="تحليل متقدم للبيانات المالية باستخدام خوارزميات الذكاء الاصطناعي",
            font=("Arial", 14),
            text_color="#ecf0f1"
        )
        subtitle_label.pack(pady=(0, 15))
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f2f3f7", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إطار التحكم
        self.create_control_panel(main_container)
        
        # إطار النتائج
        self.create_results_panel(main_container)
        
        # إطار التوصيات
        self.create_recommendations_panel(main_container)

    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        control_frame.pack(pady=10, fill="x")
        
        # عنوان لوحة التحكم
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎛️ لوحة التحكم في التحليل",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        control_title.pack(pady=15)
        
        # إطار الخيارات
        options_frame = ctk.CTkFrame(control_frame, fg_color="#f2f3f7", corner_radius=10)
        options_frame.pack(pady=10, padx=20, fill="x")
        
        # صف الخيارات الأول
        row1 = ctk.CTkFrame(options_frame, fg_color="transparent")
        row1.pack(fill="x", pady=10)
        
        # نوع التحليل
        ctk.CTkLabel(row1, text="نوع التحليل:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.analysis_type = ctk.CTkComboBox(
            row1,
            values=[
                "تحليل شامل للأداء المالي",
                "تحليل اتجاهات المبيعات",
                "تحليل أداء الكاشيرين",
                "تحليل الأنماط الزمنية",
                "تحليل التنبؤات المستقبلية",
                "تحليل المخاطر المالية"
            ],
            width=300
        )
        self.analysis_type.pack(side="left", padx=10)
        
        # فترة التحليل
        ctk.CTkLabel(row1, text="فترة التحليل:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.time_period = ctk.CTkComboBox(
            row1,
            values=["آخر 7 أيام", "آخر 30 يوم", "آخر 3 أشهر", "آخر 6 أشهر", "آخر سنة"],
            width=150
        )
        self.time_period.pack(side="left", padx=10)
        
        # صف الخيارات الثاني
        row2 = ctk.CTkFrame(options_frame, fg_color="transparent")
        row2.pack(fill="x", pady=10)
        
        # مستوى التفصيل
        ctk.CTkLabel(row2, text="مستوى التفصيل:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.detail_level = ctk.CTkComboBox(
            row2,
            values=["أساسي", "متوسط", "متقدم", "خبير"],
            width=150
        )
        self.detail_level.pack(side="left", padx=10)
        
        # خيارات إضافية
        self.include_predictions = ctk.CTkCheckBox(row2, text="تضمين التنبؤات")
        self.include_predictions.pack(side="left", padx=20)
        
        self.include_recommendations = ctk.CTkCheckBox(row2, text="تضمين التوصيات")
        self.include_recommendations.pack(side="left", padx=20)
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        buttons_frame.pack(pady=15)
        
        self.start_analysis_btn = ctk.CTkButton(
            buttons_frame,
            text="🚀 بدء التحليل الذكي",
            command=self.start_analysis,
            fg_color="#27ae60",
            hover_color="#229954",
            width=200,
            height=40,
            font=("Arial", 14, "bold")
        )
        self.start_analysis_btn.pack(side="left", padx=10)
        
        self.stop_analysis_btn = ctk.CTkButton(
            buttons_frame,
            text="⏹️ إيقاف التحليل",
            command=self.stop_analysis,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=150,
            height=40,
            font=("Arial", 14, "bold"),
            state="disabled"
        )
        self.stop_analysis_btn.pack(side="left", padx=10)
        
        self.export_results_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير النتائج",
            command=self.export_results,
            fg_color="#3498db",
            hover_color="#2980b9",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        self.export_results_btn.pack(side="left", padx=10)

        self.print_results_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة التحليل",
            command=self.print_analysis,
            fg_color="#8e44ad",
            hover_color="#7d3c98",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        self.print_results_btn.pack(side="left", padx=10)

        self.save_pdf_btn = ctk.CTkButton(
            buttons_frame,
            text="📄 حفظ PDF",
            command=self.save_as_pdf,
            fg_color="#d35400",
            hover_color="#c0392b",
            width=120,
            height=40,
            font=("Arial", 14, "bold")
        )
        self.save_pdf_btn.pack(side="left", padx=10)
        
        # شريط التقدم
        self.progress_frame = ctk.CTkFrame(control_frame, fg_color="#f2f3f7", corner_radius=10)
        self.progress_frame.pack(pady=10, padx=20, fill="x")
        
        self.progress_label = ctk.CTkLabel(
            self.progress_frame,
            text="جاهز لبدء التحليل",
            font=("Arial", 12),
            text_color="#7f8c8d"
        )
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ctk.CTkProgressBar(self.progress_frame, width=400)
        self.progress_bar.pack(pady=5)
        self.progress_bar.set(0)

    def create_results_panel(self, parent):
        """إنشاء لوحة النتائج"""
        results_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        results_frame.pack(pady=10, fill="both", expand=True)
        
        # عنوان النتائج
        results_title = ctk.CTkLabel(
            results_frame,
            text="📈 نتائج التحليل الذكي",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        results_title.pack(pady=15)
        
        # إطار النتائج القابل للتمرير
        self.results_scrollable = ctk.CTkScrollableFrame(
            results_frame,
            fg_color="#ffffff",
            corner_radius=10
        )
        self.results_scrollable.pack(pady=10, padx=20, fill="both", expand=True)
        
        # رسالة افتراضية
        self.default_message = ctk.CTkLabel(
            self.results_scrollable,
            text="🤖 اختر نوع التحليل واضغط 'بدء التحليل الذكي' لعرض النتائج",
            font=("Arial", 16),
            text_color="#7f8c8d"
        )
        self.default_message.pack(pady=50)

    def create_recommendations_panel(self, parent):
        """إنشاء لوحة التوصيات"""
        recommendations_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        recommendations_frame.pack(pady=10, fill="x")
        
        # عنوان التوصيات
        recommendations_title = ctk.CTkLabel(
            recommendations_frame,
            text="💡 التوصيات الذكية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        recommendations_title.pack(pady=15)
        
        # إطار التوصيات
        self.recommendations_container = ctk.CTkFrame(
            recommendations_frame,
            fg_color="#f2f3f7",
            corner_radius=10
        )
        self.recommendations_container.pack(pady=10, padx=20, fill="x")
        
        # رسالة افتراضية للتوصيات
        self.recommendations_default = ctk.CTkLabel(
            self.recommendations_container,
            text="💡 ستظهر التوصيات الذكية هنا بعد اكتمال التحليل",
            font=("Arial", 14),
            text_color="#7f8c8d"
        )
        self.recommendations_default.pack(pady=20)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تعيين القيم الافتراضية
            self.analysis_type.set("تحليل شامل للأداء المالي")
            self.time_period.set("آخر 30 يوم")
            self.detail_level.set("متوسط")
            self.include_predictions.select()
            self.include_recommendations.select()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")

    def start_analysis(self):
        """بدء التحليل الذكي"""
        if self.analysis_running:
            return

        self.analysis_running = True
        self.start_analysis_btn.configure(state="disabled")
        self.stop_analysis_btn.configure(state="normal")

        # مسح النتائج السابقة
        self.clear_results()

        # بدء التحليل في خيط منفصل
        analysis_thread = threading.Thread(target=self.run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def stop_analysis(self):
        """إيقاف التحليل"""
        self.analysis_running = False
        self.start_analysis_btn.configure(state="normal")
        self.stop_analysis_btn.configure(state="disabled")
        self.progress_label.configure(text="تم إيقاف التحليل")

    def clear_results(self):
        """مسح النتائج السابقة"""
        for widget in self.results_scrollable.winfo_children():
            widget.destroy()

        for widget in self.recommendations_container.winfo_children():
            widget.destroy()

    def run_analysis(self):
        """تشغيل التحليل الذكي"""
        try:
            # مراحل التحليل
            analysis_steps = [
                ("جمع البيانات من قاعدة البيانات", self.collect_data),
                ("تحليل الأنماط والاتجاهات", self.analyze_patterns),
                ("تطبيق خوارزميات الذكاء الاصطناعي", self.apply_ai_algorithms),
                ("إنشاء التنبؤات المستقبلية", self.generate_predictions),
                ("تحليل المخاطر والفرص", self.analyze_risks_opportunities),
                ("إنشاء التوصيات الذكية", self.generate_recommendations),
                ("تجميع النتائج النهائية", self.compile_final_results)
            ]

            total_steps = len(analysis_steps)

            for i, (step_name, step_function) in enumerate(analysis_steps):
                if not self.analysis_running:
                    break

                # تحديث شريط التقدم
                progress = (i + 1) / total_steps
                self.update_progress(progress, f"جاري {step_name}...")

                # تنفيذ الخطوة
                step_function()

                # محاكاة وقت المعالجة
                time.sleep(1)

            if self.analysis_running:
                self.update_progress(1.0, "اكتمل التحليل بنجاح!")
                # استخدام after للتحديث الآمن
                self.after(100, self.display_results)

        except Exception as e:
            self.update_progress(0, f"خطأ في التحليل: {e}")
        finally:
            self.analysis_running = False
            self.start_analysis_btn.configure(state="normal")
            self.stop_analysis_btn.configure(state="disabled")

    def update_progress(self, progress, message):
        """تحديث شريط التقدم"""
        try:
            # استخدام after للتحديث الآمن من thread
            self.after(0, lambda: self._update_progress_ui(progress, message))
        except Exception as e:
            print(f"خطأ في تحديث التقدم: {e}")

    def _update_progress_ui(self, progress, message):
        """تحديث واجهة شريط التقدم"""
        try:
            self.progress_bar.set(progress)
            self.progress_label.configure(text=message)
        except Exception as e:
            print(f"خطأ في تحديث واجهة التقدم: {e}")

    def collect_data(self):
        """جمع البيانات من قاعدة البيانات"""
        try:
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(DB_PATH):
                print(f"ملف قاعدة البيانات غير موجود: {DB_PATH}")
                self.raw_data = []
                self.processed_data = self.get_default_processed_data()
                return

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # تحديد فترة التحليل
            days_map = {
                "آخر 7 أيام": 7,
                "آخر 30 يوم": 30,
                "آخر 3 أشهر": 90,
                "آخر 6 أشهر": 180,
                "آخر سنة": 365
            }

            days = days_map.get(self.time_period.get(), 30)
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')

            # التحقق من وجود الجداول
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='filters'")
            if not c.fetchone():
                print("جدول filters غير موجود")
                self.raw_data = []
                self.processed_data = self.get_default_processed_data()
                conn.close()
                return

            # جمع بيانات التصفيات
            c.execute("""
                SELECT f.date, f.data, COALESCE(ca.name, 'غير محدد') as cashier_name
                FROM filters f
                LEFT JOIN cashiers ca ON f.cashier_id = ca.id
                WHERE f.date >= ?
                ORDER BY f.date DESC
            """, (start_date,))

            self.raw_data = c.fetchall()
            self.processed_data = self.process_raw_data(self.raw_data)

            conn.close()

            print(f"تم جمع {len(self.raw_data)} تصفية للتحليل")

        except Exception as e:
            print(f"خطأ في جمع البيانات: {e}")
            import traceback
            traceback.print_exc()
            self.raw_data = []
            self.processed_data = self.get_default_processed_data()

    def get_default_processed_data(self):
        """إرجاع بيانات افتراضية في حالة عدم وجود بيانات"""
        return {
            'total_filters': 0,
            'total_revenue': 0,
            'daily_revenue': {},
            'cashier_performance': {},
            'transaction_types': {
                'bank': 0, 'cash': 0, 'credit': 0, 'client': 0, 'return': 0
            }
        }

    def process_raw_data(self, raw_data):
        """معالجة البيانات الخام"""
        processed = {
            'total_filters': len(raw_data),
            'total_revenue': 0,
            'daily_revenue': {},
            'cashier_performance': {},
            'transaction_types': {
                'bank': 0, 'cash': 0, 'credit': 0, 'client': 0, 'return': 0
            }
        }

        if not raw_data:
            print("لا توجد بيانات للمعالجة")
            return processed

        for date, data_json, cashier_name in raw_data:
            try:
                if not data_json:
                    continue

                data = json.loads(data_json)
                totals = data.get('totals', {})

                if not totals:
                    continue

                # حساب الإيرادات اليومية
                daily_total = sum(totals.values()) - totals.get('client', 0)
                processed['total_revenue'] += daily_total
                processed['daily_revenue'][date] = daily_total

                # أداء الكاشيرين
                if cashier_name and cashier_name != 'غير محدد':
                    if cashier_name not in processed['cashier_performance']:
                        processed['cashier_performance'][cashier_name] = {
                            'total': 0, 'count': 0, 'average': 0
                        }
                    processed['cashier_performance'][cashier_name]['total'] += daily_total
                    processed['cashier_performance'][cashier_name]['count'] += 1

                # أنواع المعاملات
                for trans_type, amount in totals.items():
                    if trans_type in processed['transaction_types'] and isinstance(amount, (int, float)):
                        processed['transaction_types'][trans_type] += amount

            except json.JSONDecodeError as e:
                print(f"خطأ في تحليل JSON: {e}")
                continue
            except Exception as e:
                print(f"خطأ في معالجة البيانات: {e}")
                continue

        # حساب المتوسطات
        for cashier in processed['cashier_performance']:
            perf = processed['cashier_performance'][cashier]
            if perf['count'] > 0:
                perf['average'] = perf['total'] / perf['count']

        print(f"تمت معالجة {processed['total_filters']} تصفية بإجمالي {processed['total_revenue']:.2f} ريال")
        return processed

    def analyze_patterns(self):
        """تحليل الأنماط والاتجاهات"""
        if not self.processed_data or self.processed_data.get('total_filters', 0) == 0:
            print("لا توجد بيانات كافية لتحليل الأنماط")
            self.patterns = {
                'trend': 'غير محدد',
                'seasonality': 'غير محدد',
                'anomalies': [],
                'growth_rate': 0
            }
            return

        try:
            self.patterns = {
                'trend': self.calculate_trend(),
                'seasonality': self.detect_seasonality(),
                'anomalies': self.detect_anomalies(),
                'growth_rate': self.calculate_growth_rate()
            }
            print(f"تم تحليل الأنماط: اتجاه {self.patterns['trend']}, نمو {self.patterns['growth_rate']:.1f}%")
        except Exception as e:
            print(f"خطأ في تحليل الأنماط: {e}")
            self.patterns = {
                'trend': 'خطأ في التحليل',
                'seasonality': 'خطأ في التحليل',
                'anomalies': [],
                'growth_rate': 0
            }

    def calculate_trend(self):
        """حساب الاتجاه العام"""
        daily_revenue = self.processed_data.get('daily_revenue', {})
        if len(daily_revenue) < 2:
            return "غير محدد"

        values = list(daily_revenue.values())
        first_half = sum(values[:len(values)//2])
        second_half = sum(values[len(values)//2:])

        if second_half > first_half * 1.1:
            return "تصاعدي"
        elif second_half < first_half * 0.9:
            return "تنازلي"
        else:
            return "مستقر"

    def detect_seasonality(self):
        """اكتشاف الموسمية"""
        # محاكاة تحليل الموسمية
        patterns = ["نمط أسبوعي قوي", "نمط شهري متوسط", "لا يوجد نمط واضح"]
        return random.choice(patterns)

    def detect_anomalies(self):
        """اكتشاف الشذوذ في البيانات"""
        daily_revenue = self.processed_data.get('daily_revenue', {})
        if not daily_revenue:
            return []

        values = list(daily_revenue.values())
        if not values:
            return []

        mean_val = sum(values) / len(values)
        anomalies = []

        for date, value in daily_revenue.items():
            if abs(value - mean_val) > mean_val * 0.5:  # انحراف أكثر من 50%
                anomalies.append({
                    'date': date,
                    'value': value,
                    'deviation': ((value - mean_val) / mean_val) * 100
                })

        return anomalies

    def calculate_growth_rate(self):
        """حساب معدل النمو"""
        daily_revenue = self.processed_data.get('daily_revenue', {})
        if len(daily_revenue) < 7:
            return 0

        values = list(daily_revenue.values())
        first_week = sum(values[:7]) / 7
        last_week = sum(values[-7:]) / 7

        if first_week == 0:
            return 0

        return ((last_week - first_week) / first_week) * 100

    def apply_ai_algorithms(self):
        """تطبيق خوارزميات الذكاء الاصطناعي"""
        try:
            # حساب نقاط الأداء أولاً
            performance_score = self.calculate_performance_score()

            # إنشاء كائن ai_insights
            self.ai_insights = {
                'performance_score': performance_score,
                'efficiency_rating': self.calculate_efficiency_rating(performance_score),
                'risk_assessment': self.assess_risks(),
                'optimization_opportunities': self.find_optimization_opportunities()
            }

            print(f"تم تطبيق خوارزميات الذكاء الاصطناعي: نقاط الأداء {performance_score:.1f}")

        except Exception as e:
            print(f"خطأ في تطبيق خوارزميات الذكاء الاصطناعي: {e}")
            self.ai_insights = {
                'performance_score': 0,
                'efficiency_rating': 'غير محدد',
                'risk_assessment': [],
                'optimization_opportunities': []
            }

    def calculate_performance_score(self):
        """حساب نقاط الأداء باستخدام الذكاء الاصطناعي"""
        if not self.processed_data:
            return 0

        # خوارزمية تقييم الأداء المتعددة المعايير
        factors = {
            'revenue_consistency': 0,
            'growth_trend': 0,
            'transaction_diversity': 0,
            'cashier_efficiency': 0
        }

        # تقييم ثبات الإيرادات
        daily_revenue = list(self.processed_data.get('daily_revenue', {}).values())
        if daily_revenue:
            mean_revenue = sum(daily_revenue) / len(daily_revenue)
            variance = sum((x - mean_revenue) ** 2 for x in daily_revenue) / len(daily_revenue)
            cv = (variance ** 0.5) / mean_revenue if mean_revenue > 0 else 1
            factors['revenue_consistency'] = max(0, 100 - cv * 100)

        # تقييم اتجاه النمو
        growth_rate = self.patterns.get('growth_rate', 0)
        factors['growth_trend'] = min(100, max(0, 50 + growth_rate))

        # تقييم تنوع المعاملات
        trans_types = self.processed_data.get('transaction_types', {})
        non_zero_types = sum(1 for v in trans_types.values() if v > 0)
        factors['transaction_diversity'] = (non_zero_types / len(trans_types)) * 100

        # تقييم كفاءة الكاشيرين
        cashier_perf = self.processed_data.get('cashier_performance', {})
        if cashier_perf:
            avg_performance = sum(c['average'] for c in cashier_perf.values()) / len(cashier_perf)
            factors['cashier_efficiency'] = min(100, avg_performance / 1000 * 100)

        # حساب النقاط الإجمالية
        weights = {'revenue_consistency': 0.3, 'growth_trend': 0.3,
                  'transaction_diversity': 0.2, 'cashier_efficiency': 0.2}

        total_score = sum(factors[key] * weights[key] for key in factors)
        return round(total_score, 1)

    def calculate_efficiency_rating(self, performance_score=None):
        """حساب تقييم الكفاءة"""
        if performance_score is None:
            performance_score = getattr(self, 'ai_insights', {}).get('performance_score', 0)

        if performance_score >= 90:
            return "ممتاز"
        elif performance_score >= 75:
            return "جيد جداً"
        elif performance_score >= 60:
            return "جيد"
        elif performance_score >= 45:
            return "مقبول"
        else:
            return "يحتاج تحسين"

    def assess_risks(self):
        """تقييم المخاطر"""
        risks = []

        # تحليل تقلبات الإيرادات
        daily_revenue = list(self.processed_data.get('daily_revenue', {}).values())
        if daily_revenue:
            mean_revenue = sum(daily_revenue) / len(daily_revenue)
            high_variance_days = sum(1 for x in daily_revenue if abs(x - mean_revenue) > mean_revenue * 0.3)
            if high_variance_days > len(daily_revenue) * 0.2:
                risks.append({
                    'type': 'تقلبات عالية في الإيرادات',
                    'severity': 'متوسط',
                    'description': f'{high_variance_days} أيام بتقلبات عالية من أصل {len(daily_revenue)} يوم'
                })

        # تحليل اعتماد على نوع معاملة واحد
        trans_types = self.processed_data.get('transaction_types', {})
        if trans_types:
            total_trans = sum(trans_types.values())
            for trans_type, amount in trans_types.items():
                if amount > total_trans * 0.7:
                    risks.append({
                        'type': 'اعتماد مفرط على نوع معاملة واحد',
                        'severity': 'عالي',
                        'description': f'اعتماد {(amount/total_trans)*100:.1f}% على {trans_type}'
                    })

        # تحليل أداء الكاشيرين
        cashier_perf = self.processed_data.get('cashier_performance', {})
        if len(cashier_perf) > 1:
            performances = [c['average'] for c in cashier_perf.values()]
            max_perf = max(performances)
            min_perf = min(performances)
            if max_perf > min_perf * 2:
                risks.append({
                    'type': 'تفاوت كبير في أداء الكاشيرين',
                    'severity': 'متوسط',
                    'description': f'فجوة أداء تصل إلى {((max_perf-min_perf)/min_perf)*100:.1f}%'
                })

        return risks

    def find_optimization_opportunities(self):
        """العثور على فرص التحسين"""
        opportunities = []

        # فرص تحسين الإيرادات
        growth_rate = self.patterns.get('growth_rate', 0)
        if growth_rate < 5:
            opportunities.append({
                'area': 'نمو الإيرادات',
                'potential': 'عالي',
                'suggestion': 'تطبيق استراتيجيات تسويقية لزيادة المبيعات'
            })

        # فرص تحسين كفاءة الكاشيرين
        cashier_perf = self.processed_data.get('cashier_performance', {})
        if cashier_perf:
            avg_performance = sum(c['average'] for c in cashier_perf.values()) / len(cashier_perf)
            low_performers = [name for name, perf in cashier_perf.items()
                            if perf['average'] < avg_performance * 0.8]
            if low_performers:
                opportunities.append({
                    'area': 'تدريب الكاشيرين',
                    'potential': 'متوسط',
                    'suggestion': f'تدريب إضافي لـ {len(low_performers)} كاشير'
                })

        # فرص تنويع المعاملات
        trans_types = self.processed_data.get('transaction_types', {})
        zero_types = [t for t, amount in trans_types.items() if amount == 0]
        if zero_types:
            opportunities.append({
                'area': 'تنويع المعاملات',
                'potential': 'متوسط',
                'suggestion': f'تفعيل {len(zero_types)} نوع معاملة غير مستخدم'
            })

        return opportunities

    def generate_predictions(self):
        """إنشاء التنبؤات المستقبلية"""
        if not self.include_predictions.get():
            self.predictions = {}
            return

        daily_revenue = self.processed_data.get('daily_revenue', {})
        if not daily_revenue:
            self.predictions = {}
            return

        # تنبؤات بسيطة باستخدام المتوسط المتحرك
        recent_values = list(daily_revenue.values())[-7:]  # آخر 7 أيام
        avg_recent = sum(recent_values) / len(recent_values) if recent_values else 0

        growth_rate = self.patterns.get('growth_rate', 0) / 100

        self.predictions = {
            'next_week_revenue': avg_recent * 7 * (1 + growth_rate),
            'next_month_revenue': avg_recent * 30 * (1 + growth_rate),
            'confidence_level': self.calculate_prediction_confidence(),
            'trend_direction': 'صاعد' if growth_rate > 0.02 else 'نازل' if growth_rate < -0.02 else 'مستقر'
        }

    def calculate_prediction_confidence(self):
        """حساب مستوى الثقة في التنبؤات"""
        # حساب مستوى الثقة بناءً على ثبات البيانات
        daily_revenue = list(self.processed_data.get('daily_revenue', {}).values())
        if len(daily_revenue) < 7:
            return "منخفض"

        mean_revenue = sum(daily_revenue) / len(daily_revenue)
        variance = sum((x - mean_revenue) ** 2 for x in daily_revenue) / len(daily_revenue)
        cv = (variance ** 0.5) / mean_revenue if mean_revenue > 0 else 1

        if cv < 0.2:
            return "عالي"
        elif cv < 0.4:
            return "متوسط"
        else:
            return "منخفض"

    def analyze_risks_opportunities(self):
        """تحليل المخاطر والفرص"""
        # تم تنفيذها في الدوال السابقة
        pass

    def generate_recommendations(self):
        """إنشاء التوصيات الذكية"""
        if not self.include_recommendations.get():
            self.recommendations = []
            return

        recommendations = []

        # توصيات بناءً على الأداء
        performance_score = self.ai_insights.get('performance_score', 0)
        if performance_score < 60:
            recommendations.append({
                'priority': 'عالي',
                'category': 'تحسين الأداء',
                'title': 'تحسين الأداء العام مطلوب',
                'description': 'نقاط الأداء الحالية منخفضة، يُنصح بمراجعة العمليات',
                'action': 'مراجعة شاملة للعمليات والإجراءات'
            })

        # توصيات بناءً على النمو
        growth_rate = self.patterns.get('growth_rate', 0)
        if growth_rate < 0:
            recommendations.append({
                'priority': 'عالي',
                'category': 'استراتيجية النمو',
                'title': 'معالجة الانخفاض في الإيرادات',
                'description': f'انخفاض في الإيرادات بمعدل {abs(growth_rate):.1f}%',
                'action': 'تطبيق خطة تحفيز المبيعات وتحليل أسباب الانخفاض'
            })

        # توصيات بناءً على المخاطر
        risks = self.ai_insights.get('risk_assessment', [])
        for risk in risks[:2]:  # أهم مخاطرين
            recommendations.append({
                'priority': 'متوسط',
                'category': 'إدارة المخاطر',
                'title': f'معالجة {risk["type"]}',
                'description': risk['description'],
                'action': 'وضع خطة للتخفيف من هذا المخاطر'
            })

        # توصيات بناءً على الفرص
        opportunities = self.ai_insights.get('optimization_opportunities', [])
        for opp in opportunities[:2]:  # أهم فرصتين
            recommendations.append({
                'priority': 'متوسط',
                'category': 'فرص التحسين',
                'title': f'استغلال فرصة {opp["area"]}',
                'description': opp['suggestion'],
                'action': 'تطبيق الاقتراح المذكور لتحسين الأداء'
            })

        self.recommendations = recommendations

    def compile_final_results(self):
        """تجميع النتائج النهائية"""
        # تجميع جميع النتائج في هيكل موحد
        self.final_results = {
            'summary': self.create_executive_summary(),
            'detailed_analysis': {
                'data_overview': self.processed_data,
                'patterns': self.patterns,
                'ai_insights': self.ai_insights,
                'predictions': getattr(self, 'predictions', {}),
                'recommendations': getattr(self, 'recommendations', [])
            },
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def create_executive_summary(self):
        """إنشاء ملخص تنفيذي"""
        total_revenue = self.processed_data.get('total_revenue', 0)
        total_filters = self.processed_data.get('total_filters', 0)
        performance_score = self.ai_insights.get('performance_score', 0)
        efficiency_rating = self.ai_insights.get('efficiency_rating', 'غير محدد')

        return {
            'period': self.time_period.get(),
            'total_revenue': total_revenue,
            'total_filters': total_filters,
            'avg_daily_revenue': total_revenue / max(1, total_filters),
            'performance_score': performance_score,
            'efficiency_rating': efficiency_rating,
            'main_trend': self.patterns.get('trend', 'غير محدد'),
            'risk_count': len(self.ai_insights.get('risk_assessment', [])),
            'opportunity_count': len(self.ai_insights.get('optimization_opportunities', []))
        }

    def display_results(self):
        """عرض النتائج في الواجهة"""
        # مسح الرسالة الافتراضية
        for widget in self.results_scrollable.winfo_children():
            widget.destroy()

        # عرض الملخص التنفيذي
        self.display_executive_summary()

        # عرض التحليل المفصل
        self.display_detailed_analysis()

        # عرض التنبؤات
        if hasattr(self, 'predictions') and self.predictions:
            self.display_predictions()

        # عرض التوصيات
        if hasattr(self, 'recommendations') and self.recommendations:
            self.display_recommendations()

    def display_executive_summary(self):
        """عرض الملخص التنفيذي"""
        summary = self.final_results['summary']

        # إطار الملخص التنفيذي
        summary_frame = ctk.CTkFrame(self.results_scrollable, fg_color="#3498db", corner_radius=15)
        summary_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الملخص
        title_label = ctk.CTkLabel(
            summary_frame,
            text="📊 الملخص التنفيذي",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # شبكة المؤشرات
        indicators_frame = ctk.CTkFrame(summary_frame, fg_color="transparent")
        indicators_frame.pack(fill="x", padx=20, pady=10)

        # الصف الأول من المؤشرات
        row1 = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        row1.pack(fill="x", pady=5)

        self.create_indicator_card(row1, "إجمالي الإيرادات", f"{summary['total_revenue']:,.0f} ريال", "#27ae60")
        self.create_indicator_card(row1, "عدد التصفيات", f"{summary['total_filters']}", "#e74c3c")
        self.create_indicator_card(row1, "متوسط الإيرادات اليومية", f"{summary['avg_daily_revenue']:,.0f} ريال", "#f39c12")

        # الصف الثاني من المؤشرات
        row2 = ctk.CTkFrame(indicators_frame, fg_color="transparent")
        row2.pack(fill="x", pady=5)

        self.create_indicator_card(row2, "نقاط الأداء", f"{summary['performance_score']}/100", "#9b59b6")
        self.create_indicator_card(row2, "تقييم الكفاءة", summary['efficiency_rating'], "#1abc9c")
        self.create_indicator_card(row2, "الاتجاه العام", summary['main_trend'], "#34495e")

    def create_indicator_card(self, parent, title, value, color):
        """إنشاء بطاقة مؤشر"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)
        card.pack(side="left", fill="both", expand=True, padx=5)

        title_label = ctk.CTkLabel(card, text=title, font=("Arial", 12), text_color="white")
        title_label.pack(pady=(10, 5))

        value_label = ctk.CTkLabel(card, text=str(value), font=("Arial", 16, "bold"), text_color="white")
        value_label.pack(pady=(0, 10))

    def display_detailed_analysis(self):
        """عرض التحليل المفصل"""
        # إطار التحليل المفصل
        analysis_frame = ctk.CTkFrame(self.results_scrollable, fg_color="#ecf0f1", corner_radius=15)
        analysis_frame.pack(fill="x", padx=10, pady=10)

        # عنوان التحليل المفصل
        title_label = ctk.CTkLabel(
            analysis_frame,
            text="🔍 التحليل المفصل",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)

        # تحليل الأنماط
        patterns_frame = ctk.CTkFrame(analysis_frame, fg_color="#ffffff", corner_radius=10)
        patterns_frame.pack(fill="x", padx=20, pady=10)

        patterns_title = ctk.CTkLabel(
            patterns_frame,
            text="📈 تحليل الأنماط والاتجاهات",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        patterns_title.pack(pady=10)

        patterns_text = f"""
الاتجاه العام: {self.patterns.get('trend', 'غير محدد')}
الموسمية: {self.patterns.get('seasonality', 'غير محدد')}
معدل النمو: {self.patterns.get('growth_rate', 0):.1f}%
عدد الشذوذات المكتشفة: {len(self.patterns.get('anomalies', []))}
        """

        patterns_label = ctk.CTkLabel(
            patterns_frame,
            text=patterns_text.strip(),
            font=("Arial", 12),
            text_color="#34495e",
            justify="right"
        )
        patterns_label.pack(pady=10)

        # تحليل المخاطر
        if self.ai_insights.get('risk_assessment'):
            self.display_risks_analysis(analysis_frame)

        # فرص التحسين
        if self.ai_insights.get('optimization_opportunities'):
            self.display_opportunities_analysis(analysis_frame)

    def display_risks_analysis(self, parent):
        """عرض تحليل المخاطر"""
        risks_frame = ctk.CTkFrame(parent, fg_color="#e74c3c", corner_radius=10)
        risks_frame.pack(fill="x", padx=20, pady=10)

        risks_title = ctk.CTkLabel(
            risks_frame,
            text="⚠️ تحليل المخاطر",
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        risks_title.pack(pady=10)

        risks = self.ai_insights.get('risk_assessment', [])
        for risk in risks[:3]:  # عرض أهم 3 مخاطر
            risk_text = f"• {risk['type']} ({risk['severity']}): {risk['description']}"
            risk_label = ctk.CTkLabel(
                risks_frame,
                text=risk_text,
                font=("Arial", 11),
                text_color="white",
                wraplength=800,
                justify="right"
            )
            risk_label.pack(pady=2, padx=15)

    def display_opportunities_analysis(self, parent):
        """عرض تحليل الفرص"""
        opportunities_frame = ctk.CTkFrame(parent, fg_color="#27ae60", corner_radius=10)
        opportunities_frame.pack(fill="x", padx=20, pady=10)

        opportunities_title = ctk.CTkLabel(
            opportunities_frame,
            text="🚀 فرص التحسين",
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        opportunities_title.pack(pady=10)

        opportunities = self.ai_insights.get('optimization_opportunities', [])
        for opp in opportunities[:3]:  # عرض أهم 3 فرص
            opp_text = f"• {opp['area']} ({opp['potential']}): {opp['suggestion']}"
            opp_label = ctk.CTkLabel(
                opportunities_frame,
                text=opp_text,
                font=("Arial", 11),
                text_color="white",
                wraplength=800,
                justify="right"
            )
            opp_label.pack(pady=2, padx=15)

    def display_predictions(self):
        """عرض التنبؤات"""
        predictions_frame = ctk.CTkFrame(self.results_scrollable, fg_color="#9b59b6", corner_radius=15)
        predictions_frame.pack(fill="x", padx=10, pady=10)

        title_label = ctk.CTkLabel(
            predictions_frame,
            text="🔮 التنبؤات المستقبلية",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        pred_content = ctk.CTkFrame(predictions_frame, fg_color="#ffffff", corner_radius=10)
        pred_content.pack(fill="x", padx=20, pady=10)

        predictions_text = f"""
التنبؤ للأسبوع القادم: {self.predictions.get('next_week_revenue', 0):,.0f} ريال
التنبؤ للشهر القادم: {self.predictions.get('next_month_revenue', 0):,.0f} ريال
اتجاه التوقع: {self.predictions.get('trend_direction', 'غير محدد')}
مستوى الثقة: {self.predictions.get('confidence_level', 'غير محدد')}
        """

        pred_label = ctk.CTkLabel(
            pred_content,
            text=predictions_text.strip(),
            font=("Arial", 12),
            text_color="#2c3e50",
            justify="right"
        )
        pred_label.pack(pady=15)

    def display_recommendations(self):
        """عرض التوصيات في لوحة منفصلة"""
        # مسح التوصيات السابقة
        for widget in self.recommendations_container.winfo_children():
            widget.destroy()

        if not self.recommendations:
            no_rec_label = ctk.CTkLabel(
                self.recommendations_container,
                text="لا توجد توصيات متاحة",
                font=("Arial", 14),
                text_color="#7f8c8d"
            )
            no_rec_label.pack(pady=20)
            return

        # عرض التوصيات
        for i, rec in enumerate(self.recommendations[:4]):  # أهم 4 توصيات
            self.create_recommendation_card(rec, i)

    def create_recommendation_card(self, recommendation, index):
        """إنشاء بطاقة توصية"""
        # ألوان حسب الأولوية
        priority_colors = {
            'عالي': '#e74c3c',
            'متوسط': '#f39c12',
            'منخفض': '#27ae60'
        }

        color = priority_colors.get(recommendation.get('priority', 'متوسط'), '#3498db')

        rec_frame = ctk.CTkFrame(self.recommendations_container, fg_color=color, corner_radius=10)
        rec_frame.pack(fill="x", padx=10, pady=5)

        # عنوان التوصية
        title_label = ctk.CTkLabel(
            rec_frame,
            text=f"{index + 1}. {recommendation.get('title', 'توصية')}",
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(10, 5), padx=15, anchor="w")

        # وصف التوصية
        desc_label = ctk.CTkLabel(
            rec_frame,
            text=recommendation.get('description', ''),
            font=("Arial", 11),
            text_color="white",
            wraplength=800,
            justify="right"
        )
        desc_label.pack(pady=2, padx=15, anchor="w")

        # الإجراء المطلوب
        action_label = ctk.CTkLabel(
            rec_frame,
            text=f"الإجراء: {recommendation.get('action', '')}",
            font=("Arial", 11, "italic"),
            text_color="#ecf0f1",
            wraplength=800,
            justify="right"
        )
        action_label.pack(pady=(2, 10), padx=15, anchor="w")

    def export_results(self):
        """تصدير النتائج"""
        if not hasattr(self, 'final_results'):
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير. يرجى تشغيل التحليل أولاً.")
            return

        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ai_analysis_report_{timestamp}.json"

            # حفظ النتائج
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.final_results, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم تصدير النتائج بنجاح إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النتائج: {e}")

    def print_analysis(self):
        """طباعة التحليل الذكي"""
        if not hasattr(self, 'final_results'):
            messagebox.showwarning("تحذير", "لا توجد نتائج للطباعة. يرجى تشغيل التحليل أولاً.")
            return

        try:
            # إنشاء تقرير HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ الملف المؤقت
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("نجح", "تم فتح التقرير في المتصفح للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التحليل: {e}")

    def save_as_pdf(self):
        """حفظ التحليل كملف PDF"""
        if not hasattr(self, 'final_results'):
            messagebox.showwarning("تحذير", "لا توجد نتائج للحفظ. يرجى تشغيل التحليل أولاً.")
            return

        try:
            # محاولة استيراد مكتبة PDF
            try:
                from weasyprint import HTML, CSS
                pdf_available = True
            except ImportError:
                pdf_available = False

            if not pdf_available:
                # إذا لم تكن مكتبة PDF متاحة، استخدم HTML
                messagebox.showinfo(
                    "معلومات",
                    "مكتبة PDF غير متاحة. سيتم حفظ التقرير كملف HTML.\n"
                    "لحفظ PDF، يرجى تثبيت مكتبة weasyprint"
                )
                self.save_as_html()
                return

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ai_analysis_report_{timestamp}.pdf"

            # إنشاء HTML
            html_content = self.generate_print_html()

            # تحويل إلى PDF
            HTML(string=html_content).write_pdf(filename)

            messagebox.showinfo("نجح", f"تم حفظ التقرير كملف PDF:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ PDF: {e}")
            # محاولة حفظ كـ HTML كبديل
            try:
                self.save_as_html()
            except:
                pass

    def save_as_html(self):
        """حفظ التحليل كملف HTML"""
        try:
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ai_analysis_report_{timestamp}.html"

            # إنشاء HTML
            html_content = self.generate_print_html()

            # حفظ الملف
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            messagebox.showinfo("نجح", f"تم حفظ التقرير كملف HTML:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ HTML: {e}")

    def generate_print_html(self):
        """إنشاء HTML للطباعة"""
        summary = self.final_results['summary']
        detailed_analysis = self.final_results['detailed_analysis']

        # إنشاء محتوى HTML
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التحليل الذكي بالذكاء الاصطناعي</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}

        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }}

        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}

        .content {{
            padding: 30px;
        }}

        .summary-section {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }}

        .summary-title {{
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }}

        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }}

        .summary-card {{
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}

        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 0.9em;
            opacity: 0.9;
        }}

        .summary-card .value {{
            font-size: 1.5em;
            font-weight: bold;
        }}

        .section {{
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }}

        .section-title {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}

        .patterns-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}

        .pattern-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}

        .pattern-label {{
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }}

        .pattern-value {{
            color: #7f8c8d;
        }}

        .risks-section {{
            background: #ffe6e6;
            border-left: 4px solid #e74c3c;
        }}

        .opportunities-section {{
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
        }}

        .predictions-section {{
            background: #f3e5f5;
            border-left: 4px solid #9b59b6;
        }}

        .risk-item, .opportunity-item, .prediction-item {{
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}

        .risk-title {{
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }}

        .opportunity-title {{
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 5px;
        }}

        .prediction-title {{
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 5px;
        }}

        .recommendations-section {{
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            border-radius: 10px;
            padding: 25px;
        }}

        .recommendation-item {{
            background: rgba(255,255,255,0.1);
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
        }}

        .recommendation-title {{
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 8px;
        }}

        .recommendation-desc {{
            margin-bottom: 8px;
            opacity: 0.9;
        }}

        .recommendation-action {{
            font-style: italic;
            opacity: 0.8;
        }}

        .footer {{
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }}

        @media print {{
            body {{
                background: white;
            }}
            .container {{
                box-shadow: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 تقرير التحليل الذكي بالذكاء الاصطناعي</h1>
            <p>تحليل متقدم للبيانات المالية - {self.final_results['timestamp']}</p>
        </div>

        <div class="content">
            <!-- الملخص التنفيذي -->
            <div class="summary-section">
                <div class="summary-title">📊 الملخص التنفيذي</div>
                <div class="summary-grid">
                    <div class="summary-card">
                        <h3>فترة التحليل</h3>
                        <div class="value">{summary['period']}</div>
                    </div>
                    <div class="summary-card">
                        <h3>إجمالي الإيرادات</h3>
                        <div class="value">{summary['total_revenue']:,.0f} ريال</div>
                    </div>
                    <div class="summary-card">
                        <h3>عدد التصفيات</h3>
                        <div class="value">{summary['total_filters']}</div>
                    </div>
                    <div class="summary-card">
                        <h3>متوسط الإيرادات اليومية</h3>
                        <div class="value">{summary['avg_daily_revenue']:,.0f} ريال</div>
                    </div>
                    <div class="summary-card">
                        <h3>نقاط الأداء</h3>
                        <div class="value">{summary['performance_score']}/100</div>
                    </div>
                    <div class="summary-card">
                        <h3>تقييم الكفاءة</h3>
                        <div class="value">{summary['efficiency_rating']}</div>
                    </div>
                </div>
            </div>

            <!-- تحليل الأنماط -->
            <div class="section">
                <div class="section-title">📈 تحليل الأنماط والاتجاهات</div>
                <div class="patterns-grid">
                    <div class="pattern-item">
                        <div class="pattern-label">الاتجاه العام</div>
                        <div class="pattern-value">{detailed_analysis['patterns']['trend']}</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">الموسمية</div>
                        <div class="pattern-value">{detailed_analysis['patterns']['seasonality']}</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">معدل النمو</div>
                        <div class="pattern-value">{detailed_analysis['patterns']['growth_rate']:.1f}%</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">عدد الشذوذات</div>
                        <div class="pattern-value">{len(detailed_analysis['patterns']['anomalies'])}</div>
                    </div>
                </div>
            </div>
"""

        # إضافة المخاطر
        risks = detailed_analysis['ai_insights']['risk_assessment']
        if risks:
            html_content += """
            <div class="section risks-section">
                <div class="section-title">⚠️ تحليل المخاطر</div>
"""
            for risk in risks:
                html_content += f"""
                <div class="risk-item">
                    <div class="risk-title">{risk['type']} ({risk['severity']})</div>
                    <div>{risk['description']}</div>
                </div>
"""
            html_content += "</div>"

        # إضافة الفرص
        opportunities = detailed_analysis['ai_insights']['optimization_opportunities']
        if opportunities:
            html_content += """
            <div class="section opportunities-section">
                <div class="section-title">🚀 فرص التحسين</div>
"""
            for opp in opportunities:
                html_content += f"""
                <div class="opportunity-item">
                    <div class="opportunity-title">{opp['area']} (إمكانية {opp['potential']})</div>
                    <div>{opp['suggestion']}</div>
                </div>
"""
            html_content += "</div>"

        # إضافة التنبؤات
        predictions = detailed_analysis.get('predictions', {})
        if predictions:
            html_content += f"""
            <div class="section predictions-section">
                <div class="section-title">🔮 التنبؤات المستقبلية</div>
                <div class="prediction-item">
                    <div class="prediction-title">التنبؤ للأسبوع القادم</div>
                    <div>{predictions.get('next_week_revenue', 0):,.0f} ريال</div>
                </div>
                <div class="prediction-item">
                    <div class="prediction-title">التنبؤ للشهر القادم</div>
                    <div>{predictions.get('next_month_revenue', 0):,.0f} ريال</div>
                </div>
                <div class="prediction-item">
                    <div class="prediction-title">اتجاه التوقع</div>
                    <div>{predictions.get('trend_direction', 'غير محدد')}</div>
                </div>
                <div class="prediction-item">
                    <div class="prediction-title">مستوى الثقة</div>
                    <div>{predictions.get('confidence_level', 'غير محدد')}</div>
                </div>
            </div>
"""

        # إضافة التوصيات
        recommendations = detailed_analysis.get('recommendations', [])
        if recommendations:
            html_content += """
            <div class="recommendations-section">
                <div class="section-title">💡 التوصيات الذكية</div>
"""
            for i, rec in enumerate(recommendations, 1):
                html_content += f"""
                <div class="recommendation-item">
                    <div class="recommendation-title">{i}. {rec.get('title', 'توصية')}</div>
                    <div class="recommendation-desc">{rec.get('description', '')}</div>
                    <div class="recommendation-action">الإجراء: {rec.get('action', '')}</div>
                </div>
"""
            html_content += "</div>"

        # إنهاء HTML
        html_content += """
        </div>

        <div class="footer">
            <p>تطوير: محمد الكامل | نظام تصفية الكاشير | جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>
"""

        return html_content


def show_ai_analysis(parent=None):
    """عرض نافذة التحليل الذكي"""
    AIAnalysisWindow(parent)
