@echo off
chcp 65001 > nul
title خادم التقارير المحسن v3.5.0
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           🌐 خادم التقارير المحسن v3.5.0                   ██
echo ██                                                            ██
echo ██  📊 الميزات الجديدة في التقارير:                          ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  📄 رقم المرجع للمعاملات البنكية                         ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء الحقيقية                               ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██  🎨 تصميم احترافي مع ألوان وأيقونات                     ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
echo 🔍 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تثبيت Python من: https://python.org/downloads
    pause
    exit /b 1
)

echo ✅ Python متاح
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    الإصدار: %PYTHON_VERSION%

REM التحقق من Flask
echo.
echo 📦 التحقق من Flask...
python -c "import flask" 2>nul
if errorlevel 1 (
    echo    تثبيت Flask...
    pip install flask flask-cors --quiet
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)
echo ✅ Flask متاح

REM التحقق من ملف خادم التقارير
echo.
echo 📄 التحقق من ملفات الخادم...
if not exist "web_server.py" (
    echo ❌ ملف web_server.py غير موجود!
    echo 💡 تأكد من وجود جميع ملفات النظام
    pause
    exit /b 1
)
echo ✅ ملفات الخادم موجودة

REM التحقق من قاعدة البيانات
echo.
echo 🗄️ التحقق من قاعدة البيانات...
if not exist "db\cashier_filter.db" (
    echo ⚠️ قاعدة البيانات غير موجودة
    echo 💡 شغل التطبيق الرئيسي أولاً لإنشاء قاعدة البيانات
    echo.
    set /p choice="هل تريد المتابعة؟ (y/n): "
    if /i not "%choice%"=="y" exit /b 1
) else (
    echo ✅ قاعدة البيانات موجودة
)

REM إنشاء مجلد القوالب إذا لم يكن موجوداً
if not exist "web_templates" mkdir web_templates
if not exist "web_static" mkdir web_static

echo.
echo 🚀 بدء تشغيل خادم التقارير المحسن...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🌐 خادم التقارير سيكون متاحاً على:                        │
echo │                                                             │
echo │     🔗 http://localhost:5000                               │
echo │     🔗 http://127.0.0.1:5000                               │
echo │                                                             │
echo │  📊 الميزات المتاحة:                                        │
echo │     • قائمة جميع التصفيات                                   │
echo │     • التقرير الشامل لكل تصفية                             │
echo │     • طباعة التقارير                                       │
echo │     • تصدير البيانات                                       │
echo │                                                             │
echo │  💡 للوصول للتقرير الشامل:                                 │
echo │     http://localhost:5000/filter/[رقم]/comprehensive       │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM تشغيل الخادم
echo ⏳ جاري تشغيل الخادم...
echo.
python web_server.py

REM التحقق من حالة الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل خادم التقارير
    echo.
    echo 🔍 خطوات استكشاف الأخطاء:
    echo    1. تأكد من عدم استخدام المنفذ 5000
    echo    2. تحقق من تثبيت Flask
    echo    3. تأكد من وجود ملف web_server.py
    echo    4. راجع رسائل الخطأ أعلاه
    echo.
    echo 💡 لإغلاق التطبيقات التي تستخدم المنفذ 5000:
    echo    netstat -ano ^| findstr :5000
    echo    taskkill /PID [رقم_العملية] /F
    echo.
    pause
) else (
    echo.
    echo ✅ تم إيقاف خادم التقارير بنجاح
    echo 👋 شكراً لاستخدام خادم التقارير المحسن!
    echo.
)

echo 🔄 يمكنك تشغيل الخادم مرة أخرى بالنقر على هذا الملف
pause
