#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص قاعدة البيانات - نظام تصفية الكاشير
Database Check - Cashier Filter System
"""

import sqlite3
import os
import json
from datetime import datetime

def check_database():
    """فحص شامل لقاعدة البيانات"""
    
    db_path = 'cashier_filter/db/cashier_filter.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🗄️ فحص قاعدة البيانات...")
        print(f"📍 المسار: {db_path}")
        print(f"📏 حجم الملف: {os.path.getsize(db_path)} بايت")
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"\n📋 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # فحص بنية كل جدول
        for table in tables:
            table_name = table[0]
            print(f"\n🔍 بنية جدول {table_name}:")
            
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            for col in columns:
                null_status = "NOT NULL" if col[3] else "NULL"
                pk_status = "PK" if col[5] else ""
                print(f"  - {col[1]} ({col[2]}) - {null_status} {pk_status}")
            
            # عدد السجلات
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"  📊 عدد السجلات: {count}")
            
            # عينة من البيانات (أول 3 سجلات)
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = cursor.fetchall()
                print(f"  📄 عينة من البيانات:")
                for i, row in enumerate(sample_data, 1):
                    print(f"    {i}. {row}")
        
        # فحص الفهارس
        print(f"\n🔗 الفهارس:")
        cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index';")
        indexes = cursor.fetchall()
        
        for index in indexes:
            if not index[0].startswith('sqlite_'):  # تجاهل الفهارس الداخلية
                print(f"  - {index[0]} على جدول {index[1]}")
        
        # فحص المفاتيح الخارجية
        print(f"\n🔑 المفاتيح الخارجية:")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"PRAGMA foreign_key_list({table_name})")
            foreign_keys = cursor.fetchall()
            
            if foreign_keys:
                print(f"  جدول {table_name}:")
                for fk in foreign_keys:
                    print(f"    - {fk[3]} -> {fk[2]}.{fk[4]}")
        
        conn.close()
        print("\n✅ فحص قاعدة البيانات اكتمل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def check_data_integrity():
    """فحص سلامة البيانات"""
    
    db_path = 'cashier_filter/db/cashier_filter.db'
    
    if not os.path.exists(db_path):
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔍 فحص سلامة البيانات...")
        
        # فحص البيانات المفقودة في الجداول الرئيسية
        cursor.execute("SELECT COUNT(*) FROM filters WHERE cashier_id IS NULL")
        null_cashier = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM filters WHERE admin_id IS NULL")
        null_admin = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM filters WHERE date IS NULL OR date = ''")
        null_date = cursor.fetchone()[0]
        
        print(f"  📊 إحصائيات البيانات المفقودة:")
        print(f"    - تصفيات بدون كاشير: {null_cashier}")
        print(f"    - تصفيات بدون مدير: {null_admin}")
        print(f"    - تصفيات بدون تاريخ: {null_date}")
        
        # فحص صحة JSON في حقل data
        cursor.execute("SELECT id, data FROM filters WHERE data IS NOT NULL AND data != ''")
        filters_data = cursor.fetchall()
        
        invalid_json_count = 0
        for filter_id, data in filters_data:
            try:
                json.loads(data)
            except json.JSONDecodeError:
                invalid_json_count += 1
                print(f"    ⚠️ JSON غير صحيح في التصفية رقم {filter_id}")
        
        print(f"    - تصفيات بـ JSON غير صحيح: {invalid_json_count}")
        
        # فحص المراجع الخارجية
        cursor.execute("""
            SELECT COUNT(*) FROM filters f 
            LEFT JOIN cashiers c ON f.cashier_id = c.id 
            WHERE f.cashier_id IS NOT NULL AND c.id IS NULL
        """)
        orphaned_cashier_refs = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(*) FROM filters f 
            LEFT JOIN admins a ON f.admin_id = a.id 
            WHERE f.admin_id IS NOT NULL AND a.id IS NULL
        """)
        orphaned_admin_refs = cursor.fetchone()[0]
        
        print(f"    - مراجع كاشير معطلة: {orphaned_cashier_refs}")
        print(f"    - مراجع مدير معطلة: {orphaned_admin_refs}")
        
        conn.close()
        
        total_issues = null_cashier + null_admin + null_date + invalid_json_count + orphaned_cashier_refs + orphaned_admin_refs
        
        if total_issues == 0:
            print("  ✅ جميع البيانات سليمة")
        else:
            print(f"  ⚠️ تم العثور على {total_issues} مشكلة في البيانات")
        
        return total_issues == 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص سلامة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء فحص قاعدة البيانات...")
    
    db_ok = check_database()
    integrity_ok = check_data_integrity()
    
    print(f"\n📋 ملخص النتائج:")
    print(f"  - بنية قاعدة البيانات: {'✅ سليمة' if db_ok else '❌ بها مشاكل'}")
    print(f"  - سلامة البيانات: {'✅ سليمة' if integrity_ok else '❌ بها مشاكل'}")
    
    if db_ok and integrity_ok:
        print("\n🎉 قاعدة البيانات في حالة ممتازة!")
    else:
        print("\n⚠️ قاعدة البيانات تحتاج إلى مراجعة")
