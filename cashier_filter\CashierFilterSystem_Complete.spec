# -*- mode: python ; coding: utf-8 -*-
"""
Complete PyInstaller spec file for Cashier Filter System
ملف spec كامل لنظام تصفية الكاشير
"""

import os
import sys
from pathlib import Path

block_cipher = None

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define data files to include - COMPLETE VERSION
added_files = [
    # Core application directories
    (os.path.join(current_dir, 'ui'), 'ui'),
    (os.path.join(current_dir, 'db'), 'db'),
    (os.path.join(current_dir, 'reports'), 'reports'),
    (os.path.join(current_dir, 'utils'), 'utils'),
    (os.path.join(current_dir, 'web_templates'), 'web_templates'),
    (os.path.join(current_dir, 'web_static'), 'web_static'),
    (os.path.join(current_dir, 'assets'), 'assets'),
    
    # Configuration and utility files
    (os.path.join(current_dir, 'config.py'), '.'),
    (os.path.join(current_dir, 'pyinstaller_utils.py'), '.'),
    (os.path.join(current_dir, 'settings.json'), '.'),
    (os.path.join(current_dir, 'version_info.txt'), '.'),
    (os.path.join(current_dir, 'requirements.txt'), '.'),
    
    # Documentation files
    (os.path.join(current_dir, 'README.md'), '.'),
    (os.path.join(current_dir, 'USER_GUIDE.md'), '.'),
    (os.path.join(current_dir, 'PYINSTALLER_GUIDE.md'), '.'),
]

# Add files only if they exist
filtered_files = []
for src, dst in added_files:
    if os.path.exists(src):
        filtered_files.append((src, dst))
        print(f"✅ Adding: {os.path.basename(src)}")
    else:
        print(f"⚠️ Missing: {os.path.basename(src)}")

# Complete hidden imports - ALL required modules
hidden_imports = [
    # Core GUI Framework
    'customtkinter',
    'customtkinter.windows',
    'customtkinter.windows.widgets',
    'customtkinter.widgets',
    'customtkinter.widgets.core_rendering',
    'customtkinter.widgets.core_widget_classes',
    'customtkinter.widgets.font',
    'customtkinter.widgets.scaling',
    'customtkinter.widgets.theme',
    'customtkinter.widgets.appearance_mode',
    'customtkinter.widgets.ctk_button',
    'customtkinter.widgets.ctk_canvas',
    'customtkinter.widgets.ctk_checkbox',
    'customtkinter.widgets.ctk_combobox',
    'customtkinter.widgets.ctk_entry',
    'customtkinter.widgets.ctk_frame',
    'customtkinter.widgets.ctk_image',
    'customtkinter.widgets.ctk_label',
    'customtkinter.widgets.ctk_optionmenu',
    'customtkinter.widgets.ctk_progressbar',
    'customtkinter.widgets.ctk_radiobutton',
    'customtkinter.widgets.ctk_scrollbar',
    'customtkinter.widgets.ctk_scrollable_frame',
    'customtkinter.widgets.ctk_segmented_button',
    'customtkinter.widgets.ctk_slider',
    'customtkinter.widgets.ctk_switch',
    'customtkinter.widgets.ctk_tabview',
    'customtkinter.widgets.ctk_textbox',
    'customtkinter.widgets.ctk_toplevel',
    
    # Tkinter - Complete
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'tkinter.colorchooser',
    'tkinter.font',
    'tkinter.scrolledtext',
    'tkinter.constants',
    'tkinter.dnd',
    'tkinter.tix',
    
    # Web Framework - Complete
    'flask',
    'flask.app',
    'flask.blueprints',
    'flask.cli',
    'flask.config',
    'flask.ctx',
    'flask.debughelpers',
    'flask.globals',
    'flask.helpers',
    'flask.json',
    'flask.logging',
    'flask.sessions',
    'flask.signals',
    'flask.templating',
    'flask.testing',
    'flask.views',
    'flask.wrappers',
    'werkzeug',
    'werkzeug.serving',
    'werkzeug.utils',
    'werkzeug.urls',
    'werkzeug.http',
    'werkzeug.datastructures',
    'werkzeug.exceptions',
    'werkzeug.routing',
    'werkzeug.security',
    'werkzeug.wsgi',
    'jinja2',
    'jinja2.ext',
    'jinja2.loaders',
    'jinja2.runtime',
    'jinja2.compiler',
    'jinja2.environment',
    'jinja2.filters',
    'jinja2.tests',
    'jinja2.utils',
    
    # Data Processing - Complete
    'pandas',
    'pandas.core',
    'pandas.io',
    'pandas.plotting',
    'pandas.util',
    'numpy',
    'numpy.core',
    'numpy.lib',
    'numpy.random',
    'openpyxl',
    'openpyxl.workbook',
    'openpyxl.worksheet',
    'openpyxl.styles',
    'openpyxl.utils',
    'openpyxl.reader',
    'openpyxl.writer',
    
    # PDF Generation - Complete
    'fpdf',
    'fpdf.fpdf',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.pdfgen.canvas',
    'reportlab.lib',
    'reportlab.lib.colors',
    'reportlab.lib.pagesizes',
    'reportlab.lib.styles',
    'reportlab.lib.units',
    'reportlab.platypus',
    
    # Image Processing - Complete
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'PIL.ImageFont',
    'PIL.ImageDraw',
    'PIL.ImageFilter',
    'PIL.ImageEnhance',
    'PIL.ImageOps',
    'PIL.ImageChops',
    'PIL.ImageFile',
    'PIL.ImageGrab',
    'PIL.ImagePath',
    'PIL.ImageSequence',
    'PIL.ImageStat',
    'PIL.ImageTransform',
    'PIL.ImageWin',
    
    # Network and HTTP - Complete
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'requests.hooks',
    'requests.models',
    'requests.packages',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
    'urllib3',
    'urllib3.connection',
    'urllib3.connectionpool',
    'urllib3.exceptions',
    'urllib3.fields',
    'urllib3.filepost',
    'urllib3.poolmanager',
    'urllib3.request',
    'urllib3.response',
    'urllib3.util',
    'httpx',
    'aiohttp',
    
    # Cryptography - Complete
    'cryptography',
    'cryptography.fernet',
    'cryptography.hazmat',
    'cryptography.x509',
    'pycryptodome',
    'Crypto',
    'Crypto.Cipher',
    'Crypto.Hash',
    'Crypto.PublicKey',
    'Crypto.Random',
    'Crypto.Signature',
    'Crypto.Util',
    
    # System Utilities - Complete
    'psutil',
    'schedule',
    'pyperclip',
    'certifi',
    
    # Cloud Integration - Complete
    'google',
    'google.api_core',
    'google.auth',
    'google_api_python_client',
    'googleapiclient',
    'googleapiclient.discovery',
    'googleapiclient.errors',
    'dropbox',
    'boto3',
    'botocore',
    'azure',
    'azure.storage',
    'azure.storage.blob',
    
    # Compression - Complete
    'py7zr',
    'zipfile',
    'tarfile',
    'gzip',
    'bz2',
    'lzma',
    
    # Standard library modules
    'sqlite3',
    'json',
    'datetime',
    'threading',
    'webbrowser',
    'subprocess',
    'pathlib',
    'tempfile',
    'shutil',
    'hashlib',
    'base64',
    'uuid',
    'logging',
    'email',
    'email.mime',
    'email.mime.text',
    'email.mime.multipart',
    'smtplib',
    'ftplib',
    'pickle',
    'csv',
    'xml',
    'xml.etree',
    'xml.etree.ElementTree',
    'html',
    'html.parser',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'http.server',
    'socketserver',
    'socket',
    'ssl',
    'platform',
    'getpass',
    'locale',
    'calendar',
    'time',
    'math',
    'statistics',
    'random',
    'secrets',
    'collections',
    'itertools',
    'functools',
    'operator',
    'copy',
    'gc',
    'atexit',
    'signal',
    'traceback',
    'warnings',
    'contextlib',
    'weakref',
    'types',
    'inspect',
    'ast',
    'keyword',
    'token',
    'tokenize',
    'argparse',
    'configparser',
    'fileinput',
    'linecache',
    'glob',
    'fnmatch',
    'stat',
    'filecmp',
    'binascii',
    'codecs',
    'unicodedata',
    'encodings',
    'encodings.utf_8',
    'encodings.latin_1',
    'encodings.ascii',
    'encodings.cp1252',
]

# Binaries to include
binaries = []

# Modules to exclude completely (to avoid conflicts)
excludes = [
    'distutils',
    '_distutils_hack',
    'setuptools',
    'pip',
    'wheel',
    'pkg_resources',
    'matplotlib.tests',
    'numpy.tests',
    'pandas.tests',
    'PIL.tests',
    'test',
    'tests',
    'testing',
    'unittest',
    'doctest',
    'pdb',
    'profile',
    'pstats',
    'timeit',
    'trace',
]

a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=binaries,
    datas=filtered_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Remove duplicate files
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CashierFilterSystem_v3.5.0_Complete',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to True for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(current_dir, 'assets', 'icon.ico') if os.path.exists(os.path.join(current_dir, 'assets', 'icon.ico')) else None,
    version=os.path.join(current_dir, 'version_info.txt') if os.path.exists(os.path.join(current_dir, 'version_info.txt')) else None,
)
