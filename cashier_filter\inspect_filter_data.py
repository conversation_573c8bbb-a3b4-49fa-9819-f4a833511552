#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص البيانات الخام للتصفيات
Inspect Raw Filter Data
"""

import sqlite3
import json
from pathlib import Path

DB_PATH = Path(__file__).parent / "db" / "cashier_filter.db"

def inspect_filter_data():
    """فحص البيانات الخام للتصفيات"""
    print("🔍 فحص البيانات الخام للتصفيات...")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # جلب آخر تصفية لها بيانات
        cursor.execute("""
            SELECT id, date, data, admin_name, sequence_number
            FROM filters 
            WHERE data IS NOT NULL AND data != ''
            ORDER BY id DESC 
            LIMIT 1
        """)
        
        filter_row = cursor.fetchone()
        
        if not filter_row:
            print("❌ لا توجد تصفيات بها بيانات")
            return
        
        print(f"📋 فحص التصفية #{filter_row['id']} (تسلسل: {filter_row['sequence_number']})")
        print(f"📅 التاريخ: {filter_row['date']}")
        print(f"👤 المسؤول: {filter_row['admin_name']}")
        print()
        
        # تحليل بيانات JSON
        try:
            data = json.loads(filter_row['data'])
            print("📊 بنية البيانات:")
            print("=" * 40)
            
            # طباعة المفاتيح الرئيسية
            for key in data.keys():
                print(f"🔑 {key}: {type(data[key])}")
            
            print("\n💰 تفاصيل المجاميع:")
            print("=" * 40)
            if 'totals' in data:
                totals = data['totals']
                for key, value in totals.items():
                    print(f"   {key}: {value}")
            
            print("\n🏦 المعاملات البنكية:")
            print("=" * 40)
            if 'bank_transactions' in data:
                bank_transactions = data['bank_transactions']
                print(f"عدد المعاملات: {len(bank_transactions)}")
                
                for i, transaction in enumerate(bank_transactions, 1):
                    print(f"\n  معاملة #{i}:")
                    for key, value in transaction.items():
                        print(f"    {key}: {value}")
            
            print("\n💵 تفاصيل النقدي:")
            print("=" * 40)
            if 'cash_details' in data:
                cash_details = data['cash_details']
                print(f"عدد الفئات: {len(cash_details)}")
                
                for denomination, details in cash_details.items():
                    print(f"  {denomination}: {details}")
            
            print("\n📋 بيانات إضافية:")
            print("=" * 40)
            additional_keys = ['credit_details', 'client_details', 'return_details', 
                             'expenses', 'adjustments', 'invoice_details']
            
            for key in additional_keys:
                if key in data:
                    print(f"✅ {key}: {type(data[key])} - {len(data[key]) if isinstance(data[key], (list, dict)) else 'N/A'}")
                    
                    # عرض عينة من البيانات
                    if isinstance(data[key], list) and len(data[key]) > 0:
                        print(f"   عينة: {data[key][0]}")
                    elif isinstance(data[key], dict) and len(data[key]) > 0:
                        first_key = list(data[key].keys())[0]
                        print(f"   عينة: {first_key}: {data[key][first_key]}")
                else:
                    print(f"❌ {key}: غير موجود")
            
            # البحث عن أسماء العملاء
            print("\n👥 البحث عن أسماء العملاء:")
            print("=" * 40)
            
            customer_fields = []
            
            def find_customer_names(obj, path=""):
                """البحث عن أسماء العملاء في البيانات"""
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        if 'customer' in key.lower() or 'client' in key.lower() or 'name' in key.lower():
                            customer_fields.append(f"{current_path}: {value}")
                        find_customer_names(value, current_path)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        find_customer_names(item, f"{path}[{i}]")
            
            find_customer_names(data)
            
            if customer_fields:
                print("🔍 حقول العملاء الموجودة:")
                for field in customer_fields:
                    print(f"   {field}")
            else:
                print("⚠️ لم يتم العثور على أسماء عملاء في البيانات")
            
            # البحث عن الفارق
            print("\n⚖️ البحث عن الفارق:")
            print("=" * 40)
            
            variance_fields = []
            
            def find_variance_fields(obj, path=""):
                """البحث عن حقول الفارق"""
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        current_path = f"{path}.{key}" if path else key
                        if any(word in key.lower() for word in ['variance', 'difference', 'فارق', 'فرق']):
                            variance_fields.append(f"{current_path}: {value}")
                        find_variance_fields(value, current_path)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        find_variance_fields(item, f"{path}[{i}]")
            
            find_variance_fields(data)
            
            if variance_fields:
                print("🔍 حقول الفارق الموجودة:")
                for field in variance_fields:
                    print(f"   {field}")
            else:
                print("⚠️ لم يتم العثور على حقول الفارق في البيانات")
            
        except json.JSONDecodeError as e:
            print(f"❌ خطأ في تحليل JSON: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {e}")

def suggest_data_structure():
    """اقتراح بنية البيانات المطلوبة"""
    print("\n💡 اقتراح بنية البيانات المطلوبة:")
    print("=" * 60)
    
    suggested_structure = {
        "totals": {
            "bank": 0,
            "cash": 0,
            "credit": 0,
            "client": 0,
            "return": 0
        },
        "bank_transactions": [
            {
                "amount": 500.0,
                "card_type": "فيزا",
                "last_four": "1234",
                "reference": "REF001",
                "time": "10:30"
            }
        ],
        "cash_details": {
            "500": {"count": 5, "total": 2500.0},
            "200": {"count": 3, "total": 600.0}
        },
        "credit_details": [
            {
                "invoice_number": "INV001",
                "customer_name": "أحمد محمد",
                "amount": 1000.0,
                "due_date": "2025-08-09",
                "phone": "**********"
            }
        ],
        "client_details": [
            {
                "customer_name": "سارة أحمد",
                "invoice_number": "INV002",
                "amount": 750.0,
                "payment_method": "cash",
                "notes": "سداد فاتورة سابقة"
            }
        ],
        "return_details": [
            {
                "return_invoice": "RET001",
                "original_invoice": "INV003",
                "customer_name": "محمد علي",
                "reason": "عيب في المنتج",
                "amount": 200.0,
                "date": "2025-07-09"
            }
        ],
        "variance": {
            "expected_total": 10000.0,
            "actual_total": 9950.0,
            "difference": -50.0,
            "reason": "فارق في العد"
        }
    }
    
    print("📋 البنية المقترحة:")
    print(json.dumps(suggested_structure, ensure_ascii=False, indent=2))

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص بيانات التصفيات لإضافة أسماء العملاء والفارق")
    print("=" * 70)
    
    # فحص البيانات الحالية
    inspect_filter_data()
    
    # اقتراح بنية البيانات
    suggest_data_structure()
    
    print("\n" + "=" * 70)
    print("✅ انتهى الفحص")
    print("💡 استخدم هذه المعلومات لتحسين عرض البيانات في التقارير")

if __name__ == "__main__":
    main()
