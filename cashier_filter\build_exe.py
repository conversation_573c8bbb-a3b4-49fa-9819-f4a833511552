#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for creating EXE with PyInstaller
سكريبت بناء الملف التنفيذي باستخدام PyInstaller
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_banner():
    """Print build banner"""
    print("=" * 70)
    print("🔨 Cashier Filter System - EXE Builder")
    print("   نظام تصفية الكاشير - منشئ الملف التنفيذي")
    print("=" * 70)

def check_requirements():
    """Check if all requirements are installed"""
    print("📦 Checking requirements...")
    
    required_packages = [
        'pyinstaller',
        'customtkinter',
        'flask',
        'pandas',
        'fpdf2',
        'PIL',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package} - Missing")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All requirements satisfied")
    return True

def clean_build_dirs():
    """Clean previous build directories"""
    print("🧹 Cleaning previous builds...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   ✅ Removed {dir_name}")
            except Exception as e:
                print(f"   ⚠️ Could not remove {dir_name}: {e}")
    
    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass

def verify_files():
    """Verify that all necessary files exist"""
    print("📁 Verifying files...")
    
    required_files = [
        'main.py',
        'config.py',
        'pyinstaller_utils.py',
        'CashierFilterSystem_Fixed.spec'
    ]
    
    required_dirs = [
        'ui',
        'db',
        'reports',
        'utils',
        'web_templates',
        'web_static'
    ]
    
    missing_items = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_items.append(f"File: {file}")
        else:
            print(f"   ✅ {file}")
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_items.append(f"Directory: {dir_name}")
        else:
            print(f"   ✅ {dir_name}/")
    
    if missing_items:
        print(f"\n❌ Missing items:")
        for item in missing_items:
            print(f"   - {item}")
        return False
    
    print("✅ All files verified")
    return True

def build_exe():
    """Build the EXE file using PyInstaller"""
    print("🔨 Building EXE with PyInstaller...")
    
    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'CashierFilterSystem_Fixed.spec'
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print("This may take several minutes...")
    
    start_time = time.time()
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        end_time = time.time()
        build_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ Build completed successfully in {build_time:.1f} seconds")
            
            # Check if EXE was created
            exe_path = os.path.join('dist', 'CashierFilterSystem_v3.5.0_Fixed.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📦 EXE created: {exe_path}")
                print(f"📏 File size: {file_size:.1f} MB")
                return True
            else:
                print("❌ EXE file not found in dist directory")
                return False
        else:
            print("❌ Build failed")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_distribution_package():
    """Create a distribution package with the EXE and necessary files"""
    print("📦 Creating distribution package...")
    
    exe_path = os.path.join('dist', 'CashierFilterSystem_v3.5.0_Fixed.exe')
    if not os.path.exists(exe_path):
        print("❌ EXE file not found")
        return False
    
    # Create distribution directory
    dist_dir = 'CashierFilterSystem_Distribution'
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    
    # Copy EXE
    shutil.copy2(exe_path, dist_dir)
    
    # Copy essential files
    essential_files = [
        'README.md',
        'requirements.txt',
        'version_info.txt'
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
    
    # Create a sample database directory
    db_dir = os.path.join(dist_dir, 'db')
    os.makedirs(db_dir, exist_ok=True)
    
    # Copy database if it exists
    if os.path.exists(os.path.join('db', 'cashier_filter.db')):
        shutil.copy2(os.path.join('db', 'cashier_filter.db'), db_dir)
    
    # Create startup batch file
    batch_content = '''@echo off
echo Starting Cashier Filter System...
echo نظام تصفية الكاشير
echo.
CashierFilterSystem_v3.5.0_Fixed.exe
pause
'''
    
    with open(os.path.join(dist_dir, 'Start_CashierFilterSystem.bat'), 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    # Create README for distribution
    readme_content = '''# Cashier Filter System - Distribution Package
# نظام تصفية الكاشير - حزمة التوزيع

## How to Run / كيفية التشغيل:

1. Double-click on "Start_CashierFilterSystem.bat"
   أو انقر نقراً مزدوجاً على "Start_CashierFilterSystem.bat"

2. Or run "CashierFilterSystem_v3.5.0_Fixed.exe" directly
   أو شغل "CashierFilterSystem_v3.5.0_Fixed.exe" مباشرة

## Login Credentials / بيانات تسجيل الدخول:
- Username / اسم المستخدم: admin
- Password / كلمة المرور: 123456

## System Requirements / متطلبات النظام:
- Windows 10/11 (64-bit)
- 4GB RAM minimum
- 500MB free disk space

## Support / الدعم:
- Email: <EMAIL>

## Version / الإصدار: 3.5.0
'''
    
    with open(os.path.join(dist_dir, 'README_Distribution.txt'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Distribution package created: {dist_dir}")
    return True

def main():
    """Main build process"""
    print_banner()
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Step 1: Check requirements
    if not check_requirements():
        print("❌ Build aborted due to missing requirements")
        return False
    
    # Step 2: Verify files
    if not verify_files():
        print("❌ Build aborted due to missing files")
        return False
    
    # Step 3: Clean previous builds
    clean_build_dirs()
    
    # Step 4: Build EXE
    if not build_exe():
        print("❌ Build failed")
        return False
    
    # Step 5: Create distribution package
    if not create_distribution_package():
        print("❌ Failed to create distribution package")
        return False
    
    print("\n🎉 Build completed successfully!")
    print("📦 Your EXE is ready in the 'CashierFilterSystem_Distribution' folder")
    print("🚀 You can now distribute this folder to users")
    
    return True

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 70)
    if success:
        print("✅ BUILD SUCCESSFUL")
    else:
        print("❌ BUILD FAILED")
    print("=" * 70)
    
    input("\nPress Enter to exit...")
