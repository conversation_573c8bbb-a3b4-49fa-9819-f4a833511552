# شاشة إدخال بيانات التصفية اليومية - محسنة
import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class FilterEntryWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("إدخال بيانات التصفية اليومية")
        self.geometry("900x750")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        # جعل النافذة تظهر في المقدمة
        self.after(100, self.setup_window_focus)

        self.create_widgets()
        self.load_data()

    def setup_window_focus(self):
        """إعداد النافذة لتظهر في المقدمة"""
        try:
            self.deiconify()
            self.lift()
            self.attributes('-topmost', True)
            self.focus_force()
            try:
                self.wm_state('normal')
                self.tkraise()
            except:
                pass
            def remove_topmost():
                try:
                    self.attributes('-topmost', False)
                    self.focus_set()
                except:
                    pass
            self.after(300, remove_topmost)
        except Exception as e:
            print(f"خطأ في إعداد تركيز النافذة: {e}")

    def create_widgets(self):
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=25)
        title_frame.pack(pady=20, padx=20, fill="x")

        title = ctk.CTkLabel(
            title_frame,
            text="📋 إدخال بيانات التصفية اليومية",
            font=("Arial", 28, "bold"),
            text_color="#2c3e50"
        )
        title.pack(pady=20)

        # إطار الإدخال الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        main_frame.pack(pady=20, padx=20, fill="both", expand=True)

        # قسم بيانات الكاشير
        cashier_section = ctk.CTkFrame(main_frame, fg_color="#f2f3f7", corner_radius=15)
        cashier_section.pack(pady=15, padx=20, fill="x")

        cashier_title = ctk.CTkLabel(
            cashier_section,
            text="👤 بيانات الكاشير",
            font=("Arial", 18, "bold"),
            text_color="#34495e"
        )
        cashier_title.pack(pady=10)

        # إطار حقول الكاشير
        cashier_fields = ctk.CTkFrame(cashier_section, fg_color="#f2f3f7", corner_radius=0)
        cashier_fields.pack(pady=10, padx=20, fill="x")

        # اسم الكاشير مع قائمة منسدلة
        cashier_name_label = ctk.CTkLabel(cashier_fields, text="اسم الكاشير:", font=("Arial", 14, "bold"))
        cashier_name_label.grid(row=0, column=0, sticky="e", padx=10, pady=10)

        self.cashier_name_combo = ctk.CTkComboBox(
            cashier_fields,
            width=250,
            font=("Arial", 12),
            command=self.on_cashier_select,
            values=["اختر الكاشير..."]
        )
        self.cashier_name_combo.set("اختر الكاشير...")
        self.cashier_name_combo.grid(row=0, column=1, padx=10, pady=10)

        # رقم الكاشير (يتم ملؤه تلقائياً)
        cashier_id_label = ctk.CTkLabel(cashier_fields, text="رقم الكاشير:", font=("Arial", 14, "bold"))
        cashier_id_label.grid(row=0, column=2, sticky="e", padx=10, pady=10)

        self.cashier_id_entry = ctk.CTkEntry(
            cashier_fields,
            width=150,
            font=("Arial", 12),
            state="readonly"
        )
        self.cashier_id_entry.grid(row=0, column=3, padx=10, pady=10)

        # قسم بيانات المسؤول
        admin_section = ctk.CTkFrame(main_frame, fg_color="#f2f3f7", corner_radius=15)
        admin_section.pack(pady=15, padx=20, fill="x")

        admin_title = ctk.CTkLabel(
            admin_section,
            text="🧑‍💼 بيانات المسؤول",
            font=("Arial", 18, "bold"),
            text_color="#34495e"
        )
        admin_title.pack(pady=10)

        # إطار حقول المسؤول
        admin_fields = ctk.CTkFrame(admin_section, fg_color="#f2f3f7", corner_radius=0)
        admin_fields.pack(pady=10, padx=20, fill="x")

        # اسم المسؤول مع قائمة منسدلة
        admin_name_label = ctk.CTkLabel(admin_fields, text="اسم المسؤول:", font=("Arial", 14, "bold"))
        admin_name_label.grid(row=0, column=0, sticky="e", padx=10, pady=10)

        self.admin_name_combo = ctk.CTkComboBox(
            admin_fields,
            width=250,
            font=("Arial", 12),
            values=["اختر المسؤول..."]
        )
        self.admin_name_combo.set("اختر المسؤول...")
        self.admin_name_combo.grid(row=0, column=1, padx=10, pady=10)

        # تاريخ التصفية
        date_label = ctk.CTkLabel(admin_fields, text="تاريخ التصفية:", font=("Arial", 14, "bold"))
        date_label.grid(row=0, column=2, sticky="e", padx=10, pady=10)

        self.date_entry = ctk.CTkEntry(
            admin_fields,
            width=150,
            font=("Arial", 12)
        )
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        self.date_entry.grid(row=0, column=3, padx=10, pady=10)

        # قسم الملاحظات
        notes_section = ctk.CTkFrame(main_frame, fg_color="#f2f3f7", corner_radius=15)
        notes_section.pack(pady=15, padx=20, fill="x")

        notes_title = ctk.CTkLabel(
            notes_section,
            text="📝 ملاحظات إضافية (اختياري)",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        notes_title.pack(pady=10)

        self.notes_text = ctk.CTkTextbox(
            notes_section,
            height=80,
            font=("Arial", 12),
            corner_radius=10
        )
        self.notes_text.pack(pady=10, padx=20, fill="x")

        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="#e0e5ec", corner_radius=0)
        buttons_frame.pack(pady=20)

        # زر بدء التصفية
        start_btn = ctk.CTkButton(
            buttons_frame,
            text="🚀 ابدأ التصفية",
            font=("Arial", 18, "bold"),
            corner_radius=15,
            fg_color="#4CAF50",
            hover_color="#45a049",
            text_color="white",
            width=200,
            height=50,
            command=self.start_filter
        )
        start_btn.pack(side="left", padx=10)

        # زر إلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            font=("Arial", 18, "bold"),
            corner_radius=15,
            fg_color="#f44336",
            hover_color="#d32f2f",
            text_color="white",
            width=150,
            height=50,
            command=self.destroy
        )
        cancel_btn.pack(side="left", padx=10)

    def load_data(self):
        """تحميل بيانات الكاشيرين والمسؤولين من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # تحميل الكاشيرين
            c.execute("SELECT name, number FROM cashiers ORDER BY name")
            cashiers = c.fetchall()
            cashier_names = ["اختر الكاشير..."] + [f"{name} ({number})" for name, number in cashiers]
            self.cashier_name_combo.configure(values=cashier_names)

            # تحميل المسؤولين
            c.execute("SELECT name FROM admins ORDER BY name")
            admins = c.fetchall()
            admin_names = ["اختر المسؤول..."] + [name[0] for name in admins]
            self.admin_name_combo.configure(values=admin_names)

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {e}")

    def on_cashier_select(self, choice):
        """عند اختيار كاشير من القائمة المنسدلة"""
        if choice and "(" in choice and choice != "اختر الكاشير...":
            # استخراج رقم الكاشير من النص
            cashier_number = choice.split("(")[1].split(")")[0]
            self.cashier_id_entry.configure(state="normal")
            self.cashier_id_entry.delete(0, "end")
            self.cashier_id_entry.insert(0, cashier_number)
            self.cashier_id_entry.configure(state="readonly")
        elif choice == "اختر الكاشير...":
            # مسح رقم الكاشير إذا تم اختيار القيمة الافتراضية
            self.cashier_id_entry.configure(state="normal")
            self.cashier_id_entry.delete(0, "end")
            self.cashier_id_entry.configure(state="readonly")

    def validate_inputs(self):
        """التحقق من صحة البيانات المدخلة"""
        # التحقق من اختيار الكاشير
        cashier_name = self.cashier_name_combo.get().strip()
        admin_name = self.admin_name_combo.get().strip()
        date = self.date_entry.get().strip()



        if not cashier_name or cashier_name == "" or cashier_name == "اختر الكاشير...":
            messagebox.showwarning("تحذير", "يرجى اختيار اسم الكاشير من القائمة المنسدلة\nتأكد من النقر على اسم الكاشير المطلوب")
            self.cashier_name_combo.focus()
            return False

        if not admin_name or admin_name == "" or admin_name == "اختر المسؤول...":
            messagebox.showwarning("تحذير", "يرجى اختيار اسم المسؤول من القائمة المنسدلة\nتأكد من النقر على اسم المسؤول المطلوب")
            self.admin_name_combo.focus()
            return False

        if not date:
            messagebox.showwarning("تحذير", "يرجى إدخال تاريخ التصفية")
            self.date_entry.focus()
            return False

        # التحقق من صيغة التاريخ
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            messagebox.showerror("خطأ", "صيغة التاريخ غير صحيحة. يرجى استخدام الصيغة: YYYY-MM-DD")
            self.date_entry.focus()
            return False

        return True

    def start_filter(self):
        """بدء التصفية اليومية"""
        if not self.validate_inputs():
            return

        # جمع البيانات
        cashier_full_name = self.cashier_name_combo.get()
        cashier_name = cashier_full_name.split(" (")[0] if " (" in cashier_full_name else cashier_full_name
        cashier_id = self.cashier_id_entry.get()
        admin_name = self.admin_name_combo.get()
        date = self.date_entry.get()
        notes = self.notes_text.get("1.0", "end-1c").strip()

        filter_data = {
            "cashier_name": cashier_name,
            "cashier_id": cashier_id,
            "admin_name": admin_name,
            "date": date,
            "notes": notes
        }

        try:
            # فتح شاشة التصفية اليومية
            from ui.daily_filter import DailyFilterWindow
            DailyFilterWindow(self.master, filter_data=filter_data)
            self.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في فتح شاشة التصفية: {e}")
