#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إحصائيات المستخدمين
User Statistics Window
"""

import customtkinter as ctk
from tkinter import messagebox, ttk
import tkinter as tk
import sqlite3
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.operations_logger import operations_logger
    from utils.font_manager import get_stable_font
except ImportError:
    def get_stable_font(size_type="normal", weight="normal"):
        sizes = {"small": 12, "normal": 14, "medium": 16, "large": 18}
        size = sizes.get(size_type, 14)
        return ("Arial", size, weight)

# تحديد مسار قاعدة البيانات
if os.path.exists("db/cashier_filter.db"):
    DB_PATH = "db/cashier_filter.db"
else:
    DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class UserStatisticsWindow(ctk.CTkToplevel):
    """نافذة إحصائيات المستخدمين"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title("📊 إحصائيات المستخدمين")
        self.geometry("1000x700")
        self.transient(parent)
        self.grab_set()
        
        # تعيين أيقونة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
        
        self.create_widgets()
        self.load_statistics()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="#f8f9fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="📊 إحصائيات المستخدمين والأنشطة",
            font=get_stable_font("large", "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)
        
        # إطار الفلاتر
        self.create_filters_frame(main_frame)
        
        # إطار الإحصائيات العامة
        self.create_general_stats_frame(main_frame)
        
        # إطار الجداول
        self.create_tables_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_filters_frame(self, parent):
        """إنشاء إطار الفلاتر"""
        filters_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        filters_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الفلاتر
        filters_title = ctk.CTkLabel(
            filters_frame,
            text="📅 فترة الإحصائيات",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        filters_title.pack(pady=(10, 5))
        
        # إطار الفلاتر الأفقي
        filters_container = ctk.CTkFrame(filters_frame, fg_color="transparent")
        filters_container.pack(fill="x", padx=20, pady=(0, 15))
        
        # فلتر الفترة
        period_label = ctk.CTkLabel(filters_container, text="📅 الفترة:", font=get_stable_font("normal"))
        period_label.pack(side="left", padx=(0, 10))
        
        self.period_var = ctk.StringVar(value="آخر 30 يوم")
        self.period_combo = ctk.CTkComboBox(
            filters_container, variable=self.period_var, width=200,
            values=["آخر 7 أيام", "آخر 30 يوم", "آخر 90 يوم", "آخر سنة", "الكل"]
        )
        self.period_combo.pack(side="left", padx=(0, 20))
        
        # زر التحديث
        refresh_btn = ctk.CTkButton(
            filters_container, text="🔄 تحديث الإحصائيات", width=150,
            command=self.load_statistics,
            fg_color="#007bff", hover_color="#0056b3"
        )
        refresh_btn.pack(side="left")
    
    def create_general_stats_frame(self, parent):
        """إنشاء إطار الإحصائيات العامة"""
        stats_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        stats_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الإحصائيات
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 الإحصائيات العامة",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        stats_title.pack(pady=(10, 5))
        
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # بطاقات الإحصائيات
        self.total_users_card = self.create_stat_card(cards_frame, "👥", "إجمالي المستخدمين", "0", "#007bff")
        self.active_users_card = self.create_stat_card(cards_frame, "🟢", "المستخدمين النشطين", "0", "#28a745")
        self.total_operations_card = self.create_stat_card(cards_frame, "📝", "إجمالي العمليات", "0", "#6f42c1")
        self.success_rate_card = self.create_stat_card(cards_frame, "✅", "معدل النجاح", "0%", "#17a2b8")
    
    def create_stat_card(self, parent, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=15, width=200, height=100)
        card.pack(side="left", padx=10, fill="y")
        card.pack_propagate(False)
        
        # الأيقونة
        icon_label = ctk.CTkLabel(card, text=icon, font=("Arial", 24), text_color="white")
        icon_label.pack(pady=(10, 0))
        
        # القيمة
        value_label = ctk.CTkLabel(card, text=value, font=get_stable_font("large", "bold"), text_color="white")
        value_label.pack()
        
        # العنوان
        title_label = ctk.CTkLabel(card, text=title, font=get_stable_font("small"), text_color="white")
        title_label.pack(pady=(0, 10))
        
        return {"card": card, "value": value_label, "title": title_label}
    
    def create_tables_frame(self, parent):
        """إنشاء إطار الجداول"""
        tables_frame = ctk.CTkFrame(parent, fg_color="transparent")
        tables_frame.pack(fill="both", expand=True, padx=10)
        
        # تكوين الشبكة
        tables_frame.grid_columnconfigure(0, weight=1)
        tables_frame.grid_columnconfigure(1, weight=1)
        tables_frame.grid_rowconfigure(0, weight=1)
        tables_frame.grid_rowconfigure(1, weight=1)
        
        # جدول أكثر المستخدمين نشاطاً
        self.create_active_users_table(tables_frame)
        
        # جدول أنواع العمليات
        self.create_operations_types_table(tables_frame)
        
        # جدول النشاط اليومي
        self.create_daily_activity_table(tables_frame)
        
        # جدول النشاط الساعي
        self.create_hourly_activity_table(tables_frame)
    
    def create_active_users_table(self, parent):
        """إنشاء جدول أكثر المستخدمين نشاطاً"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="👥 أكثر المستخدمين نشاطاً", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("المستخدم", "عدد العمليات", "آخر نشاط")
        self.active_users_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.active_users_tree.heading("المستخدم", text="👤 المستخدم")
        self.active_users_tree.heading("عدد العمليات", text="📝 عدد العمليات")
        self.active_users_tree.heading("آخر نشاط", text="🕐 آخر نشاط")
        
        self.active_users_tree.column("المستخدم", width=120, anchor="center")
        self.active_users_tree.column("عدد العمليات", width=100, anchor="center")
        self.active_users_tree.column("آخر نشاط", width=120, anchor="center")
        
        scrollbar1 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.active_users_tree.yview)
        self.active_users_tree.configure(yscrollcommand=scrollbar1.set)
        
        self.active_users_tree.pack(side="left", fill="both", expand=True)
        scrollbar1.pack(side="right", fill="y")
    
    def create_operations_types_table(self, parent):
        """إنشاء جدول أنواع العمليات"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="🔧 أنواع العمليات", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("نوع العملية", "العدد", "النسبة")
        self.operations_types_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.operations_types_tree.heading("نوع العملية", text="🔧 نوع العملية")
        self.operations_types_tree.heading("العدد", text="📊 العدد")
        self.operations_types_tree.heading("النسبة", text="📈 النسبة")
        
        self.operations_types_tree.column("نوع العملية", width=120, anchor="center")
        self.operations_types_tree.column("العدد", width=80, anchor="center")
        self.operations_types_tree.column("النسبة", width=80, anchor="center")
        
        scrollbar2 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.operations_types_tree.yview)
        self.operations_types_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.operations_types_tree.pack(side="left", fill="both", expand=True)
        scrollbar2.pack(side="right", fill="y")
    
    def create_daily_activity_table(self, parent):
        """إنشاء جدول النشاط اليومي"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="📅 النشاط اليومي", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("التاريخ", "عدد العمليات", "المستخدمين النشطين")
        self.daily_activity_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.daily_activity_tree.heading("التاريخ", text="📅 التاريخ")
        self.daily_activity_tree.heading("عدد العمليات", text="📝 عدد العمليات")
        self.daily_activity_tree.heading("المستخدمين النشطين", text="👥 المستخدمين النشطين")
        
        self.daily_activity_tree.column("التاريخ", width=100, anchor="center")
        self.daily_activity_tree.column("عدد العمليات", width=100, anchor="center")
        self.daily_activity_tree.column("المستخدمين النشطين", width=120, anchor="center")
        
        scrollbar3 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.daily_activity_tree.yview)
        self.daily_activity_tree.configure(yscrollcommand=scrollbar3.set)
        
        self.daily_activity_tree.pack(side="left", fill="both", expand=True)
        scrollbar3.pack(side="right", fill="y")
    
    def create_hourly_activity_table(self, parent):
        """إنشاء جدول النشاط الساعي"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="🕐 النشاط الساعي", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("الساعة", "عدد العمليات", "النسبة")
        self.hourly_activity_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=8)
        
        self.hourly_activity_tree.heading("الساعة", text="🕐 الساعة")
        self.hourly_activity_tree.heading("عدد العمليات", text="📝 عدد العمليات")
        self.hourly_activity_tree.heading("النسبة", text="📈 النسبة")
        
        self.hourly_activity_tree.column("الساعة", width=80, anchor="center")
        self.hourly_activity_tree.column("عدد العمليات", width=100, anchor="center")
        self.hourly_activity_tree.column("النسبة", width=80, anchor="center")
        
        scrollbar4 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.hourly_activity_tree.yview)
        self.hourly_activity_tree.configure(yscrollcommand=scrollbar4.set)
        
        self.hourly_activity_tree.pack(side="left", fill="both", expand=True)
        scrollbar4.pack(side="right", fill="y")
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=10)
        
        # زر تصدير
        export_btn = ctk.CTkButton(
            buttons_frame, text="📤 تصدير الإحصائيات", width=150,
            command=self.export_statistics,
            fg_color="#17a2b8", hover_color="#138496"
        )
        export_btn.pack(side="left", padx=5)
        
        # زر طباعة
        print_btn = ctk.CTkButton(
            buttons_frame, text="🖨️ طباعة", width=120,
            command=self.print_statistics,
            fg_color="#6f42c1", hover_color="#5a32a3"
        )
        print_btn.pack(side="left", padx=5)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame, text="❌ إغلاق", width=120,
            command=self.destroy,
            fg_color="#dc3545", hover_color="#c82333"
        )
        close_btn.pack(side="right", padx=5)

    def get_period_days(self):
        """الحصول على عدد الأيام حسب الفترة المختارة"""
        period = self.period_var.get()
        if period == "آخر 7 أيام":
            return 7
        elif period == "آخر 30 يوم":
            return 30
        elif period == "آخر 90 يوم":
            return 90
        elif period == "آخر سنة":
            return 365
        else:  # الكل
            return None

    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            days = self.get_period_days()

            # تحميل الإحصائيات العامة
            self.load_general_statistics(days)

            # تحميل جداول البيانات
            self.load_active_users(days)
            self.load_operations_types(days)
            self.load_daily_activity(days)
            self.load_hourly_activity(days)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإحصائيات: {e}")

    def load_general_statistics(self, days):
        """تحميل الإحصائيات العامة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # إجمالي المستخدمين
            c.execute("SELECT COUNT(DISTINCT username) FROM admins")
            total_users = c.fetchone()[0]
            self.total_users_card["value"].configure(text=str(total_users))

            # المستخدمين النشطين
            if days:
                c.execute('''
                    SELECT COUNT(DISTINCT username) FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                '''.format(days))
            else:
                c.execute("SELECT COUNT(DISTINCT username) FROM operations_log")
            active_users = c.fetchone()[0]
            self.active_users_card["value"].configure(text=str(active_users))

            # إجمالي العمليات
            if days:
                c.execute('''
                    SELECT COUNT(*) FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                '''.format(days))
            else:
                c.execute("SELECT COUNT(*) FROM operations_log")
            total_operations = c.fetchone()[0]
            self.total_operations_card["value"].configure(text=str(total_operations))

            # معدل النجاح
            if days:
                c.execute('''
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
                    FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                '''.format(days))
            else:
                c.execute('''
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
                    FROM operations_log
                ''')

            result = c.fetchone()
            if result[0] > 0:
                success_rate = (result[1] / result[0]) * 100
                self.success_rate_card["value"].configure(text=f"{success_rate:.1f}%")
            else:
                self.success_rate_card["value"].configure(text="0%")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات العامة: {e}")

    def load_active_users(self, days):
        """تحميل أكثر المستخدمين نشاطاً"""
        try:
            # مسح الجدول
            for item in self.active_users_tree.get_children():
                self.active_users_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            if days:
                c.execute('''
                    SELECT
                        username,
                        COUNT(*) as operations_count,
                        MAX(timestamp) as last_activity
                    FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                    GROUP BY username
                    ORDER BY operations_count DESC
                    LIMIT 10
                '''.format(days))
            else:
                c.execute('''
                    SELECT
                        username,
                        COUNT(*) as operations_count,
                        MAX(timestamp) as last_activity
                    FROM operations_log
                    GROUP BY username
                    ORDER BY operations_count DESC
                    LIMIT 10
                ''')

            results = c.fetchall()

            for username, count, last_activity in results:
                # تنسيق آخر نشاط
                try:
                    last_activity_dt = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                    formatted_last_activity = last_activity_dt.strftime('%Y-%m-%d')
                except:
                    formatted_last_activity = last_activity[:10] if last_activity else "غير محدد"

                self.active_users_tree.insert("", "end", values=(
                    username, count, formatted_last_activity
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المستخدمين النشطين: {e}")

    def load_operations_types(self, days):
        """تحميل أنواع العمليات"""
        try:
            # مسح الجدول
            for item in self.operations_types_tree.get_children():
                self.operations_types_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            if days:
                c.execute('''
                    SELECT operation_type, COUNT(*) as count
                    FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                    GROUP BY operation_type
                    ORDER BY count DESC
                '''.format(days))
            else:
                c.execute('''
                    SELECT operation_type, COUNT(*) as count
                    FROM operations_log
                    GROUP BY operation_type
                    ORDER BY count DESC
                ''')

            results = c.fetchall()
            total_operations = sum(count for _, count in results)

            # ترجمة أنواع العمليات
            operation_names = {
                'AUTH': 'المصادقة',
                'FILTER': 'التصفية',
                'REPORT': 'التقارير',
                'ADMIN': 'الإدارة',
                'SYSTEM': 'النظام'
            }

            for operation_type, count in results:
                percentage = (count / total_operations * 100) if total_operations > 0 else 0
                operation_name = operation_names.get(operation_type, operation_type)

                self.operations_types_tree.insert("", "end", values=(
                    operation_name, count, f"{percentage:.1f}%"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل أنواع العمليات: {e}")

    def load_daily_activity(self, days):
        """تحميل النشاط اليومي"""
        try:
            # مسح الجدول
            for item in self.daily_activity_tree.get_children():
                self.daily_activity_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            if days:
                c.execute('''
                    SELECT
                        DATE(timestamp) as date,
                        COUNT(*) as operations_count,
                        COUNT(DISTINCT username) as active_users
                    FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC
                    LIMIT 15
                '''.format(days))
            else:
                c.execute('''
                    SELECT
                        DATE(timestamp) as date,
                        COUNT(*) as operations_count,
                        COUNT(DISTINCT username) as active_users
                    FROM operations_log
                    GROUP BY DATE(timestamp)
                    ORDER BY date DESC
                    LIMIT 15
                ''')

            results = c.fetchall()

            for date, operations_count, active_users in results:
                self.daily_activity_tree.insert("", "end", values=(
                    date, operations_count, active_users
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل النشاط اليومي: {e}")

    def load_hourly_activity(self, days):
        """تحميل النشاط الساعي"""
        try:
            # مسح الجدول
            for item in self.hourly_activity_tree.get_children():
                self.hourly_activity_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            if days:
                c.execute('''
                    SELECT
                        strftime('%H', timestamp) as hour,
                        COUNT(*) as count
                    FROM operations_log
                    WHERE timestamp >= datetime('now', '-{} days')
                    GROUP BY strftime('%H', timestamp)
                    ORDER BY hour
                '''.format(days))
            else:
                c.execute('''
                    SELECT
                        strftime('%H', timestamp) as hour,
                        COUNT(*) as count
                    FROM operations_log
                    GROUP BY strftime('%H', timestamp)
                    ORDER BY hour
                ''')

            results = c.fetchall()
            total_operations = sum(count for _, count in results)

            for hour, count in results:
                percentage = (count / total_operations * 100) if total_operations > 0 else 0
                hour_display = f"{hour}:00"

                self.hourly_activity_tree.insert("", "end", values=(
                    hour_display, count, f"{percentage:.1f}%"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل النشاط الساعي: {e}")

    def export_statistics(self):
        """تصدير الإحصائيات"""
        try:
            from tkinter import filedialog
            import csv

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ إحصائيات المستخدمين"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة الإحصائيات العامة
                    writer.writerow(['إحصائيات المستخدمين'])
                    writer.writerow(['الفترة:', self.period_var.get()])
                    writer.writerow(['تاريخ التصدير:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                    writer.writerow([])

                    # الإحصائيات العامة
                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي المستخدمين', self.total_users_card["value"].cget("text")])
                    writer.writerow(['المستخدمين النشطين', self.active_users_card["value"].cget("text")])
                    writer.writerow(['إجمالي العمليات', self.total_operations_card["value"].cget("text")])
                    writer.writerow(['معدل النجاح', self.success_rate_card["value"].cget("text")])
                    writer.writerow([])

                    # أكثر المستخدمين نشاطاً
                    writer.writerow(['أكثر المستخدمين نشاطاً'])
                    writer.writerow(['المستخدم', 'عدد العمليات', 'آخر نشاط'])
                    for item in self.active_users_tree.get_children():
                        values = self.active_users_tree.item(item)['values']
                        writer.writerow(values)
                    writer.writerow([])

                    # أنواع العمليات
                    writer.writerow(['أنواع العمليات'])
                    writer.writerow(['نوع العملية', 'العدد', 'النسبة'])
                    for item in self.operations_types_tree.get_children():
                        values = self.operations_types_tree.item(item)['values']
                        writer.writerow(values)

                messagebox.showinfo("نجح", "تم تصدير الإحصائيات بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الإحصائيات: {e}")

    def print_statistics(self):
        """طباعة الإحصائيات"""
        try:
            messagebox.showinfo("طباعة", "ميزة الطباعة قيد التطوير")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الطباعة: {e}")
