#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete EXE Builder for Cashier Filter System
منشئ الملف التنفيذي الكامل لنظام تصفية الكاشير
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_banner():
    """Print build banner"""
    print("=" * 80)
    print("🔨 Cashier Filter System - COMPLETE EXE Builder")
    print("   نظام تصفية الكاشير - منشئ الملف التنفيذي الكامل")
    print("   Version 3.5.0 - Full Featured Build")
    print("=" * 80)

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    print(f"   Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 13):
        print("   ⚠️ Warning: Python 3.13+ may have compatibility issues with PyInstaller")
        print("   💡 Recommendation: Use Python 3.11 or 3.12 for best results")
        print("   🔧 Applying compatibility fixes...")
        return True
    elif version >= (3, 8):
        print("   ✅ Python version is compatible")
        return True
    else:
        print("   ❌ Python version too old. Requires Python 3.8+")
        return False

def install_missing_packages():
    """Install any missing packages"""
    print("📦 Checking and installing missing packages...")
    
    required_packages = [
        'pyinstaller>=5.13.0',
        'customtkinter>=5.2.0',
        'flask>=2.3.0',
        'pandas>=2.0.0',
        'fpdf2>=2.7.0',
        'Pillow>=10.0.0',
        'requests>=2.31.0',
        'openpyxl>=3.1.0',
        'reportlab>=4.0.0',
        'cryptography>=41.0.0',
        'psutil>=5.9.0',
    ]
    
    for package in required_packages:
        try:
            package_name = package.split('>=')[0]
            if package_name == 'fpdf2':
                import fpdf
            elif package_name == 'Pillow':
                import PIL
            elif package_name == 'pyinstaller':
                import PyInstaller
            else:
                __import__(package_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            print(f"   📥 Installing {package}...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                             check=True, capture_output=True)
                print(f"   ✅ Installed {package}")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Failed to install {package}: {e}")
                return False
    
    return True

def clean_build_environment():
    """Clean build environment thoroughly"""
    print("🧹 Cleaning build environment...")
    
    # Directories to clean
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   ✅ Removed {dir_name}")
            except Exception as e:
                print(f"   ⚠️ Could not remove {dir_name}: {e}")
    
    # Clean .pyc files recursively
    print("   🧹 Cleaning .pyc files...")
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass
    
    # Clean .spec files from previous builds
    for spec_file in ['*.spec']:
        if os.path.exists(spec_file):
            try:
                os.remove(spec_file)
                print(f"   ✅ Removed old {spec_file}")
            except:
                pass

def verify_project_structure():
    """Verify project structure is complete"""
    print("📁 Verifying project structure...")
    
    required_files = [
        'main.py',
        'config.py',
        'pyinstaller_utils.py',
        'CashierFilterSystem_Complete.spec'
    ]
    
    required_dirs = [
        'ui',
        'db',
        'reports',
        'utils',
        'web_templates',
        'web_static'
    ]
    
    missing_items = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_items.append(f"File: {file}")
        else:
            print(f"   ✅ {file}")
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_items.append(f"Directory: {dir_name}")
        else:
            file_count = len([f for f in os.listdir(dir_name) if os.path.isfile(os.path.join(dir_name, f))])
            print(f"   ✅ {dir_name}/ ({file_count} files)")
    
    if missing_items:
        print(f"\n❌ Missing items:")
        for item in missing_items:
            print(f"   - {item}")
        return False
    
    print("✅ Project structure verified")
    return True

def build_complete_exe():
    """Build the complete EXE using the comprehensive spec file"""
    print("🔨 Building COMPLETE EXE with all features...")
    
    # Use the complete spec file
    spec_file = 'CashierFilterSystem_Complete.spec'
    
    if not os.path.exists(spec_file):
        print(f"❌ Spec file not found: {spec_file}")
        return False
    
    # PyInstaller command with compatibility fixes
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        '--log-level=INFO',
        spec_file
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print("🕐 This will take 5-15 minutes depending on your system...")
    print("📊 Progress will be shown below:")
    
    start_time = time.time()
    
    try:
        # Run PyInstaller with real-time output
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=os.getcwd()
        )
        
        # Show progress
        for line in process.stdout:
            line = line.strip()
            if line:
                if 'INFO:' in line:
                    print(f"   📋 {line.split('INFO:')[-1].strip()}")
                elif 'WARNING:' in line:
                    print(f"   ⚠️ {line.split('WARNING:')[-1].strip()}")
                elif 'ERROR:' in line:
                    print(f"   ❌ {line.split('ERROR:')[-1].strip()}")
                elif 'Building' in line or 'Analyzing' in line:
                    print(f"   🔧 {line}")
        
        process.wait()
        
        end_time = time.time()
        build_time = end_time - start_time
        
        if process.returncode == 0:
            print(f"\n✅ Build completed successfully in {build_time/60:.1f} minutes")
            
            # Check if EXE was created
            exe_path = os.path.join('dist', 'CashierFilterSystem_v3.5.0_Complete.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📦 EXE created: {exe_path}")
                print(f"📏 File size: {file_size:.1f} MB")
                return True
            else:
                print("❌ EXE file not found in dist directory")
                return False
        else:
            print(f"\n❌ Build failed with return code: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False

def create_complete_distribution():
    """Create a complete distribution package"""
    print("📦 Creating COMPLETE distribution package...")
    
    exe_path = os.path.join('dist', 'CashierFilterSystem_v3.5.0_Complete.exe')
    if not os.path.exists(exe_path):
        print("❌ EXE file not found")
        return False
    
    # Create distribution directory
    dist_dir = 'CashierFilterSystem_Complete_Distribution'
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    
    # Copy EXE
    shutil.copy2(exe_path, dist_dir)
    print(f"   ✅ Copied EXE to {dist_dir}")
    
    # Copy essential files
    essential_files = [
        'README.md',
        'USER_GUIDE.md',
        'PYINSTALLER_GUIDE.md',
        'requirements.txt',
        'version_info.txt'
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_dir)
            print(f"   ✅ Copied {file}")
    
    # Create database directory with sample
    db_dir = os.path.join(dist_dir, 'db')
    os.makedirs(db_dir, exist_ok=True)
    
    if os.path.exists(os.path.join('db', 'cashier_filter.db')):
        shutil.copy2(os.path.join('db', 'cashier_filter.db'), db_dir)
        print(f"   ✅ Copied database")
    
    # Create comprehensive startup scripts
    batch_content = '''@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║         Cashier Filter System 2025 - Complete Edition               ║
echo ║                                                                      ║
echo ║    الإصدار 3.5.0 - النسخة الكاملة                                  ║
echo ║    Version 3.5.0 - Complete Edition                                 ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 Starting Cashier Filter System...
echo    بدء تشغيل نظام تصفية الكاشير...
echo.
CashierFilterSystem_v3.5.0_Complete.exe
if %errorlevel% neq 0 (
    echo.
    echo ❌ Error starting the application
    echo    خطأ في تشغيل التطبيق
    pause
)
'''
    
    with open(os.path.join(dist_dir, 'Start_CashierFilterSystem_Complete.bat'), 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    # Create comprehensive README
    readme_content = '''# 🏪 Cashier Filter System 2025 - Complete Edition
# نظام تصفية الكاشير المتكامل 2025 - النسخة الكاملة

## 🎯 Complete Features / الميزات الكاملة:

### ✅ Core Features / الميزات الأساسية:
- 💰 Complete cashier filtering system / نظام تصفية كاشير كامل
- 📊 Advanced reporting with all data types / تقارير متقدمة مع جميع أنواع البيانات
- 🖨️ Professional printing with suppliers data / طباعة احترافية مع بيانات الموردين
- 🌐 Web-based reports and statistics / تقارير وإحصائيات عبر الويب
- 🤖 AI-powered analysis and insights / تحليل ذكي بالذكاء الاصطناعي
- ☁️ Cloud integration capabilities / قدرات التكامل السحابي
- 🔒 Advanced security and encryption / أمان وتشفير متقدم
- 📱 Mobile-responsive web interface / واجهة ويب متجاوبة مع الجوال

### 🚀 How to Run / كيفية التشغيل:

#### Method 1 / الطريقة الأولى:
Double-click: `Start_CashierFilterSystem_Complete.bat`
انقر نقراً مزدوجاً على: `Start_CashierFilterSystem_Complete.bat`

#### Method 2 / الطريقة الثانية:
Run directly: `CashierFilterSystem_v3.5.0_Complete.exe`
شغل مباشرة: `CashierFilterSystem_v3.5.0_Complete.exe`

### 🔐 Login Credentials / بيانات تسجيل الدخول:
- **Username / اسم المستخدم:** admin
- **Password / كلمة المرور:** 123456

### 💻 System Requirements / متطلبات النظام:
- **OS / نظام التشغيل:** Windows 10/11 (64-bit)
- **RAM / الذاكرة:** 4GB minimum (8GB recommended)
- **Storage / التخزين:** 1GB free space
- **Network / الشبكة:** Internet connection for cloud features

### 📚 Documentation / الوثائق:
- `USER_GUIDE.md` - Complete user manual / دليل المستخدم الكامل
- `PYINSTALLER_GUIDE.md` - Technical documentation / الوثائق التقنية

### 🆘 Support / الدعم:
- **Email / البريد الإلكتروني:** <EMAIL>
- **Developer / المطور:** Mohamed Al-Kamel - محمد الكامل

### 📋 Version Information / معلومات الإصدار:
- **Version / الإصدار:** 3.5.0 Complete
- **Build Date / تاريخ البناء:** ''' + time.strftime('%Y-%m-%d %H:%M:%S') + '''
- **Build Type / نوع البناء:** Complete Distribution with All Features

### 🎉 What's Included / ما هو مشمول:
✅ Complete GUI application / تطبيق واجهة رسومية كامل
✅ Web server for reports / خادم ويب للتقارير
✅ Database with sample data / قاعدة بيانات مع بيانات عينة
✅ All documentation / جميع الوثائق
✅ Startup scripts / سكريبتات التشغيل
✅ Professional printing / طباعة احترافية
✅ Cloud integration / التكامل السحابي
✅ AI analysis tools / أدوات التحليل الذكي
✅ Mobile web interface / واجهة ويب للجوال

---

**🚀 Ready for production use! / جاهز للاستخدام الإنتاجي!**
'''
    
    with open(os.path.join(dist_dir, 'README_Complete_Distribution.txt'), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Complete distribution package created: {dist_dir}")
    return True

def main():
    """Main complete build process"""
    print_banner()
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Step 1: Check Python version
    if not check_python_version():
        print("❌ Build aborted due to Python version issues")
        return False
    
    # Step 2: Install missing packages
    if not install_missing_packages():
        print("❌ Build aborted due to missing packages")
        return False
    
    # Step 3: Clean build environment
    clean_build_environment()
    
    # Step 4: Verify project structure
    if not verify_project_structure():
        print("❌ Build aborted due to missing project files")
        return False
    
    # Step 5: Build complete EXE
    if not build_complete_exe():
        print("❌ Build failed")
        return False
    
    # Step 6: Create complete distribution
    if not create_complete_distribution():
        print("❌ Failed to create distribution package")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 COMPLETE BUILD SUCCESSFUL!")
    print("📦 Your complete EXE with ALL features is ready!")
    print("📁 Location: CashierFilterSystem_Complete_Distribution/")
    print("🚀 Ready for distribution and production use!")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ COMPLETE BUILD SUCCESSFUL")
        print("🎊 All features included and ready!")
    else:
        print("❌ COMPLETE BUILD FAILED")
        print("🔧 Check the errors above and try again")
    print("=" * 80)
    
    input("\nPress Enter to exit...")
