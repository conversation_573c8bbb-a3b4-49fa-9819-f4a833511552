#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سجل العمليات وإحصائيات المستخدمين
Test Operations Log and User Statistics
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_operations_logger():
    """اختبار نظام تسجيل العمليات"""
    print("🧪 اختبار نظام تسجيل العمليات...")
    
    try:
        from utils.operations_logger import operations_logger, log_login, log_filter_operation
        
        print("✅ تم استيراد نظام تسجيل العمليات بنجاح")
        
        # اختبار تسجيل عملية
        result = log_login(1, "admin", True, "127.0.0.1")
        if result:
            print("✅ تم تسجيل عملية تسجيل الدخول بنجاح")
        else:
            print("❌ فشل في تسجيل عملية تسجيل الدخول")
        
        # اختبار تسجيل عملية تصفية
        result = log_filter_operation(1, "admin", "بدء تصفية جديدة", 123, {"amount": 1000})
        if result:
            print("✅ تم تسجيل عملية التصفية بنجاح")
        else:
            print("❌ فشل في تسجيل عملية التصفية")
        
        # اختبار استرجاع العمليات
        operations = operations_logger.get_operations_log(limit=5)
        print(f"✅ تم استرجاع {len(operations)} عملية من السجل")
        
        # اختبار الإحصائيات
        stats = operations_logger.get_operations_statistics(30)
        print(f"✅ تم الحصول على الإحصائيات: {len(stats)} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام تسجيل العمليات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_operations_log_window():
    """اختبار نافذة سجل العمليات"""
    print("\n🧪 اختبار نافذة سجل العمليات...")
    
    try:
        from ui.operations_log import OperationsLogWindow
        
        print("✅ تم استيراد نافذة سجل العمليات بنجاح")
        
        # فحص الدوال المطلوبة
        required_methods = [
            'create_widgets',
            'create_filters_frame',
            'create_table_frame',
            'load_operations',
            'apply_filters',
            'export_data'
        ]
        
        available_methods = []
        for method in required_methods:
            if hasattr(OperationsLogWindow, method):
                available_methods.append(method)
        
        print(f"✅ الدوال المتاحة: {len(available_methods)}/{len(required_methods)}")
        for method in available_methods:
            print(f"   ✅ {method}")
        
        return len(available_methods) == len(required_methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة سجل العمليات: {e}")
        return False

def test_user_statistics_window():
    """اختبار نافذة إحصائيات المستخدمين"""
    print("\n🧪 اختبار نافذة إحصائيات المستخدمين...")
    
    try:
        from ui.user_statistics import UserStatisticsWindow
        
        print("✅ تم استيراد نافذة إحصائيات المستخدمين بنجاح")
        
        # فحص الدوال المطلوبة
        required_methods = [
            'create_widgets',
            'create_filters_frame',
            'create_general_stats_frame',
            'load_statistics',
            'load_general_statistics',
            'export_statistics'
        ]
        
        available_methods = []
        for method in required_methods:
            if hasattr(UserStatisticsWindow, method):
                available_methods.append(method)
        
        print(f"✅ الدوال المتاحة: {len(available_methods)}/{len(required_methods)}")
        for method in available_methods:
            print(f"   ✅ {method}")
        
        return len(available_methods) == len(required_methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة إحصائيات المستخدمين: {e}")
        return False

def test_operations_statistics_window():
    """اختبار نافذة إحصائيات العمليات"""
    print("\n🧪 اختبار نافذة إحصائيات العمليات...")
    
    try:
        from ui.operations_statistics import OperationsStatisticsWindow
        
        print("✅ تم استيراد نافذة إحصائيات العمليات بنجاح")
        
        # فحص الدوال المطلوبة
        required_methods = [
            'create_widgets',
            'create_filters_frame',
            'create_general_stats_frame',
            'load_statistics',
            'load_general_statistics',
            'export_statistics'
        ]
        
        available_methods = []
        for method in required_methods:
            if hasattr(OperationsStatisticsWindow, method):
                available_methods.append(method)
        
        print(f"✅ الدوال المتاحة: {len(available_methods)}/{len(required_methods)}")
        for method in available_methods:
            print(f"   ✅ {method}")
        
        return len(available_methods) == len(required_methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة إحصائيات العمليات: {e}")
        return False

def test_main_window_integration():
    """اختبار التكامل مع الواجهة الرئيسية"""
    print("\n🧪 اختبار التكامل مع الواجهة الرئيسية...")
    
    try:
        from ui.main_window import MainWindow
        
        # فحص الدوال المحدثة
        required_methods = [
            'show_operations_log',
            'show_user_statistics'
        ]
        
        available_methods = []
        for method in required_methods:
            if hasattr(MainWindow, method):
                available_methods.append(method)
        
        print(f"✅ الدوال المحدثة: {len(available_methods)}/{len(required_methods)}")
        for method in available_methods:
            print(f"   ✅ {method}")
        
        return len(available_methods) == len(required_methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        import sqlite3
        
        # تحديد مسار قاعدة البيانات
        if os.path.exists("db/cashier_filter.db"):
            db_path = "db/cashier_filter.db"
        else:
            db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة")
            return False
        
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # فحص وجود جدول سجل العمليات
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='operations_log'")
        if c.fetchone():
            print("✅ جدول سجل العمليات موجود")
        else:
            print("❌ جدول سجل العمليات غير موجود")
            conn.close()
            return False
        
        # فحص عدد السجلات
        c.execute("SELECT COUNT(*) FROM operations_log")
        count = c.fetchone()[0]
        print(f"✅ عدد العمليات المسجلة: {count}")
        
        # فحص الفهارس
        c.execute("SELECT name FROM sqlite_master WHERE type='index' AND tbl_name='operations_log'")
        indexes = c.fetchall()
        print(f"✅ عدد الفهارس: {len(indexes)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def display_features():
    """عرض الميزات الجديدة"""
    print("\n🌟 الميزات الجديدة المطورة:")
    print("=" * 60)
    
    print("\n📝 سجل العمليات:")
    print("   • تسجيل جميع العمليات والأنشطة")
    print("   • فلترة متقدمة حسب المستخدم والنوع والتاريخ")
    print("   • عرض تفصيلي مع التنقل بين الصفحات")
    print("   • تصدير البيانات إلى CSV")
    print("   • عرض تفاصيل كل عملية")
    
    print("\n📊 إحصائيات المستخدمين:")
    print("   • إحصائيات عامة شاملة")
    print("   • أكثر المستخدمين نشاطاً")
    print("   • أنواع العمليات والنسب")
    print("   • النشاط اليومي والساعي")
    print("   • تصدير الإحصائيات")
    
    print("\n📈 إحصائيات العمليات:")
    print("   • إحصائيات العمليات حسب النوع")
    print("   • إحصائيات العمليات حسب المستخدم")
    print("   • النشاط اليومي والساعي")
    print("   • معدلات النجاح والفشل")
    print("   • تصدير شامل للبيانات")
    
    print("\n🔧 التكامل مع النظام:")
    print("   • تسجيل تلقائي لجميع العمليات")
    print("   • ربط مع الواجهة الرئيسية")
    print("   • قوائم منفصلة في شريط الوصول السريع")
    print("   • واجهات احترافية ومتقدمة")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار سجل العمليات وإحصائيات المستخدمين")
    print("=" * 70)
    
    # تشغيل الاختبارات
    tests = [
        ("نظام تسجيل العمليات", test_operations_logger),
        ("نافذة سجل العمليات", test_operations_log_window),
        ("نافذة إحصائيات المستخدمين", test_user_statistics_window),
        ("نافذة إحصائيات العمليات", test_operations_statistics_window),
        ("التكامل مع الواجهة الرئيسية", test_main_window_integration),
        ("عمليات قاعدة البيانات", test_database_operations)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print(f"\n📊 نتائج الاختبارات:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 الإجمالي: {success_count}/{total_count} اختبار نجح")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! الوظائف الجديدة جاهزة!")
        display_features()
        print("\n🚀 لتشغيل النظام مع الوظائف الجديدة:")
        print("   python main.py")
        print("\n💡 للوصول للوظائف الجديدة:")
        print("   • سجل العمليات: قائمة الإدارة → سجل العمليات")
        print("   • إحصائيات المستخدمين: قائمة الإدارة → إحصائيات المستخدمين")
    else:
        print("⚠️ بعض الاختبارات فشلت، لكن الوظائف قد تعمل جزئياً")

if __name__ == "__main__":
    main()
