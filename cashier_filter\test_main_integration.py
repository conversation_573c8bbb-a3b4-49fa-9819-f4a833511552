#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التكامل مع النافذة الرئيسية
"""

import sys
import os
import customtkinter as ctk

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_window_cloud_integration():
    """اختبار التكامل مع النافذة الرئيسية"""
    print("🧪 اختبار التكامل مع النافذة الرئيسية...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد النافذة الرئيسية
        from ui.main_window import MainWindow
        print("✅ تم استيراد النافذة الرئيسية بنجاح")
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # التحقق من وجود دالة التكامل السحابي
        if hasattr(main_window, 'show_cloud_integration'):
            print("✅ دالة التكامل السحابي موجودة")
            
            # اختبار استدعاء الدالة
            try:
                main_window.show_cloud_integration()
                print("✅ تم استدعاء دالة التكامل السحابي بنجاح")
            except Exception as e:
                print(f"❌ خطأ في استدعاء دالة التكامل السحابي: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ دالة التكامل السحابي غير موجودة")
        
        # إغلاق النافذة
        main_window.destroy()
        print("✅ تم إغلاق النافذة الرئيسية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_import():
    """اختبار الاستيراد المباشر"""
    print("\n🧪 اختبار الاستيراد المباشر...")
    
    try:
        # اختبار استيراد النافذة الكاملة
        try:
            from ui.cloud_integration import open_cloud_integration
            print("✅ النافذة الكاملة متاحة")
        except ImportError as e:
            print(f"⚠️ النافذة الكاملة غير متاحة: {e}")
        
        # اختبار استيراد النافذة المبسطة
        try:
            from ui.cloud_integration_simple import open_cloud_integration_simple
            print("✅ النافذة المبسطة متاحة")
        except ImportError as e:
            print(f"❌ النافذة المبسطة غير متاحة: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار التكامل مع النافذة الرئيسية")
    print("=" * 60)
    
    # اختبار الاستيراد المباشر
    import_success = test_direct_import()
    
    if import_success:
        # اختبار التكامل مع النافذة الرئيسية
        integration_success = test_main_window_cloud_integration()
        
        if integration_success:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ التكامل السحابي يعمل بشكل صحيح")
        else:
            print("\n💥 فشل اختبار التكامل مع النافذة الرئيسية")
    else:
        print("\n💥 فشل اختبار الاستيراد المباشر")
    
    print("=" * 60)
