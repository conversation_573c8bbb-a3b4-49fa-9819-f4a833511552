#!/bin/bash
# -*- coding: utf-8 -*-

echo "========================================"
echo "   📋 تقرير العملاء الآجل الشامل"
echo "========================================"
echo

echo "🔄 جاري تشغيل خادم التقارير..."
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ خطأ: Python غير مثبت على النظام"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# التحقق من وجود المتطلبات
echo "📦 التحقق من المتطلبات..."
if ! python3 -c "import flask" &> /dev/null; then
    echo "🔧 تثبيت المتطلبات..."
    pip3 install flask
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        exit 1
    fi
fi

# تهيئة قاعدة البيانات
echo "🗄️ تهيئة قاعدة البيانات..."
python3 db/init_db.py

# إضافة بيانات عينة إذا لم تكن موجودة
echo "📊 التحقق من وجود بيانات العينة..."
echo "1" | python3 test_credit_customers_data.py > /dev/null 2>&1

# تشغيل خادم الويب
echo "✅ تشغيل خادم التقارير..."
echo
echo "============================================================"
echo "🌐 خادم تقارير العملاء الآجل"
echo "============================================================"
echo "🔗 الرابط المحلي: http://localhost:5000/credit-customers"
echo "📱 واجهة الهاتف: http://localhost:5000/mobile"
echo "🏠 الصفحة الرئيسية: http://localhost:5000/"
echo "============================================================"
echo "💡 للوصول من الهاتف:"
echo "   1. تأكد من أن الهاتف والكمبيوتر على نفس الشبكة"
echo "   2. استخدم عنوان IP الكمبيوتر بدلاً من localhost"
echo "============================================================"
echo "⏹️  للإيقاف: اضغط Ctrl+C"
echo "============================================================"
echo

# فتح المتصفح تلقائياً (إذا كان متاحاً)
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5000/credit-customers &
elif command -v open &> /dev/null; then
    open http://localhost:5000/credit-customers &
fi

# تشغيل الخادم
python3 web_server.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ حدث خطأ في تشغيل خادم التقارير"
    read -p "اضغط Enter للخروج..."
fi
