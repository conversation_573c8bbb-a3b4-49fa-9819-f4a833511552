# 🌐 دليل تشغيل خادم التقارير

## 📍 أين تجد زر خادم التقارير؟

### 🎯 الموقع الصحيح:
بعد تسجيل الدخول للتطبيق الرئيسي، ستجد زر **🌐 خادم التقارير** في قائمة الأزرار الرئيسية.

### 📋 الخطوات التفصيلية:

#### 1. تشغيل التطبيق:
```
python main.py
```

#### 2. تسجيل الدخول:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `123456`

#### 3. البحث عن الزر:
في الواجهة الرئيسية، ابحث عن زر:
```
🌐 خادم التقارير
```

**موقعه:** بين زر "🌐 التكامل السحابي" وزر "🔔 الإشعارات والتنبيهات"

#### 4. انقر على الزر:
- سيظهر رسالة تأكيد
- سيتم تشغيل الخادم في نافذة منفصلة
- سيفتح المتصفح تلقائياً على http://localhost:5000

## 🔄 إذا لم تجد الزر:

### السبب المحتمل:
التطبيق يحتاج إعادة تشغيل لتحديث الواجهة.

### الحل:
1. **أغلق التطبيق** تماماً
2. **أعد تشغيله:** `python main.py`
3. **سجل الدخول** مرة أخرى
4. **ابحث عن الزر** في قائمة الأزرار

## 🛠️ الطرق البديلة لتشغيل خادم التقارير:

### الطريقة الأولى: ملف Batch (الأسهل)
```
انقر نقراً مزدوجاً على: تشغيل_خادم_التقارير.bat
```

### الطريقة الثانية: Python مباشر
```bash
python start_web_server.py
```

### الطريقة الثالثة: الخادم الأساسي
```bash
python web_server.py
```

## 📱 بعد تشغيل الخادم:

### 🔗 الروابط المتاحة:
- **للكمبيوتر:** http://localhost:5000
- **للهاتف:** http://localhost:5000/mobile
- **التقارير:** http://localhost:5000/reports

### 🌍 للوصول العالمي:
1. **اذهب للإعدادات** (⚙️)
2. **ابحث عن قسم "الوصول العالمي"**
3. **انقر "تشغيل النفق العالمي"**
4. **انسخ الرابط** واستخدمه من أي مكان

## ✅ التحقق من نجاح التشغيل:

### علامات النجاح:
- ✅ ظهور نافذة سوداء مع معلومات الخادم
- ✅ فتح المتصفح تلقائياً
- ✅ ظهور واجهة التقارير في المتصفح
- ✅ رسالة "تم تشغيل خادم التقارير بنجاح"

### إذا لم يعمل:
1. **تحقق من رسائل الخطأ** في النافذة السوداء
2. **تأكد من وجود قاعدة البيانات** (db/cashier_filter.db)
3. **جرب منفذ مختلف** إذا كان 5000 مستخدم
4. **أعد تشغيل الكمبيوتر** إذا استمرت المشاكل

## 🎯 ترتيب الأزرار في الواجهة الرئيسية:

```
➕ بدء تصفية جديدة
📁 عرض تقارير التصفية  
🔍 البحث المتقدم
📈 التقارير المتقدمة
🤖 التحليل الذكي بالذكاء الاصطناعي
📊 لوحة المعلومات التفاعلية
📝 تعديل تصفية محفوظة
👤 إدارة الكاشير
🧑‍💼 إدارة المسؤولين
🔐 إدارة الصلاحيات
💾 إدارة النسخ الاحتياطي
📊 الإحصائيات والتحليلات
🌐 التكامل السحابي
🌐 خادم التقارير          ← هنا!
🔔 الإشعارات والتنبيهات
⚙️ إعدادات التطبيق
```

## 🔧 استكشاف الأخطاء:

### المشكلة: "الزر غير موجود"
**الحل:**
1. أعد تشغيل التطبيق
2. تأكد من تحديث الملفات
3. تحقق من إصدار التطبيق

### المشكلة: "خطأ عند النقر على الزر"
**الحل:**
1. تحقق من وجود ملف start_web_server.py
2. تأكد من تثبيت Flask: `pip install Flask`
3. تحقق من قاعدة البيانات

### المشكلة: "الخادم لا يبدأ"
**الحل:**
1. تحقق من المنفذ 5000 (قد يكون مستخدم)
2. شغل كمسؤول (Run as Administrator)
3. تحقق من Firewall

## 📞 للمساعدة السريعة:

### إذا لم تجد الزر:
1. **أعد تشغيل التطبيق** - هذا يحل 90% من المشاكل
2. **استخدم الطريقة البديلة:** `تشغيل_خادم_التقارير.bat`
3. **تحقق من تحديث الملفات**

### إذا وجدت الزر لكنه لا يعمل:
1. **تحقق من رسائل الخطأ**
2. **جرب الطريقة اليدوية:** `python start_web_server.py`
3. **تأكد من تثبيت المتطلبات:** `pip install -r requirements.txt`

---

## 🎉 خلاصة سريعة:

**لتشغيل خادم التقارير:**
1. شغل التطبيق: `python main.py`
2. سجل الدخول: admin / 123456
3. انقر: **🌐 خادم التقارير**
4. انتظر فتح المتصفح على: http://localhost:5000

**للوصول العالمي:**
1. بعد تشغيل الخادم
2. اذهب للإعدادات → الوصول العالمي
3. انقر "تشغيل النفق العالمي"
4. انسخ الرابط واستخدمه من أي مكان!

**الزر موجود الآن - أعد تشغيل التطبيق إذا لم تجده!** 🚀
