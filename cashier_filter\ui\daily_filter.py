# شاشة التصفية اليومية الكاملة - محسنة
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
from datetime import datetime

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class DailyFilterWindow(ctk.CTkToplevel):
    def __init__(self, master=None, filter_data=None, old_details=None, filter_id=None):
        super().__init__(master)

        self.filter_data = filter_data or {}
        self.old_details = old_details
        self.filter_id = filter_id

        # الحصول على الرقم التسلسلي
        self.sequence_number = self.get_sequence_number()

        # تحديث عنوان النافذة ليشمل الرقم التسلسلي
        if self.sequence_number:
            self.title(f"التصفية اليومية للكاشير - رقم {self.sequence_number}")
        else:
            self.title("التصفية اليومية للكاشير")

        self.geometry("1400x900")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        # متغيرات لحفظ المجاميع
        self.totals = {
            'bank': 0.0,
            'cash': 0.0,
            'credit': 0.0,
            'client': 0.0,
            'return': 0.0
        }

        self.create_widgets()
        self.load_old_data_if_exists()

    def get_sequence_number(self):
        """الحصول على الرقم التسلسلي للتصفية"""
        try:
            if self.filter_id:
                # إذا كانت تصفية موجودة، احصل على رقمها التسلسلي
                import sqlite3
                from config import DB_PATH

                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                c.execute("SELECT sequence_number FROM filters WHERE id = ?", (self.filter_id,))
                result = c.fetchone()
                conn.close()

                if result and result[0]:
                    return result[0]

            # إذا كانت تصفية جديدة، احصل على الرقم التسلسلي التالي
            from db.filter_ops import get_next_sequence_number
            return get_next_sequence_number()

        except Exception as e:
            print(f"خطأ في الحصول على الرقم التسلسلي: {e}")
            return None

    def create_widgets(self):
        # إنشاء إطار قابل للتمرير
        self.main_scrollable = ctk.CTkScrollableFrame(
            self,
            fg_color="#f2f3f7",
            corner_radius=0
        )
        self.main_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

        # العنوان وبيانات التصفية
        self.create_header()

        # الأزرار العلوية
        self.create_top_buttons()

        # الجداول السبعة
        self.create_bank_section()
        self.create_cash_section()
        self.create_credit_section()
        self.create_client_section()
        self.create_return_section()
        self.create_suppliers_section()  # قسم الموردين الجديد

        # ملخص التصفية
        self.create_summary_section()

    def create_header(self):
        """إنشاء رأس الصفحة مع بيانات التصفية"""
        header_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=10, padx=20, fill="x")

        # العنوان مع الرقم التسلسلي
        if self.sequence_number:
            title_text = f"📊 التصفية اليومية للكاشير - رقم {self.sequence_number}"
        else:
            title_text = "📊 التصفية اليومية للكاشير"

        title_label = ctk.CTkLabel(
            header_frame,
            text=title_text,
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)

        # بيانات التصفية
        info_frame = ctk.CTkFrame(header_frame, fg_color="#f2f3f7", corner_radius=10)
        info_frame.pack(pady=10, padx=20, fill="x")

        # الصف الأول
        row1 = ctk.CTkFrame(info_frame, fg_color="#f2f3f7", corner_radius=0)
        row1.pack(pady=5, fill="x")

        cashier_info = f"👤 الكاشير: {self.filter_data.get('cashier_name', 'غير محدد')} ({self.filter_data.get('cashier_id', 'غير محدد')})"
        ctk.CTkLabel(row1, text=cashier_info, font=("Arial", 14, "bold"), text_color="#34495e").pack(side="left", padx=20)

        admin_info = f"🧑‍💼 المسؤول: {self.filter_data.get('admin_name', 'غير محدد')}"
        ctk.CTkLabel(row1, text=admin_info, font=("Arial", 14, "bold"), text_color="#34495e").pack(side="right", padx=20)

        # الصف الثاني
        row2 = ctk.CTkFrame(info_frame, fg_color="#f2f3f7", corner_radius=0)
        row2.pack(pady=5, fill="x")

        date_info = f"📅 التاريخ: {self.filter_data.get('date', datetime.now().strftime('%Y-%m-%d'))}"
        ctk.CTkLabel(row2, text=date_info, font=("Arial", 14, "bold"), text_color="#34495e").pack(side="left", padx=20)

        time_info = f"🕐 وقت الإنشاء: {datetime.now().strftime('%H:%M:%S')}"
        ctk.CTkLabel(row2, text=time_info, font=("Arial", 14), text_color="#7f8c8d").pack(side="right", padx=20)

        # الصف الثالث - الرقم التسلسلي والحالة
        if self.sequence_number:
            row3 = ctk.CTkFrame(info_frame, fg_color="#f2f3f7", corner_radius=0)
            row3.pack(pady=5, fill="x")

            sequence_info = f"🔢 الرقم التسلسلي: {self.sequence_number}"
            ctk.CTkLabel(row3, text=sequence_info, font=("Arial", 16, "bold"), text_color="#e74c3c").pack(side="left", padx=20)

            # حالة التصفية
            if self.filter_id:
                status_info = "✏️ تعديل تصفية موجودة"
                status_color = "#f39c12"
            else:
                status_info = "➕ تصفية جديدة"
                status_color = "#27ae60"

            ctk.CTkLabel(row3, text=status_info, font=("Arial", 14, "bold"), text_color=status_color).pack(side="right", padx=20)

    def create_top_buttons(self):
        """إنشاء الأزرار العلوية"""
        buttons_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        buttons_frame.pack(pady=10, padx=20, fill="x")

        btn_container = ctk.CTkFrame(buttons_frame, fg_color="#e0e5ec", corner_radius=0)
        btn_container.pack(pady=15)

        # زر حفظ التصفية
        save_btn = ctk.CTkButton(
            btn_container,
            text="💾 حفظ التصفية",
            font=("Arial", 16, "bold"),
            corner_radius=15,
            fg_color="#4CAF50",
            hover_color="#45a049",
            text_color="white",
            width=150,
            height=40,
            command=self.save_filter
        )
        save_btn.pack(side="left", padx=10)

        # إذا كان تعديل، أضف زر تحديث
        if self.old_details is not None and self.filter_id is not None:
            update_btn = ctk.CTkButton(
                btn_container,
                text="🔄 تحديث التصفية",
                font=("Arial", 16, "bold"),
                corner_radius=15,
                fg_color="#2196F3",
                hover_color="#1976D2",
                text_color="white",
                width=150,
                height=40,
                command=self.update_filter
            )
            update_btn.pack(side="left", padx=10)

        # زر طباعة
        print_btn = ctk.CTkButton(
            btn_container,
            text="🖨️ طباعة",
            font=("Arial", 16, "bold"),
            corner_radius=15,
            fg_color="#FF9800",
            hover_color="#F57C00",
            text_color="white",
            width=120,
            height=40,
            command=self.print_filter
        )
        print_btn.pack(side="left", padx=10)

        # زر تصدير PDF
        pdf_btn = ctk.CTkButton(
            btn_container,
            text="📄 تصدير PDF",
            font=("Arial", 16, "bold"),
            corner_radius=15,
            fg_color="#e57373",
            hover_color="#d32f2f",
            text_color="white",
            width=130,
            height=40,
            command=self.export_pdf
        )
        pdf_btn.pack(side="left", padx=10)

    def create_bank_section(self):
        """إنشاء قسم المقبوضات البنكية"""
        bank_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        bank_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(bank_frame, fg_color="#4CAF50", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="💳 المقبوضات البنكية",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(bank_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # نوع العملية
        ctk.CTkLabel(fields_frame, text="نوع العملية:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.bank_type_combo = ctk.CTkComboBox(
            fields_frame,
            values=["ماستر", "مدى", "ماستر كارد", "فيزا", "أمريكان إكسبريس"],
            width=150
        )
        self.bank_type_combo.grid(row=0, column=1, padx=5, pady=5)

        # اسم البنك
        ctk.CTkLabel(fields_frame, text="اسم البنك:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.bank_name_combo = ctk.CTkComboBox(
            fields_frame,
            values=["الأهلي", "الراجحي", "سامبا", "الرياض", "البلاد", "الإنماء", "الجزيرة", "ساب"],
            width=150
        )
        self.bank_name_combo.grid(row=0, column=3, padx=5, pady=5)

        # مبلغ العملية
        ctk.CTkLabel(fields_frame, text="المبلغ:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.bank_amount_entry = ctk.CTkEntry(fields_frame, width=100, placeholder_text="0.00")
        self.bank_amount_entry.grid(row=0, column=5, padx=5, pady=5)

        # زر إضافة
        add_bank_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة",
            command=self.add_bank_transaction,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=80,
            height=30
        )
        add_bank_btn.grid(row=0, column=6, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("نوع العملية", "اسم البنك", "المبلغ")
        self.bank_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.bank_tree.heading(col, text=col)
            self.bank_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        bank_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.bank_tree.yview)
        self.bank_tree.configure(yscrollcommand=bank_scrollbar.set)

        self.bank_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        bank_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف مع تأخير لضمان التحديد
        self.bank_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.bank_tree, self.update_bank_total)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.bank_tree, self.update_bank_total)

        # إجمالي المقبوضات البنكية
        self.bank_total_label = ctk.CTkLabel(
            bank_frame,
            text="إجمالي المقبوضات البنكية: 0.00 ريال",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        self.bank_total_label.pack(pady=10)

        # تلميح للحذف
        delete_hint = ctk.CTkLabel(
            bank_frame,
            text="💡 تلميح: انقر مرتين على أي عنصر لحذفه، أو انقر بالزر الأيمن",
            font=("Arial", 10),
            text_color="#7f8c8d"
        )
        delete_hint.pack(pady=2)

    def create_cash_section(self):
        """إنشاء قسم المقبوضات النقدية"""
        cash_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        cash_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(cash_frame, fg_color="#2196F3", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="💵 المقبوضات النقدية",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(cash_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # فئة العملة
        ctk.CTkLabel(fields_frame, text="فئة العملة:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.cash_denomination_combo = ctk.CTkComboBox(
            fields_frame,
            values=["500 ريال", "200 ريال", "100 ريال", "50 ريال", "20 ريال", "10 ريال", "5 ريال", "1 ريال", "50 هللة", "25 هللة", "10 هللة", "5 هللة"],
            width=120
        )
        self.cash_denomination_combo.grid(row=0, column=1, padx=5, pady=5)

        # العدد
        ctk.CTkLabel(fields_frame, text="العدد:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.cash_count_entry = ctk.CTkEntry(fields_frame, width=80, placeholder_text="0")
        self.cash_count_entry.grid(row=0, column=3, padx=5, pady=5)

        # المبلغ الإجمالي
        ctk.CTkLabel(fields_frame, text="المبلغ:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.cash_amount_entry = ctk.CTkEntry(fields_frame, width=100, placeholder_text="0.00")
        self.cash_amount_entry.grid(row=0, column=5, padx=5, pady=5)

        # زر حساب تلقائي
        calc_btn = ctk.CTkButton(
            fields_frame,
            text="🧮 حساب",
            command=self.calculate_cash_amount,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=70,
            height=30
        )
        calc_btn.grid(row=0, column=6, padx=5, pady=5)

        # زر إضافة
        add_cash_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة",
            command=self.add_cash_transaction,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=80,
            height=30
        )
        add_cash_btn.grid(row=0, column=7, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("فئة العملة", "العدد", "المبلغ")
        self.cash_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.cash_tree.heading(col, text=col)
            self.cash_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        cash_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.cash_tree.yview)
        self.cash_tree.configure(yscrollcommand=cash_scrollbar.set)

        self.cash_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        cash_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف مع تأخير لضمان التحديد
        self.cash_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.cash_tree, self.update_cash_total)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.cash_tree, self.update_cash_total)

        # إجمالي المقبوضات النقدية
        self.cash_total_label = ctk.CTkLabel(
            cash_frame,
            text="إجمالي المقبوضات النقدية: 0.00 ريال",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        self.cash_total_label.pack(pady=10)

    def create_credit_section(self):
        """إنشاء قسم المبيعات الآجلة"""
        credit_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        credit_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(credit_frame, fg_color="#FF9800", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="📋 المبيعات الآجلة",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(credit_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # اسم العميل
        ctk.CTkLabel(fields_frame, text="اسم العميل:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.credit_client_entry = ctk.CTkEntry(fields_frame, width=200, placeholder_text="اسم العميل")
        self.credit_client_entry.grid(row=0, column=1, padx=5, pady=5)

        # رقم الفاتورة
        ctk.CTkLabel(fields_frame, text="رقم الفاتورة:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.credit_invoice_entry = ctk.CTkEntry(fields_frame, width=120, placeholder_text="رقم الفاتورة")
        self.credit_invoice_entry.grid(row=0, column=3, padx=5, pady=5)

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.credit_amount_entry = ctk.CTkEntry(fields_frame, width=100, placeholder_text="0.00")
        self.credit_amount_entry.grid(row=0, column=5, padx=5, pady=5)

        # زر إضافة
        add_credit_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة",
            command=self.add_credit_transaction,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=80,
            height=30
        )
        add_credit_btn.grid(row=0, column=6, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("اسم العميل", "رقم الفاتورة", "المبلغ")
        self.credit_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.credit_tree.heading(col, text=col)
            self.credit_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        credit_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.credit_tree.yview)
        self.credit_tree.configure(yscrollcommand=credit_scrollbar.set)

        self.credit_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        credit_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف مع تأخير لضمان التحديد
        self.credit_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.credit_tree, self.update_credit_total)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.credit_tree, self.update_credit_total)

        # إجمالي المبيعات الآجلة
        self.credit_total_label = ctk.CTkLabel(
            credit_frame,
            text="إجمالي المبيعات الآجلة: 0.00 ريال",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        self.credit_total_label.pack(pady=10)

    def create_client_section(self):
        """إنشاء قسم المقبوضات من العملاء"""
        client_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        client_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(client_frame, fg_color="#9C27B0", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="👥 المقبوضات من العملاء",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(client_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # اسم العميل
        ctk.CTkLabel(fields_frame, text="اسم العميل:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.client_name_entry = ctk.CTkEntry(fields_frame, width=200, placeholder_text="اسم العميل")
        self.client_name_entry.grid(row=0, column=1, padx=5, pady=5)

        # نوع المقبوض
        ctk.CTkLabel(fields_frame, text="نوع المقبوض:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.client_type_combo = ctk.CTkComboBox(
            fields_frame,
            values=["سداد فاتورة", "دفعة مقدمة", "تسوية حساب"],
            width=150
        )
        self.client_type_combo.grid(row=0, column=3, padx=5, pady=5)

        # طريقة الدفع
        ctk.CTkLabel(fields_frame, text="طريقة الدفع:", font=("Arial", 12, "bold")).grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.client_payment_method_combo = ctk.CTkComboBox(
            fields_frame,
            values=["نقدي", "شبكة"],
            width=120
        )
        self.client_payment_method_combo.grid(row=1, column=1, padx=5, pady=5)
        self.client_payment_method_combo.set("نقدي")  # القيمة الافتراضية

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.client_amount_entry = ctk.CTkEntry(fields_frame, width=100, placeholder_text="0.00")
        self.client_amount_entry.grid(row=0, column=5, padx=5, pady=5)

        # رقم المرجع (للشبكة)
        ctk.CTkLabel(fields_frame, text="رقم المرجع:", font=("Arial", 12, "bold")).grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.client_ref_entry = ctk.CTkEntry(fields_frame, width=150, placeholder_text="رقم المرجع (اختياري)")
        self.client_ref_entry.grid(row=1, column=3, padx=5, pady=5)

        # زر إضافة
        add_client_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة",
            command=self.add_client_transaction,
            fg_color="#9C27B0",
            hover_color="#7B1FA2",
            width=80,
            height=30
        )
        add_client_btn.grid(row=1, column=4, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("اسم العميل", "نوع المقبوض", "طريقة الدفع", "المبلغ", "رقم المرجع")
        self.client_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.client_tree.heading(col, text=col)
            self.client_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        client_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.client_tree.yview)
        self.client_tree.configure(yscrollcommand=client_scrollbar.set)

        self.client_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        client_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف مع تأخير لضمان التحديد
        self.client_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.client_tree, self.update_client_total)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.client_tree, self.update_client_total)

        # إجمالي المقبوضات من العملاء
        self.client_total_label = ctk.CTkLabel(
            client_frame,
            text="إجمالي المقبوضات من العملاء: 0.00 ريال",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        self.client_total_label.pack(pady=10)

    def create_return_section(self):
        """إنشاء قسم المرتجعات"""
        return_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        return_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(return_frame, fg_color="#E91E63", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="↩️ المرتجعات",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(return_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # رقم الفاتورة
        ctk.CTkLabel(fields_frame, text="رقم الفاتورة:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.return_invoice_entry = ctk.CTkEntry(fields_frame, width=150, placeholder_text="رقم الفاتورة")
        self.return_invoice_entry.grid(row=0, column=1, padx=5, pady=5)

        # سبب الإرجاع
        ctk.CTkLabel(fields_frame, text="سبب الإرجاع:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.return_reason_combo = ctk.CTkComboBox(
            fields_frame,
            values=["عيب في المنتج", "عدم مطابقة المواصفات", "طلب العميل", "انتهاء الصلاحية"],
            width=180
        )
        self.return_reason_combo.grid(row=0, column=3, padx=5, pady=5)

        # المبلغ
        ctk.CTkLabel(fields_frame, text="المبلغ:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.return_amount_entry = ctk.CTkEntry(fields_frame, width=100, placeholder_text="0.00")
        self.return_amount_entry.grid(row=0, column=5, padx=5, pady=5)

        # زر إضافة
        add_return_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة",
            command=self.add_return_transaction,
            fg_color="#E91E63",
            hover_color="#C2185B",
            width=80,
            height=30
        )
        add_return_btn.grid(row=0, column=6, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("رقم الفاتورة", "سبب الإرجاع", "المبلغ")
        self.return_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.return_tree.heading(col, text=col)
            self.return_tree.column(col, width=150, anchor="center")

        # شريط التمرير
        return_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.return_tree.yview)
        self.return_tree.configure(yscrollcommand=return_scrollbar.set)

        self.return_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        return_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف مع تأخير لضمان التحديد
        self.return_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.return_tree, self.update_return_total)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.return_tree, self.update_return_total)

        # إجمالي المرتجعات
        self.return_total_label = ctk.CTkLabel(
            return_frame,
            text="إجمالي المرتجعات: 0.00 ريال",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        self.return_total_label.pack(pady=10)

    def create_suppliers_section(self):
        """إنشاء قسم الموردين (لا يؤثر على الحسابات)"""
        suppliers_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=15)
        suppliers_frame.pack(pady=10, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(suppliers_frame, fg_color="#795548", corner_radius=10)
        title_frame.pack(pady=10, padx=15, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="🏭 الموردين (لا يؤثر على الحسابات)",
            font=("Arial", 18, "bold"),
            text_color="white"
        ).pack(pady=10)

        # إطار الإدخال
        input_frame = ctk.CTkFrame(suppliers_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=15, fill="x")

        # حقول الإدخال
        fields_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=0)
        fields_frame.pack(pady=10, padx=10, fill="x")

        # اسم المورد
        ctk.CTkLabel(fields_frame, text="اسم المورد:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.supplier_name_entry = ctk.CTkEntry(fields_frame, width=200, placeholder_text="اسم المورد")
        self.supplier_name_entry.grid(row=0, column=1, padx=5, pady=5)

        # المبلغ المسلم
        ctk.CTkLabel(fields_frame, text="المبلغ المسلم:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.supplier_amount_entry = ctk.CTkEntry(fields_frame, width=120, placeholder_text="0.00")
        self.supplier_amount_entry.grid(row=0, column=3, padx=5, pady=5)

        # نوع الدفع
        ctk.CTkLabel(fields_frame, text="طريقة الدفع:", font=("Arial", 12, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky="e")
        self.supplier_payment_combo = ctk.CTkComboBox(
            fields_frame,
            values=["نقدي", "شيك", "تحويل بنكي"],
            width=120
        )
        self.supplier_payment_combo.grid(row=0, column=5, padx=5, pady=5)
        self.supplier_payment_combo.set("نقدي")

        # ملاحظات
        ctk.CTkLabel(fields_frame, text="ملاحظات:", font=("Arial", 12, "bold")).grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.supplier_notes_entry = ctk.CTkEntry(fields_frame, width=300, placeholder_text="ملاحظات (اختياري)")
        self.supplier_notes_entry.grid(row=1, column=1, columnspan=3, padx=5, pady=5, sticky="ew")

        # زر إضافة
        add_supplier_btn = ctk.CTkButton(
            fields_frame,
            text="➕ إضافة مورد",
            command=self.add_supplier_transaction,
            fg_color="#795548",
            hover_color="#5D4037",
            width=100,
            height=30
        )
        add_supplier_btn.grid(row=1, column=4, columnspan=2, padx=10, pady=5)

        # الجدول
        table_frame = ctk.CTkFrame(input_frame, fg_color="#f2f3f7", corner_radius=5)
        table_frame.pack(pady=10, padx=10, fill="both", expand=True)

        columns = ("اسم المورد", "المبلغ المسلم", "طريقة الدفع", "ملاحظات")
        self.suppliers_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=6)

        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == "اسم المورد":
                self.suppliers_tree.column(col, width=200, anchor="center")
            elif col == "المبلغ المسلم":
                self.suppliers_tree.column(col, width=120, anchor="center")
            elif col == "طريقة الدفع":
                self.suppliers_tree.column(col, width=120, anchor="center")
            else:  # ملاحظات
                self.suppliers_tree.column(col, width=250, anchor="center")

        # شريط التمرير
        suppliers_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=suppliers_scrollbar.set)

        self.suppliers_tree.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        suppliers_scrollbar.pack(side="right", fill="y", pady=5)

        # ربط النقر المزدوج للحذف
        self.suppliers_tree.bind("<Double-1>", lambda e: self.after(10, lambda: self.delete_selected_item(self.suppliers_tree, self.update_suppliers_display)))

        # إضافة قائمة سياق للحذف بالنقر الأيمن
        self.create_context_menu(self.suppliers_tree, self.update_suppliers_display)

        # إجمالي المدفوعات للموردين (للعرض فقط)
        self.suppliers_total_label = ctk.CTkLabel(
            suppliers_frame,
            text="إجمالي المدفوعات للموردين: 0.00 ريال (لا يؤثر على الحسابات)",
            font=("Arial", 14, "bold"),
            text_color="#795548"
        )
        self.suppliers_total_label.pack(pady=10)

        # تنبيه
        warning_label = ctk.CTkLabel(
            suppliers_frame,
            text="⚠️ ملاحظة: هذا القسم للمتابعة فقط ولا يؤثر على حسابات التصفية",
            font=("Arial", 12, "italic"),
            text_color="#e67e22"
        )
        warning_label.pack(pady=5)

    def create_summary_section(self):
        """إنشاء ملخص التصفية"""
        summary_frame = ctk.CTkFrame(self.main_scrollable, fg_color="#e0e5ec", corner_radius=20)
        summary_frame.pack(pady=20, padx=20, fill="x")

        # العنوان
        title_frame = ctk.CTkFrame(summary_frame, fg_color="#34495e", corner_radius=15)
        title_frame.pack(pady=15, padx=20, fill="x")

        ctk.CTkLabel(
            title_frame,
            text="📊 ملخص التصفية النهائي",
            font=("Arial", 20, "bold"),
            text_color="white"
        ).pack(pady=15)

        # إطار الملخص
        content_frame = ctk.CTkFrame(summary_frame, fg_color="#f2f3f7", corner_radius=15)
        content_frame.pack(pady=15, padx=20, fill="x")

        # الصف الأول - المجاميع
        totals_row = ctk.CTkFrame(content_frame, fg_color="#f2f3f7", corner_radius=0)
        totals_row.pack(pady=15, fill="x")

        # إجمالي المقبوضات
        self.total_receipts_label = ctk.CTkLabel(
            totals_row,
            text="إجمالي المقبوضات: 0.00 ريال",
            font=("Arial", 16, "bold"),
            text_color="#27ae60"
        )
        self.total_receipts_label.pack(side="left", padx=20)

        # مبيعات النظام
        system_sales_frame = ctk.CTkFrame(totals_row, fg_color="#f2f3f7", corner_radius=0)
        system_sales_frame.pack(side="right", padx=20)

        ctk.CTkLabel(system_sales_frame, text="مبيعات النظام:", font=("Arial", 14, "bold")).pack(side="left", padx=5)
        self.system_sales_entry = ctk.CTkEntry(system_sales_frame, width=120, placeholder_text="0.00")
        self.system_sales_entry.pack(side="left", padx=5)
        self.system_sales_entry.bind("<KeyRelease>", self.calculate_difference)

        # الصف الثاني - الفارق
        difference_row = ctk.CTkFrame(content_frame, fg_color="#f2f3f7", corner_radius=0)
        difference_row.pack(pady=15, fill="x")

        self.difference_label = ctk.CTkLabel(
            difference_row,
            text="الفارق: 0.00 ريال",
            font=("Arial", 18, "bold"),
            text_color="#e74c3c"
        )
        self.difference_label.pack(pady=10)

    # دوال إضافة العمليات
    def add_bank_transaction(self):
        """إضافة عملية مقبوضات بنكية"""
        bank_type = self.bank_type_combo.get()
        bank_name = self.bank_name_combo.get()
        amount_str = self.bank_amount_entry.get()

        if not bank_type or not bank_name or not amount_str:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # إضافة للجدول
        self.bank_tree.insert("", "end", values=(bank_type, bank_name, f"{amount:.2f}"))

        # تحديث المجموع
        self.totals['bank'] += amount
        self.update_bank_total()
        self.update_total_receipts()

        # مسح الحقول
        self.bank_type_combo.set("")
        self.bank_name_combo.set("")
        self.bank_amount_entry.delete(0, "end")

    def add_cash_transaction(self):
        """إضافة عملية مقبوضات نقدية"""
        denomination = self.cash_denomination_combo.get()
        count_str = self.cash_count_entry.get()
        amount_str = self.cash_amount_entry.get()

        if not denomination or not amount_str:
            messagebox.showwarning("تحذير", "يرجى ملء الحقول المطلوبة")
            return

        try:
            amount = float(amount_str)
            count = int(count_str) if count_str else 0
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")
            return

        # إضافة للجدول
        self.cash_tree.insert("", "end", values=(denomination, count, f"{amount:.2f}"))

        # تحديث المجموع
        self.totals['cash'] += amount
        self.update_cash_total()
        self.update_total_receipts()

        # مسح الحقول
        self.cash_denomination_combo.set("")
        self.cash_count_entry.delete(0, "end")
        self.cash_amount_entry.delete(0, "end")

    def add_credit_transaction(self):
        """إضافة عملية مبيعات آجلة"""
        client_name = self.credit_client_entry.get()
        invoice_number = self.credit_invoice_entry.get()
        amount_str = self.credit_amount_entry.get()

        if not client_name or not invoice_number or not amount_str:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # إضافة للجدول
        self.credit_tree.insert("", "end", values=(client_name, invoice_number, f"{amount:.2f}"))

        # تحديث المجموع
        self.totals['credit'] += amount
        self.update_credit_total()
        self.update_total_receipts()

        # مسح الحقول
        self.credit_client_entry.delete(0, "end")
        self.credit_invoice_entry.delete(0, "end")
        self.credit_amount_entry.delete(0, "end")

    def add_client_transaction(self):
        """إضافة عملية مقبوضات من العملاء"""
        client_name = self.client_name_entry.get()
        client_type = self.client_type_combo.get()
        payment_method = self.client_payment_method_combo.get()
        amount_str = self.client_amount_entry.get()
        ref_number = self.client_ref_entry.get()

        if not client_name or not client_type or not payment_method or not amount_str:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول المطلوبة")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # التحقق من رقم المرجع للشبكة
        if payment_method == "شبكة" and not ref_number:
            if not messagebox.askyesno("تأكيد", "لم تدخل رقم المرجع للدفع بالشبكة. هل تريد المتابعة؟"):
                return

        # إضافة للجدول
        self.client_tree.insert("", "end", values=(
            client_name,
            client_type,
            payment_method,
            f"{amount:.2f}",
            ref_number or "-"
        ))

        # تحديث المجموع
        self.totals['client'] += amount
        self.update_client_total()
        self.update_total_receipts()

        # مسح الحقول
        self.client_name_entry.delete(0, "end")
        self.client_type_combo.set("")
        self.client_payment_method_combo.set("نقدي")
        self.client_amount_entry.delete(0, "end")
        self.client_ref_entry.delete(0, "end")

    def add_supplier_transaction(self):
        """إضافة عملية دفع لمورد (لا تؤثر على الحسابات)"""
        supplier_name = self.supplier_name_entry.get()
        amount_str = self.supplier_amount_entry.get()
        payment_method = self.supplier_payment_combo.get()
        notes = self.supplier_notes_entry.get()

        if not supplier_name or not amount_str or not payment_method:
            messagebox.showwarning("تحذير", "يرجى ملء الحقول المطلوبة (اسم المورد، المبلغ، طريقة الدفع)")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # إضافة للجدول
        self.suppliers_tree.insert("", "end", values=(
            supplier_name,
            f"{amount:.2f}",
            payment_method,
            notes or "-"
        ))

        # تحديث العرض (لا يؤثر على المجاميع الرئيسية)
        self.update_suppliers_display()

        # مسح الحقول
        self.supplier_name_entry.delete(0, "end")
        self.supplier_amount_entry.delete(0, "end")
        self.supplier_payment_combo.set("نقدي")
        self.supplier_notes_entry.delete(0, "end")

        # رسالة تأكيد
        messagebox.showinfo("تم", f"تم إضافة المورد '{supplier_name}' بمبلغ {amount:.2f} ريال\n(لا يؤثر على حسابات التصفية)")

    def add_return_transaction(self):
        """إضافة عملية مرتجعات"""
        invoice_number = self.return_invoice_entry.get()
        return_reason = self.return_reason_combo.get()
        amount_str = self.return_amount_entry.get()

        if not invoice_number or not return_reason or not amount_str:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showwarning("تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
            return

        # إضافة للجدول
        self.return_tree.insert("", "end", values=(invoice_number, return_reason, f"{amount:.2f}"))

        # تحديث المجموع
        self.totals['return'] += amount
        self.update_return_total()
        self.update_total_receipts()

        # مسح الحقول
        self.return_invoice_entry.delete(0, "end")
        self.return_reason_combo.set("")
        self.return_amount_entry.delete(0, "end")

    # دوال مساعدة
    def calculate_cash_amount(self):
        """حساب مبلغ النقدية تلقائياً"""
        denomination_str = self.cash_denomination_combo.get()
        count_str = self.cash_count_entry.get()

        if not denomination_str or not count_str:
            return

        try:
            # استخراج القيمة من النص
            if "ريال" in denomination_str:
                value = float(denomination_str.split()[0])
            elif "هللة" in denomination_str:
                value = float(denomination_str.split()[0]) / 100
            else:
                return

            count = int(count_str)
            total = value * count

            self.cash_amount_entry.delete(0, "end")
            self.cash_amount_entry.insert(0, f"{total:.2f}")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

    def delete_selected_item(self, tree, update_function):
        """حذف العنصر المحدد من الجدول"""
        try:
            selected_item = tree.selection()
            if not selected_item:
                messagebox.showwarning("تحذير", "يرجى اختيار عنصر للحذف")
                return

            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا العنصر؟"
            )

            if not result:
                return

            # الحصول على قيمة المبلغ
            item_values = tree.item(selected_item[0])['values']

            if not item_values:
                messagebox.showerror("خطأ", "لا يمكن الحصول على بيانات العنصر")
                return

            # تحويل المبلغ إلى رقم عشري مع معالجة الأخطاء
            try:
                amount_str = str(item_values[-1])  # المبلغ في العمود الأخير
                # إزالة أي نصوص إضافية مثل "ريال" أو فراغات أو رموز
                amount_str = (amount_str.replace("ريال", "")
                                      .replace(",", "")
                                      .replace("$", "")
                                      .replace("SR", "")
                                      .replace("SAR", "")
                                      .strip())

                # إذا كان النص فارغاً بعد التنظيف، استخدم 0
                if not amount_str:
                    amount = 0.0
                else:
                    amount = float(amount_str)

            except (ValueError, IndexError) as e:
                print(f"تحذير: لا يمكن تحويل المبلغ '{item_values[-1]}' إلى رقم: {e}")
                # محاولة أخيرة لاستخراج الأرقام فقط
                import re
                try:
                    numbers = re.findall(r'\d+\.?\d*', str(item_values[-1]))
                    if numbers:
                        amount = float(numbers[0])
                    else:
                        amount = 0.0
                except:
                    messagebox.showerror("خطأ", f"لا يمكن تحويل المبلغ إلى رقم: {item_values[-1]}")
                    return

            # تحديث المجموع
            if tree == self.bank_tree:
                self.totals['bank'] -= amount
                self.totals['bank'] = max(0, self.totals['bank'])  # تجنب القيم السالبة
            elif tree == self.cash_tree:
                self.totals['cash'] -= amount
                self.totals['cash'] = max(0, self.totals['cash'])
            elif tree == self.credit_tree:
                self.totals['credit'] -= amount
                self.totals['credit'] = max(0, self.totals['credit'])
            elif tree == self.client_tree:
                self.totals['client'] -= amount
                self.totals['client'] = max(0, self.totals['client'])
            elif tree == self.return_tree:
                self.totals['return'] -= amount
                self.totals['return'] = max(0, self.totals['return'])

            # حذف العنصر
            tree.delete(selected_item[0])

            # تحديث المجاميع
            update_function()
            self.update_total_receipts()

            # إعادة حساب الفارق
            self.calculate_difference()

            # رسالة نجاح (اختيارية - يمكن إزالتها لتجنب الإزعاج)
            # messagebox.showinfo("نجح", "تم حذف العنصر بنجاح")

            print(f"تم حذف عنصر بقيمة {amount:.2f} ريال بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العنصر: {e}")
            print(f"خطأ في حذف العنصر: {e}")
            import traceback
            traceback.print_exc()

    def create_context_menu(self, tree, update_function):
        """إنشاء قائمة سياق للحذف بالنقر الأيمن"""
        from tkinter import Menu

        def show_context_menu(event):
            try:
                # تحديد العنصر المنقور عليه
                item = tree.identify_row(event.y)
                if item:
                    tree.selection_set(item)
                    context_menu.post(event.x_root, event.y_root)
            except Exception as e:
                print(f"خطأ في عرض قائمة السياق: {e}")

        def delete_item():
            """حذف العنصر المحدد"""
            self.delete_selected_item(tree, update_function)

        # إنشاء قائمة السياق
        context_menu = Menu(tree, tearoff=0)
        context_menu.add_command(label="🗑️ حذف العنصر", command=delete_item)
        context_menu.add_separator()
        context_menu.add_command(label="❌ إلغاء", command=lambda: None)

        # ربط النقر الأيمن
        tree.bind("<Button-3>", show_context_menu)  # Windows/Linux
        tree.bind("<Button-2>", show_context_menu)  # Mac

    # دوال تحديث المجاميع
    def update_bank_total(self):
        """تحديث إجمالي المقبوضات البنكية"""
        self.bank_total_label.configure(text=f"إجمالي المقبوضات البنكية: {self.totals['bank']:.2f} ريال")

    def update_cash_total(self):
        """تحديث إجمالي المقبوضات النقدية"""
        self.cash_total_label.configure(text=f"إجمالي المقبوضات النقدية: {self.totals['cash']:.2f} ريال")

    def update_credit_total(self):
        """تحديث إجمالي المبيعات الآجلة"""
        self.credit_total_label.configure(text=f"إجمالي المبيعات الآجلة: {self.totals['credit']:.2f} ريال")

    def update_client_total(self):
        """تحديث إجمالي المقبوضات من العملاء"""
        self.client_total_label.configure(text=f"إجمالي المقبوضات من العملاء: {self.totals['client']:.2f} ريال")

    def update_return_total(self):
        """تحديث إجمالي المرتجعات"""
        self.return_total_label.configure(text=f"إجمالي المرتجعات: {self.totals['return']:.2f} ريال")

    def update_suppliers_display(self):
        """تحديث عرض إجمالي الموردين (للعرض فقط - لا يؤثر على الحسابات)"""
        total_suppliers = 0.0
        for item in self.suppliers_tree.get_children():
            values = self.suppliers_tree.item(item)['values']
            if len(values) >= 2:
                try:
                    amount = float(values[1])
                    total_suppliers += amount
                except (ValueError, IndexError):
                    continue

        self.suppliers_total_label.configure(
            text=f"إجمالي المدفوعات للموردين: {total_suppliers:.2f} ريال (لا يؤثر على الحسابات)"
        )

    def update_total_receipts(self):
        """تحديث إجمالي المقبوضات"""
        total = (self.totals['bank'] + self.totals['cash'] +
                self.totals['credit'] + self.totals['return'] -
                self.totals['client'])

        self.total_receipts_label.configure(text=f"إجمالي المقبوضات: {total:.2f} ريال")
        self.calculate_difference()

    def calculate_difference(self, event=None):
        """حساب الفارق بين المقبوضات ومبيعات النظام"""
        try:
            system_sales = float(self.system_sales_entry.get() or 0)
            total_receipts = (self.totals['bank'] + self.totals['cash'] +
                            self.totals['credit'] + self.totals['return'] -
                            self.totals['client'])

            difference = total_receipts - system_sales

            if difference > 0:
                color = "#27ae60"  # أخضر للفائض
                text = f"الفارق: +{difference:.2f} ريال (فائض)"
            elif difference < 0:
                color = "#e74c3c"  # أحمر للعجز
                text = f"الفارق: {difference:.2f} ريال (عجز)"
            else:
                color = "#f39c12"  # برتقالي للتوازن
                text = "الفارق: 0.00 ريال (متوازن)"

            self.difference_label.configure(text=text, text_color=color)

        except ValueError:
            self.difference_label.configure(text="الفارق: -- ريال", text_color="#7f8c8d")

    def load_old_data_if_exists(self):
        """تحميل البيانات القديمة إذا كانت موجودة"""
        if self.old_details:
            try:
                # تحميل البيانات من JSON
                data = json.loads(self.old_details)

                # تحميل المجاميع
                if 'totals' in data:
                    self.totals = data['totals']
                    self.update_bank_total()
                    self.update_cash_total()
                    self.update_credit_total()
                    self.update_client_total()
                    self.update_return_total()
                    self.update_total_receipts()

                # تحميل مبيعات النظام
                if 'system_sales' in data:
                    self.system_sales_entry.insert(0, str(data['system_sales']))
                    self.calculate_difference()

                # تحميل تفاصيل العمليات في الجداول
                self.load_transactions_to_tables(data)

            except Exception as e:
                print(f"خطأ في تحميل البيانات القديمة: {e}")

    def load_transactions_to_tables(self, data):
        """تحميل تفاصيل العمليات في الجداول"""
        try:
            # تحميل المقبوضات البنكية
            bank_transactions = data.get('bank_transactions', [])
            for transaction in bank_transactions:
                self.bank_tree.insert("", "end", values=(
                    transaction.get('type', ''),
                    transaction.get('bank', ''),
                    f"{transaction.get('amount', 0):.2f}",
                    transaction.get('ref', '')
                ))

            # تحميل فواتير عملاء الآجل
            credit_transactions = data.get('credit_transactions', [])
            for transaction in credit_transactions:
                self.credit_tree.insert("", "end", values=(
                    transaction.get('client', ''),
                    transaction.get('invoice', ''),
                    f"{transaction.get('amount', 0):.2f}"
                ))

            # تحميل المقبوضات من العملاء (مع طريقة الدفع الجديدة)
            client_transactions = data.get('client_transactions', [])
            for transaction in client_transactions:
                self.client_tree.insert("", "end", values=(
                    transaction.get('client', ''),
                    transaction.get('type', ''),
                    transaction.get('payment_method', 'نقدي'),
                    f"{transaction.get('amount', 0):.2f}",
                    transaction.get('ref', '')
                ))

            # تحميل الموردين (إذا كانت موجودة)
            suppliers_transactions = data.get('suppliers_transactions', [])
            for transaction in suppliers_transactions:
                self.suppliers_tree.insert("", "end", values=(
                    transaction.get('supplier_name', ''),
                    f"{transaction.get('amount', 0):.2f}",
                    transaction.get('payment_method', 'نقدي'),
                    transaction.get('notes', '')
                ))

            # تحديث عرض الموردين
            if suppliers_transactions:
                self.update_suppliers_display()

            # تحميل المرتجعات
            return_transactions = data.get('return_transactions', [])
            for transaction in return_transactions:
                self.return_tree.insert("", "end", values=(
                    transaction.get('invoice', ''),
                    transaction.get('client', ''),
                    f"{transaction.get('amount', 0):.2f}",
                    transaction.get('reason', '')
                ))

            # تحميل المقبوضات النقدية
            cash_transactions = data.get('cash_transactions', [])
            for transaction in cash_transactions:
                self.cash_tree.insert("", "end", values=(
                    transaction.get('denomination', ''),
                    transaction.get('count', 0),
                    f"{transaction.get('amount', 0):.2f}"
                ))

            print(f"تم تحميل البيانات: {len(bank_transactions)} بنكية، {len(credit_transactions)} آجل، {len(client_transactions)} عملاء، {len(return_transactions)} مرتجعات")

        except Exception as e:
            print(f"خطأ في تحميل التفاصيل: {e}")

    # دوال الحفظ والطباعة
    def save_filter(self):
        """حفظ التصفية"""
        try:
            # جمع البيانات
            filter_details = {
                'totals': self.totals,
                'system_sales': float(self.system_sales_entry.get() or 0),
                'bank_transactions': [],
                'cash_transactions': [],
                'credit_transactions': [],
                'client_transactions': [],
                'return_transactions': []
            }

            # جمع تفاصيل العمليات من الجداول

            # المقبوضات البنكية
            for item in self.bank_tree.get_children():
                values = self.bank_tree.item(item)['values']
                filter_details['bank_transactions'].append({
                    'type': values[0],
                    'bank': values[1],
                    'amount': float(values[2]),
                    'ref': values[3] if len(values) > 3 else ''
                })

            # المقبوضات النقدية
            for item in self.cash_tree.get_children():
                values = self.cash_tree.item(item)['values']
                filter_details['cash_transactions'].append({
                    'denomination': values[0],
                    'count': int(values[1]) if values[1] else 0,
                    'amount': float(values[2])
                })

            # فواتير عملاء الآجل
            for item in self.credit_tree.get_children():
                values = self.credit_tree.item(item)['values']
                filter_details['credit_transactions'].append({
                    'client': values[0],
                    'invoice': values[1],
                    'amount': float(values[2]),
                    'date': self.filter_data.get('date', '')
                })

            # المقبوضات من العملاء (مع طريقة الدفع الجديدة)
            for item in self.client_tree.get_children():
                values = self.client_tree.item(item)['values']
                filter_details['client_transactions'].append({
                    'client': values[0],
                    'type': values[1],
                    'payment_method': values[2] if len(values) > 2 else 'نقدي',
                    'amount': float(values[3] if len(values) > 3 else values[2]),
                    'ref': values[4] if len(values) > 4 else ''
                })

            # المرتجعات
            for item in self.return_tree.get_children():
                values = self.return_tree.item(item)['values']
                filter_details['return_transactions'].append({
                    'invoice': values[0],
                    'client': values[1] if len(values) > 1 else '',
                    'amount': float(values[2] if len(values) > 2 else values[1]),
                    'reason': values[3] if len(values) > 3 else 'غير محدد'
                })

            # الموردين (للمتابعة فقط - لا يؤثر على الحسابات)
            filter_details['suppliers_transactions'] = []
            for item in self.suppliers_tree.get_children():
                values = self.suppliers_tree.item(item)['values']
                filter_details['suppliers_transactions'].append({
                    'supplier_name': values[0],
                    'amount': float(values[1]),
                    'payment_method': values[2],
                    'notes': values[3] if len(values) > 3 else ''
                })

            # استخدام دالة الحفظ المحسنة مع الرقم التسلسلي
            from db.filter_ops import save_filter

            # تحضير بيانات التصفية
            filter_data_for_save = {
                'cashier_name': self.filter_data.get('cashier_name', ''),
                'cashier_id': self.filter_data.get('cashier_id', ''),
                'admin_name': self.filter_data.get('admin_name', ''),
                'date': self.filter_data.get('date', ''),
                'notes': self.filter_data.get('notes', '')
            }

            # حفظ التصفية مع الرقم التسلسلي
            success = save_filter(filter_data_for_save, filter_details)

            if success:
                # تحديث الرقم التسلسلي في النافذة
                if not self.sequence_number:
                    self.sequence_number = self.get_sequence_number() - 1  # الرقم الذي تم حفظه للتو
                    # تحديث عنوان النافذة
                    self.title(f"التصفية اليومية للكاشير - رقم {self.sequence_number}")

                messagebox.showinfo("نجح", f"تم حفظ التصفية رقم {self.sequence_number} بنجاح!")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ التصفية")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التصفية: {e}")

    def update_filter(self):
        """تحديث التصفية الموجودة"""
        if not self.filter_id:
            messagebox.showerror("خطأ", "لا يمكن تحديث التصفية")
            return

        try:
            # جمع البيانات المحدثة
            filter_details = {
                'totals': self.totals.copy(),
                'system_sales': float(self.system_sales_entry.get() or 0),
                'cashier': self.filter_data.get('cashier', '') if hasattr(self, 'filter_data') else '',
                'date': self.filter_data.get('date', '') if hasattr(self, 'filter_data') else '',
                'admin_name': self.filter_data.get('admin_name', '') if hasattr(self, 'filter_data') else '',
                'notes': self.notes_text.get("1.0", "end-1c") if hasattr(self, 'notes_text') else '',
                'bank_transactions': [],
                'cash_transactions': [],
                'credit_transactions': [],
                'client_transactions': [],
                'return_transactions': []
            }

            # جمع تفاصيل العمليات من الجداول للتحديث

            # المقبوضات البنكية
            for item in self.bank_tree.get_children():
                values = self.bank_tree.item(item)['values']
                filter_details['bank_transactions'].append({
                    'type': values[0],
                    'bank': values[1],
                    'amount': float(values[2]),
                    'ref': values[3] if len(values) > 3 else ''
                })

            # المقبوضات النقدية
            for item in self.cash_tree.get_children():
                values = self.cash_tree.item(item)['values']
                filter_details['cash_transactions'].append({
                    'denomination': values[0],
                    'count': int(values[1]) if values[1] else 0,
                    'amount': float(values[2])
                })

            # فواتير عملاء الآجل
            for item in self.credit_tree.get_children():
                values = self.credit_tree.item(item)['values']
                filter_details['credit_transactions'].append({
                    'client': values[0],
                    'invoice': values[1],
                    'amount': float(values[2]),
                    'date': self.filter_data.get('date', '')
                })

            # المقبوضات من العملاء (مع طريقة الدفع الجديدة)
            for item in self.client_tree.get_children():
                values = self.client_tree.item(item)['values']
                filter_details['client_transactions'].append({
                    'client': values[0],
                    'type': values[1],
                    'payment_method': values[2] if len(values) > 2 else 'نقدي',
                    'amount': float(values[3] if len(values) > 3 else values[2]),
                    'ref': values[4] if len(values) > 4 else ''
                })

            # المرتجعات
            for item in self.return_tree.get_children():
                values = self.return_tree.item(item)['values']
                filter_details['return_transactions'].append({
                    'invoice': values[0],
                    'client': values[1] if len(values) > 1 else '',
                    'amount': float(values[2] if len(values) > 2 else values[1]),
                    'reason': values[3] if len(values) > 3 else 'غير محدد'
                })

            # تحديث في قاعدة البيانات
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # التحقق من وجود التصفية قبل التحديث
            c.execute("SELECT id FROM filters WHERE id = ?", (self.filter_id,))
            if not c.fetchone():
                messagebox.showerror("خطأ", f"لم يتم العثور على التصفية رقم {self.filter_id}")
                conn.close()
                return

            # تحديث البيانات
            c.execute("""
                UPDATE filters
                SET data = ?
                WHERE id = ?
            """, (
                json.dumps(filter_details, ensure_ascii=False),
                self.filter_id
            ))

            if c.rowcount > 0:
                conn.commit()
                # عرض رسالة النجاح مع الرقم التسلسلي
                sequence_text = f" (الرقم التسلسلي: {self.sequence_number})" if self.sequence_number else ""
                messagebox.showinfo("نجح", f"تم تحديث التصفية رقم {self.filter_id}{sequence_text} بنجاح!")
            else:
                messagebox.showerror("خطأ", "لم يتم تحديث أي بيانات")

            conn.close()

        except sqlite3.Error as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تحديث التصفية: {e}")
        except json.JSONEncodeError as e:
            messagebox.showerror("خطأ في البيانات", f"فشل في تحويل البيانات: {e}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث التصفية: {e}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")

    def print_filter(self):
        """طباعة التصفية"""
        try:
            from reports.html_print import generate_filter_report

            # جمع البيانات الحالية من الجداول للطباعة
            credit_transactions = []
            client_transactions = []
            bank_transactions = []
            return_transactions = []

            # جمع فواتير عملاء الآجل
            for item in self.credit_tree.get_children():
                values = self.credit_tree.item(item)['values']
                credit_transactions.append({
                    'client': values[0],
                    'invoice': values[1],
                    'amount': float(values[2]),
                    'date': self.filter_data.get('date', '')
                })

            # جمع المقبوضات من العملاء (مع طريقة الدفع الجديدة)
            for item in self.client_tree.get_children():
                values = self.client_tree.item(item)['values']
                client_transactions.append({
                    'client': values[0],
                    'type': values[1],
                    'payment_method': values[2] if len(values) > 2 else 'نقدي',
                    'amount': float(values[3] if len(values) > 3 else values[2]),
                    'ref': values[4] if len(values) > 4 else ''
                })

            # جمع المقبوضات البنكية
            for item in self.bank_tree.get_children():
                values = self.bank_tree.item(item)['values']
                bank_transactions.append({
                    'type': values[0],
                    'bank': values[1],
                    'amount': float(values[2]),
                    'ref': values[3] if len(values) > 3 else ''
                })

            # جمع المرتجعات
            for item in self.return_tree.get_children():
                values = self.return_tree.item(item)['values']
                return_transactions.append({
                    'invoice': values[0],
                    'client': values[1] if len(values) > 1 else '',
                    'amount': float(values[2] if len(values) > 2 else values[1]),
                    'reason': values[3] if len(values) > 3 else 'غير محدد'
                })

            # تحضير البيانات للطباعة
            filter_data_for_print = {
                'sequence_number': self.get_sequence_number(),
                'cashier_name': self.filter_data.get('cashier_name', ''),
                'cashier_number': self.filter_data.get('cashier_id', ''),  # إضافة رقم الكاشير
                'admin_name': self.filter_data.get('admin_name', ''),
                'date': self.filter_data.get('date', ''),
                'credit_transactions': credit_transactions,
                'client_transactions': client_transactions,
                'bank_transactions': bank_transactions,
                'return_transactions': return_transactions
            }

            system_sales = float(self.system_sales_entry.get() or 0)

            if generate_filter_report(filter_data_for_print, self.totals, system_sales):
                messagebox.showinfo("نجح", "تم فتح التقرير للطباعة!")
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء التقرير")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التصفية: {e}")

    def export_pdf(self):
        """تصدير التصفية كـ PDF"""
        try:
            from reports.export_utils import export_filter_to_pdf
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ التقرير كـ PDF"
            )
            if filename:
                # تحضير البيانات للتصدير
                filter_data_for_export = {
                    'sequence_number': self.get_sequence_number(),
                    'cashier_name': self.filter_data.get('cashier_name', ''),
                    'cashier_number': self.filter_data.get('cashier_id', ''),  # إضافة رقم الكاشير
                    'admin_name': self.filter_data.get('admin_name', ''),
                    'date': self.filter_data.get('date', '')
                }

                system_sales = float(self.system_sales_entry.get() or 0)

                if export_filter_to_pdf(filter_data_for_export, self.totals, system_sales, filename):
                    messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير PDF")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {e}")