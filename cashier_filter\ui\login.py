# نافذة تسجيل الدخول المحسنة
import customtkinter as ctk
from tkinter import messagebox
import sqlite3
import hashlib
from datetime import datetime
import json

# تحديد مسار قاعدة البيانات
import os
if os.path.exists("db/cashier_filter.db"):
    DB_PATH = "db/cashier_filter.db"
else:
    DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class LoginWindow(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        self.title("تسجيل الدخول - نظام تصفية الكاشير")
        self.geometry("500x650")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)
        
        # متغيرات تسجيل الدخول
        self.login_attempts = 0
        self.max_attempts = 3
        self.current_user = None
        
        # تعيين أيقونة إذا كانت متوفرة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
            
        self.create_widgets()
        self.center_window()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(
            self,
            fg_color="#f2f3f7",
            corner_radius=0
        )
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(
            main_frame,
            fg_color="#ffffff",
            corner_radius=25,
            border_width=2,
            border_color="#e0e5ec"
        )
        login_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العنوان الرئيسي
        title_frame = ctk.CTkFrame(
            login_frame,
            fg_color="transparent",
            corner_radius=0
        )
        title_frame.pack(pady=40, padx=30, fill="x")
        
        # أيقونة النظام
        icon_label = ctk.CTkLabel(
            title_frame,
            text="🏪",
            font=("Arial", 60),
            text_color="#4CAF50"
        )
        icon_label.pack()
        
        # عنوان النظام
        title_label = ctk.CTkLabel(
            title_frame,
            text="نظام تصفية الكاشير المتكامل 2025",
            font=("Arial", 28, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=(10, 5))
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="الإصدار الثالث - مع ذكاء اصطناعي متطور",
            font=("Arial", 16),
            text_color="#7f8c8d"
        )
        subtitle_label.pack()
        
        # إطار الحقول
        fields_frame = ctk.CTkFrame(
            login_frame,
            fg_color="transparent",
            corner_radius=0
        )
        fields_frame.pack(pady=30, padx=40, fill="x")
        
        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(
            fields_frame,
            text="👤 اسم المستخدم:",
            font=("Arial", 14, "bold"),
            text_color="#34495e"
        )
        username_label.pack(anchor="w", pady=(0, 5))
        
        self.username_entry = ctk.CTkEntry(
            fields_frame,
            placeholder_text="أدخل اسم المستخدم",
            font=("Arial", 14),
            height=45,
            corner_radius=10,
            border_width=2,
            border_color="#bdc3c7"
        )
        self.username_entry.pack(fill="x", pady=(0, 20))
        self.username_entry.bind("<Return>", lambda e: self.password_entry.focus())
        
        # حقل كلمة المرور
        password_label = ctk.CTkLabel(
            fields_frame,
            text="🔒 كلمة المرور:",
            font=("Arial", 14, "bold"),
            text_color="#34495e"
        )
        password_label.pack(anchor="w", pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            fields_frame,
            placeholder_text="أدخل كلمة المرور",
            font=("Arial", 14),
            height=45,
            corner_radius=10,
            border_width=2,
            border_color="#bdc3c7",
            show="*"
        )
        self.password_entry.pack(fill="x", pady=(0, 10))
        self.password_entry.bind("<Return>", lambda e: self.login())
        
        # خيار إظهار كلمة المرور
        self.show_password_var = ctk.BooleanVar()
        show_password_check = ctk.CTkCheckBox(
            fields_frame,
            text="إظهار كلمة المرور",
            variable=self.show_password_var,
            command=self.toggle_password_visibility,
            font=("Arial", 12),
            text_color="#7f8c8d"
        )
        show_password_check.pack(anchor="w", pady=(0, 20))
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(
            fields_frame,
            fg_color="transparent",
            corner_radius=0
        )
        buttons_frame.pack(fill="x", pady=10)
        
        # زر تسجيل الدخول
        self.login_btn = ctk.CTkButton(
            buttons_frame,
            text="🚀 تسجيل الدخول",
            font=("Arial", 16, "bold"),
            height=50,
            corner_radius=15,
            fg_color="#4CAF50",
            hover_color="#45a049",
            command=self.login
        )
        self.login_btn.pack(fill="x", pady=(0, 10))
        
        # زر الخروج
        exit_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ خروج",
            font=("Arial", 14),
            height=40,
            corner_radius=15,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            command=self.quit
        )
        exit_btn.pack(fill="x")
        
        # معلومات إضافية
        info_frame = ctk.CTkFrame(
            login_frame,
            fg_color="#ecf0f1",
            corner_radius=15
        )
        info_frame.pack(pady=20, padx=40, fill="x")
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="💡 للحصول على حساب جديد، تواصل مع مدير النظام",
            font=("Arial", 12),
            text_color="#7f8c8d",
            wraplength=350
        )
        info_label.pack(pady=15)
        
        # معلومات الإصدار وحقوق التطوير - إصدار 2025
        version_label = ctk.CTkLabel(
            login_frame,
            text="الإصدار 3.0.0 - نظام تصفية الكاشير المتكامل 2025",
            font=("Arial", 11, "bold"),
            text_color="#95a5a6"
        )
        version_label.pack(side="bottom", pady=5)

        developer_label = ctk.CTkLabel(
            login_frame,
            text="تطوير: محمد الكامل | © 2025 | جميع الحقوق محفوظة",
            font=("Arial", 9),
            text_color="#7f8c8d"
        )
        developer_label.pack(side="bottom", pady=5)

        features_label = ctk.CTkLabel(
            login_frame,
            text="✨ جديد: ذكاء اصطناعي | لوحة معلومات تفاعلية | تكامل سحابي",
            font=("Arial", 8),
            text_color="#3498db"
        )
        features_label.pack(side="bottom", pady=2)
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()

    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.show_password_var.get():
            self.password_entry.configure(show="")
        else:
            self.password_entry.configure(show="*")

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def validate_input(self):
        """التحقق من صحة البيانات المدخلة"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return False
        
        if not password:
            messagebox.showwarning("تحذير", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return False
        
        if len(password) < 6:
            messagebox.showwarning("تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            self.password_entry.focus()
            return False
        
        return True

    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            hashed_password = self.hash_password(password)
            
            c.execute("""
                SELECT id, name, username FROM admins 
                WHERE username = ? AND password = ?
            """, (username, hashed_password))
            
            user = c.fetchone()
            conn.close()
            
            return user
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في قاعدة البيانات: {e}")
            return None

    def log_login_attempt(self, username, success, user_id=None):
        """تسجيل محاولة تسجيل الدخول"""
        try:
            # يمكن إضافة جدول لتسجيل محاولات الدخول لاحقاً
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "username": username,
                "success": success,
                "user_id": user_id,
                "ip_address": "localhost"  # يمكن تحسينه لاحقاً
            }
            
            # حفظ في ملف سجل مؤقت
            with open("logs/login_attempts.log", "a", encoding="utf-8") as f:
                f.write(json.dumps(log_data, ensure_ascii=False) + "\n")
                
        except Exception:
            pass  # تجاهل أخطاء السجل

    def login(self):
        """تسجيل الدخول"""
        if not self.validate_input():
            return
        
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        # تعطيل زر تسجيل الدخول مؤقتاً
        self.login_btn.configure(state="disabled", text="جاري التحقق...")
        self.update()
        
        try:
            user = self.authenticate_user(username, password)
            
            if user:
                # تسجيل دخول ناجح
                self.current_user = {
                    "id": user[0],
                    "name": user[1],
                    "username": user[2],
                    "login_time": datetime.now()
                }
                
                self.log_login_attempt(username, True, user[0])
                
                messagebox.showinfo(
                    "مرحباً", 
                    f"مرحباً بك {user[1]}!\nتم تسجيل الدخول بنجاح"
                )
                
                # فتح النافذة الرئيسية
                self.open_main_window()
                
            else:
                # تسجيل دخول فاشل
                self.login_attempts += 1
                self.log_login_attempt(username, False)
                
                remaining_attempts = self.max_attempts - self.login_attempts
                
                if remaining_attempts > 0:
                    messagebox.showerror(
                        "خطأ في تسجيل الدخول",
                        f"اسم المستخدم أو كلمة المرور غير صحيحة\n"
                        f"المحاولات المتبقية: {remaining_attempts}"
                    )
                    
                    # مسح كلمة المرور
                    self.password_entry.delete(0, "end")
                    self.password_entry.focus()
                    
                else:
                    messagebox.showerror(
                        "تم حظر الوصول",
                        "تم استنفاد عدد المحاولات المسموحة\n"
                        "سيتم إغلاق التطبيق"
                    )
                    self.quit()
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ غير متوقع: {e}")
        
        finally:
            # إعادة تفعيل زر تسجيل الدخول
            self.login_btn.configure(state="normal", text="🚀 تسجيل الدخول")

    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        try:
            # إخفاء نافذة تسجيل الدخول
            self.withdraw()
            
            # تهيئة نظام الصلاحيات
            from utils.permissions import set_current_user
            set_current_user(self.current_user)

            # استيراد وفتح النافذة الرئيسية
            from ui.main_window import MainWindow

            main_app = MainWindow(current_user=self.current_user)
            
            # إضافة معلومات المستخدم للواجهة الرئيسية
            # self.add_user_info_to_main_window(main_app)  # معطل مؤقتاً
            
            # عند إغلاق النافذة الرئيسية، إظهار نافذة تسجيل الدخول مرة أخرى
            main_app.protocol("WM_DELETE_WINDOW", lambda: self.on_main_window_close(main_app))
            
            main_app.mainloop()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح النافذة الرئيسية: {e}")
            self.deiconify()  # إظهار نافذة تسجيل الدخول مرة أخرى

    def add_user_info_to_main_window(self, main_window):
        """إضافة معلومات المستخدم للنافذة الرئيسية"""
        try:
            print(f"إضافة معلومات المستخدم: {self.current_user}")  # للتشخيص

            # إضافة شريط معلومات المستخدم
            user_frame = ctk.CTkFrame(main_window, fg_color="#34495e", corner_radius=0, height=40)
            user_frame.pack(side="bottom", fill="x")
            user_frame.pack_propagate(False)

            # معلومات المستخدم
            if self.current_user and isinstance(self.current_user, dict):
                user_name = self.current_user.get('name', 'مستخدم')
                login_time = self.current_user.get('login_time')
                if login_time:
                    time_str = login_time.strftime('%H:%M:%S')
                    user_info = f"👤 {user_name} | 🕐 {time_str}"
                else:
                    user_info = f"👤 {user_name}"
            else:
                user_info = "👤 مستخدم"

            user_label = ctk.CTkLabel(
                user_frame,
                text=user_info,
                font=("Arial", 12),
                text_color="white"
            )
            user_label.pack(side="left", padx=20, pady=10)

            # بيانات التطوير في الوسط
            developer_info = "تطوير: محمد الكامل | الإصدار 3.0.0 | جميع الحقوق محفوظة"
            developer_label = ctk.CTkLabel(
                user_frame,
                text=developer_info,
                font=("Arial", 11),
                text_color="#bdc3c7"
            )
            developer_label.pack(expand=True, pady=10)

            # زر تسجيل الخروج
            logout_btn = ctk.CTkButton(
                user_frame,
                text="تسجيل خروج",
                font=("Arial", 12),
                width=100,
                height=25,
                fg_color="#e74c3c",
                hover_color="#c0392b",
                command=lambda: self.logout(main_window)
            )
            logout_btn.pack(side="right", padx=20, pady=7)

            print("تم إنشاء زر تسجيل الخروج بنجاح")  # للتشخيص

        except Exception as e:
            print(f"خطأ في إضافة معلومات المستخدم: {e}")
            import traceback
            traceback.print_exc()

    def logout(self, main_window):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج من النظام؟"):
            try:
                # تسجيل عملية الخروج
                if self.current_user and isinstance(self.current_user, dict):
                    username = self.current_user.get('username', 'unknown')
                    user_id = self.current_user.get('id', 0)
                    self.log_login_attempt(username, True, user_id)
            except Exception as e:
                print(f"خطأ في تسجيل عملية الخروج: {e}")

            # إغلاق النافذة الرئيسية
            main_window.destroy()
            
            # إعادة تعيين البيانات
            self.current_user = None
            self.login_attempts = 0
            self.username_entry.delete(0, "end")
            self.password_entry.delete(0, "end")
            
            # إظهار نافذة تسجيل الدخول
            self.deiconify()
            self.username_entry.focus()

    def on_main_window_close(self, main_window):
        """عند إغلاق النافذة الرئيسية"""
        if messagebox.askyesno("إغلاق التطبيق", "هل تريد إغلاق التطبيق؟"):
            main_window.destroy()
            self.quit()

def run_login():
    """تشغيل نافذة تسجيل الدخول"""
    app = LoginWindow()
    app.mainloop()

if __name__ == "__main__":
    run_login()
