#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة الموردين
Test Suppliers UI
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    import customtkinter as ctk
    import tkinter as tk
    from tkinter import ttk
    from datetime import datetime
    
    def test_suppliers_tree():
        """اختبار جدول الموردين"""
        print("🌳 اختبار جدول الموردين...")
        print("=" * 50)
        
        try:
            # إنشاء نافذة اختبار
            root = ctk.CTk()
            root.title("اختبار جدول الموردين")
            root.geometry("800x600")
            
            # إنشاء جدول الموردين (محاكاة daily_filter.py)
            suppliers_frame = ctk.CTkFrame(root)
            suppliers_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            # عنوان
            title_label = ctk.CTkLabel(suppliers_frame, text="🏭 الموردين", font=("Arial", 16, "bold"))
            title_label.pack(pady=10)
            
            # إطار الجدول
            tree_frame = ctk.CTkFrame(suppliers_frame)
            tree_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # إنشاء Treeview
            columns = ("supplier_name", "amount", "payment_method", "notes")
            suppliers_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=10)
            
            # تعيين العناوين
            suppliers_tree.heading("supplier_name", text="اسم المورد")
            suppliers_tree.heading("amount", text="المبلغ")
            suppliers_tree.heading("payment_method", text="طريقة الدفع")
            suppliers_tree.heading("notes", text="ملاحظات")
            
            # تعيين عرض الأعمدة
            suppliers_tree.column("supplier_name", width=200)
            suppliers_tree.column("amount", width=100)
            suppliers_tree.column("payment_method", width=120)
            suppliers_tree.column("notes", width=200)
            
            suppliers_tree.pack(fill="both", expand=True, padx=10, pady=10)
            
            # إضافة بيانات تجريبية
            test_suppliers = [
                ("شركة الأغذية المتحدة", "15000.00", "تحويل بنكي", "فاتورة رقم 2024-001"),
                ("مؤسسة التوريدات الحديثة", "8500.00", "شيك", "شيك رقم 123456"),
                ("شركة المنظفات الحديثة", "3500.00", "تحويل بنكي", "فاتورة شهرية"),
                ("شركة الألبان الطبيعية", "1800.00", "نقدي", "دفع نقدي"),
                ("ساجد", "1000.00", "نقدي", "")
            ]
            
            print("📋 إضافة بيانات تجريبية:")
            for i, supplier in enumerate(test_suppliers, 1):
                suppliers_tree.insert("", "end", values=supplier)
                print(f"   {i}. {supplier[0]}: {supplier[1]} ريال ({supplier[2]})")
            
            # دالة اختبار جمع البيانات
            def test_collect_data():
                print("\n🔍 اختبار جمع البيانات من الجدول:")
                suppliers_transactions = []
                suppliers_items = suppliers_tree.get_children()
                
                print(f"   عدد العناصر في الجدول: {len(suppliers_items)}")
                
                for i, item in enumerate(suppliers_items):
                    values = suppliers_tree.item(item)['values']
                    print(f"   {i+1}. القيم: {values}")
                    
                    if len(values) >= 3:
                        supplier_data = {
                            'supplier_name': values[0] if values[0] else 'غير محدد',
                            'amount': float(values[1]) if values[1] else 0.0,
                            'payment_method': values[2] if values[2] else 'نقدي',
                            'notes': values[3] if len(values) > 3 and values[3] else ''
                        }
                        suppliers_transactions.append(supplier_data)
                        print(f"      ✅ تم إضافة: {supplier_data}")
                    else:
                        print(f"      ❌ بيانات غير كاملة: {values}")
                
                print(f"\n📊 النتيجة النهائية:")
                print(f"   عدد الموردين المجمعين: {len(suppliers_transactions)}")
                print(f"   إجمالي المبالغ: {sum(s['amount'] for s in suppliers_transactions):.2f} ريال")
                
                return suppliers_transactions
            
            # دالة اختبار الطباعة
            def test_print():
                print("\n🖨️ اختبار الطباعة:")
                suppliers_data = test_collect_data()
                
                if suppliers_data:
                    # محاكاة استدعاء دالة الطباعة
                    from reports.html_print import generate_filter_report
                    
                    filter_data_for_print = {
                        'sequence_number': 'UI-TEST-001',
                        'cashier_name': 'كاشير اختبار الواجهة',
                        'cashier_number': '888',
                        'admin_name': 'مسؤول اختبار الواجهة',
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'credit_transactions': [],
                        'client_transactions': [],
                        'bank_transactions': [],
                        'return_transactions': [],
                        'suppliers_transactions': suppliers_data
                    }
                    
                    test_totals = {'bank': 0, 'cash': 0, 'credit': 0, 'client': 0, 'return': 0}
                    test_system_sales = 0
                    
                    if generate_filter_report(filter_data_for_print, test_totals, test_system_sales):
                        print("   ✅ تم إنشاء التقرير بنجاح!")
                        print("   🔍 تحقق من المتصفح لرؤية بيانات الموردين")
                    else:
                        print("   ❌ فشل في إنشاء التقرير")
                else:
                    print("   ❌ لا توجد بيانات موردين للطباعة")
            
            # أزرار الاختبار
            buttons_frame = ctk.CTkFrame(suppliers_frame)
            buttons_frame.pack(fill="x", padx=10, pady=10)
            
            collect_btn = ctk.CTkButton(buttons_frame, text="🔍 اختبار جمع البيانات", command=test_collect_data)
            collect_btn.pack(side="left", padx=5)
            
            print_btn = ctk.CTkButton(buttons_frame, text="🖨️ اختبار الطباعة", command=test_print)
            print_btn.pack(side="left", padx=5)
            
            close_btn = ctk.CTkButton(buttons_frame, text="❌ إغلاق", command=root.destroy)
            close_btn.pack(side="right", padx=5)
            
            print("\n✅ تم إنشاء واجهة الاختبار")
            print("🔍 استخدم الأزرار لاختبار جمع البيانات والطباعة")
            print("⏰ النافذة ستغلق تلقائياً بعد 30 ثانية")
            
            # إغلاق تلقائي بعد 30 ثانية
            root.after(30000, root.destroy)
            
            # تشغيل الواجهة
            root.mainloop()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الواجهة: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def main():
        """تشغيل اختبار الواجهة"""
        print("🧪 اختبار واجهة الموردين")
        print("=" * 60)
        
        # اختبار الواجهة
        ui_test = test_suppliers_tree()
        
        print("\n🎯 ملخص النتائج:")
        print("=" * 60)
        
        if ui_test:
            print("✅ اختبار الواجهة نجح")
            print("🔍 إذا كانت البيانات تظهر في الاختبار ولا تظهر في التطبيق:")
            print("   1. تأكد من إضافة بيانات في جدول الموردين قبل الطباعة")
            print("   2. تحقق من أن الجدول يحتوي على بيانات عند الضغط على طباعة")
            print("   3. راجع رسائل التشخيص في وحدة التحكم")
        else:
            print("❌ اختبار الواجهة فشل")
        
        print("=" * 60)

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("تأكد من تثبيت customtkinter:")
    print("pip install customtkinter")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
