#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف تنفيذي (.exe) للاستخدام الفوري
Create Executable (.exe) for Immediate Use
"""

import os
import sys
import shutil
import subprocess
import zipfile
import json
from datetime import datetime
from pathlib import Path

def check_pyinstaller():
    """التحقق من وجود PyInstaller وتثبيته إذا لزم الأمر"""
    print("🔍 التحقق من PyInstaller...")

    try:
        import PyInstaller
        print("✅ PyInstaller متاح")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت PyInstaller: {e}")
            return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    print("📄 إنشاء ملف التكوين (.spec)...")

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# تحديد الملفات الإضافية المطلوبة
added_files = [
    ('ui', 'ui'),
    ('db', 'db'),
    ('reports', 'reports'),
    ('utils', 'utils'),
    ('web_templates', 'web_templates'),
    ('web_static', 'web_static'),
    ('assets', 'assets'),
    ('config.py', '.'),
    ('settings.json', '.'),
    ('version_info.txt', '.'),
]

# تحديد الوحدات المخفية المطلوبة
hidden_imports = [
    'customtkinter',
    'flask',
    'requests',
    'pandas',
    'fpdf2',
    'PIL',
    'sqlite3',
    'json',
    'datetime',
    'threading',
    'webbrowser',
    'subprocess',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CashierFilterSystem_v3.5.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''

    with open('CashierFilterSystem.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✅ تم إنشاء ملف CashierFilterSystem.spec")
    return True

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    print("⏳ هذا قد يستغرق عدة دقائق...")

    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "CashierFilterSystem.spec"
        ]

        print(f"🚀 تشغيل الأمر: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي")
            print("📄 رسالة الخطأ:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ خطأ في بناء الملف التنفيذي: {e}")
        return False

def create_web_server_exe():
    """إنشاء ملف تنفيذي منفصل لخادم التقارير"""
    print("\n🌐 إنشاء ملف تنفيذي لخادم التقارير...")

    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--clean",
            "--noconfirm",
            "--name", "WebReportServer_v3.5.0",
            "--icon", "assets/icon.ico" if os.path.exists('assets/icon.ico') else None,
            "--add-data", "web_templates;web_templates",
            "--add-data", "web_static;web_static",
            "--add-data", "db;db",
            "--add-data", "reports;reports",
            "--hidden-import", "flask",
            "--hidden-import", "requests",
            "--hidden-import", "pandas",
            "web_server.py"
        ]

        # إزالة None من القائمة
        cmd = [arg for arg in cmd if arg is not None]

        print(f"🚀 بناء خادم التقارير...")

        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ تم بناء خادم التقارير بنجاح!")
            return True
        else:
            print("⚠️ فشل في بناء خادم التقارير (اختياري)")
            print(result.stderr[:500])  # أول 500 حرف من الخطأ
            return False

    except Exception as e:
        print(f"⚠️ خطأ في بناء خادم التقارير: {e}")
        return False

def create_exe_package():
    """إنشاء حزمة الملفات التنفيذية"""
    print("\n📦 إنشاء حزمة الملفات التنفيذية...")

    # إنشاء مجلد الحزمة
    package_dir = Path("CashierFilterSystem_EXE_Package")
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()

    print(f"📁 تم إنشاء مجلد الحزمة: {package_dir}")

    # نسخ الملف التنفيذي الرئيسي
    main_exe = Path("dist/CashierFilterSystem_v3.5.0.exe")
    if main_exe.exists():
        shutil.copy2(main_exe, package_dir / "CashierFilterSystem_v3.5.0.exe")
        print("✅ تم نسخ الملف التنفيذي الرئيسي")
    else:
        print("❌ الملف التنفيذي الرئيسي غير موجود")
        return False

    # نسخ خادم التقارير إذا كان موجوداً
    web_exe = Path("dist/WebReportServer_v3.5.0.exe")
    if web_exe.exists():
        shutil.copy2(web_exe, package_dir / "WebReportServer_v3.5.0.exe")
        print("✅ تم نسخ خادم التقارير")
    else:
        print("⚠️ خادم التقارير غير متاح (اختياري)")

    # إنشاء قاعدة بيانات فارغة
    db_dir = package_dir / "db"
    db_dir.mkdir()

    # نسخ ملف إنشاء قاعدة البيانات
    if Path("db/init_db.py").exists():
        shutil.copy2("db/init_db.py", db_dir / "init_db.py")

    # إنشاء قاعدة بيانات فارغة
    try:
        import sqlite3
        db_path = db_dir / "cashier_filter.db"
        conn = sqlite3.connect(str(db_path))

        # إنشاء الجداول الأساسية
        cursor = conn.cursor()

        # جدول المديرين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admins (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول الكاشيرين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cashiers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                cashier_number TEXT UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول التصفيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS filters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sequence_number INTEGER UNIQUE,
                cashier_name TEXT,
                cashier_number TEXT,
                admin_name TEXT,
                date TEXT,
                data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إدراج المدير الافتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO admins (username, password, full_name)
            VALUES (?, ?, ?)
        ''', ('admin', '123456', 'المدير الرئيسي'))

        conn.commit()
        conn.close()

        print("✅ تم إنشاء قاعدة البيانات")

    except Exception as e:
        print(f"⚠️ خطأ في إنشاء قاعدة البيانات: {e}")

    # نسخ الملفات الإضافية
    additional_files = [
        "README_COMPLETE.md",
        "دليل_الميزات_الجديدة.md",
        "دليل_التقارير_المحسنة.md",
        "دليل_الوصول_العالمي.md",
        "تعليمات_التوزيع_السريع.txt",
        "requirements_complete.txt"
    ]

    print("\n📄 نسخ الملفات الإضافية...")
    for file_name in additional_files:
        src = Path(file_name)
        if src.exists():
            shutil.copy2(src, package_dir / file_name)
            print(f"   ✅ {file_name}")
        else:
            print(f"   ⚠️ {file_name} (غير موجود)")

    return package_dir

def create_batch_files_for_exe(package_dir):
    """إنشاء ملفات تشغيل للملفات التنفيذية"""
    print("\n⚡ إنشاء ملفات التشغيل...")

    # ملف تشغيل النظام الرئيسي
    main_bat = f"""@echo off
chcp 65001 > nul
title نظام تصفية الكاشير v3.5.0 - الإصدار التنفيذي
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🏪 نظام تصفية الكاشير v3.5.0 - الإصدار التنفيذي      ██
echo ██                                                            ██
echo ██  🎉 جميع الميزات الجديدة مدمجة:                            ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء في التقارير                            ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██  📊 التقرير الشامل المحسن                                ██
echo ██  🌐 الوصول العالمي                                       ██
echo ██                                                            ██
echo ██  ⚡ لا يحتاج Python - يعمل فوراً!                        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 بدء تشغيل النظام...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🎯 بيانات تسجيل الدخول الافتراضية:                        │
echo │                                                             │
echo │     👤 اسم المستخدم: admin                                │
echo │     🔐 كلمة المرور: 123456                                │
echo │                                                             │
echo │  💡 يمكنك تغيير كلمة المرور من الإعدادات                  │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM تشغيل النظام
CashierFilterSystem_v3.5.0.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 💡 تأكد من وجود ملف CashierFilterSystem_v3.5.0.exe
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo 👋 شكراً لاستخدام نظام تصفية الكاشير!
)

pause
"""

    with open(package_dir / "تشغيل_النظام.bat", 'w', encoding='utf-8') as f:
        f.write(main_bat)
    print("   ✅ تشغيل_النظام.bat")

    # ملف تشغيل خادم التقارير
    if (package_dir / "WebReportServer_v3.5.0.exe").exists():
        web_bat = f"""@echo off
chcp 65001 > nul
title خادم التقارير المحسن v3.5.0
color 0B
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           🌐 خادم التقارير المحسن v3.5.0                   ██
echo ██                                                            ██
echo ██  📊 الميزات الجديدة في التقارير:                          ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  📄 رقم المرجع للمعاملات البنكية                         ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء الحقيقية                               ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██                                                            ██
echo ██  ⚡ لا يحتاج Python - يعمل فوراً!                        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 بدء تشغيل خادم التقارير...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🌐 خادم التقارير سيكون متاحاً على:                        │
echo │                                                             │
echo │     🔗 http://localhost:5000                               │
echo │                                                             │
echo │  📊 الميزات المتاحة:                                        │
echo │     • قائمة جميع التصفيات                                   │
echo │     • التقرير الشامل لكل تصفية                             │
echo │     • طباعة التقارير                                       │
echo │     • تصدير البيانات                                       │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

WebReportServer_v3.5.0.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل خادم التقارير
    pause
) else (
    echo.
    echo ✅ تم إيقاف خادم التقارير بنجاح
)

pause
"""

        with open(package_dir / "تشغيل_خادم_التقارير.bat", 'w', encoding='utf-8') as f:
            f.write(web_bat)
        print("   ✅ تشغيل_خادم_التقارير.bat")

def create_installation_guide_exe(package_dir):
    """إنشاء دليل التثبيت للإصدار التنفيذي"""
    print("\n📖 إنشاء دليل التثبيت للإصدار التنفيذي...")

    guide_content = f"""# 🚀 نظام تصفية الكاشير v3.5.0 - الإصدار التنفيذي

## ⚡ **لا يحتاج Python - يعمل فوراً على أي كمبيوتر!**

### 🎉 **الميزات الجديدة المدمجة:**
- ✅ **طريقة الدفع** في مقبوضات العملاء (نقدي/شبكة)
- ✅ **رقم المرجع** للمعاملات البنكية
- ✅ **جدول الموردين** منفصل عن الحسابات
- ✅ **أسماء العملاء** في جميع التقارير
- ✅ **حساب الفارق** الدقيق في التصفية
- ✅ **التقرير الشامل** المحسن على الويب
- ✅ **الوصول العالمي** عبر الإنترنت

---

## ⚡ التشغيل الفوري (10 ثوانٍ)

### 🖥️ **على أي كمبيوتر Windows:**
```
1. فك الضغط عن الملف
2. انقر مزدوجاً على: تشغيل_النظام.bat
3. انتظر التحميل (10 ثوانٍ)
4. سجل الدخول: admin / 123456
5. ابدأ الاستخدام فوراً! 🎉
```

### 🌐 **لتشغيل خادم التقارير:**
```
1. انقر مزدوجاً على: تشغيل_خادم_التقارير.bat
2. اذهب إلى: http://localhost:5000
3. استمتع بالتقارير المحسنة
```

---

## 🎯 بيانات تسجيل الدخول

### 🔐 **المدير الرئيسي:**
```
👤 اسم المستخدم: admin
🔑 كلمة المرور: 123456
```

### 💡 **ملاحظات:**
- لا يحتاج تثبيت Python أو أي برامج إضافية
- يعمل على Windows 7, 8, 10, 11
- حجم صغير وسرعة عالية
- جميع البيانات محفوظة محلياً

---

## 🌟 الميزات الجديدة

### 💳 **طريقة الدفع في مقبوضات العملاء:**
- اختيار نقدي أو شبكة
- إدخال رقم المرجع للشبكة
- عرض مميز في التقارير

### 🏭 **جدول الموردين:**
- تسجيل مدفوعات الموردين
- لا يؤثر على حسابات التصفية
- طرق دفع متعددة

### 👥 **أسماء العملاء:**
- عرض الأسماء الحقيقية في التقارير
- تفاصيل كاملة مع الهواتف
- ربط المعاملات بالعملاء

### ⚖️ **حساب الفارق:**
- حساب تلقائي للفارق
- تحديد نوع الفارق (متوازن/فائض/عجز)
- نسبة مئوية دقيقة

---

## 📊 التقرير الشامل

### 🌐 **الوصول:**
```
http://localhost:5000/filter/[رقم]/comprehensive
```

### ✨ **المحتويات:**
- الملخص المالي مع الألوان
- تحليل الفارق الدقيق
- أسماء العملاء الحقيقية
- طرق الدفع مع الأيقونات
- جدول الموردين منفصل
- تصميم احترافي للطباعة

---

## 🔍 استكشاف الأخطاء

### ❓ **لا يفتح التطبيق:**
```
1. تأكد من Windows 7 أو أحدث
2. شغل كمدير (Run as Administrator)
3. تحقق من مكافح الفيروسات
4. أعد تحميل الملف
```

### ❓ **رسالة "Windows protected your PC":**
```
1. انقر "More info"
2. انقر "Run anyway"
3. أو أضف للاستثناءات في Windows Defender
```

### ❓ **خادم التقارير لا يعمل:**
```
1. تأكد من عدم استخدام المنفذ 5000
2. أغلق برامج أخرى قد تستخدم المنفذ
3. شغل كمدير
```

---

## 📁 محتويات الحزمة

### 🔧 **الملفات التنفيذية:**
- `CashierFilterSystem_v3.5.0.exe` - التطبيق الرئيسي
- `WebReportServer_v3.5.0.exe` - خادم التقارير (اختياري)

### ⚡ **ملفات التشغيل:**
- `تشغيل_النظام.bat` - تشغيل التطبيق الرئيسي
- `تشغيل_خادم_التقارير.bat` - تشغيل خادم التقارير

### 📚 **الأدلة:**
- `README_COMPLETE.md` - دليل شامل
- `دليل_الميزات_الجديدة.md` - الميزات الجديدة
- `دليل_التقارير_المحسنة.md` - التقارير والطباعة

### 🗄️ **قاعدة البيانات:**
- `db/cashier_filter.db` - قاعدة بيانات فارغة جاهزة

---

## 🎊 المزايا

### ✅ **سهولة الاستخدام:**
- لا يحتاج تثبيت Python
- لا يحتاج تثبيت مكتبات
- يعمل فوراً بنقرة واحدة
- حجم صغير ومحمول

### ✅ **الأمان:**
- لا يحتاج اتصال إنترنت
- جميع البيانات محلية
- لا يرسل بيانات لأي خادم
- آمن تماماً للاستخدام

### ✅ **التوافق:**
- Windows 7, 8, 10, 11
- 32-bit و 64-bit
- لا يحتاج صلاحيات خاصة
- يعمل من USB أو أي مجلد

---

## 🎉 ابدأ الآن!

### 🚀 **خطوات بسيطة:**
1. **فك الضغط** عن الملف
2. **انقر مزدوجاً** على `تشغيل_النظام.bat`
3. **سجل الدخول** بـ admin / 123456
4. **استمتع** بجميع الميزات الجديدة!

**لا يحتاج Python - يعمل فوراً على أي كمبيوتر!** ⚡

---

**المطور:** محمد الكامل
**الإصدار:** 3.5.0 EXE
**تاريخ البناء:** {datetime.now().strftime("%Y-%m-%d")}
**الحالة:** ✅ جاهز للاستخدام الفوري
**النوع:** ملف تنفيذي مستقل
"""

    with open(package_dir / "دليل_الاستخدام_الفوري.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)

    print("✅ تم إنشاء دليل_الاستخدام_الفوري.md")

def create_final_zip(package_dir):
    """إنشاء ملف ZIP نهائي للتوزيع"""
    print("\n📦 إنشاء ملف ZIP للتوزيع...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"CashierFilterSystem_v3.5.0_EXE_{timestamp}.zip"

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_path)

    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    print(f"✅ تم إنشاء {zip_filename} ({zip_size:.1f} MB)")

    return zip_filename, zip_size

def create_package_info(package_dir, zip_filename, zip_size):
    """إنشاء ملف معلومات الحزمة"""
    print("\n📋 إنشاء ملف معلومات الحزمة...")

    # حساب حجم الملف التنفيذي
    main_exe = package_dir / "CashierFilterSystem_v3.5.0.exe"
    exe_size = 0
    if main_exe.exists():
        exe_size = main_exe.stat().st_size / (1024 * 1024)  # MB

    package_info = {
        "name": "نظام تصفية الكاشير - الإصدار التنفيذي",
        "version": "3.5.0 EXE",
        "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "type": "Executable Package",
        "zip_file": zip_filename,
        "zip_size_mb": round(zip_size, 1),
        "exe_size_mb": round(exe_size, 1),
        "requires_python": False,
        "features": [
            "طريقة الدفع في مقبوضات العملاء (نقدي/شبكة)",
            "رقم المرجع للمعاملات البنكية",
            "جدول الموردين منفصل عن الحسابات",
            "أسماء العملاء في التقارير",
            "حساب الفارق في التصفية",
            "التقرير الشامل المحسن",
            "خادم التقارير المحسن",
            "الوصول العالمي عبر Cloudflare",
            "تقارير HTML للطباعة",
            "واجهة محسنة مع تحسينات بصرية",
            "ملف تنفيذي مستقل - لا يحتاج Python"
        ],
        "system_requirements": [
            "Windows 7/8/10/11 (32-bit أو 64-bit)",
            "4GB RAM كحد أدنى",
            "500MB مساحة فارغة",
            "لا يحتاج Python أو مكتبات إضافية"
        ],
        "files": {
            "main_executable": "CashierFilterSystem_v3.5.0.exe",
            "web_server": "WebReportServer_v3.5.0.exe",
            "batch_files": ["تشغيل_النظام.bat", "تشغيل_خادم_التقارير.bat"],
            "documentation": ["دليل_الاستخدام_الفوري.md", "README_COMPLETE.md"]
        }
    }

    with open(package_dir / "PACKAGE_INFO_EXE.json", 'w', encoding='utf-8') as f:
        json.dump(package_info, f, ensure_ascii=False, indent=2)

    print("✅ تم إنشاء PACKAGE_INFO_EXE.json")
    return package_info

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء الإصدار التنفيذي (.exe) للاستخدام الفوري")
    print("=" * 80)
    print("⚡ لا يحتاج Python - يعمل على أي كمبيوتر Windows فوراً!")
    print("🎉 جميع الميزات الجديدة مدمجة:")
    print("   💳 طريقة الدفع في مقبوضات العملاء")
    print("   🏭 جدول الموردين منفصل")
    print("   📄 رقم المرجع للمعاملات البنكية")
    print("   👥 أسماء العملاء في التقارير")
    print("   ⚖️ حساب الفارق الدقيق")
    print("   📊 التقرير الشامل المحسن")
    print("   🌐 الوصول العالمي")
    print("=" * 80)

    try:
        # التحقق من PyInstaller وتثبيته
        if not check_pyinstaller():
            print("❌ فشل في تثبيت PyInstaller")
            return False

        # إنشاء ملف .spec
        if not create_spec_file():
            print("❌ فشل في إنشاء ملف التكوين")
            return False

        # بناء الملف التنفيذي الرئيسي
        if not build_executable():
            print("❌ فشل في بناء الملف التنفيذي")
            return False

        # بناء خادم التقارير (اختياري)
        create_web_server_exe()

        # إنشاء حزمة الملفات التنفيذية
        package_dir = create_exe_package()
        if not package_dir:
            print("❌ فشل في إنشاء حزمة الملفات التنفيذية")
            return False

        # إنشاء ملفات التشغيل
        create_batch_files_for_exe(package_dir)

        # إنشاء دليل الاستخدام
        create_installation_guide_exe(package_dir)

        # إنشاء ملف ZIP نهائي
        zip_filename, zip_size = create_final_zip(package_dir)

        # إنشاء ملف معلومات الحزمة
        package_info = create_package_info(package_dir, zip_filename, zip_size)

        print(f"\n🎉 تم إنشاء الإصدار التنفيذي بنجاح!")
        print("=" * 80)
        print(f"📦 اسم الحزمة: {zip_filename}")
        print(f"📏 حجم الحزمة: {zip_size:.1f} MB")
        print(f"💾 حجم الملف التنفيذي: {package_info['exe_size_mb']:.1f} MB")
        print(f"🗓️ تاريخ البناء: {package_info['build_date']}")

        print(f"\n✅ الحزمة جاهزة للتوزيع والاستخدام الفوري!")
        print("⚡ لا يحتاج Python - يعمل على أي كمبيوتر Windows!")
        print("📖 راجع دليل_الاستخدام_الفوري.md للتعليمات")

        print(f"\n🎯 للاستخدام الفوري:")
        print("1. فك الضغط عن الملف")
        print("2. انقر مزدوجاً على: تشغيل_النظام.bat")
        print("3. سجل الدخول: admin / 123456")
        print("4. استمتع بجميع الميزات الجديدة!")

        print(f"\n🌟 مزايا الإصدار التنفيذي:")
        print("✅ لا يحتاج تثبيت Python")
        print("✅ لا يحتاج تثبيت مكتبات")
        print("✅ يعمل فوراً بنقرة واحدة")
        print("✅ حجم صغير ومحمول")
        print("✅ آمن ومستقل تماماً")

        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء الإصدار التنفيذي: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()