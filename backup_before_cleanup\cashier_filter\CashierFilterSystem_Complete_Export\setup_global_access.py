#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد الوصول العالمي لتقارير نظام تصفية الكاشير
Global Access Setup for Cashier Filter Reports

يوفر عدة طرق للوصول للتقارير من أي مكان في العالم بطرق آمنة ومجانية
"""

import os
import sys
import json
import subprocess
import threading
import time
import requests
from pathlib import Path

class GlobalAccessManager:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_file = self.project_root / "global_access_config.json"
        self.ngrok_process = None
        self.current_tunnel_url = None
        
    def check_ngrok_installed(self):
        """التحقق من تثبيت ngrok"""
        try:
            result = subprocess.run(['ngrok', 'version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False
    
    def install_ngrok_instructions(self):
        """تعليمات تثبيت ngrok"""
        print("📦 تعليمات تثبيت ngrok:")
        print("=" * 50)
        print("1. اذهب إلى: https://ngrok.com/download")
        print("2. قم بالتسجيل مجاناً")
        print("3. حمل ngrok لنظام Windows")
        print("4. فك الضغط في مجلد المشروع")
        print("5. شغل: ngrok config add-authtoken YOUR_TOKEN")
        print("=" * 50)
        
    def setup_ngrok_tunnel(self, port=5000):
        """إعداد نفق ngrok"""
        if not self.check_ngrok_installed():
            print("❌ ngrok غير مثبت")
            self.install_ngrok_instructions()
            return None
        
        try:
            print("🚀 بدء تشغيل نفق ngrok...")
            
            # تشغيل ngrok في الخلفية
            self.ngrok_process = subprocess.Popen([
                'ngrok', 'http', str(port), '--log=stdout'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # انتظار حتى يبدأ النفق
            time.sleep(3)
            
            # الحصول على رابط النفق
            tunnel_url = self.get_ngrok_tunnel_url()
            if tunnel_url:
                self.current_tunnel_url = tunnel_url
                self.save_tunnel_config(tunnel_url)
                return tunnel_url
            else:
                print("❌ فشل في الحصول على رابط النفق")
                return None
                
        except Exception as e:
            print(f"❌ خطأ في إعداد ngrok: {e}")
            return None
    
    def get_ngrok_tunnel_url(self):
        """الحصول على رابط نفق ngrok"""
        try:
            # الاتصال بـ API المحلي لـ ngrok
            response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
            if response.status_code == 200:
                data = response.json()
                tunnels = data.get('tunnels', [])
                for tunnel in tunnels:
                    if tunnel.get('proto') == 'https':
                        return tunnel.get('public_url')
            return None
        except:
            return None
    
    def save_tunnel_config(self, tunnel_url):
        """حفظ إعدادات النفق"""
        config = {
            'tunnel_url': tunnel_url,
            'created_at': time.time(),
            'type': 'ngrok'
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def load_tunnel_config(self):
        """تحميل إعدادات النفق"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return None
    
    def stop_ngrok_tunnel(self):
        """إيقاف نفق ngrok"""
        if self.ngrok_process:
            self.ngrok_process.terminate()
            self.ngrok_process = None
            print("⏹️ تم إيقاف نفق ngrok")
    
    def create_cloudflare_tunnel_script(self):
        """إنشاء سكريبت Cloudflare Tunnel"""
        script_content = '''@echo off
echo ========================================
echo 🌐 Cloudflare Tunnel للوصول العالمي
echo ========================================
echo.

echo 📦 تحميل cloudflared...
curl -L https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe -o cloudflared.exe

echo.
echo 🚀 بدء النفق...
echo الرابط العالمي سيظهر أدناه:
echo.

cloudflared.exe tunnel --url http://localhost:5000

pause
'''
        
        script_path = self.project_root / "cloudflare_tunnel.bat"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ تم إنشاء سكريبت Cloudflare: {script_path}")
        return script_path
    
    def create_localtunnel_script(self):
        """إنشاء سكريبت LocalTunnel"""
        script_content = '''@echo off
echo ========================================
echo 🌐 LocalTunnel للوصول العالمي
echo ========================================
echo.

echo 📦 تثبيت localtunnel...
npm install -g localtunnel

echo.
echo 🚀 بدء النفق...
echo الرابط العالمي سيظهر أدناه:
echo.

lt --port 5000 --subdomain cashier-reports

pause
'''
        
        script_path = self.project_root / "localtunnel.bat"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        print(f"✅ تم إنشاء سكريبت LocalTunnel: {script_path}")
        return script_path
    
    def print_global_access_info(self, tunnel_url):
        """طباعة معلومات الوصول العالمي"""
        print("=" * 70)
        print("🌍 الوصول العالمي لتقارير نظام تصفية الكاشير")
        print("=" * 70)
        print(f"🔗 الرابط العالمي: {tunnel_url}")
        print(f"📱 للهاتف: {tunnel_url}/mobile")
        print(f"📊 التقارير: {tunnel_url}/reports")
        print()
        print("🔒 الأمان:")
        print("   ✅ اتصال مشفر (HTTPS)")
        print("   ✅ رابط عشوائي آمن")
        print("   ⚠️ لا تشارك الرابط مع غير الموثوقين")
        print()
        print("📱 للاستخدام:")
        print("   1. انسخ الرابط أعلاه")
        print("   2. افتحه في أي متصفح في العالم")
        print("   3. أضفه للمفضلة أو الشاشة الرئيسية")
        print()
        print("⏹️ للإيقاف: اضغط Ctrl+C")
        print("=" * 70)

def main():
    """الدالة الرئيسية"""
    manager = GlobalAccessManager()
    
    print("🌍 إعداد الوصول العالمي لتقارير نظام تصفية الكاشير")
    print("=" * 60)
    
    # التحقق من تشغيل الخادم المحلي
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        if response.status_code != 200:
            print("❌ الخادم المحلي لا يعمل")
            print("   يرجى تشغيل: python web_server.py")
            return
    except:
        print("❌ الخادم المحلي لا يعمل")
        print("   يرجى تشغيل: python web_server.py")
        return
    
    print("✅ الخادم المحلي يعمل")
    
    # إعداد نفق ngrok
    tunnel_url = manager.setup_ngrok_tunnel()
    
    if tunnel_url:
        manager.print_global_access_info(tunnel_url)
        
        try:
            # إبقاء النفق مفتوحاً
            while True:
                time.sleep(10)
                # التحقق من حالة النفق
                current_url = manager.get_ngrok_tunnel_url()
                if not current_url:
                    print("⚠️ انقطع الاتصال، محاولة إعادة الاتصال...")
                    tunnel_url = manager.setup_ngrok_tunnel()
                    if tunnel_url:
                        manager.print_global_access_info(tunnel_url)
                
        except KeyboardInterrupt:
            print("\n👋 إيقاف النفق...")
            manager.stop_ngrok_tunnel()
    
    else:
        print("\n🔧 حلول بديلة:")
        print("=" * 30)
        
        # إنشاء سكريبتات بديلة
        cloudflare_script = manager.create_cloudflare_tunnel_script()
        localtunnel_script = manager.create_localtunnel_script()
        
        print(f"1. Cloudflare Tunnel: {cloudflare_script}")
        print(f"2. LocalTunnel: {localtunnel_script}")
        print("\nشغل أي من السكريبتات أعلاه للوصول العالمي")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم الإنهاء")
    except Exception as e:
        print(f"❌ خطأ: {e}")
