#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة البحث عن الملفات المكررة وإزالتها
Duplicate Files Finder and Remover
"""

import os
import hashlib
import shutil
from pathlib import Path
from collections import defaultdict

def calculate_file_hash(file_path):
    """حساب hash للملف"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"خطأ في قراءة الملف {file_path}: {e}")
        return None

def find_duplicate_files(root_dir):
    """البحث عن الملفات المكررة"""
    print(f"🔍 البحث عن الملفات المكررة في: {root_dir}")
    
    file_hashes = defaultdict(list)
    total_files = 0
    
    # البحث في جميع الملفات
    for root, dirs, files in os.walk(root_dir):
        # تجاهل مجلدات معينة
        dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules', '.vscode']]
        
        for file in files:
            file_path = Path(root) / file
            total_files += 1
            
            # حساب hash للملف
            file_hash = calculate_file_hash(file_path)
            if file_hash:
                file_hashes[file_hash].append(file_path)
    
    print(f"📊 تم فحص {total_files} ملف")
    
    # العثور على المكررات
    duplicates = {hash_val: paths for hash_val, paths in file_hashes.items() if len(paths) > 1}
    
    print(f"🔄 تم العثور على {len(duplicates)} مجموعة من الملفات المكررة")
    
    return duplicates

def analyze_duplicates(duplicates):
    """تحليل الملفات المكررة"""
    print("\n📋 تحليل الملفات المكررة:")
    
    total_duplicate_files = 0
    total_wasted_space = 0
    
    for i, (file_hash, file_paths) in enumerate(duplicates.items(), 1):
        file_size = file_paths[0].stat().st_size
        duplicate_count = len(file_paths) - 1
        wasted_space = file_size * duplicate_count
        
        total_duplicate_files += duplicate_count
        total_wasted_space += wasted_space
        
        print(f"\n{i}. مجموعة مكررة:")
        print(f"   📏 حجم الملف: {file_size:,} بايت")
        print(f"   🔢 عدد النسخ: {len(file_paths)}")
        print(f"   💾 مساحة مهدرة: {wasted_space:,} بايت")
        
        for j, path in enumerate(file_paths):
            status = "🟢 الأصل" if j == 0 else "🔴 مكرر"
            print(f"     {status} {path}")
    
    print(f"\n📊 الملخص:")
    print(f"   🔄 إجمالي الملفات المكررة: {total_duplicate_files}")
    print(f"   💾 إجمالي المساحة المهدرة: {total_wasted_space:,} بايت ({total_wasted_space/1024/1024:.2f} MB)")
    
    return total_duplicate_files, total_wasted_space

def get_preferred_file(file_paths):
    """تحديد الملف المفضل للاحتفاظ به"""
    # ترتيب الأولوية:
    # 1. الملفات في المجلد الرئيسي cashier_filter
    # 2. الملفات الأحدث
    # 3. الملفات في مسارات أقصر
    
    def priority_score(path):
        score = 0
        path_str = str(path)
        
        # أولوية للمجلد الرئيسي
        if 'cashier_filter' in path_str and 'CashierFilterSystem_Complete_Export' not in path_str:
            score += 1000
        
        # أولوية للملفات الأحدث
        try:
            score += path.stat().st_mtime
        except:
            pass
        
        # أولوية للمسارات الأقصر
        score -= len(path.parts) * 10
        
        return score
    
    return max(file_paths, key=priority_score)

def remove_duplicates(duplicates, dry_run=True):
    """إزالة الملفات المكررة"""
    print(f"\n{'🧪 محاكاة' if dry_run else '🗑️ إزالة'} الملفات المكررة:")
    
    removed_files = 0
    saved_space = 0
    
    for file_hash, file_paths in duplicates.items():
        if len(file_paths) <= 1:
            continue
        
        # تحديد الملف المفضل
        preferred_file = get_preferred_file(file_paths)
        files_to_remove = [f for f in file_paths if f != preferred_file]
        
        print(f"\n📁 مجموعة: {preferred_file.name}")
        print(f"   ✅ الاحتفاظ بـ: {preferred_file}")
        
        for file_to_remove in files_to_remove:
            file_size = file_to_remove.stat().st_size
            
            if dry_run:
                print(f"   🧪 سيتم حذف: {file_to_remove}")
            else:
                try:
                    file_to_remove.unlink()
                    print(f"   ✅ تم حذف: {file_to_remove}")
                except Exception as e:
                    print(f"   ❌ فشل حذف {file_to_remove}: {e}")
                    continue
            
            removed_files += 1
            saved_space += file_size
    
    print(f"\n📊 النتائج:")
    print(f"   🗑️ ملفات {'ستحذف' if dry_run else 'محذوفة'}: {removed_files}")
    print(f"   💾 مساحة {'ستوفر' if dry_run else 'موفرة'}: {saved_space:,} بايت ({saved_space/1024/1024:.2f} MB)")
    
    return removed_files, saved_space

def create_backup_before_cleanup():
    """إنشاء نسخة احتياطية قبل التنظيف"""
    print("💾 إنشاء نسخة احتياطية...")
    
    backup_dir = Path("backup_before_cleanup")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    # نسخ المجلد الرئيسي فقط
    try:
        shutil.copytree("cashier_filter", backup_dir / "cashier_filter")
        print(f"✅ تم إنشاء نسخة احتياطية في: {backup_dir}")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧹 أداة تنظيف الملفات المكررة")
    print("=" * 40)
    
    root_directory = "cashier_filter"
    
    if not Path(root_directory).exists():
        print(f"❌ المجلد {root_directory} غير موجود")
        return
    
    # البحث عن المكررات
    duplicates = find_duplicate_files(root_directory)
    
    if not duplicates:
        print("🎉 لا توجد ملفات مكررة!")
        return
    
    # تحليل المكررات
    total_duplicates, wasted_space = analyze_duplicates(duplicates)
    
    # محاكاة الحذف أولاً
    print("\n" + "="*50)
    removed_files, saved_space = remove_duplicates(duplicates, dry_run=True)
    
    # السؤال عن التأكيد
    print("\n" + "="*50)
    response = input("هل تريد المتابعة مع الحذف الفعلي؟ (y/N): ").strip().lower()
    
    if response in ['y', 'yes', 'نعم']:
        # إنشاء نسخة احتياطية
        if create_backup_before_cleanup():
            # الحذف الفعلي
            print("\n🗑️ بدء الحذف الفعلي...")
            removed_files, saved_space = remove_duplicates(duplicates, dry_run=False)
            print("\n🎉 تم تنظيف الملفات المكررة بنجاح!")
        else:
            print("❌ تم إلغاء العملية بسبب فشل النسخة الاحتياطية")
    else:
        print("❌ تم إلغاء العملية")

if __name__ == "__main__":
    main()
