{% extends "base.html" %}

{% block title %}التقارير - نظام تصفية الكاشير{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-file-alt text-primary"></i>
                    تقارير التصفية
                </h2>
                <p class="card-text">جميع تقارير التصفية مرتبة حسب التاريخ</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> البحث والتصفية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">البحث بالكاشير:</label>
                        <input type="text" class="form-control" id="cashier-search" placeholder="اسم الكاشير...">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">من تاريخ:</label>
                        <input type="date" class="form-control" id="date-from">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">إلى تاريخ:</label>
                        <input type="date" class="form-control" id="date-to">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-success" onclick="exportFiltered()">
                            <i class="fas fa-download"></i> تصدير النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reports Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> قائمة التقارير</h5>
                <span class="badge bg-primary" id="results-count">
                    {{ filters|length }} تقرير
                </span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="reports-table">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ</th>
                                <th>الكاشير</th>
                                <th>المسؤول</th>
                                <th>المقبوضات البنكية</th>
                                <th>المقبوضات النقدية</th>
                                <th>المجموع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="reports-tbody">
                            {% for filter in filters %}
                            <tr data-filter-id="{{ filter.id }}" 
                                data-cashier="{{ filter.cashier_name or '' }}" 
                                data-date="{{ filter.date }}">
                                <td>
                                    <i class="fas fa-calendar text-primary"></i>
                                    {{ filter.date }}
                                    {% if filter.sequence_number %}
                                    <br><small class="text-muted">#{{ filter.sequence_number }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <i class="fas fa-user text-success"></i>
                                    {{ filter.cashier_name or 'غير محدد' }}
                                </td>
                                <td>
                                    <i class="fas fa-user-tie text-info"></i>
                                    {{ filter.admin_name or 'غير محدد' }}
                                </td>
                                <td class="amount">
                                    <i class="fas fa-credit-card"></i>
                                    {{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال
                                </td>
                                <td class="amount">
                                    <i class="fas fa-money-bill"></i>
                                    {{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال
                                </td>
                                <td class="amount">
                                    <strong>
                                        {{ "{:,.2f}".format((filter.details.bank_total or 0) + (filter.details.cash_total or 0)) }} ريال
                                    </strong>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/filter/{{ filter.id }}" class="btn btn-sm btn-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="/filter/{{ filter.id }}/comprehensive" class="btn btn-sm btn-success" title="التقرير الشامل">
                                            <i class="fas fa-file-invoice-dollar"></i> شامل
                                        </a>
                                        <button class="btn btn-sm btn-info" onclick="printFilter({{ filter.id }})" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if not filters %}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تقارير متاحة</h5>
                    <p class="text-muted">ابدأ بإنشاء تصفية جديدة من التطبيق الرئيسي</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if filters|length >= 20 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                <li class="page-item {{ 'disabled' if page <= 1 else '' }}">
                    <a class="page-link" href="?page={{ page - 1 }}">السابق</a>
                </li>
                <li class="page-item active">
                    <span class="page-link">{{ page }}</span>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page + 1 }}">التالي</a>
                </li>
            </ul>
        </nav>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تطبيق الفلاتر
    function applyFilters() {
        const cashierSearch = document.getElementById('cashier-search').value.toLowerCase();
        const dateFrom = document.getElementById('date-from').value;
        const dateTo = document.getElementById('date-to').value;
        
        const rows = document.querySelectorAll('#reports-tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const cashierName = row.dataset.cashier.toLowerCase();
            const filterDate = row.dataset.date;
            
            let show = true;
            
            // فلتر الكاشير
            if (cashierSearch && !cashierName.includes(cashierSearch)) {
                show = false;
            }
            
            // فلتر التاريخ من
            if (dateFrom && filterDate < dateFrom) {
                show = false;
            }
            
            // فلتر التاريخ إلى
            if (dateTo && filterDate > dateTo) {
                show = false;
            }
            
            row.style.display = show ? '' : 'none';
            if (show) visibleCount++;
        });
        
        // تحديث عداد النتائج
        document.getElementById('results-count').textContent = `${visibleCount} تقرير`;
    }
    
    // مسح الفلاتر
    function clearFilters() {
        document.getElementById('cashier-search').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';
        
        const rows = document.querySelectorAll('#reports-tbody tr');
        rows.forEach(row => {
            row.style.display = '';
        });
        
        document.getElementById('results-count').textContent = `${rows.length} تقرير`;
    }
    
    // طباعة تصفية محددة
    function printFilter(filterId) {
        window.open(`/filter/${filterId}?print=1`, '_blank');
    }
    
    // تصدير النتائج المفلترة
    function exportFiltered() {
        const visibleRows = Array.from(document.querySelectorAll('#reports-tbody tr'))
            .filter(row => row.style.display !== 'none');
        
        if (visibleRows.length === 0) {
            alert('لا توجد نتائج للتصدير');
            return;
        }
        
        // إنشاء CSV
        let csv = 'التاريخ,الكاشير,المسؤول,المقبوضات البنكية,المقبوضات النقدية,المجموع\n';
        
        visibleRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const date = row.dataset.date;
            const cashier = row.dataset.cashier;
            const admin = cells[2].textContent.trim().replace(/\s+/g, ' ');
            const bank = cells[3].textContent.trim().replace(/[^\d.,]/g, '');
            const cash = cells[4].textContent.trim().replace(/[^\d.,]/g, '');
            const total = cells[5].textContent.trim().replace(/[^\d.,]/g, '');
            
            csv += `"${date}","${cashier}","${admin}","${bank}","${cash}","${total}"\n`;
        });
        
        // تحميل الملف
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `تقارير_التصفية_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
    }
    
    // البحث المباشر
    document.getElementById('cashier-search').addEventListener('input', function() {
        if (this.value.length >= 2 || this.value.length === 0) {
            applyFilters();
        }
    });
    
    // تطبيق الفلاتر عند تغيير التاريخ
    document.getElementById('date-from').addEventListener('change', applyFilters);
    document.getElementById('date-to').addEventListener('change', applyFilters);
</script>
{% endblock %}
