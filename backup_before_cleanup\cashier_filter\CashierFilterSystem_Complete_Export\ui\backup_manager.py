# نافذة إدارة النسخ الاحتياطي
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime
import threading

class BackupManagerWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("إدارة النسخ الاحتياطي")
        self.geometry("900x700")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()
        
        # استيراد مدير النسخ الاحتياطي
        try:
            from utils.backup import get_backup_manager, initialize_backup_manager
            self.backup_manager = get_backup_manager()
            if not self.backup_manager:
                db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
                self.backup_manager = initialize_backup_manager(db_path)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة مدير النسخ الاحتياطي: {e}")
            self.destroy()
            return
        
        self.create_widgets()
        self.load_backup_list()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="💾 إدارة النسخ الاحتياطي",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # إطار العمليات السريعة
        quick_actions_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        quick_actions_frame.pack(pady=10, padx=20, fill="x")
        
        actions_title = ctk.CTkLabel(
            quick_actions_frame,
            text="العمليات السريعة",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        actions_title.pack(pady=10)
        
        # أزرار العمليات
        buttons_frame = ctk.CTkFrame(quick_actions_frame, fg_color="#e0e5ec", corner_radius=0)
        buttons_frame.pack(pady=10, padx=20, fill="x")
        
        # الصف الأول من الأزرار
        row1_frame = ctk.CTkFrame(buttons_frame, fg_color="#e0e5ec", corner_radius=0)
        row1_frame.pack(fill="x", pady=5)
        
        create_backup_btn = ctk.CTkButton(
            row1_frame,
            text="📦 إنشاء نسخة احتياطية",
            command=self.create_backup,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=200,
            height=40,
            font=("Arial", 14, "bold")
        )
        create_backup_btn.pack(side="left", padx=5)
        
        refresh_btn = ctk.CTkButton(
            row1_frame,
            text="🔄 تحديث القائمة",
            command=self.load_backup_list,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        refresh_btn.pack(side="left", padx=5)
        
        export_btn = ctk.CTkButton(
            row1_frame,
            text="📤 تصدير البيانات",
            command=self.export_data,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        export_btn.pack(side="left", padx=5)
        
        # الصف الثاني من الأزرار
        row2_frame = ctk.CTkFrame(buttons_frame, fg_color="#e0e5ec", corner_radius=0)
        row2_frame.pack(fill="x", pady=5)
        
        import_btn = ctk.CTkButton(
            row2_frame,
            text="📥 استيراد البيانات",
            command=self.import_data,
            fg_color="#9C27B0",
            hover_color="#7B1FA2",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        import_btn.pack(side="left", padx=5)
        
        cleanup_btn = ctk.CTkButton(
            row2_frame,
            text="🧹 تنظيف النسخ القديمة",
            command=self.cleanup_old_backups,
            fg_color="#607D8B",
            hover_color="#455A64",
            width=180,
            height=40,
            font=("Arial", 14, "bold")
        )
        cleanup_btn.pack(side="left", padx=5)
        
        # قائمة النسخ الاحتياطية
        list_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        list_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        list_title = ctk.CTkLabel(
            list_frame,
            text="قائمة النسخ الاحتياطية",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        list_title.pack(pady=10)
        
        # إطار الجدول
        table_frame = ctk.CTkFrame(list_frame, fg_color="#f2f3f7", corner_radius=10)
        table_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إنشاء الجدول
        columns = ("التاريخ", "النوع", "الحجم", "الإصدار", "المسار")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        column_widths = [150, 100, 100, 100, 300]
        for i, col in enumerate(columns):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths[i], anchor="center")
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # تخطيط الجدول وأشرطة التمرير
        self.tree.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        scrollbar_v.grid(row=0, column=1, sticky="ns", pady=5)
        scrollbar_h.grid(row=1, column=0, sticky="ew", padx=5)
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط النقر المزدوج
        self.tree.bind("<Double-1>", self.on_backup_double_click)
        self.tree.bind("<Button-3>", self.show_context_menu)  # النقر بالزر الأيمن
        
        # إطار معلومات إضافية
        info_frame = ctk.CTkFrame(self, fg_color="#ecf0f1", corner_radius=15)
        info_frame.pack(pady=10, padx=20, fill="x")
        
        self.info_label = ctk.CTkLabel(
            info_frame,
            text="💡 انقر نقراً مزدوجاً على نسخة احتياطية لاستعادتها | انقر بالزر الأيمن للمزيد من الخيارات",
            font=("Arial", 12),
            text_color="#7f8c8d"
        )
        self.info_label.pack(pady=15)

    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # الحصول على قائمة النسخ الاحتياطية
            backup_list = self.backup_manager.get_backup_list()
            
            for backup in backup_list:
                # تنسيق التاريخ
                try:
                    timestamp = datetime.fromisoformat(backup["timestamp"])
                    formatted_date = timestamp.strftime("%Y-%m-%d %H:%M")
                except:
                    formatted_date = backup["timestamp"]
                
                # تنسيق الحجم
                file_size = backup.get("file_size", 0)
                if file_size > 1024 * 1024:
                    size_str = f"{file_size / (1024 * 1024):.1f} MB"
                elif file_size > 1024:
                    size_str = f"{file_size / 1024:.1f} KB"
                else:
                    size_str = f"{file_size} B"
                
                # تنسيق النوع
                backup_type = backup.get("type", "unknown")
                type_map = {
                    "manual": "يدوي",
                    "auto": "تلقائي",
                    "pre_restore": "قبل الاستعادة",
                    "unknown": "غير معروف"
                }
                formatted_type = type_map.get(backup_type, backup_type)
                
                # إدراج البيانات في الجدول
                self.tree.insert("", "end", values=(
                    formatted_date,
                    formatted_type,
                    size_str,
                    backup.get("version", "غير معروف"),
                    os.path.basename(backup["file_path"])
                ), tags=(backup["file_path"],))
            
            # تحديث معلومات الحالة
            count = len(backup_list)
            self.info_label.configure(
                text=f"📊 إجمالي النسخ الاحتياطية: {count} | آخر تحديث: {datetime.now().strftime('%H:%M:%S')}"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل قائمة النسخ الاحتياطية: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        try:
            # إظهار رسالة التقدم
            progress_window = self.show_progress_window("إنشاء نسخة احتياطية...")
            
            def backup_thread():
                try:
                    backup_path = self.backup_manager.create_backup("manual")
                    
                    # إغلاق نافذة التقدم
                    progress_window.destroy()
                    
                    if backup_path:
                        messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح!\nالمسار: {backup_path}")
                        self.load_backup_list()
                    else:
                        messagebox.showerror("خطأ", "فشل في إنشاء النسخة الاحتياطية")
                        
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("خطأ", f"حدث خطأ: {e}")
            
            # تشغيل النسخ الاحتياطي في خيط منفصل
            threading.Thread(target=backup_thread, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء النسخ الاحتياطي: {e}")

    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        if not messagebox.askyesno(
            "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
            "سيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة."
        ):
            return
        
        try:
            # إظهار رسالة التقدم
            progress_window = self.show_progress_window("استعادة النسخة الاحتياطية...")
            
            def restore_thread():
                try:
                    result = self.backup_manager.restore_backup(backup_path)
                    
                    # إغلاق نافذة التقدم
                    progress_window.destroy()
                    
                    if result:
                        messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!")
                        self.load_backup_list()
                    else:
                        messagebox.showerror("خطأ", "فشل في استعادة النسخة الاحتياطية")
                        
                except Exception as e:
                    progress_window.destroy()
                    messagebox.showerror("خطأ", f"حدث خطأ: {e}")
            
            # تشغيل الاستعادة في خيط منفصل
            threading.Thread(target=restore_thread, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في بدء الاستعادة: {e}")

    def export_data(self):
        """تصدير البيانات إلى ملف JSON"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="حفظ البيانات المصدرة"
            )
            
            if filename:
                progress_window = self.show_progress_window("تصدير البيانات...")
                
                def export_thread():
                    try:
                        result = self.backup_manager.export_data_to_json(filename)
                        progress_window.destroy()
                        
                        if result:
                            messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{filename}")
                        else:
                            messagebox.showerror("خطأ", "فشل في تصدير البيانات")
                            
                    except Exception as e:
                        progress_window.destroy()
                        messagebox.showerror("خطأ", f"حدث خطأ: {e}")
                
                threading.Thread(target=export_thread, daemon=True).start()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")

    def import_data(self):
        """استيراد البيانات من ملف JSON"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="اختيار ملف البيانات للاستيراد"
            )
            
            if filename:
                if not messagebox.askyesno(
                    "تأكيد الاستيراد",
                    "هل أنت متأكد من استيراد البيانات؟\n"
                    "سيتم استبدال جميع البيانات الحالية!"
                ):
                    return
                
                progress_window = self.show_progress_window("استيراد البيانات...")
                
                def import_thread():
                    try:
                        result = self.backup_manager.import_data_from_json(filename)
                        progress_window.destroy()
                        
                        if result:
                            messagebox.showinfo("نجح", "تم استيراد البيانات بنجاح!")
                        else:
                            messagebox.showerror("خطأ", "فشل في استيراد البيانات")
                            
                    except Exception as e:
                        progress_window.destroy()
                        messagebox.showerror("خطأ", f"حدث خطأ: {e}")
                
                threading.Thread(target=import_thread, daemon=True).start()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استيراد البيانات: {e}")

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        if messagebox.askyesno("تأكيد التنظيف", "هل تريد حذف النسخ الاحتياطية القديمة؟"):
            try:
                self.backup_manager.cleanup_old_backups()
                messagebox.showinfo("نجح", "تم تنظيف النسخ الاحتياطية القديمة")
                self.load_backup_list()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في التنظيف: {e}")

    def show_progress_window(self, message):
        """إظهار نافذة التقدم"""
        progress_window = ctk.CTkToplevel(self)
        progress_window.title("جاري العمل...")
        progress_window.geometry("300x150")
        progress_window.configure(bg="#f2f3f7")
        progress_window.resizable(False, False)
        
        # توسيط النافذة
        progress_window.transient(self)
        progress_window.grab_set()
        
        # رسالة التقدم
        label = ctk.CTkLabel(
            progress_window,
            text=message,
            font=("Arial", 14),
            text_color="#2c3e50"
        )
        label.pack(expand=True)
        
        # شريط التقدم
        progress_bar = ctk.CTkProgressBar(progress_window, width=250)
        progress_bar.pack(pady=20)
        progress_bar.set(0.5)  # قيمة غير محددة
        
        return progress_window

    def on_backup_double_click(self, event):
        """عند النقر المزدوج على نسخة احتياطية"""
        selected = self.tree.selection()
        if selected:
            backup_path = self.tree.item(selected[0])["tags"][0]
            self.restore_backup(backup_path)

    def show_context_menu(self, event):
        """إظهار قائمة السياق"""
        selected = self.tree.selection()
        if selected:
            backup_path = self.tree.item(selected[0])["tags"][0]
            
            # إنشاء قائمة السياق
            context_menu = ctk.CTkToplevel(self)
            context_menu.title("")
            context_menu.geometry("200x120")
            context_menu.configure(bg="#f2f3f7")
            context_menu.resizable(False, False)
            
            # وضع القائمة في موقع النقر
            x, y = event.x_root, event.y_root
            context_menu.geometry(f"+{x}+{y}")
            
            # أزرار القائمة
            restore_btn = ctk.CTkButton(
                context_menu,
                text="استعادة",
                command=lambda: [self.restore_backup(backup_path), context_menu.destroy()],
                width=180,
                height=30
            )
            restore_btn.pack(pady=5)
            
            delete_btn = ctk.CTkButton(
                context_menu,
                text="حذف",
                command=lambda: [self.delete_backup(backup_path), context_menu.destroy()],
                fg_color="#e74c3c",
                hover_color="#c0392b",
                width=180,
                height=30
            )
            delete_btn.pack(pady=5)
            
            # إغلاق القائمة عند النقر خارجها
            context_menu.bind("<FocusOut>", lambda e: context_menu.destroy())
            context_menu.focus_set()

    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه النسخة الاحتياطية؟"):
            try:
                os.remove(backup_path)
                messagebox.showinfo("نجح", "تم حذف النسخة الاحتياطية")
                self.load_backup_list()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف النسخة الاحتياطية: {e}")
