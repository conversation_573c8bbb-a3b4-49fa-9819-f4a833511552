# واجهة التقارير المجدولة والتلقائية
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
from datetime import datetime, timedelta
import calendar
from collections import defaultdict
import webbrowser
import os
from pathlib import Path
import threading
import time
import schedule

# استخدام مسار نسبي
BASE_DIR = Path(__file__).parent.parent
DB_PATH = BASE_DIR / "db" / "cashier_filter.db"

class ScheduledReportsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("⏰ التقارير المجدولة والتلقائية")
        self.geometry("1400x900")
        self.configure(bg="#f8fafc")
        self.resizable(True, True)
        
        # متغيرات الجدولة
        self.scheduled_reports = []
        self.scheduler_running = False
        
        self.create_widgets()
        self.load_scheduled_reports()
        self.start_scheduler()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الشريط العلوي
        self.create_header()
        
        # شريط الأدوات
        self.create_toolbar()
        
        # المحتوى الرئيسي
        self.create_main_content()
        
        # شريط الحالة
        self.create_status_bar()

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self, fg_color="#1e40af", corner_radius=0, height=80)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="⏰ التقارير المجدولة والتلقائية",
            font=("Arial", 28, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(side="left", padx=30, pady=20)
        
        # معلومات الجدولة
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="right", padx=30, pady=20)
        
        self.scheduler_status = ctk.CTkLabel(
            info_frame,
            text="🟢 الجدولة نشطة",
            font=("Arial", 12, "bold"),
            text_color="#10b981"
        )
        self.scheduler_status.pack(anchor="e")
        
        self.next_report = ctk.CTkLabel(
            info_frame,
            text="📅 التقرير التالي: --",
            font=("Arial", 10),
            text_color="#bfdbfe"
        )
        self.next_report.pack(anchor="e")

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ctk.CTkFrame(self, fg_color="#3b82f6", corner_radius=0, height=60)
        toolbar_frame.pack(fill="x", padx=0, pady=0)
        toolbar_frame.pack_propagate(False)
        
        # أزرار الإجراءات
        add_btn = ctk.CTkButton(
            toolbar_frame,
            text="➕ إضافة تقرير مجدول",
            command=self.add_scheduled_report,
            fg_color="#059669",
            hover_color="#047857",
            width=180,
            height=35
        )
        add_btn.pack(side="left", padx=20, pady=12)
        
        edit_btn = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_scheduled_report,
            fg_color="#d97706",
            hover_color="#b45309",
            width=100,
            height=35
        )
        edit_btn.pack(side="left", padx=5, pady=12)
        
        delete_btn = ctk.CTkButton(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_scheduled_report,
            fg_color="#dc2626",
            hover_color="#b91c1c",
            width=100,
            height=35
        )
        delete_btn.pack(side="left", padx=5, pady=12)
        
        run_now_btn = ctk.CTkButton(
            toolbar_frame,
            text="▶️ تشغيل الآن",
            command=self.run_report_now,
            fg_color="#7c3aed",
            hover_color="#6d28d9",
            width=120,
            height=35
        )
        run_now_btn.pack(side="left", padx=5, pady=12)
        
        # أدوات التحكم في الجدولة
        scheduler_controls = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        scheduler_controls.pack(side="right", padx=20, pady=12)
        
        self.pause_btn = ctk.CTkButton(
            scheduler_controls,
            text="⏸️ إيقاف مؤقت",
            command=self.toggle_scheduler,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        self.pause_btn.pack(side="right", padx=5)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        
        # دفتر التبويبات
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=10)
        
        # تبويب التقارير المجدولة
        self.scheduled_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.scheduled_frame, text="📋 التقارير المجدولة")
        
        # تبويب سجل التنفيذ
        self.execution_log_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.execution_log_frame, text="📜 سجل التنفيذ")
        
        # تبويب الإعدادات
        self.settings_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.settings_frame, text="⚙️ الإعدادات")
        
        # إنشاء محتوى التبويبات
        self.create_scheduled_reports_tab()
        self.create_execution_log_tab()
        self.create_settings_tab()

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = ctk.CTkFrame(self, fg_color="#1e40af", corner_radius=0, height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="🟢 جاهز - الجدولة نشطة",
            font=("Arial", 10),
            text_color="#ffffff"
        )
        self.status_label.pack(side="left", padx=20, pady=5)
        
        self.reports_count = ctk.CTkLabel(
            status_frame,
            text="📊 التقارير المجدولة: 0",
            font=("Arial", 10),
            text_color="#bfdbfe"
        )
        self.reports_count.pack(side="right", padx=20, pady=5)

    def create_scheduled_reports_tab(self):
        """إنشاء تبويب التقارير المجدولة"""
        
        # جدول التقارير المجدولة
        columns = ("الاسم", "النوع", "التكرار", "الوقت", "آخر تنفيذ", "التنفيذ التالي", "الحالة")
        
        self.scheduled_tree = ttk.Treeview(self.scheduled_frame, columns=columns, show="headings", height=20)
        
        for col in columns:
            self.scheduled_tree.heading(col, text=col)
            self.scheduled_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar_scheduled = ttk.Scrollbar(self.scheduled_frame, orient="vertical", command=self.scheduled_tree.yview)
        self.scheduled_tree.configure(yscrollcommand=scrollbar_scheduled.set)
        
        self.scheduled_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_scheduled.pack(side="right", fill="y", pady=10)

    def create_execution_log_tab(self):
        """إنشاء تبويب سجل التنفيذ"""
        
        # جدول سجل التنفيذ
        log_columns = ("التاريخ والوقت", "اسم التقرير", "النوع", "الحالة", "الرسالة", "المدة")
        
        self.log_tree = ttk.Treeview(self.execution_log_frame, columns=log_columns, show="headings", height=20)
        
        for col in log_columns:
            self.log_tree.heading(col, text=col)
            self.log_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        scrollbar_log = ttk.Scrollbar(self.execution_log_frame, orient="vertical", command=self.log_tree.yview)
        self.log_tree.configure(yscrollcommand=scrollbar_log.set)
        
        self.log_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_log.pack(side="right", fill="y", pady=10)

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        
        settings_container = ctk.CTkScrollableFrame(self.settings_frame, fg_color="#f8fafc")
        settings_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إعدادات عامة
        general_frame = ctk.CTkFrame(settings_container, fg_color="#ffffff", corner_radius=10)
        general_frame.pack(fill="x", padx=10, pady=10)
        
        general_title = ctk.CTkLabel(
            general_frame,
            text="⚙️ الإعدادات العامة",
            font=("Arial", 18, "bold"),
            text_color="#1f2937"
        )
        general_title.pack(pady=15)
        
        # إعدادات البريد الإلكتروني
        email_frame = ctk.CTkFrame(settings_container, fg_color="#ffffff", corner_radius=10)
        email_frame.pack(fill="x", padx=10, pady=10)
        
        email_title = ctk.CTkLabel(
            email_frame,
            text="📧 إعدادات البريد الإلكتروني",
            font=("Arial", 18, "bold"),
            text_color="#1f2937"
        )
        email_title.pack(pady=15)
        
        # إعدادات التخزين
        storage_frame = ctk.CTkFrame(settings_container, fg_color="#ffffff", corner_radius=10)
        storage_frame.pack(fill="x", padx=10, pady=10)
        
        storage_title = ctk.CTkLabel(
            storage_frame,
            text="💾 إعدادات التخزين",
            font=("Arial", 18, "bold"),
            text_color="#1f2937"
        )
        storage_title.pack(pady=15)

    def add_scheduled_report(self):
        """إضافة تقرير مجدول جديد"""
        dialog = ScheduledReportDialog(self)
        self.wait_window(dialog)
        
        if hasattr(dialog, 'result') and dialog.result:
            self.scheduled_reports.append(dialog.result)
            self.refresh_scheduled_reports()
            self.save_scheduled_reports()

    def edit_scheduled_report(self):
        """تعديل تقرير مجدول"""
        selected = self.scheduled_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تقرير للتعديل")
            return
        
        # تنفيذ التعديل
        messagebox.showinfo("تعديل", "سيتم فتح نافذة التعديل")

    def delete_scheduled_report(self):
        """حذف تقرير مجدول"""
        selected = self.scheduled_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تقرير للحذف")
            return
        
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا التقرير المجدول؟"):
            # تنفيذ الحذف
            messagebox.showinfo("تم", "تم حذف التقرير المجدول")

    def run_report_now(self):
        """تشغيل تقرير مجدول الآن"""
        selected = self.scheduled_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تقرير للتشغيل")
            return
        
        # تنفيذ التقرير
        messagebox.showinfo("تشغيل", "جاري تشغيل التقرير...")

    def toggle_scheduler(self):
        """تبديل حالة الجدولة"""
        self.scheduler_running = not self.scheduler_running
        
        if self.scheduler_running:
            self.pause_btn.configure(text="⏸️ إيقاف مؤقت")
            self.scheduler_status.configure(text="🟢 الجدولة نشطة", text_color="#10b981")
            self.status_label.configure(text="🟢 جاهز - الجدولة نشطة")
        else:
            self.pause_btn.configure(text="▶️ تشغيل")
            self.scheduler_status.configure(text="⏸️ الجدولة متوقفة", text_color="#ef4444")
            self.status_label.configure(text="⏸️ الجدولة متوقفة")

    def load_scheduled_reports(self):
        """تحميل التقارير المجدولة"""
        try:
            # تحميل من ملف JSON أو قاعدة البيانات
            self.refresh_scheduled_reports()
        except Exception as e:
            print(f"خطأ في تحميل التقارير المجدولة: {e}")

    def save_scheduled_reports(self):
        """حفظ التقارير المجدولة"""
        try:
            # حفظ في ملف JSON أو قاعدة البيانات
            pass
        except Exception as e:
            print(f"خطأ في حفظ التقارير المجدولة: {e}")

    def refresh_scheduled_reports(self):
        """تحديث جدول التقارير المجدولة"""
        # مسح الجدول الحالي
        for item in self.scheduled_tree.get_children():
            self.scheduled_tree.delete(item)
        
        # إضافة التقارير المجدولة
        for report in self.scheduled_reports:
            self.scheduled_tree.insert("", "end", values=(
                report.get('name', ''),
                report.get('type', ''),
                report.get('frequency', ''),
                report.get('time', ''),
                report.get('last_run', '--'),
                report.get('next_run', '--'),
                report.get('status', 'نشط')
            ))
        
        # تحديث العداد
        self.reports_count.configure(text=f"📊 التقارير المجدولة: {len(self.scheduled_reports)}")

    def start_scheduler(self):
        """بدء الجدولة"""
        def scheduler_loop():
            while True:
                if self.scheduler_running:
                    schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
        
        scheduler_thread = threading.Thread(target=scheduler_loop, daemon=True)
        scheduler_thread.start()
        self.scheduler_running = True

class ScheduledReportDialog(ctk.CTkToplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("إضافة تقرير مجدول")
        self.geometry("600x700")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        
        self.result = None
        
        self.create_dialog_widgets()

    def create_dialog_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text="➕ إضافة تقرير مجدول جديد",
            font=("Arial", 20, "bold"),
            text_color="#1f2937"
        )
        title_label.pack(pady=20)
        
        # نموذج الإدخال
        form_frame = ctk.CTkFrame(self, fg_color="#f8fafc", corner_radius=10)
        form_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # اسم التقرير
        ctk.CTkLabel(form_frame, text="اسم التقرير:", font=("Arial", 12, "bold")).pack(anchor="w", padx=20, pady=(20, 5))
        self.name_entry = ctk.CTkEntry(form_frame, width=400, placeholder_text="أدخل اسم التقرير")
        self.name_entry.pack(padx=20, pady=(0, 10))
        
        # نوع التقرير
        ctk.CTkLabel(form_frame, text="نوع التقرير:", font=("Arial", 12, "bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.type_combo = ctk.CTkComboBox(
            form_frame,
            values=["تقرير شامل", "تقرير الكاشيرين", "تقرير المقبوضات", "تقرير الاتجاهات"],
            width=400
        )
        self.type_combo.pack(padx=20, pady=(0, 10))
        
        # التكرار
        ctk.CTkLabel(form_frame, text="التكرار:", font=("Arial", 12, "bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.frequency_combo = ctk.CTkComboBox(
            form_frame,
            values=["يومي", "أسبوعي", "شهري", "ربع سنوي", "سنوي"],
            width=400
        )
        self.frequency_combo.pack(padx=20, pady=(0, 10))
        
        # الوقت
        ctk.CTkLabel(form_frame, text="الوقت:", font=("Arial", 12, "bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.time_entry = ctk.CTkEntry(form_frame, width=400, placeholder_text="مثال: 09:00")
        self.time_entry.pack(padx=20, pady=(0, 10))
        
        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=20)
        
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_report,
            fg_color="#059669",
            hover_color="#047857",
            width=120,
            height=35
        )
        save_btn.pack(side="right", padx=5)
        
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        cancel_btn.pack(side="right", padx=5)

    def save_report(self):
        """حفظ التقرير المجدول"""
        name = self.name_entry.get().strip()
        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التقرير")
            return
        
        self.result = {
            'name': name,
            'type': self.type_combo.get(),
            'frequency': self.frequency_combo.get(),
            'time': self.time_entry.get(),
            'status': 'نشط',
            'created_at': datetime.now().isoformat()
        }
        
        self.destroy()
