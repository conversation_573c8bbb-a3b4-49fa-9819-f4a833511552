#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نوافذ إعدادات موفري الخدمات السحابية
"""

import sys
import os
import customtkinter as ctk

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_provider_settings():
    """اختبار نوافذ إعدادات موفري الخدمات"""
    print("🧪 اختبار نوافذ إعدادات موفري الخدمات السحابية...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد النافذة الرئيسية
        from ui.cloud_integration_simple import CloudIntegrationSimpleWindow
        print("✅ تم استيراد النافذة الرئيسية بنجاح")
        
        # إنشاء النافذة الرئيسية
        main_window = CloudIntegrationSimpleWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار فتح إعدادات Google Drive
        try:
            main_window.show_provider_settings("google_drive")
            print("✅ تم فتح إعدادات Google Drive بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فتح إعدادات Google Drive: {e}")
        
        # إغلاق تلقائي بعد 5 ثوان
        main_window.after(5000, main_window.destroy)
        
        print("🚀 تشغيل النافذة...")
        main_window.mainloop()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_providers():
    """اختبار جميع موفري الخدمات"""
    print("\n🧪 اختبار جميع موفري الخدمات...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد الكلاسات
        from ui.cloud_integration_simple import CloudIntegrationSimpleWindow, CloudProviderSettingsWindow
        print("✅ تم استيراد الكلاسات بنجاح")
        
        # إنشاء النافذة الرئيسية
        main_window = CloudIntegrationSimpleWindow()
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار كل موفر
        providers = ["google_drive", "dropbox", "onedrive", "aws_s3", "azure"]
        
        for provider_id in providers:
            try:
                if provider_id in main_window.cloud_providers:
                    provider_info = main_window.cloud_providers[provider_id]
                    print(f"✅ موفر {provider_info['name']} متاح")
                else:
                    print(f"⚠️ موفر {provider_id} غير متاح")
            except Exception as e:
                print(f"❌ خطأ في موفر {provider_id}: {e}")
        
        # إغلاق النافذة
        main_window.destroy()
        print("✅ تم إغلاق النافذة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الموفرين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_window_directly():
    """اختبار نافذة الإعدادات مباشرة"""
    print("\n🧪 اختبار نافذة الإعدادات مباشرة...")
    
    try:
        # إعداد CustomTkinter
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # استيراد الكلاس
        from ui.cloud_integration_simple import CloudProviderSettingsWindow
        print("✅ تم استيراد كلاس نافذة الإعدادات بنجاح")
        
        # إنشاء نافذة وهمية كوالد
        root = ctk.CTk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # بيانات موفر وهمي
        provider_info = {
            "name": "Google Drive",
            "enabled": True
        }
        
        # إنشاء نافذة الإعدادات
        settings_window = CloudProviderSettingsWindow(root, "google_drive", provider_info)
        print("✅ تم إنشاء نافذة الإعدادات بنجاح")
        
        # إغلاق تلقائي بعد 3 ثوان
        settings_window.after(3000, settings_window.destroy)
        
        print("🚀 تشغيل نافذة الإعدادات...")
        settings_window.mainloop()
        
        print("✅ تم إغلاق نافذة الإعدادات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الإعدادات: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار نوافذ إعدادات موفري الخدمات السحابية")
    print("=" * 70)
    
    # اختبار نافذة الإعدادات مباشرة
    direct_test = test_settings_window_directly()
    
    if direct_test:
        print("\n" + "=" * 70)
        
        # اختبار جميع الموفرين
        providers_test = test_all_providers()
        
        if providers_test:
            print("\n" + "=" * 70)
            
            # اختبار التكامل الكامل
            integration_test = test_provider_settings()
            
            if integration_test:
                print("\n🎉 جميع الاختبارات نجحت!")
                print("✅ نوافذ إعدادات موفري الخدمات تعمل بشكل صحيح")
            else:
                print("\n💥 فشل اختبار التكامل الكامل")
        else:
            print("\n💥 فشل اختبار الموفرين")
    else:
        print("\n💥 فشل اختبار نافذة الإعدادات المباشر")
    
    print("=" * 70)
