# نظام التنبيهات والإشعارات
import customtkinter as ctk
from tkinter import messagebox
import threading
import time
from datetime import datetime, timedelta
import sqlite3
import json
import os

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
NOTIFICATIONS_FILE = "notifications.json"

class NotificationSystem:
    def __init__(self):
        self.notifications = []
        self.notification_window = None
        self.load_notifications()
        
    def load_notifications(self):
        """تحميل الإشعارات المحفوظة"""
        try:
            if os.path.exists(NOTIFICATIONS_FILE):
                with open(NOTIFICATIONS_FILE, 'r', encoding='utf-8') as f:
                    self.notifications = json.load(f)
        except Exception as e:
            print(f"خطأ في تحميل الإشعارات: {e}")
            self.notifications = []
    
    def save_notifications(self):
        """حفظ الإشعارات"""
        try:
            with open(NOTIFICATIONS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.notifications, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإشعارات: {e}")
    
    def add_notification(self, title, message, type="info", priority="normal"):
        """إضافة إشعار جديد"""
        notification = {
            "id": len(self.notifications) + 1,
            "title": title,
            "message": message,
            "type": type,  # info, warning, error, success
            "priority": priority,  # low, normal, high, urgent
            "timestamp": datetime.now().isoformat(),
            "read": False
        }
        
        self.notifications.append(notification)
        self.save_notifications()
        
        # عرض الإشعار فوراً إذا كان عالي الأولوية
        if priority in ["high", "urgent"]:
            self.show_popup_notification(notification)
    
    def show_popup_notification(self, notification):
        """عرض إشعار منبثق"""
        if notification["type"] == "error":
            messagebox.showerror(notification["title"], notification["message"])
        elif notification["type"] == "warning":
            messagebox.showwarning(notification["title"], notification["message"])
        elif notification["type"] == "success":
            messagebox.showinfo(notification["title"], notification["message"])
        else:
            messagebox.showinfo(notification["title"], notification["message"])
    
    def mark_as_read(self, notification_id):
        """تمييز الإشعار كمقروء"""
        for notification in self.notifications:
            if notification["id"] == notification_id:
                notification["read"] = True
                break
        self.save_notifications()
    
    def get_unread_count(self):
        """الحصول على عدد الإشعارات غير المقروءة"""
        return len([n for n in self.notifications if not n["read"]])
    
    def get_recent_notifications(self, limit=10):
        """الحصول على الإشعارات الحديثة"""
        sorted_notifications = sorted(
            self.notifications, 
            key=lambda x: x["timestamp"], 
            reverse=True
        )
        return sorted_notifications[:limit]
    
    def clear_old_notifications(self, days=30):
        """مسح الإشعارات القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)
        self.notifications = [
            n for n in self.notifications 
            if datetime.fromisoformat(n["timestamp"]) > cutoff_date
        ]
        self.save_notifications()
    
    def show_notifications_window(self, parent=None):
        """عرض نافذة الإشعارات"""
        if self.notification_window and self.notification_window.winfo_exists():
            self.notification_window.focus()
            return
        
        self.notification_window = NotificationWindow(parent, self)

class NotificationWindow(ctk.CTkToplevel):
    def __init__(self, parent, notification_system):
        super().__init__(parent)
        self.notification_system = notification_system
        
        self.title("الإشعارات والتنبيهات")
        self.geometry("800x600")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)
        
        self.create_widgets()
        self.load_notifications()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="🔔 الإشعارات والتنبيهات",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # أزرار التحكم
        controls_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        controls_frame.pack(pady=10, padx=20, fill="x")
        
        btn_container = ctk.CTkFrame(controls_frame, fg_color="#e0e5ec", corner_radius=0)
        btn_container.pack(pady=15)
        
        # زر تمييز الكل كمقروء
        mark_all_btn = ctk.CTkButton(
            btn_container,
            text="✅ تمييز الكل كمقروء",
            command=self.mark_all_as_read,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=150,
            height=35
        )
        mark_all_btn.pack(side="left", padx=10)
        
        # زر مسح القديمة
        clear_old_btn = ctk.CTkButton(
            btn_container,
            text="🗑️ مسح القديمة",
            command=self.clear_old_notifications,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=120,
            height=35
        )
        clear_old_btn.pack(side="left", padx=10)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            btn_container,
            text="🔄 تحديث",
            command=self.load_notifications,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=100,
            height=35
        )
        refresh_btn.pack(side="left", padx=10)
        
        # قائمة الإشعارات
        self.notifications_frame = ctk.CTkScrollableFrame(
            self,
            fg_color="#f2f3f7",
            corner_radius=15
        )
        self.notifications_frame.pack(pady=10, padx=20, fill="both", expand=True)
    
    def load_notifications(self):
        """تحميل وعرض الإشعارات"""
        # مسح الإشعارات الحالية
        for widget in self.notifications_frame.winfo_children():
            widget.destroy()
        
        notifications = self.notification_system.get_recent_notifications(50)
        
        if not notifications:
            no_notifications_label = ctk.CTkLabel(
                self.notifications_frame,
                text="📭 لا توجد إشعارات",
                font=("Arial", 16),
                text_color="#7f8c8d"
            )
            no_notifications_label.pack(pady=50)
            return
        
        for notification in notifications:
            self.create_notification_card(notification)
    
    def create_notification_card(self, notification):
        """إنشاء بطاقة إشعار"""
        # تحديد اللون حسب النوع
        if notification["type"] == "error":
            color = "#e74c3c"
        elif notification["type"] == "warning":
            color = "#f39c12"
        elif notification["type"] == "success":
            color = "#27ae60"
        else:
            color = "#3498db"
        
        # تحديد الشفافية حسب حالة القراءة
        bg_color = "#ffffff" if not notification["read"] else "#f8f9fa"
        
        card_frame = ctk.CTkFrame(
            self.notifications_frame,
            fg_color=bg_color,
            corner_radius=10,
            border_width=2,
            border_color=color
        )
        card_frame.pack(pady=5, padx=10, fill="x")
        
        # رأس البطاقة
        header_frame = ctk.CTkFrame(card_frame, fg_color=color, corner_radius=8)
        header_frame.pack(pady=5, padx=5, fill="x")
        
        # العنوان والوقت
        title_frame = ctk.CTkFrame(header_frame, fg_color=color, corner_radius=0)
        title_frame.pack(fill="x", padx=10, pady=5)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text=notification["title"],
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        title_label.pack(side="left")
        
        # تنسيق الوقت
        timestamp = datetime.fromisoformat(notification["timestamp"])
        time_str = timestamp.strftime("%Y-%m-%d %H:%M")
        
        time_label = ctk.CTkLabel(
            title_frame,
            text=time_str,
            font=("Arial", 10),
            text_color="white"
        )
        time_label.pack(side="right")
        
        # محتوى الإشعار
        content_frame = ctk.CTkFrame(card_frame, fg_color=bg_color, corner_radius=0)
        content_frame.pack(fill="x", padx=10, pady=5)
        
        message_label = ctk.CTkLabel(
            content_frame,
            text=notification["message"],
            font=("Arial", 12),
            text_color="#2c3e50",
            wraplength=700,
            justify="right"
        )
        message_label.pack(anchor="e", pady=5)
        
        # أزرار الإجراءات
        if not notification["read"]:
            actions_frame = ctk.CTkFrame(card_frame, fg_color=bg_color, corner_radius=0)
            actions_frame.pack(fill="x", padx=10, pady=5)
            
            mark_read_btn = ctk.CTkButton(
                actions_frame,
                text="تمييز كمقروء",
                command=lambda: self.mark_notification_as_read(notification["id"]),
                fg_color="#95a5a6",
                hover_color="#7f8c8d",
                width=100,
                height=25,
                font=("Arial", 10)
            )
            mark_read_btn.pack(side="right", padx=5)
    
    def mark_notification_as_read(self, notification_id):
        """تمييز إشعار محدد كمقروء"""
        self.notification_system.mark_as_read(notification_id)
        self.load_notifications()
    
    def mark_all_as_read(self):
        """تمييز جميع الإشعارات كمقروءة"""
        for notification in self.notification_system.notifications:
            notification["read"] = True
        self.notification_system.save_notifications()
        self.load_notifications()
        messagebox.showinfo("تم", "تم تمييز جميع الإشعارات كمقروءة")
    
    def clear_old_notifications(self):
        """مسح الإشعارات القديمة"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح الإشعارات الأقدم من 30 يوم؟"):
            self.notification_system.clear_old_notifications()
            self.load_notifications()
            messagebox.showinfo("تم", "تم مسح الإشعارات القديمة")

# مثيل عام لنظام الإشعارات
notification_system = NotificationSystem()

# دوال مساعدة للاستخدام السريع
def notify_info(title, message):
    """إشعار معلوماتي"""
    notification_system.add_notification(title, message, "info", "normal")

def notify_warning(title, message):
    """إشعار تحذيري"""
    notification_system.add_notification(title, message, "warning", "high")

def notify_error(title, message):
    """إشعار خطأ"""
    notification_system.add_notification(title, message, "error", "urgent")

def notify_success(title, message):
    """إشعار نجاح"""
    notification_system.add_notification(title, message, "success", "normal")

def get_unread_notifications_count():
    """الحصول على عدد الإشعارات غير المقروءة"""
    return notification_system.get_unread_count()

def show_notifications_window(parent=None):
    """عرض نافذة الإشعارات"""
    notification_system.show_notifications_window(parent)
