#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة سجل العمليات
Operations Log Window
"""

import customtkinter as ctk
from tkinter import messagebox, ttk
import tkinter as tk
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.operations_logger import operations_logger
    from utils.font_manager import get_stable_font
except ImportError:
    def get_stable_font(size_type="normal", weight="normal"):
        sizes = {"small": 12, "normal": 14, "medium": 16, "large": 18}
        size = sizes.get(size_type, 14)
        return ("Arial", size, weight)

class OperationsLogWindow(ctk.CTkToplevel):
    """نافذة سجل العمليات"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title("📝 سجل العمليات")
        self.geometry("1200x700")
        self.transient(parent)
        self.grab_set()
        
        # متغيرات الفلترة
        self.current_page = 0
        self.page_size = 50
        self.total_records = 0
        
        # تعيين أيقونة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
        
        self.create_widgets()
        self.load_operations()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self, fg_color="#f8f9fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="📝 سجل العمليات والأنشطة",
            font=get_stable_font("large", "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)
        
        # إطار الفلاتر
        self.create_filters_frame(main_frame)
        
        # إطار الجدول
        self.create_table_frame(main_frame)
        
        # إطار التنقل
        self.create_navigation_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_filters_frame(self, parent):
        """إنشاء إطار الفلاتر"""
        filters_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        filters_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الفلاتر
        filters_title = ctk.CTkLabel(
            filters_frame,
            text="🔍 فلاتر البحث",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        filters_title.pack(pady=(10, 5))
        
        # إطار الفلاتر الأفقي
        filters_container = ctk.CTkFrame(filters_frame, fg_color="transparent")
        filters_container.pack(fill="x", padx=20, pady=(0, 15))
        
        # الصف الأول من الفلاتر
        row1 = ctk.CTkFrame(filters_container, fg_color="transparent")
        row1.pack(fill="x", pady=5)
        
        # فلتر المستخدم
        user_label = ctk.CTkLabel(row1, text="👤 المستخدم:", font=get_stable_font("small"))
        user_label.pack(side="left", padx=(0, 5))
        
        self.user_var = ctk.StringVar(value="الكل")
        self.user_combo = ctk.CTkComboBox(
            row1, variable=self.user_var, width=150,
            values=["الكل"] + self.get_users_list()
        )
        self.user_combo.pack(side="left", padx=(0, 20))
        
        # فلتر نوع العملية
        type_label = ctk.CTkLabel(row1, text="🔧 نوع العملية:", font=get_stable_font("small"))
        type_label.pack(side="left", padx=(0, 5))
        
        self.type_var = ctk.StringVar(value="الكل")
        self.type_combo = ctk.CTkComboBox(
            row1, variable=self.type_var, width=150,
            values=["الكل", "AUTH", "FILTER", "REPORT", "ADMIN", "SYSTEM"]
        )
        self.type_combo.pack(side="left", padx=(0, 20))
        
        # فلتر الحالة
        status_label = ctk.CTkLabel(row1, text="✅ الحالة:", font=get_stable_font("small"))
        status_label.pack(side="left", padx=(0, 5))
        
        self.status_var = ctk.StringVar(value="الكل")
        self.status_combo = ctk.CTkComboBox(
            row1, variable=self.status_var, width=120,
            values=["الكل", "ناجح", "فاشل"]
        )
        self.status_combo.pack(side="left")
        
        # الصف الثاني من الفلاتر
        row2 = ctk.CTkFrame(filters_container, fg_color="transparent")
        row2.pack(fill="x", pady=5)
        
        # فلتر التاريخ
        date_label = ctk.CTkLabel(row2, text="📅 الفترة:", font=get_stable_font("small"))
        date_label.pack(side="left", padx=(0, 5))
        
        self.period_var = ctk.StringVar(value="آخر 7 أيام")
        self.period_combo = ctk.CTkComboBox(
            row2, variable=self.period_var, width=150,
            values=["اليوم", "آخر 7 أيام", "آخر 30 يوم", "آخر 90 يوم", "الكل"]
        )
        self.period_combo.pack(side="left", padx=(0, 20))
        
        # زر البحث
        search_btn = ctk.CTkButton(
            row2, text="🔍 بحث", width=100,
            command=self.apply_filters,
            fg_color="#007bff", hover_color="#0056b3"
        )
        search_btn.pack(side="left", padx=(0, 10))
        
        # زر إعادة تعيين
        reset_btn = ctk.CTkButton(
            row2, text="🔄 إعادة تعيين", width=120,
            command=self.reset_filters,
            fg_color="#6c757d", hover_color="#545b62"
        )
        reset_btn.pack(side="left")
    
    def create_table_frame(self, parent):
        """إنشاء إطار الجدول"""
        table_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        table_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # عنوان الجدول
        table_title = ctk.CTkLabel(
            table_frame,
            text="📋 سجل العمليات",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        table_title.pack(pady=(10, 5))
        
        # إطار الجدول مع شريط التمرير
        tree_frame = tk.Frame(table_frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # إنشاء الجدول
        columns = ("الوقت", "المستخدم", "نوع العملية", "اسم العملية", "الوصف", "الحالة", "عدد السجلات")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        self.tree.heading("الوقت", text="🕐 الوقت")
        self.tree.heading("المستخدم", text="👤 المستخدم")
        self.tree.heading("نوع العملية", text="🔧 نوع العملية")
        self.tree.heading("اسم العملية", text="📝 اسم العملية")
        self.tree.heading("الوصف", text="📄 الوصف")
        self.tree.heading("الحالة", text="✅ الحالة")
        self.tree.heading("عدد السجلات", text="📊 عدد السجلات")
        
        # تعيين عرض الأعمدة
        self.tree.column("الوقت", width=150, anchor="center")
        self.tree.column("المستخدم", width=120, anchor="center")
        self.tree.column("نوع العملية", width=100, anchor="center")
        self.tree.column("اسم العملية", width=150, anchor="center")
        self.tree.column("الوصف", width=250, anchor="w")
        self.tree.column("الحالة", width=80, anchor="center")
        self.tree.column("عدد السجلات", width=100, anchor="center")
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)
        
        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # ربط النقر المزدوج
        self.tree.bind("<Double-1>", self.show_operation_details)
    
    def create_navigation_frame(self, parent):
        """إنشاء إطار التنقل"""
        nav_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15, height=60)
        nav_frame.pack(fill="x", padx=10, pady=(0, 10))
        nav_frame.pack_propagate(False)
        
        # معلومات الصفحة
        self.page_info_label = ctk.CTkLabel(
            nav_frame,
            text="الصفحة 1 من 1 (0 سجل)",
            font=get_stable_font("small"),
            text_color="#6c757d"
        )
        self.page_info_label.pack(side="left", padx=20, pady=20)
        
        # أزرار التنقل
        nav_buttons_frame = ctk.CTkFrame(nav_frame, fg_color="transparent")
        nav_buttons_frame.pack(side="right", padx=20, pady=15)
        
        self.first_btn = ctk.CTkButton(
            nav_buttons_frame, text="⏮️ الأولى", width=80,
            command=self.go_to_first_page,
            fg_color="#6c757d", hover_color="#545b62"
        )
        self.first_btn.pack(side="left", padx=2)
        
        self.prev_btn = ctk.CTkButton(
            nav_buttons_frame, text="⬅️ السابقة", width=80,
            command=self.go_to_previous_page,
            fg_color="#6c757d", hover_color="#545b62"
        )
        self.prev_btn.pack(side="left", padx=2)
        
        self.next_btn = ctk.CTkButton(
            nav_buttons_frame, text="التالية ➡️", width=80,
            command=self.go_to_next_page,
            fg_color="#6c757d", hover_color="#545b62"
        )
        self.next_btn.pack(side="left", padx=2)
        
        self.last_btn = ctk.CTkButton(
            nav_buttons_frame, text="الأخيرة ⏭️", width=80,
            command=self.go_to_last_page,
            fg_color="#6c757d", hover_color="#545b62"
        )
        self.last_btn.pack(side="left", padx=2)
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=5)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            buttons_frame, text="🔄 تحديث", width=120,
            command=self.refresh_data,
            fg_color="#28a745", hover_color="#218838"
        )
        refresh_btn.pack(side="left", padx=5)
        
        # زر تصدير
        export_btn = ctk.CTkButton(
            buttons_frame, text="📤 تصدير", width=120,
            command=self.export_data,
            fg_color="#17a2b8", hover_color="#138496"
        )
        export_btn.pack(side="left", padx=5)
        
        # زر الإحصائيات
        stats_btn = ctk.CTkButton(
            buttons_frame, text="📊 الإحصائيات", width=120,
            command=self.show_statistics,
            fg_color="#6f42c1", hover_color="#5a32a3"
        )
        stats_btn.pack(side="left", padx=5)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame, text="❌ إغلاق", width=120,
            command=self.destroy,
            fg_color="#dc3545", hover_color="#c82333"
        )
        close_btn.pack(side="right", padx=5)

    def get_users_list(self):
        """الحصول على قائمة المستخدمين"""
        try:
            import sqlite3
            if os.path.exists("db/cashier_filter.db"):
                db_path = "db/cashier_filter.db"
            else:
                db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

            conn = sqlite3.connect(db_path)
            c = conn.cursor()
            c.execute("SELECT DISTINCT username FROM operations_log ORDER BY username")
            users = [row[0] for row in c.fetchall()]
            conn.close()
            return users
        except:
            return []

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.current_page = 0
        self.load_operations()

    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.user_var.set("الكل")
        self.type_var.set("الكل")
        self.status_var.set("الكل")
        self.period_var.set("آخر 7 أيام")
        self.current_page = 0
        self.load_operations()

    def get_filter_params(self):
        """الحصول على معاملات الفلترة"""
        params = {}

        # فلتر المستخدم
        if self.user_var.get() != "الكل":
            params['username'] = self.user_var.get()

        # فلتر نوع العملية
        if self.type_var.get() != "الكل":
            params['operation_type'] = self.type_var.get()

        # فلتر الحالة
        if self.status_var.get() != "الكل":
            params['success_only'] = self.status_var.get() == "ناجح"

        # فلتر التاريخ
        period = self.period_var.get()
        if period != "الكل":
            if period == "اليوم":
                params['start_date'] = datetime.now().strftime('%Y-%m-%d')
            elif period == "آخر 7 أيام":
                params['start_date'] = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            elif period == "آخر 30 يوم":
                params['start_date'] = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            elif period == "آخر 90 يوم":
                params['start_date'] = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

        return params

    def load_operations(self):
        """تحميل العمليات"""
        try:
            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)

            # الحصول على معاملات الفلترة
            filter_params = self.get_filter_params()

            # حساب العدد الإجمالي
            self.total_records = operations_logger.get_operations_count(**filter_params)

            # تحميل البيانات
            operations = operations_logger.get_operations_log(
                limit=self.page_size,
                offset=self.current_page * self.page_size,
                **filter_params
            )

            # إضافة البيانات للجدول
            for op in operations:
                # تنسيق الوقت
                timestamp = datetime.fromisoformat(op['timestamp'].replace('Z', '+00:00'))
                formatted_time = timestamp.strftime('%Y-%m-%d %H:%M')

                # تنسيق الحالة
                status = "✅ ناجح" if op['success'] else "❌ فاشل"

                # إضافة الصف
                self.tree.insert("", "end", values=(
                    formatted_time,
                    op['username'],
                    op['operation_type'],
                    op['operation_name'],
                    op['description'][:50] + "..." if len(op['description']) > 50 else op['description'],
                    status,
                    op['affected_records']
                ))

            # تحديث معلومات الصفحة
            self.update_page_info()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العمليات: {e}")

    def update_page_info(self):
        """تحديث معلومات الصفحة"""
        total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        current_page_display = self.current_page + 1

        self.page_info_label.configure(
            text=f"الصفحة {current_page_display} من {total_pages} ({self.total_records} سجل)"
        )

        # تحديث حالة الأزرار
        self.first_btn.configure(state="disabled" if self.current_page == 0 else "normal")
        self.prev_btn.configure(state="disabled" if self.current_page == 0 else "normal")
        self.next_btn.configure(state="disabled" if self.current_page >= total_pages - 1 else "normal")
        self.last_btn.configure(state="disabled" if self.current_page >= total_pages - 1 else "normal")

    def go_to_first_page(self):
        """الذهاب للصفحة الأولى"""
        self.current_page = 0
        self.load_operations()

    def go_to_previous_page(self):
        """الذهاب للصفحة السابقة"""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_operations()

    def go_to_next_page(self):
        """الذهاب للصفحة التالية"""
        total_pages = (self.total_records + self.page_size - 1) // self.page_size
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self.load_operations()

    def go_to_last_page(self):
        """الذهاب للصفحة الأخيرة"""
        total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        self.current_page = total_pages - 1
        self.load_operations()

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_operations()

    def export_data(self):
        """تصدير البيانات"""
        try:
            from tkinter import filedialog
            import csv

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ سجل العمليات"
            )

            if filename:
                # الحصول على جميع البيانات
                filter_params = self.get_filter_params()
                all_operations = operations_logger.get_operations_log(
                    limit=10000,  # حد أقصى للتصدير
                    **filter_params
                )

                # كتابة البيانات
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    writer.writerow([
                        'الوقت', 'المستخدم', 'نوع العملية', 'اسم العملية',
                        'الوصف', 'الحالة', 'عدد السجلات', 'عنوان IP'
                    ])

                    # كتابة البيانات
                    for op in all_operations:
                        timestamp = datetime.fromisoformat(op['timestamp'].replace('Z', '+00:00'))
                        formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        status = "ناجح" if op['success'] else "فاشل"

                        writer.writerow([
                            formatted_time,
                            op['username'],
                            op['operation_type'],
                            op['operation_name'],
                            op['description'],
                            status,
                            op['affected_records'],
                            op['ip_address']
                        ])

                messagebox.showinfo("نجح", f"تم تصدير {len(all_operations)} سجل بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")

    def show_statistics(self):
        """عرض الإحصائيات"""
        try:
            from ui.operations_statistics import OperationsStatisticsWindow
            stats_window = OperationsStatisticsWindow(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الإحصائيات: {e}")

    def show_operation_details(self, event):
        """عرض تفاصيل العملية"""
        try:
            selection = self.tree.selection()
            if not selection:
                return

            item = self.tree.item(selection[0])
            values = item['values']

            # إنشاء نافذة التفاصيل
            details_window = ctk.CTkToplevel(self)
            details_window.title("تفاصيل العملية")
            details_window.geometry("600x400")
            details_window.transient(self)
            details_window.grab_set()

            # عرض التفاصيل
            details_text = ctk.CTkTextbox(details_window, font=get_stable_font("normal"))
            details_text.pack(fill="both", expand=True, padx=20, pady=20)

            details_content = f"""
تفاصيل العملية:

🕐 الوقت: {values[0]}
👤 المستخدم: {values[1]}
🔧 نوع العملية: {values[2]}
📝 اسم العملية: {values[3]}
📄 الوصف: {values[4]}
✅ الحالة: {values[5]}
📊 عدد السجلات المتأثرة: {values[6]}
            """

            details_text.insert("1.0", details_content)
            details_text.configure(state="disabled")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل: {e}")
