# نافذة التكامل السحابي المتقدمة
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
import os
import threading
import time
from datetime import datetime, timedelta
import hashlib
import base64
import requests
from pathlib import Path

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class CloudIntegrationWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("☁️ التكامل السحابي المتقدم")
        self.geometry("1200x800")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()
        
        # متغيرات التكامل السحابي
        self.cloud_providers = {
            "google_drive": {"name": "Google Drive", "icon": "📁", "status": "غير متصل"},
            "dropbox": {"name": "Dropbox", "icon": "📦", "status": "غير متصل"},
            "onedrive": {"name": "OneDrive", "icon": "☁️", "status": "غير متصل"},
            "aws_s3": {"name": "Amazon S3", "icon": "🗄️", "status": "غير متصل"},
            "azure": {"name": "Azure Storage", "icon": "🔷", "status": "غير متصل"}
        }
        
        self.sync_settings = {
            "auto_sync": True,
            "sync_interval": 30,  # دقائق
            "backup_retention": 30,  # أيام
            "compression": True,
            "encryption": True
        }
        
        self.sync_status = {
            "last_sync": None,
            "sync_in_progress": False,
            "total_files": 0,
            "synced_files": 0,
            "failed_files": 0
        }
        
        self.create_widgets()
        self.load_cloud_settings()
        self.start_auto_sync_monitor()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        self.create_header()
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f0f2f5", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إنشاء التبويبات
        self.create_tabs(main_container)

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        # العنوان والحالة
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="☁️ التكامل السحابي المتقدم",
            font=("Arial", 28, "bold"),
            text_color="white"
        )
        title_label.pack(side="left")
        
        # حالة الاتصال
        self.connection_status = ctk.CTkLabel(
            title_frame,
            text="🔴 غير متصل",
            font=("Arial", 14),
            text_color="#fbbf24"
        )
        self.connection_status.pack(side="right")

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب موفري الخدمات السحابية
        self.providers_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.providers_frame, text="🌐 موفري الخدمات")
        self.create_providers_tab()
        
        # تبويب المزامنة والنسخ الاحتياطي
        self.sync_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.sync_frame, text="🔄 المزامنة والنسخ الاحتياطي")
        self.create_sync_tab()
        
        # تبويب إدارة الملفات السحابية
        self.files_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.files_frame, text="📁 إدارة الملفات")
        self.create_files_tab()
        
        # تبويب الأمان والتشفير
        self.security_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.security_frame, text="🔒 الأمان والتشفير")
        self.create_security_tab()
        
        # تبويب التقارير والإحصائيات
        self.reports_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.reports_frame, text="📊 التقارير والإحصائيات")
        self.create_reports_tab()

    def create_providers_tab(self):
        """إنشاء تبويب موفري الخدمات السحابية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.providers_frame,
            text="🌐 موفري الخدمات السحابية",
            font=("Arial", 20, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار موفري الخدمات
        providers_container = ctk.CTkScrollableFrame(
            self.providers_frame,
            fg_color="#f8fafc",
            corner_radius=15,
            height=500
        )
        providers_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء بطاقات موفري الخدمات
        for provider_id, provider_info in self.cloud_providers.items():
            self.create_provider_card(providers_container, provider_id, provider_info)

    def create_provider_card(self, parent, provider_id, provider_info):
        """إنشاء بطاقة موفر خدمة سحابية"""
        # إطار البطاقة
        card_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=12)
        card_frame.pack(fill="x", pady=10, padx=10)
        
        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=15)
        
        # الجانب الأيسر - معلومات الموفر
        left_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        left_frame.pack(side="left", fill="x", expand=True)
        
        # اسم الموفر مع الأيقونة
        name_frame = ctk.CTkFrame(left_frame, fg_color="transparent")
        name_frame.pack(anchor="w")
        
        icon_label = ctk.CTkLabel(
            name_frame,
            text=provider_info["icon"],
            font=("Arial", 24)
        )
        icon_label.pack(side="left", padx=(0, 10))
        
        name_label = ctk.CTkLabel(
            name_frame,
            text=provider_info["name"],
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        name_label.pack(side="left")
        
        # حالة الاتصال
        status_color = "#10b981" if provider_info["status"] == "متصل" else "#ef4444"
        status_label = ctk.CTkLabel(
            left_frame,
            text=f"الحالة: {provider_info['status']}",
            font=("Arial", 12),
            text_color=status_color
        )
        status_label.pack(anchor="w", pady=(5, 0))
        
        # الجانب الأيمن - أزرار التحكم
        right_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        right_frame.pack(side="right")
        
        # زر الاتصال/قطع الاتصال
        connect_btn = ctk.CTkButton(
            right_frame,
            text="🔗 اتصال" if provider_info["status"] == "غير متصل" else "🔌 قطع الاتصال",
            command=lambda p=provider_id: self.toggle_provider_connection(p),
            fg_color="#3b82f6" if provider_info["status"] == "غير متصل" else "#ef4444",
            hover_color="#2563eb" if provider_info["status"] == "غير متصل" else "#dc2626",
            width=120,
            height=35
        )
        connect_btn.pack(side="top", pady=2)
        
        # زر الإعدادات
        settings_btn = ctk.CTkButton(
            right_frame,
            text="⚙️ إعدادات",
            command=lambda p=provider_id: self.show_provider_settings(p),
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        settings_btn.pack(side="top", pady=2)

    def create_sync_tab(self):
        """إنشاء تبويب المزامنة والنسخ الاحتياطي"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.sync_frame,
            text="🔄 المزامنة والنسخ الاحتياطي",
            font=("Arial", 20, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self.sync_frame, fg_color="#f8fafc", corner_radius=15)
        settings_frame.pack(fill="x", padx=20, pady=10)
        
        settings_title = ctk.CTkLabel(
            settings_frame,
            text="⚙️ إعدادات المزامنة",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        settings_title.pack(pady=15)
        
        # إعدادات المزامنة
        self.create_sync_settings(settings_frame)
        
        # إطار حالة المزامنة
        status_frame = ctk.CTkFrame(self.sync_frame, fg_color="#f8fafc", corner_radius=15)
        status_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="📊 حالة المزامنة",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        status_title.pack(pady=15)
        
        # معلومات حالة المزامنة
        self.create_sync_status(status_frame)

    def create_sync_settings(self, parent):
        """إنشاء إعدادات المزامنة"""
        settings_container = ctk.CTkFrame(parent, fg_color="transparent")
        settings_container.pack(fill="x", padx=20, pady=10)
        
        # المزامنة التلقائية
        auto_sync_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        auto_sync_frame.pack(fill="x", pady=5)
        
        self.auto_sync_var = ctk.BooleanVar(value=self.sync_settings["auto_sync"])
        auto_sync_checkbox = ctk.CTkCheckBox(
            auto_sync_frame,
            text="تفعيل المزامنة التلقائية",
            variable=self.auto_sync_var,
            command=self.update_sync_settings,
            font=("Arial", 12)
        )
        auto_sync_checkbox.pack(side="left")
        
        # فترة المزامنة
        interval_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        interval_frame.pack(fill="x", pady=5)
        
        interval_label = ctk.CTkLabel(
            interval_frame,
            text="فترة المزامنة (دقائق):",
            font=("Arial", 12)
        )
        interval_label.pack(side="left")
        
        self.interval_var = ctk.StringVar(value=str(self.sync_settings["sync_interval"]))
        interval_entry = ctk.CTkEntry(
            interval_frame,
            textvariable=self.interval_var,
            width=100
        )
        interval_entry.pack(side="left", padx=10)
        
        # مدة الاحتفاظ بالنسخ الاحتياطية
        retention_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        retention_frame.pack(fill="x", pady=5)
        
        retention_label = ctk.CTkLabel(
            retention_frame,
            text="مدة الاحتفاظ بالنسخ الاحتياطية (أيام):",
            font=("Arial", 12)
        )
        retention_label.pack(side="left")
        
        self.retention_var = ctk.StringVar(value=str(self.sync_settings["backup_retention"]))
        retention_entry = ctk.CTkEntry(
            retention_frame,
            textvariable=self.retention_var,
            width=100
        )
        retention_entry.pack(side="left", padx=10)
        
        # ضغط الملفات
        compression_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        compression_frame.pack(fill="x", pady=5)
        
        self.compression_var = ctk.BooleanVar(value=self.sync_settings["compression"])
        compression_checkbox = ctk.CTkCheckBox(
            compression_frame,
            text="ضغط الملفات قبل الرفع",
            variable=self.compression_var,
            command=self.update_sync_settings,
            font=("Arial", 12)
        )
        compression_checkbox.pack(side="left")
        
        # تشفير الملفات
        encryption_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        encryption_frame.pack(fill="x", pady=5)
        
        self.encryption_var = ctk.BooleanVar(value=self.sync_settings["encryption"])
        encryption_checkbox = ctk.CTkCheckBox(
            encryption_frame,
            text="تشفير الملفات قبل الرفع",
            variable=self.encryption_var,
            command=self.update_sync_settings,
            font=("Arial", 12)
        )
        encryption_checkbox.pack(side="left")
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(settings_container, fg_color="transparent")
        buttons_frame.pack(fill="x", pady=15)
        
        sync_now_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 مزامنة الآن",
            command=self.start_manual_sync,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        sync_now_btn.pack(side="left", padx=5)
        
        backup_now_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 نسخ احتياطي الآن",
            command=self.start_manual_backup,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=150,
            height=35
        )
        backup_now_btn.pack(side="left", padx=5)
        
        restore_btn = ctk.CTkButton(
            buttons_frame,
            text="📥 استعادة من النسخ الاحتياطية",
            command=self.show_restore_dialog,
            fg_color="#f59e0b",
            hover_color="#d97706",
            width=200,
            height=35
        )
        restore_btn.pack(side="left", padx=5)

    def create_sync_status(self, parent):
        """إنشاء معلومات حالة المزامنة"""
        status_container = ctk.CTkFrame(parent, fg_color="transparent")
        status_container.pack(fill="both", expand=True, padx=20, pady=10)

        # آخر مزامنة
        last_sync_frame = ctk.CTkFrame(status_container, fg_color="#ffffff", corner_radius=10)
        last_sync_frame.pack(fill="x", pady=5)

        last_sync_label = ctk.CTkLabel(
            last_sync_frame,
            text=f"📅 آخر مزامنة: {self.sync_status['last_sync'] or 'لم تتم بعد'}",
            font=("Arial", 12),
            text_color="#374151"
        )
        last_sync_label.pack(pady=10)

        # شريط التقدم
        progress_frame = ctk.CTkFrame(status_container, fg_color="#ffffff", corner_radius=10)
        progress_frame.pack(fill="x", pady=5)

        self.sync_progress = ctk.CTkProgressBar(
            progress_frame,
            width=400,
            height=20
        )
        self.sync_progress.pack(pady=15)
        self.sync_progress.set(0)

        self.progress_label = ctk.CTkLabel(
            progress_frame,
            text="جاهز للمزامنة",
            font=("Arial", 12),
            text_color="#6b7280"
        )
        self.progress_label.pack()

        # إحصائيات المزامنة
        stats_frame = ctk.CTkFrame(status_container, fg_color="#ffffff", corner_radius=10)
        stats_frame.pack(fill="x", pady=5)

        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.pack(pady=15)

        # إجمالي الملفات
        total_label = ctk.CTkLabel(
            stats_grid,
            text=f"📁 إجمالي الملفات: {self.sync_status['total_files']}",
            font=("Arial", 12),
            text_color="#374151"
        )
        total_label.grid(row=0, column=0, padx=20, pady=5, sticky="w")

        # الملفات المتزامنة
        synced_label = ctk.CTkLabel(
            stats_grid,
            text=f"✅ متزامنة: {self.sync_status['synced_files']}",
            font=("Arial", 12),
            text_color="#10b981"
        )
        synced_label.grid(row=0, column=1, padx=20, pady=5, sticky="w")

        # الملفات الفاشلة
        failed_label = ctk.CTkLabel(
            stats_grid,
            text=f"❌ فاشلة: {self.sync_status['failed_files']}",
            font=("Arial", 12),
            text_color="#ef4444"
        )
        failed_label.grid(row=0, column=2, padx=20, pady=5, sticky="w")

    def create_files_tab(self):
        """إنشاء تبويب إدارة الملفات السحابية"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.files_frame,
            text="📁 إدارة الملفات السحابية",
            font=("Arial", 20, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.files_frame, fg_color="#f8fafc", corner_radius=15)
        toolbar_frame.pack(fill="x", padx=20, pady=10)

        toolbar_content = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        toolbar_content.pack(fill="x", padx=20, pady=15)

        # أزرار الأدوات
        upload_btn = ctk.CTkButton(
            toolbar_content,
            text="📤 رفع ملف",
            command=self.upload_file,
            fg_color="#10b981",
            hover_color="#059669",
            width=100,
            height=35
        )
        upload_btn.pack(side="left", padx=5)

        download_btn = ctk.CTkButton(
            toolbar_content,
            text="📥 تحميل ملف",
            command=self.download_file,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=100,
            height=35
        )
        download_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            toolbar_content,
            text="🗑️ حذف ملف",
            command=self.delete_file,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=100,
            height=35
        )
        delete_btn.pack(side="left", padx=5)

        refresh_btn = ctk.CTkButton(
            toolbar_content,
            text="🔄 تحديث",
            command=self.refresh_files,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=100,
            height=35
        )
        refresh_btn.pack(side="left", padx=5)

        # قائمة الملفات
        files_frame = ctk.CTkFrame(self.files_frame, fg_color="#f8fafc", corner_radius=15)
        files_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # جدول الملفات
        columns = ("الاسم", "الحجم", "تاريخ التعديل", "الموفر", "الحالة")
        self.files_tree = ttk.Treeview(files_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.files_tree.heading(col, text=col)
            self.files_tree.column(col, width=150, anchor="center")

        # شريط التمرير للجدول
        files_scrollbar = ttk.Scrollbar(files_frame, orient="vertical", command=self.files_tree.yview)
        self.files_tree.configure(yscrollcommand=files_scrollbar.set)

        self.files_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        files_scrollbar.pack(side="right", fill="y", pady=10)

    def create_security_tab(self):
        """إنشاء تبويب الأمان والتشفير"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.security_frame,
            text="🔒 الأمان والتشفير",
            font=("Arial", 20, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # إعدادات التشفير
        encryption_frame = ctk.CTkFrame(self.security_frame, fg_color="#f8fafc", corner_radius=15)
        encryption_frame.pack(fill="x", padx=20, pady=10)

        encryption_title = ctk.CTkLabel(
            encryption_frame,
            text="🔐 إعدادات التشفير",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        encryption_title.pack(pady=15)

        # نوع التشفير
        encryption_type_frame = ctk.CTkFrame(encryption_frame, fg_color="transparent")
        encryption_type_frame.pack(fill="x", padx=20, pady=10)

        encryption_type_label = ctk.CTkLabel(
            encryption_type_frame,
            text="نوع التشفير:",
            font=("Arial", 12)
        )
        encryption_type_label.pack(side="left")

        self.encryption_type = ctk.CTkOptionMenu(
            encryption_type_frame,
            values=["AES-256", "AES-128", "ChaCha20"],
            width=150
        )
        self.encryption_type.pack(side="left", padx=10)
        self.encryption_type.set("AES-256")

        # كلمة مرور التشفير
        password_frame = ctk.CTkFrame(encryption_frame, fg_color="transparent")
        password_frame.pack(fill="x", padx=20, pady=10)

        password_label = ctk.CTkLabel(
            password_frame,
            text="كلمة مرور التشفير:",
            font=("Arial", 12)
        )
        password_label.pack(side="left")

        self.encryption_password = ctk.CTkEntry(
            password_frame,
            show="*",
            width=200,
            placeholder_text="أدخل كلمة مرور قوية"
        )
        self.encryption_password.pack(side="left", padx=10)

        # إعدادات الأمان
        security_settings_frame = ctk.CTkFrame(self.security_frame, fg_color="#f8fafc", corner_radius=15)
        security_settings_frame.pack(fill="x", padx=20, pady=10)

        security_title = ctk.CTkLabel(
            security_settings_frame,
            text="🛡️ إعدادات الأمان",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        security_title.pack(pady=15)

        # المصادقة الثنائية
        two_factor_frame = ctk.CTkFrame(security_settings_frame, fg_color="transparent")
        two_factor_frame.pack(fill="x", padx=20, pady=5)

        self.two_factor_var = ctk.BooleanVar()
        two_factor_checkbox = ctk.CTkCheckBox(
            two_factor_frame,
            text="تفعيل المصادقة الثنائية",
            variable=self.two_factor_var,
            font=("Arial", 12)
        )
        two_factor_checkbox.pack(side="left")

        # تسجيل الأنشطة
        activity_log_frame = ctk.CTkFrame(security_settings_frame, fg_color="transparent")
        activity_log_frame.pack(fill="x", padx=20, pady=5)

        self.activity_log_var = ctk.BooleanVar(value=True)
        activity_log_checkbox = ctk.CTkCheckBox(
            activity_log_frame,
            text="تسجيل جميع الأنشطة السحابية",
            variable=self.activity_log_var,
            font=("Arial", 12)
        )
        activity_log_checkbox.pack(side="left")

        # تنبيهات الأمان
        security_alerts_frame = ctk.CTkFrame(security_settings_frame, fg_color="transparent")
        security_alerts_frame.pack(fill="x", padx=20, pady=5)

        self.security_alerts_var = ctk.BooleanVar(value=True)
        security_alerts_checkbox = ctk.CTkCheckBox(
            security_alerts_frame,
            text="تنبيهات الأمان عند الوصول غير المعتاد",
            variable=self.security_alerts_var,
            font=("Arial", 12)
        )
        security_alerts_checkbox.pack(side="left")

    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.reports_frame,
            text="📊 التقارير والإحصائيات السحابية",
            font=("Arial", 20, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # إحصائيات سريعة
        stats_frame = ctk.CTkFrame(self.reports_frame, fg_color="#f8fafc", corner_radius=15)
        stats_frame.pack(fill="x", padx=20, pady=10)

        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 إحصائيات سريعة",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        stats_title.pack(pady=15)

        # شبكة الإحصائيات
        stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
        stats_grid.pack(pady=10)

        # بطاقات الإحصائيات
        self.create_stat_card(stats_grid, "💾 إجمالي البيانات المرفوعة", "0 GB", "#3b82f6", 0, 0)
        self.create_stat_card(stats_grid, "📁 عدد الملفات", "0", "#10b981", 0, 1)
        self.create_stat_card(stats_grid, "🔄 عمليات المزامنة", "0", "#f59e0b", 0, 2)
        self.create_stat_card(stats_grid, "⚡ متوسط سرعة الرفع", "0 MB/s", "#8b5cf6", 1, 0)
        self.create_stat_card(stats_grid, "📥 متوسط سرعة التحميل", "0 MB/s", "#06b6d4", 1, 1)
        self.create_stat_card(stats_grid, "🛡️ الملفات المشفرة", "0%", "#ef4444", 1, 2)

        # تقارير مفصلة
        reports_frame = ctk.CTkFrame(self.reports_frame, fg_color="#f8fafc", corner_radius=15)
        reports_frame.pack(fill="both", expand=True, padx=20, pady=10)

        reports_title = ctk.CTkLabel(
            reports_frame,
            text="📋 التقارير المفصلة",
            font=("Arial", 16, "bold"),
            text_color="#374151"
        )
        reports_title.pack(pady=15)

        # أزرار التقارير
        reports_buttons = ctk.CTkFrame(reports_frame, fg_color="transparent")
        reports_buttons.pack(pady=10)

        usage_report_btn = ctk.CTkButton(
            reports_buttons,
            text="📊 تقرير الاستخدام",
            command=self.generate_usage_report,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=150,
            height=35
        )
        usage_report_btn.pack(side="left", padx=5)

        sync_report_btn = ctk.CTkButton(
            reports_buttons,
            text="🔄 تقرير المزامنة",
            command=self.generate_sync_report,
            fg_color="#10b981",
            hover_color="#059669",
            width=150,
            height=35
        )
        sync_report_btn.pack(side="left", padx=5)

        security_report_btn = ctk.CTkButton(
            reports_buttons,
            text="🔒 تقرير الأمان",
            command=self.generate_security_report,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=150,
            height=35
        )
        security_report_btn.pack(side="left", padx=5)

    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10, width=180, height=80)
        card.grid(row=row, column=col, padx=10, pady=5, sticky="nsew")

        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 10, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(10, 5))

        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 16, "bold"),
            text_color="white"
        )
        value_label.pack(pady=(0, 10))

    # الدوال الوظيفية

    def load_cloud_settings(self):
        """تحميل إعدادات التكامل السحابي"""
        try:
            settings_file = Path("cloud_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.cloud_providers.update(settings.get('providers', {}))
                    self.sync_settings.update(settings.get('sync_settings', {}))
                    self.sync_status.update(settings.get('sync_status', {}))

            self.update_connection_status()

        except Exception as e:
            print(f"خطأ في تحميل إعدادات السحابة: {e}")

    def save_cloud_settings(self):
        """حفظ إعدادات التكامل السحابي"""
        try:
            settings = {
                'providers': self.cloud_providers,
                'sync_settings': self.sync_settings,
                'sync_status': self.sync_status
            }

            with open("cloud_settings.json", 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"خطأ في حفظ إعدادات السحابة: {e}")

    def update_connection_status(self):
        """تحديث حالة الاتصال"""
        connected_providers = sum(1 for p in self.cloud_providers.values() if p['status'] == 'متصل')
        total_providers = len(self.cloud_providers)

        if connected_providers == 0:
            status_text = "🔴 غير متصل"
            status_color = "#ef4444"
        elif connected_providers == total_providers:
            status_text = "🟢 متصل بالكامل"
            status_color = "#10b981"
        else:
            status_text = f"🟡 متصل جزئياً ({connected_providers}/{total_providers})"
            status_color = "#f59e0b"

        self.connection_status.configure(text=status_text, text_color=status_color)

    def toggle_provider_connection(self, provider_id):
        """تبديل حالة اتصال موفر الخدمة"""
        provider = self.cloud_providers[provider_id]

        if provider['status'] == 'غير متصل':
            # محاولة الاتصال
            if self.connect_to_provider(provider_id):
                provider['status'] = 'متصل'
                messagebox.showinfo("نجح", f"تم الاتصال بـ {provider['name']} بنجاح")
            else:
                messagebox.showerror("خطأ", f"فشل الاتصال بـ {provider['name']}")
        else:
            # قطع الاتصال
            if self.disconnect_from_provider(provider_id):
                provider['status'] = 'غير متصل'
                messagebox.showinfo("نجح", f"تم قطع الاتصال من {provider['name']}")

        self.update_connection_status()
        self.save_cloud_settings()
        # إعادة إنشاء التبويب لتحديث الواجهة
        self.refresh_providers_tab()

    def connect_to_provider(self, provider_id):
        """الاتصال بموفر الخدمة السحابية"""
        try:
            # هنا يتم تنفيذ منطق الاتصال الفعلي حسب كل موفر
            if provider_id == "google_drive":
                return self.connect_google_drive()
            elif provider_id == "dropbox":
                return self.connect_dropbox()
            elif provider_id == "onedrive":
                return self.connect_onedrive()
            elif provider_id == "aws_s3":
                return self.connect_aws_s3()
            elif provider_id == "azure":
                return self.connect_azure()

            return False

        except Exception as e:
            print(f"خطأ في الاتصال بـ {provider_id}: {e}")
            return False

    def disconnect_from_provider(self, provider_id):
        """قطع الاتصال من موفر الخدمة"""
        try:
            # تنظيف بيانات الاتصال
            return True
        except Exception as e:
            print(f"خطأ في قطع الاتصال من {provider_id}: {e}")
            return False

    def connect_google_drive(self):
        """الاتصال بـ Google Drive"""
        # محاكاة الاتصال
        return True

    def connect_dropbox(self):
        """الاتصال بـ Dropbox"""
        # محاكاة الاتصال
        return True

    def connect_onedrive(self):
        """الاتصال بـ OneDrive"""
        # محاكاة الاتصال
        return True

    def connect_aws_s3(self):
        """الاتصال بـ Amazon S3"""
        # محاكاة الاتصال
        return True

    def connect_azure(self):
        """الاتصال بـ Azure Storage"""
        # محاكاة الاتصال
        return True

    def show_provider_settings(self, provider_id):
        """عرض إعدادات موفر الخدمة"""
        settings_window = ProviderSettingsWindow(self, provider_id, self.cloud_providers[provider_id])

    def refresh_providers_tab(self):
        """تحديث تبويب موفري الخدمات"""
        # إعادة إنشاء محتوى التبويب
        for widget in self.providers_frame.winfo_children():
            widget.destroy()
        self.create_providers_tab()

    def update_sync_settings(self):
        """تحديث إعدادات المزامنة"""
        try:
            self.sync_settings['auto_sync'] = self.auto_sync_var.get()
            self.sync_settings['compression'] = self.compression_var.get()
            self.sync_settings['encryption'] = self.encryption_var.get()

            # تحديث الفترات الزمنية
            try:
                self.sync_settings['sync_interval'] = int(self.interval_var.get())
                self.sync_settings['backup_retention'] = int(self.retention_var.get())
            except ValueError:
                pass

            self.save_cloud_settings()

        except Exception as e:
            print(f"خطأ في تحديث إعدادات المزامنة: {e}")

    def start_manual_sync(self):
        """بدء المزامنة اليدوية"""
        if self.sync_status['sync_in_progress']:
            messagebox.showwarning("تحذير", "المزامنة قيد التشغيل بالفعل")
            return

        # بدء المزامنة في خيط منفصل
        threading.Thread(target=self.perform_sync, daemon=True).start()

    def start_manual_backup(self):
        """بدء النسخ الاحتياطي اليدوي"""
        if self.sync_status['sync_in_progress']:
            messagebox.showwarning("تحذير", "عملية مزامنة قيد التشغيل")
            return

        # بدء النسخ الاحتياطي في خيط منفصل
        threading.Thread(target=self.perform_backup, daemon=True).start()

    def perform_sync(self):
        """تنفيذ عملية المزامنة"""
        try:
            self.sync_status['sync_in_progress'] = True
            self.update_sync_progress(0, "بدء المزامنة...")

            # جمع الملفات المطلوب مزامنتها
            files_to_sync = self.get_files_to_sync()
            self.sync_status['total_files'] = len(files_to_sync)
            self.sync_status['synced_files'] = 0
            self.sync_status['failed_files'] = 0

            # مزامنة كل ملف
            for i, file_path in enumerate(files_to_sync):
                try:
                    progress = (i + 1) / len(files_to_sync)
                    self.update_sync_progress(progress, f"مزامنة {file_path}...")

                    # تنفيذ المزامنة الفعلية
                    if self.sync_file(file_path):
                        self.sync_status['synced_files'] += 1
                    else:
                        self.sync_status['failed_files'] += 1

                    time.sleep(0.1)  # محاكاة وقت المعالجة

                except Exception as e:
                    print(f"خطأ في مزامنة {file_path}: {e}")
                    self.sync_status['failed_files'] += 1

            # إنهاء المزامنة
            self.sync_status['last_sync'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.sync_status['sync_in_progress'] = False

            self.update_sync_progress(1.0, "اكتملت المزامنة بنجاح!")
            self.save_cloud_settings()

        except Exception as e:
            print(f"خطأ في المزامنة: {e}")
            self.sync_status['sync_in_progress'] = False
            self.update_sync_progress(0, "فشلت المزامنة")

    def perform_backup(self):
        """تنفيذ عملية النسخ الاحتياطي"""
        try:
            self.sync_status['sync_in_progress'] = True
            self.update_sync_progress(0, "بدء النسخ الاحتياطي...")

            # إنشاء نسخة احتياطية من قاعدة البيانات
            backup_file = self.create_database_backup()

            if backup_file:
                self.update_sync_progress(0.5, "رفع النسخة الاحتياطية...")

                # رفع النسخة الاحتياطية للسحابة
                if self.upload_backup_to_cloud(backup_file):
                    self.update_sync_progress(1.0, "اكتمل النسخ الاحتياطي بنجاح!")
                else:
                    self.update_sync_progress(0, "فشل رفع النسخة الاحتياطية")
            else:
                self.update_sync_progress(0, "فشل إنشاء النسخة الاحتياطية")

            self.sync_status['sync_in_progress'] = False

        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي: {e}")
            self.sync_status['sync_in_progress'] = False
            self.update_sync_progress(0, "فشل النسخ الاحتياطي")

    def get_files_to_sync(self):
        """الحصول على قائمة الملفات المطلوب مزامنتها"""
        files = []

        # إضافة قاعدة البيانات
        if os.path.exists(DB_PATH):
            files.append(DB_PATH)

        # إضافة ملفات التكوين
        config_files = ["cloud_settings.json", "app_settings.json"]
        for config_file in config_files:
            if os.path.exists(config_file):
                files.append(config_file)

        return files

    def sync_file(self, file_path):
        """مزامنة ملف واحد"""
        try:
            # محاكاة رفع الملف
            time.sleep(0.1)
            return True
        except Exception as e:
            print(f"خطأ في مزامنة {file_path}: {e}")
            return False

    def create_database_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_cashier_filter_{timestamp}.db"
            backup_path = os.path.join("backups", backup_filename)

            # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            os.makedirs("backups", exist_ok=True)

            # نسخ قاعدة البيانات
            if os.path.exists(DB_PATH):
                import shutil
                shutil.copy2(DB_PATH, backup_path)
                return backup_path

            return None

        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None

    def upload_backup_to_cloud(self, backup_file):
        """رفع النسخة الاحتياطية للسحابة"""
        try:
            # محاكاة رفع الملف
            time.sleep(1)
            return True
        except Exception as e:
            print(f"خطأ في رفع النسخة الاحتياطية: {e}")
            return False

    def update_sync_progress(self, progress, message):
        """تحديث شريط تقدم المزامنة"""
        try:
            self.after(0, lambda: self._update_progress_ui(progress, message))
        except:
            pass

    def _update_progress_ui(self, progress, message):
        """تحديث واجهة شريط التقدم"""
        try:
            self.sync_progress.set(progress)
            self.progress_label.configure(text=message)
        except:
            pass

    def start_auto_sync_monitor(self):
        """بدء مراقب المزامنة التلقائية"""
        if self.sync_settings['auto_sync']:
            self.after(self.sync_settings['sync_interval'] * 60 * 1000, self.auto_sync_cycle)

    def auto_sync_cycle(self):
        """دورة المزامنة التلقائية"""
        if self.sync_settings['auto_sync'] and not self.sync_status['sync_in_progress']:
            threading.Thread(target=self.perform_sync, daemon=True).start()

        # جدولة الدورة التالية
        self.after(self.sync_settings['sync_interval'] * 60 * 1000, self.auto_sync_cycle)

    def show_restore_dialog(self):
        """عرض نافذة الاستعادة من النسخ الاحتياطية"""
        restore_window = RestoreBackupWindow(self)

    def upload_file(self):
        """رفع ملف للسحابة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف للرفع",
            filetypes=[("جميع الملفات", "*.*")]
        )

        if file_path:
            threading.Thread(target=self._upload_file_thread, args=(file_path,), daemon=True).start()

    def _upload_file_thread(self, file_path):
        """رفع الملف في خيط منفصل"""
        try:
            self.update_sync_progress(0, f"رفع {os.path.basename(file_path)}...")

            # محاكاة رفع الملف
            for i in range(101):
                time.sleep(0.02)
                self.update_sync_progress(i/100, f"رفع {os.path.basename(file_path)}... {i}%")

            self.update_sync_progress(1.0, "تم رفع الملف بنجاح!")
            self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

            # تحديث قائمة الملفات
            self.refresh_files()

        except Exception as e:
            print(f"خطأ في رفع الملف: {e}")
            self.update_sync_progress(0, "فشل رفع الملف")

    def download_file(self):
        """تحميل ملف من السحابة"""
        # الحصول على الملف المحدد
        selected_item = self.files_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للتحميل")
            return

        file_name = self.files_tree.item(selected_item[0])['values'][0]

        # اختيار مكان الحفظ
        save_path = filedialog.asksaveasfilename(
            title="حفظ الملف",
            initialname=file_name,
            defaultextension=os.path.splitext(file_name)[1]
        )

        if save_path:
            threading.Thread(target=self._download_file_thread, args=(file_name, save_path), daemon=True).start()

    def _download_file_thread(self, file_name, save_path):
        """تحميل الملف في خيط منفصل"""
        try:
            self.update_sync_progress(0, f"تحميل {file_name}...")

            # محاكاة تحميل الملف
            for i in range(101):
                time.sleep(0.02)
                self.update_sync_progress(i/100, f"تحميل {file_name}... {i}%")

            self.update_sync_progress(1.0, "تم تحميل الملف بنجاح!")
            self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

        except Exception as e:
            print(f"خطأ في تحميل الملف: {e}")
            self.update_sync_progress(0, "فشل تحميل الملف")

    def delete_file(self):
        """حذف ملف من السحابة"""
        selected_item = self.files_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")
            return

        file_name = self.files_tree.item(selected_item[0])['values'][0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الملف '{file_name}'؟"):
            try:
                # محاكاة حذف الملف
                self.update_sync_progress(0.5, f"حذف {file_name}...")
                time.sleep(0.5)

                # إزالة من الجدول
                self.files_tree.delete(selected_item[0])

                self.update_sync_progress(1.0, "تم حذف الملف بنجاح!")
                self.after(2000, lambda: self.update_sync_progress(0, "جاهز"))

            except Exception as e:
                print(f"خطأ في حذف الملف: {e}")
                messagebox.showerror("خطأ", f"فشل حذف الملف: {e}")

    def refresh_files(self):
        """تحديث قائمة الملفات"""
        try:
            # مسح القائمة الحالية
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)

            # إضافة ملفات وهمية للعرض
            sample_files = [
                ("cashier_filter_backup.db", "2.5 MB", "2024-01-15 10:30", "Google Drive", "متزامن"),
                ("reports_2024.xlsx", "1.2 MB", "2024-01-14 15:45", "Dropbox", "متزامن"),
                ("settings_backup.json", "15 KB", "2024-01-13 09:20", "OneDrive", "متزامن"),
                ("user_data.csv", "850 KB", "2024-01-12 14:10", "AWS S3", "قيد المزامنة"),
                ("system_log.txt", "320 KB", "2024-01-11 11:55", "Azure", "فشل")
            ]

            for file_data in sample_files:
                self.files_tree.insert("", "end", values=file_data)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الملفات: {e}")

    def generate_usage_report(self):
        """إنشاء تقرير الاستخدام"""
        try:
            report_window = ReportWindow(self, "تقرير الاستخدام السحابي")

            # بيانات وهمية للتقرير
            usage_data = {
                "إجمالي البيانات المرفوعة": "15.7 GB",
                "إجمالي البيانات المحملة": "8.3 GB",
                "عدد الملفات المرفوعة": "1,247",
                "عدد الملفات المحملة": "892",
                "متوسط حجم الملف": "2.1 MB",
                "أكثر الموفرين استخداماً": "Google Drive (45%)",
                "عدد عمليات المزامنة": "156",
                "معدل نجاح المزامنة": "98.7%"
            }

            report_window.display_data(usage_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الاستخدام: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")

    def generate_sync_report(self):
        """إنشاء تقرير المزامنة"""
        try:
            report_window = ReportWindow(self, "تقرير المزامنة")

            # بيانات وهمية للتقرير
            sync_data = {
                "آخر مزامنة": self.sync_status.get('last_sync', 'لم تتم بعد'),
                "إجمالي الملفات": str(self.sync_status.get('total_files', 0)),
                "الملفات المتزامنة": str(self.sync_status.get('synced_files', 0)),
                "الملفات الفاشلة": str(self.sync_status.get('failed_files', 0)),
                "معدل النجاح": "95.2%",
                "متوسط وقت المزامنة": "2.3 دقيقة",
                "أسرع مزامنة": "45 ثانية",
                "أبطأ مزامنة": "8.7 دقيقة"
            }

            report_window.display_data(sync_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المزامنة: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")

    def generate_security_report(self):
        """إنشاء تقرير الأمان"""
        try:
            report_window = ReportWindow(self, "تقرير الأمان السحابي")

            # بيانات وهمية للتقرير
            security_data = {
                "الملفات المشفرة": "1,156 (92.7%)",
                "الملفات غير المشفرة": "91 (7.3%)",
                "نوع التشفير المستخدم": "AES-256",
                "عدد محاولات الوصول": "2,847",
                "محاولات الوصول المرفوضة": "12 (0.4%)",
                "آخر تسجيل دخول مشبوه": "لا يوجد",
                "حالة المصادقة الثنائية": "مفعلة",
                "عدد التنبيهات الأمنية": "0"
            }

            report_window.display_data(security_data)

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأمان: {e}")
            messagebox.showerror("خطأ", f"فشل إنشاء التقرير: {e}")


class ProviderSettingsWindow(ctk.CTkToplevel):
    """نافذة إعدادات موفر الخدمة السحابية"""

    def __init__(self, parent, provider_id, provider_info):
        super().__init__(parent)
        self.provider_id = provider_id
        self.provider_info = provider_info

        self.title(f"إعدادات {provider_info['name']}")
        self.geometry("500x600")
        self.configure(bg="#f0f2f5")
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"{self.provider_info['icon']} إعدادات {self.provider_info['name']}",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # إطار الإعدادات
        settings_frame = ctk.CTkScrollableFrame(
            self,
            fg_color="#ffffff",
            corner_radius=15,
            height=400
        )
        settings_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # إعدادات الاتصال
        self.create_connection_settings(settings_frame)

        # إعدادات المزامنة
        self.create_sync_settings(settings_frame)

        # إعدادات الأمان
        self.create_security_settings(settings_frame)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_settings,
            fg_color="#10b981",
            hover_color="#059669",
            width=100,
            height=35
        )
        save_btn.pack(side="left", padx=5)

        test_btn = ctk.CTkButton(
            buttons_frame,
            text="🧪 اختبار الاتصال",
            command=self.test_connection,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=120,
            height=35
        )
        test_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=100,
            height=35
        )
        cancel_btn.pack(side="right", padx=5)

    def create_connection_settings(self, parent):
        """إنشاء إعدادات الاتصال"""
        conn_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        conn_frame.pack(fill="x", pady=10, padx=10)

        conn_title = ctk.CTkLabel(
            conn_frame,
            text="🔗 إعدادات الاتصال",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        conn_title.pack(pady=10)

        # معرف التطبيق
        app_id_frame = ctk.CTkFrame(conn_frame, fg_color="transparent")
        app_id_frame.pack(fill="x", padx=15, pady=5)

        app_id_label = ctk.CTkLabel(
            app_id_frame,
            text="معرف التطبيق:",
            font=("Arial", 12)
        )
        app_id_label.pack(side="left")

        self.app_id_entry = ctk.CTkEntry(
            app_id_frame,
            width=250,
            placeholder_text="أدخل معرف التطبيق"
        )
        self.app_id_entry.pack(side="right")

        # مفتاح التطبيق
        app_key_frame = ctk.CTkFrame(conn_frame, fg_color="transparent")
        app_key_frame.pack(fill="x", padx=15, pady=5)

        app_key_label = ctk.CTkLabel(
            app_key_frame,
            text="مفتاح التطبيق:",
            font=("Arial", 12)
        )
        app_key_label.pack(side="left")

        self.app_key_entry = ctk.CTkEntry(
            app_key_frame,
            width=250,
            show="*",
            placeholder_text="أدخل مفتاح التطبيق"
        )
        self.app_key_entry.pack(side="right")

        # رابط الخادم (للخدمات المخصصة)
        if self.provider_id in ["aws_s3", "azure"]:
            server_frame = ctk.CTkFrame(conn_frame, fg_color="transparent")
            server_frame.pack(fill="x", padx=15, pady=5)

            server_label = ctk.CTkLabel(
                server_frame,
                text="رابط الخادم:",
                font=("Arial", 12)
            )
            server_label.pack(side="left")

            self.server_entry = ctk.CTkEntry(
                server_frame,
                width=250,
                placeholder_text="https://..."
            )
            self.server_entry.pack(side="right")

    def create_sync_settings(self, parent):
        """إنشاء إعدادات المزامنة الخاصة بالموفر"""
        sync_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        sync_frame.pack(fill="x", pady=10, padx=10)

        sync_title = ctk.CTkLabel(
            sync_frame,
            text="🔄 إعدادات المزامنة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        sync_title.pack(pady=10)

        # مجلد المزامنة
        folder_frame = ctk.CTkFrame(sync_frame, fg_color="transparent")
        folder_frame.pack(fill="x", padx=15, pady=5)

        folder_label = ctk.CTkLabel(
            folder_frame,
            text="مجلد المزامنة:",
            font=("Arial", 12)
        )
        folder_label.pack(side="left")

        self.folder_entry = ctk.CTkEntry(
            folder_frame,
            width=200,
            placeholder_text="/CashierFilter"
        )
        self.folder_entry.pack(side="left", padx=10)

        browse_btn = ctk.CTkButton(
            folder_frame,
            text="📁",
            width=30,
            height=30
        )
        browse_btn.pack(side="right")

        # أولوية المزامنة
        priority_frame = ctk.CTkFrame(sync_frame, fg_color="transparent")
        priority_frame.pack(fill="x", padx=15, pady=5)

        priority_label = ctk.CTkLabel(
            priority_frame,
            text="أولوية المزامنة:",
            font=("Arial", 12)
        )
        priority_label.pack(side="left")

        self.priority_menu = ctk.CTkOptionMenu(
            priority_frame,
            values=["عالية", "متوسطة", "منخفضة"],
            width=150
        )
        self.priority_menu.pack(side="right")
        self.priority_menu.set("متوسطة")

    def create_security_settings(self, parent):
        """إنشاء إعدادات الأمان الخاصة بالموفر"""
        security_frame = ctk.CTkFrame(parent, fg_color="#f8fafc", corner_radius=10)
        security_frame.pack(fill="x", pady=10, padx=10)

        security_title = ctk.CTkLabel(
            security_frame,
            text="🔒 إعدادات الأمان",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        security_title.pack(pady=10)

        # تشفير محلي
        local_encryption_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        local_encryption_frame.pack(fill="x", padx=15, pady=5)

        self.local_encryption_var = ctk.BooleanVar(value=True)
        local_encryption_checkbox = ctk.CTkCheckBox(
            local_encryption_frame,
            text="تشفير الملفات قبل الرفع",
            variable=self.local_encryption_var,
            font=("Arial", 12)
        )
        local_encryption_checkbox.pack(side="left")

        # التحقق من التكامل
        integrity_frame = ctk.CTkFrame(security_frame, fg_color="transparent")
        integrity_frame.pack(fill="x", padx=15, pady=5)

        self.integrity_var = ctk.BooleanVar(value=True)
        integrity_checkbox = ctk.CTkCheckBox(
            integrity_frame,
            text="التحقق من سلامة الملفات",
            variable=self.integrity_var,
            font=("Arial", 12)
        )
        integrity_checkbox.pack(side="left")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # هنا يتم حفظ الإعدادات الفعلية
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل حفظ الإعدادات: {e}")

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            # محاكاة اختبار الاتصال
            messagebox.showinfo("نجح", "تم اختبار الاتصال بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل اختبار الاتصال: {e}")


class RestoreBackupWindow(ctk.CTkToplevel):
    """نافذة استعادة النسخ الاحتياطية"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("📥 استعادة من النسخ الاحتياطية")
        self.geometry("700x500")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()
        self.load_backups()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text="📥 استعادة من النسخ الاحتياطية",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # قائمة النسخ الاحتياطية
        backups_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        backups_frame.pack(fill="both", expand=True, padx=20, pady=10)

        backups_title = ctk.CTkLabel(
            backups_frame,
            text="📋 النسخ الاحتياطية المتاحة",
            font=("Arial", 14, "bold"),
            text_color="#374151"
        )
        backups_title.pack(pady=15)

        # جدول النسخ الاحتياطية
        columns = ("التاريخ", "الحجم", "الموفر", "الحالة", "الوصف")
        self.backups_tree = ttk.Treeview(backups_frame, columns=columns, show="headings", height=12)

        for col in columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        backups_scrollbar = ttk.Scrollbar(backups_frame, orient="vertical", command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=backups_scrollbar.set)

        self.backups_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        backups_scrollbar.pack(side="right", fill="y", pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        restore_btn = ctk.CTkButton(
            buttons_frame,
            text="📥 استعادة",
            command=self.restore_backup,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        restore_btn.pack(side="left", padx=5)

        preview_btn = ctk.CTkButton(
            buttons_frame,
            text="👁️ معاينة",
            command=self.preview_backup,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=120,
            height=35
        )
        preview_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف",
            command=self.delete_backup,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=120,
            height=35
        )
        delete_btn.pack(side="left", padx=5)

        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            command=self.load_backups,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        refresh_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        close_btn.pack(side="right", padx=5)

    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            # مسح القائمة الحالية
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)

            # إضافة نسخ احتياطية وهمية
            sample_backups = [
                ("2024-01-15 10:30:00", "2.5 MB", "Google Drive", "متاح", "نسخة احتياطية يومية"),
                ("2024-01-14 10:30:00", "2.4 MB", "Dropbox", "متاح", "نسخة احتياطية يومية"),
                ("2024-01-13 10:30:00", "2.3 MB", "OneDrive", "متاح", "نسخة احتياطية يومية"),
                ("2024-01-12 10:30:00", "2.2 MB", "AWS S3", "متاح", "نسخة احتياطية أسبوعية"),
                ("2024-01-11 10:30:00", "2.1 MB", "Azure", "تالف", "نسخة احتياطية يومية"),
                ("2024-01-10 10:30:00", "2.0 MB", "Google Drive", "متاح", "نسخة احتياطية يومية"),
                ("2024-01-09 10:30:00", "1.9 MB", "Dropbox", "متاح", "نسخة احتياطية يومية"),
                ("2024-01-08 10:30:00", "1.8 MB", "OneDrive", "متاح", "نسخة احتياطية يومية")
            ]

            for backup_data in sample_backups:
                self.backups_tree.insert("", "end", values=backup_data)

        except Exception as e:
            print(f"خطأ في تحميل النسخ الاحتياطية: {e}")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية المحددة"""
        selected_item = self.backups_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return

        backup_info = self.backups_tree.item(selected_item[0])['values']
        backup_date = backup_info[0]

        if messagebox.askyesno(
            "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية من {backup_date}؟\n\n"
            "تحذير: سيتم استبدال البيانات الحالية!"
        ):
            try:
                # محاكاة عملية الاستعادة
                progress_window = ProgressWindow(self, "استعادة النسخة الاحتياطية")

                def restore_process():
                    for i in range(101):
                        time.sleep(0.02)
                        progress_window.update_progress(i, f"استعادة البيانات... {i}%")

                    progress_window.destroy()
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح!")

                threading.Thread(target=restore_process, daemon=True).start()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل استعادة النسخة الاحتياطية: {e}")

    def preview_backup(self):
        """معاينة محتوى النسخة الاحتياطية"""
        selected_item = self.backups_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للمعاينة")
            return

        backup_info = self.backups_tree.item(selected_item[0])['values']
        preview_window = BackupPreviewWindow(self, backup_info)

    def delete_backup(self):
        """حذف النسخة الاحتياطية المحددة"""
        selected_item = self.backups_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        backup_info = self.backups_tree.item(selected_item[0])['values']
        backup_date = backup_info[0]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف النسخة الاحتياطية من {backup_date}؟"):
            try:
                # حذف من الجدول
                self.backups_tree.delete(selected_item[0])
                messagebox.showinfo("نجح", "تم حذف النسخة الاحتياطية بنجاح!")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل حذف النسخة الاحتياطية: {e}")


class BackupPreviewWindow(ctk.CTkToplevel):
    """نافذة معاينة النسخة الاحتياطية"""

    def __init__(self, parent, backup_info):
        super().__init__(parent)
        self.backup_info = backup_info

        self.title("👁️ معاينة النسخة الاحتياطية")
        self.geometry("600x400")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"👁️ معاينة النسخة الاحتياطية - {self.backup_info[0]}",
            font=("Arial", 16, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # معلومات النسخة الاحتياطية
        info_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        info_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # عرض المعلومات
        info_text = ctk.CTkTextbox(
            info_frame,
            font=("Arial", 12),
            wrap="word"
        )
        info_text.pack(fill="both", expand=True, padx=15, pady=15)

        # إضافة معلومات وهمية
        preview_content = f"""
📋 معلومات النسخة الاحتياطية:

📅 التاريخ: {self.backup_info[0]}
📏 الحجم: {self.backup_info[1]}
☁️ الموفر: {self.backup_info[2]}
✅ الحالة: {self.backup_info[3]}
📝 الوصف: {self.backup_info[4]}

📊 محتوى النسخة الاحتياطية:

🗄️ قاعدة البيانات:
   • جدول المستخدمين: 15 سجل
   • جدول الكاشيرين: 8 سجل
   • جدول التصفيات: 1,247 سجل
   • جدول العملاء: 892 سجل
   • جدول الإعدادات: 45 سجل

⚙️ ملفات الإعدادات:
   • إعدادات التطبيق
   • إعدادات المستخدمين
   • إعدادات التكامل السحابي
   • إعدادات التقارير

🔒 معلومات الأمان:
   • النسخة مشفرة: نعم
   • نوع التشفير: AES-256
   • التحقق من التكامل: تم
   • التوقيع الرقمي: صالح

📈 إحصائيات:
   • إجمالي السجلات: 2,207
   • حجم البيانات المضغوطة: {self.backup_info[1]}
   • حجم البيانات الأصلية: 4.8 MB
   • معدل الضغط: 48%
        """

        info_text.insert("1.0", preview_content)
        info_text.configure(state="disabled")

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            self,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        close_btn.pack(pady=10)


class ReportWindow(ctk.CTkToplevel):
    """نافذة عرض التقارير"""

    def __init__(self, parent, report_title):
        super().__init__(parent)
        self.report_title = report_title

        self.title(report_title)
        self.geometry("700x600")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=15)
        title_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            title_frame,
            text=f"📊 {self.report_title}",
            font=("Arial", 18, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

        # إطار التقرير
        self.report_frame = ctk.CTkScrollableFrame(
            self,
            fg_color="#ffffff",
            corner_radius=15
        )
        self.report_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        export_btn = ctk.CTkButton(
            buttons_frame,
            text="📤 تصدير",
            command=self.export_report,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        export_btn.pack(side="left", padx=5)

        print_btn = ctk.CTkButton(
            buttons_frame,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=120,
            height=35
        )
        print_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#6b7280",
            hover_color="#4b5563",
            width=120,
            height=35
        )
        close_btn.pack(side="right", padx=5)

    def display_data(self, data):
        """عرض بيانات التقرير"""
        try:
            # تاريخ التقرير
            date_label = ctk.CTkLabel(
                self.report_frame,
                text=f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                font=("Arial", 12),
                text_color="#6b7280"
            )
            date_label.pack(pady=10)

            # عرض البيانات في شكل بطاقات
            for key, value in data.items():
                self.create_data_card(key, value)

        except Exception as e:
            print(f"خطأ في عرض بيانات التقرير: {e}")

    def create_data_card(self, title, value):
        """إنشاء بطاقة بيانات"""
        card_frame = ctk.CTkFrame(self.report_frame, fg_color="#f8fafc", corner_radius=10)
        card_frame.pack(fill="x", pady=5, padx=15)

        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="#374151"
        )
        title_label.pack(side="left")

        value_label = ctk.CTkLabel(
            content_frame,
            text=str(value),
            font=("Arial", 12),
            text_color="#1e3a8a"
        )
        value_label.pack(side="right")

    def export_report(self):
        """تصدير التقرير"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="تصدير التقرير",
                defaultextension=".txt",
                filetypes=[
                    ("ملف نصي", "*.txt"),
                    ("ملف CSV", "*.csv"),
                    ("ملف JSON", "*.json")
                ]
            )

            if file_path:
                # محاكاة تصدير التقرير
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تصدير التقرير: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # محاكاة طباعة التقرير
            messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل طباعة التقرير: {e}")


class ProgressWindow(ctk.CTkToplevel):
    """نافذة عرض التقدم"""

    def __init__(self, parent, title):
        super().__init__(parent)
        self.title(title)
        self.geometry("400x150")
        self.configure(bg="#f0f2f5")
        self.resizable(False, False)

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text=self.title,
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)

        # شريط التقدم
        self.progress_bar = ctk.CTkProgressBar(
            self,
            width=300,
            height=20
        )
        self.progress_bar.pack(pady=10)
        self.progress_bar.set(0)

        # نص التقدم
        self.progress_label = ctk.CTkLabel(
            self,
            text="جاري التحضير...",
            font=("Arial", 12),
            text_color="#6b7280"
        )
        self.progress_label.pack(pady=10)

    def update_progress(self, progress, message):
        """تحديث التقدم"""
        try:
            self.progress_bar.set(progress / 100)
            self.progress_label.configure(text=message)
            self.update()
        except:
            pass


# دالة لفتح نافذة التكامل السحابي
def open_cloud_integration(parent=None):
    """فتح نافذة التكامل السحابي"""
    try:
        window = CloudIntegrationWindow(parent)
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة التكامل السحابي: {e}")
        if parent:
            messagebox.showerror("خطأ", f"فشل فتح نافذة التكامل السحابي: {e}")
        return None


if __name__ == "__main__":
    # اختبار النافذة
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")

    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    app = CloudIntegrationWindow()
    app.mainloop()
