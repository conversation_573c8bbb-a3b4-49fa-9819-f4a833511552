# Complete requirements for PyInstaller build
# متطلبات كاملة لبناء PyInstaller

# Core GUI Framework
customtkinter>=5.2.0

# Web Framework
flask>=2.3.0
werkzeug>=2.3.0
jinja2>=3.1.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
openpyxl>=3.1.0

# PDF Generation
fpdf2>=2.7.0
reportlab>=4.0.0

# Image Processing
Pillow>=10.0.0

# Network and HTTP
requests>=2.31.0
urllib3>=2.0.0
httpx>=0.24.0

# Cryptography and Security
cryptography>=41.0.0
pycryptodome>=3.18.0

# System Utilities
psutil>=5.9.0
schedule>=1.2.0
pyperclip>=1.8.0

# Cloud Integration (Optional)
google-api-python-client>=2.100.0
dropbox>=11.36.0
boto3>=1.28.0
azure-storage-blob>=12.17.0

# Compression
py7zr>=0.20.0

# PyInstaller for building EXE
pyinstaller>=5.13.0

# Additional dependencies for PyInstaller
altgraph>=0.17.3
pefile>=2023.2.7
pywin32-ctypes>=0.2.2

# Graphics and visualization (optional)
matplotlib>=3.5.0

# Email support
secure-smtplib>=0.1.1

# Additional utilities
python-dateutil>=2.8.0
pytz>=2023.3
certifi>=2023.7.22
