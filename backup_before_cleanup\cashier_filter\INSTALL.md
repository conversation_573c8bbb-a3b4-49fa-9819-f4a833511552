# 🚀 دليل التثبيت السريع - نظام تصفية الكاشير المتكامل 2025

## 📋 طرق التثبيت

### 🎯 الطريقة الأولى: التثبيت التلقائي (الأسهل)

```bash
# تحميل الملفات ثم تشغيل:
python install.py
```

### 🎯 الطريقة الثانية: التثبيت المبسط

```bash
# تشغيل الملف المبسط:
python run.py
```

### 🎯 الطريقة الثالثة: التثبيت اليدوي

```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. إعداد قاعدة البيانات
python setup.py

# 3. تشغيل التطبيق
python main.py
```

---

## 💻 متطلبات النظام

### الحد الأدنى:
- **Python:** 3.8 أو أحدث
- **نظام التشغيل:** Windows 10, macOS 10.14, Ubuntu 18.04+
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 500 MB

### الموصى به:
- **Python:** 3.10 أو أحدث  
- **الذاكرة:** 8 GB RAM
- **مساحة القرص:** 2 GB

---

## 📦 المتطلبات الأساسية

```
customtkinter>=5.2.0    # واجهة المستخدم
fpdf2>=2.7.0           # تصدير PDF
pandas>=1.5.0          # معالجة البيانات
openpyxl>=3.1.0        # ملفات Excel
reportlab>=4.0.0       # تقارير PDF
numpy>=1.24.0          # العمليات الرياضية
```

### المتطلبات الاختيارية:
```
matplotlib>=3.5.0      # الرسوم البيانية
weasyprint>=60.0       # PDF متقدم
```

---

## 🔧 خطوات التثبيت التفصيلية

### 1️⃣ تثبيت Python

#### Windows:
1. تحميل من [python.org](https://python.org)
2. تشغيل المثبت مع تحديد "Add to PATH"
3. التحقق: `python --version`

#### macOS:
```bash
# باستخدام Homebrew
brew install python

# أو تحميل من python.org
```

#### Ubuntu/Linux:
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### 2️⃣ تحميل النظام

```bash
# استنساخ المشروع أو تحميل الملفات
# ثم الانتقال لمجلد المشروع
cd cashier_filter
```

### 3️⃣ التثبيت التلقائي

```bash
# تشغيل المثبت التلقائي
python install.py
```

سيقوم المثبت بـ:
- ✅ فحص النظام والمتطلبات
- ✅ تثبيت جميع المكتبات المطلوبة
- ✅ إنشاء قاعدة البيانات
- ✅ إضافة بيانات تجريبية
- ✅ إنشاء اختصارات التشغيل

### 4️⃣ التشغيل

#### Windows:
```bash
# الطريقة الأولى
python main.py

# الطريقة الثانية
تشغيل_النظام.bat
```

#### Linux/Mac:
```bash
# الطريقة الأولى  
python3 main.py

# الطريقة الثانية
./تشغيل_النظام.sh
```

---

## 🔐 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: 123456
```

---

## 🛠️ حل المشاكل الشائعة

### ❌ خطأ: Python غير موجود
```bash
# التحقق من التثبيت
python --version
# أو
python3 --version

# إضافة Python للـ PATH في Windows
```

### ❌ خطأ: pip غير موجود
```bash
# تثبيت pip
python -m ensurepip --upgrade

# أو في Ubuntu
sudo apt install python3-pip
```

### ❌ خطأ: فشل تثبيت customtkinter
```bash
# تحديث pip أولاً
python -m pip install --upgrade pip

# ثم تثبيت customtkinter
pip install customtkinter
```

### ❌ خطأ: قاعدة البيانات غير موجودة
```bash
# إعادة إنشاء قاعدة البيانات
python db/init_db.py

# أو تشغيل الإعداد
python setup.py
```

### ❌ خطأ: الملفات غير موجودة
```bash
# التأكد من وجود جميع الملفات
ls -la  # في Linux/Mac
dir     # في Windows

# إعادة تحميل الملفات إذا لزم الأمر
```

---

## 🔄 التحديث

```bash
# تحديث المتطلبات
pip install -r requirements.txt --upgrade

# إعادة تشغيل الإعداد
python setup.py
```

---

## 📱 إنشاء ملف تنفيذي

```bash
# تثبيت PyInstaller
pip install pyinstaller

# بناء الملف التنفيذي
python build.py
```

سيتم إنشاء:
- 📁 مجلد محمول مع الملف التنفيذي
- 🗜️ أرشيف مضغوط للتوزيع
- 📄 ملفات تشغيل مبسطة

---

## 💬 الدعم الفني

### 📞 طرق التواصل:
- **البريد الإلكتروني:** <EMAIL>
- **الدعم الفني:** متاح 24/7

### 🐛 الإبلاغ عن المشاكل:
1. وصف المشكلة بالتفصيل
2. إرفاق لقطات شاشة
3. ذكر نظام التشغيل وإصدار Python

---

## ✅ التحقق من التثبيت

```bash
# اختبار سريع
python -c "import customtkinter; print('✅ التثبيت ناجح')"

# اختبار شامل
python run.py
```

---

**© 2025 محمد الكامل - جميع الحقوق محفوظة**
