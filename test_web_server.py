#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار خادم الويب - نظام تصفية الكاشير
Web Server Test - Cashier Filter System
"""

import sys
import os
import time
import threading
import requests
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent / "cashier_filter"
sys.path.insert(0, str(project_root))

def test_web_server_import():
    """اختبار استيراد خادم الويب"""
    print("🌐 اختبار استيراد خادم الويب...")
    
    try:
        from web_server import WebReportServer, app
        print("  ✅ تم استيراد خادم الويب بنجاح")
        return True
    except ImportError as e:
        print(f"  ❌ فشل في استيراد خادم الويب: {e}")
        return False
    except Exception as e:
        print(f"  ❌ خطأ في استيراد خادم الويب: {e}")
        return False

def test_web_server_startup():
    """اختبار تشغيل خادم الويب"""
    print("\n🚀 اختبار تشغيل خادم الويب...")
    
    try:
        from web_server import WebReportServer
        
        server = WebReportServer()
        print(f"  📍 العنوان: {server.host}:{server.port}")
        
        # اختبار الاتصال بقاعدة البيانات
        conn = server.get_database_connection()
        if conn:
            print("  ✅ الاتصال بقاعدة البيانات نجح")
            conn.close()
        else:
            print("  ❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في تشغيل خادم الويب: {e}")
        return False

def test_web_routes():
    """اختبار مسارات الويب"""
    print("\n🛣️ اختبار مسارات الويب...")
    
    try:
        from web_server import app
        
        # إنشاء عميل اختبار
        with app.test_client() as client:
            
            # اختبار الصفحة الرئيسية
            print("  🏠 اختبار الصفحة الرئيسية...")
            response = client.get('/')
            if response.status_code == 200:
                print("    ✅ الصفحة الرئيسية تعمل")
            else:
                print(f"    ❌ خطأ في الصفحة الرئيسية: {response.status_code}")
            
            # اختبار API التصفيات
            print("  📊 اختبار API التصفيات...")
            response = client.get('/api/filters')
            if response.status_code == 200:
                print("    ✅ API التصفيات يعمل")
                data = response.get_json()
                print(f"    📈 عدد التصفيات: {len(data) if data else 0}")
            else:
                print(f"    ❌ خطأ في API التصفيات: {response.status_code}")
            
            # اختبار صفحة التقارير
            print("  📋 اختبار صفحة التقارير...")
            response = client.get('/reports')
            if response.status_code == 200:
                print("    ✅ صفحة التقارير تعمل")
            else:
                print(f"    ❌ خطأ في صفحة التقارير: {response.status_code}")
            
            # اختبار الإحصائيات
            print("  📊 اختبار API الإحصائيات...")
            response = client.get('/api/stats')
            if response.status_code == 200:
                print("    ✅ API الإحصائيات يعمل")
                stats = response.get_json()
                if stats:
                    print(f"    💰 إجمالي الإيرادات: {stats.get('total_revenue', 0)}")
                    print(f"    📊 عدد التصفيات: {stats.get('total_filters', 0)}")
            else:
                print(f"    ❌ خطأ في API الإحصائيات: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار المسارات: {e}")
        return False

def test_templates():
    """اختبار قوالب HTML"""
    print("\n📄 اختبار قوالب HTML...")
    
    templates_dir = Path("cashier_filter/web_templates")
    
    if not templates_dir.exists():
        print("  ❌ مجلد القوالب غير موجود")
        return False
    
    required_templates = [
        "base.html",
        "index.html", 
        "reports.html",
        "filter_detail.html",
        "comprehensive_report.html"
    ]
    
    missing_templates = []
    
    for template in required_templates:
        template_path = templates_dir / template
        if template_path.exists():
            print(f"  ✅ {template}")
            
            # فحص محتوى القالب
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) > 100:  # التأكد من وجود محتوى
                        print(f"    📏 حجم المحتوى: {len(content)} حرف")
                    else:
                        print(f"    ⚠️ محتوى قليل: {len(content)} حرف")
            except Exception as e:
                print(f"    ❌ خطأ في قراءة القالب: {e}")
        else:
            print(f"  ❌ {template} - غير موجود")
            missing_templates.append(template)
    
    if missing_templates:
        print(f"\n  ⚠️ قوالب مفقودة: {', '.join(missing_templates)}")
        return False
    
    return True

def test_static_files():
    """اختبار الملفات الثابتة"""
    print("\n📁 اختبار الملفات الثابتة...")
    
    static_dir = Path("cashier_filter/web_static")
    
    if not static_dir.exists():
        print("  ❌ مجلد الملفات الثابتة غير موجود")
        return False
    
    # فحص مجلد CSS
    css_dir = static_dir / "css"
    if css_dir.exists():
        css_files = list(css_dir.glob("*.css"))
        print(f"  🎨 ملفات CSS: {len(css_files)}")
        for css_file in css_files:
            print(f"    - {css_file.name}")
    else:
        print("  ⚠️ مجلد CSS غير موجود")
    
    # فحص مجلد JavaScript
    js_dir = static_dir / "js"
    if js_dir.exists():
        js_files = list(js_dir.glob("*.js"))
        print(f"  ⚡ ملفات JavaScript: {len(js_files)}")
        for js_file in js_files:
            print(f"    - {js_file.name}")
    else:
        print("  ⚠️ مجلد JavaScript غير موجود")
    
    return True

def test_cloudflare_tunnel():
    """اختبار إعداد Cloudflare Tunnel"""
    print("\n☁️ اختبار إعداد Cloudflare Tunnel...")
    
    cloudflared_path = Path("cashier_filter/cloudflared.exe")
    
    if cloudflared_path.exists():
        print("  ✅ ملف cloudflared.exe موجود")
        print(f"  📏 حجم الملف: {cloudflared_path.stat().st_size} بايت")
        
        # فحص ملف إعداد النفق
        tunnel_script = Path("cashier_filter/cloudflare_tunnel.py")
        if tunnel_script.exists():
            print("  ✅ سكريبت إعداد النفق موجود")
        else:
            print("  ⚠️ سكريبت إعداد النفق غير موجود")
        
        return True
    else:
        print("  ❌ ملف cloudflared.exe غير موجود")
        print("  💡 الوصول العالمي غير متاح")
        return False

def run_comprehensive_test():
    """تشغيل اختبار شامل لخادم الويب"""
    print("🧪 بدء اختبار شامل لخادم الويب...")
    print("=" * 50)
    
    tests = [
        ("استيراد خادم الويب", test_web_server_import),
        ("تشغيل خادم الويب", test_web_server_startup),
        ("مسارات الويب", test_web_routes),
        ("قوالب HTML", test_templates),
        ("الملفات الثابتة", test_static_files),
        ("Cloudflare Tunnel", test_cloudflare_tunnel)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
        
        print()  # سطر فارغ بين الاختبارات
    
    # ملخص النتائج
    print("📋 ملخص نتائج الاختبار:")
    print("=" * 30)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"  {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 النتيجة النهائية:")
    print(f"  ✅ نجح: {passed}")
    print(f"  ❌ فشل: {failed}")
    print(f"  📈 معدل النجاح: {(passed / len(results)) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 جميع اختبارات خادم الويب نجحت!")
    else:
        print(f"\n⚠️ {failed} اختبار فشل - يحتاج مراجعة")

if __name__ == "__main__":
    run_comprehensive_test()
