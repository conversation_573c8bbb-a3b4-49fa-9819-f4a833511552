@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    📦 تثبيت متطلبات نظام تصفية الكاشير                             ║
echo ║         Installing Cashier Filter System Requirements               ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📁 الانتقال إلى مجلد المشروع...
cd /d "%~dp0"
echo    المجلد الحالي: %CD%
echo.

echo 🐍 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo    يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo    ✅ Python متوفر
echo.

echo 🔄 تحديث pip...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo ⚠️ تحذير: فشل في تحديث pip، المتابعة بالإصدار الحالي...
)
echo.

echo 📦 تثبيت الحزم المطلوبة...
echo.

echo    🔨 تثبيت PyInstaller...
pip install pyinstaller>=5.13.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user pyinstaller>=5.13.0
    if %errorlevel% neq 0 (
        echo    ❌ فشل في تثبيت PyInstaller
        goto :error
    )
)
echo    ✅ تم تثبيت PyInstaller

echo    📄 تثبيت FPDF2...
pip install fpdf2>=2.7.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user fpdf2>=2.7.0
    if %errorlevel% neq 0 (
        echo    ❌ فشل في تثبيت FPDF2
        goto :error
    )
)
echo    ✅ تم تثبيت FPDF2

echo    🌐 تثبيت CustomTkinter...
pip install customtkinter>=5.2.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user customtkinter>=5.2.0
)
echo    ✅ تم تثبيت CustomTkinter

echo    🌍 تثبيت Flask...
pip install flask>=2.3.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user flask>=2.3.0
)
echo    ✅ تم تثبيت Flask

echo    📊 تثبيت Pandas...
pip install pandas>=2.0.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user pandas>=2.0.0
)
echo    ✅ تم تثبيت Pandas

echo    🖼️ تثبيت Pillow...
pip install Pillow>=10.0.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user Pillow>=10.0.0
)
echo    ✅ تم تثبيت Pillow

echo    🌐 تثبيت Requests...
pip install requests>=2.31.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user requests>=2.31.0
)
echo    ✅ تم تثبيت Requests

echo    📋 تثبيت ReportLab...
pip install reportlab>=4.0.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user reportlab>=4.0.0
)
echo    ✅ تم تثبيت ReportLab

echo    📊 تثبيت OpenPyXL...
pip install openpyxl>=3.1.0
if %errorlevel% neq 0 (
    echo    ⚠️ محاولة التثبيت بصلاحيات المستخدم...
    pip install --user openpyxl>=3.1.0
)
echo    ✅ تم تثبيت OpenPyXL

echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🎉 تم تثبيت جميع المتطلبات بنجاح!                                ║
echo ║         All Requirements Installed Successfully!                    ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من التثبيت...
python -c "import pyinstaller; print('✅ PyInstaller:', pyinstaller.__version__)"
python -c "import fpdf; print('✅ FPDF2: متوفر')"
python -c "import customtkinter; print('✅ CustomTkinter: متوفر')"
python -c "import flask; print('✅ Flask: متوفر')"
python -c "import pandas; print('✅ Pandas: متوفر')"
python -c "import PIL; print('✅ Pillow: متوفر')"
python -c "import requests; print('✅ Requests: متوفر')"

echo.
echo 🚀 يمكنك الآن تشغيل بناء الملف التنفيذي:
echo    python build_exe.py
echo.
echo    أو انقر نقراً مزدوجاً على: بناء_الملف_التنفيذي.bat
echo.
goto :end

:error
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    ❌ فشل في تثبيت بعض المتطلبات                                    ║
echo ║         Failed to Install Some Requirements                         ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🔧 جرب الحلول التالية:
echo    1. تشغيل Command Prompt كمسؤول (Run as Administrator)
echo    2. تحديث Python إلى أحدث إصدار
echo    3. التثبيت اليدوي: pip install pyinstaller fpdf2
echo.

:end
pause
