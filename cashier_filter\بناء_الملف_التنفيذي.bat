@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🔨 بناء الملف التنفيذي لنظام تصفية الكاشير                      ║
echo ║         Building EXE for Cashier Filter System                      ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📁 الانتقال إلى مجلد المشروع...
cd /d "%~dp0"
echo    المجلد الحالي: %CD%
echo.

echo 📦 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo    يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo    ✅ Python متوفر
echo.

echo 📋 التحقق من PyInstaller...
python -c "import pyinstaller" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ PyInstaller غير مثبت، جاري التثبيت...
    pip install pyinstaller>=5.13.0
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo    ✅ تم تثبيت PyInstaller
) else (
    echo    ✅ PyInstaller متوفر
)
echo.

echo 🚀 بدء بناء الملف التنفيذي...
echo    هذا قد يستغرق عدة دقائق...
echo.

python build_exe.py

if %errorlevel% equ 0 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════╗
    echo ║                                                                      ║
    echo ║    🎉 تم بناء الملف التنفيذي بنجاح!                                 ║
    echo ║         EXE Build Completed Successfully!                           ║
    echo ║                                                                      ║
    echo ╚══════════════════════════════════════════════════════════════════════╝
    echo.
    echo 📦 ستجد الملف التنفيذي في:
    echo    CashierFilterSystem_Distribution\CashierFilterSystem_v3.5.0_Fixed.exe
    echo.
    echo 🚀 يمكنك الآن توزيع مجلد CashierFilterSystem_Distribution
    echo.
    
    if exist "CashierFilterSystem_Distribution" (
        echo 📂 فتح مجلد التوزيع...
        explorer "CashierFilterSystem_Distribution"
    )
) else (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════╗
    echo ║                                                                      ║
    echo ║    ❌ فشل في بناء الملف التنفيذي                                    ║
    echo ║         EXE Build Failed                                            ║
    echo ║                                                                      ║
    echo ╚══════════════════════════════════════════════════════════════════════╝
    echo.
    echo 🔧 تحقق من الأخطاء أعلاه وحاول مرة أخرى
)

echo.
pause
