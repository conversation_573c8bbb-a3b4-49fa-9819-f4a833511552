# أدوات مساعدة للنوافذ
import customtkinter as ctk

def bring_window_to_front(window):
    """
    جعل النافذة تظهر في المقدمة

    Args:
        window: النافذة المراد إظهارها في المقدمة
    """
    try:
        # التأكد من أن النافذة مرئية
        window.deiconify()

        # رفع النافذة للمقدمة
        window.lift()

        # جعل النافذة في المقدمة مؤقتاً
        window.attributes('-topmost', True)

        # إعطاء التركيز للنافذة
        window.focus_force()

        # محاولة جعل النافذة نشطة (في Windows)
        try:
            window.wm_state('normal')
            # استخدام tkraise كبديل
            window.tkraise()
        except:
            pass

        # إزالة خاصية البقاء في المقدمة بعد 200 مللي ثانية
        def remove_topmost():
            try:
                window.attributes('-topmost', False)
                # محاولة أخيرة لإعطاء التركيز
                window.focus_set()
            except:
                pass

        window.after(200, remove_topmost)

    except Exception as e:
        print(f"خطأ في إظهار النافذة في المقدمة: {e}")

def setup_window_properties(window, title, geometry, resizable=True, bg_color="#f2f3f7"):
    """
    إعداد خصائص النافذة الأساسية
    
    Args:
        window: النافذة
        title: عنوان النافذة
        geometry: حجم النافذة (مثل "800x600")
        resizable: هل النافذة قابلة لتغيير الحجم
        bg_color: لون الخلفية
    """
    try:
        window.title(title)
        window.geometry(geometry)
        window.configure(bg=bg_color)
        window.resizable(resizable, resizable)
        
        # جعل النافذة تظهر في المقدمة
        bring_window_to_front(window)
        
    except Exception as e:
        print(f"خطأ في إعداد خصائص النافذة: {e}")

class BaseWindow(ctk.CTkToplevel):
    """
    فئة أساسية للنوافذ مع إعدادات افتراضية محسنة
    """
    def __init__(self, master=None, title="نافذة", geometry="800x600", 
                 resizable=True, bg_color="#f2f3f7"):
        super().__init__(master)
        
        # إعداد خصائص النافذة
        setup_window_properties(self, title, geometry, resizable, bg_color)
        
        # إعداد إضافي للنوافذ
        self.setup_additional_properties()
    
    def setup_additional_properties(self):
        """إعداد خصائص إضافية للنافذة"""
        try:
            # محاولة تعيين أيقونة التطبيق
            try:
                self.iconbitmap("assets/icon.ico")
            except:
                pass
            
            # إعداد بروتوكول الإغلاق
            self.protocol("WM_DELETE_WINDOW", self.on_closing)
            
        except Exception as e:
            print(f"خطأ في إعداد الخصائص الإضافية: {e}")
    
    def on_closing(self):
        """معالج إغلاق النافذة"""
        self.destroy()
    
    def show_in_front(self):
        """إظهار النافذة في المقدمة"""
        bring_window_to_front(self)
