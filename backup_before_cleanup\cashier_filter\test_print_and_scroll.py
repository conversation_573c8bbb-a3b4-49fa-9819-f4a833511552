#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة الطباعة وشريط التمرير
Test Print Feature and Scrollbar
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_daily_reports_enhancements():
    """اختبار التحسينات الجديدة لنافذة التقارير اليومية"""
    print("🧪 اختبار التحسينات الجديدة لنافذة التقارير اليومية...")
    
    try:
        from ui.daily_reports import DailyReportsWindow
        print("✅ تم استيراد DailyReportsWindow بنجاح")
        
        # فحص الدوال الجديدة للطباعة
        print_methods = [
            'print_report',
            'show_print_preview',
            'create_print_content',
            'create_print_table',
            'execute_print',
            'print_using_browser',
            'generate_html_report',
            'generate_table_rows',
            'save_as_pdf',
            'create_pdf_report',
            'save_html_as_pdf'
        ]
        
        available_print_methods = []
        for method in print_methods:
            if hasattr(DailyReportsWindow, method):
                available_print_methods.append(method)
        
        print(f"✅ دوال الطباعة المتاحة: {len(available_print_methods)}/{len(print_methods)}")
        for method in available_print_methods:
            print(f"   ✅ {method}")
        
        missing_methods = set(print_methods) - set(available_print_methods)
        if missing_methods:
            print("❌ الدوال المفقودة:")
            for method in missing_methods:
                print(f"   ❌ {method}")
        
        # فحص الدوال الأساسية
        basic_methods = [
            'create_widgets',
            'load_daily_data',
            'export_report'
        ]
        
        available_basic = []
        for method in basic_methods:
            if hasattr(DailyReportsWindow, method):
                available_basic.append(method)
        
        print(f"✅ الدوال الأساسية: {len(available_basic)}/{len(basic_methods)}")
        
        return len(available_print_methods) >= 8 and len(available_basic) == len(basic_methods)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحسينات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scrollbar_functionality():
    """اختبار وظيفة شريط التمرير"""
    print("\n🧪 اختبار وظيفة شريط التمرير...")
    
    try:
        from ui.daily_reports import DailyReportsWindow
        
        # فحص استيراد tkinter للتأكد من دعم شريط التمرير
        import tkinter as tk
        from tkinter import ttk
        print("✅ تم استيراد tkinter و ttk بنجاح")
        
        # فحص وجود Canvas في الكود
        import inspect
        source = inspect.getsource(DailyReportsWindow.create_widgets)
        
        if 'Canvas' in source:
            print("✅ تم العثور على Canvas في create_widgets")
        else:
            print("❌ لم يتم العثور على Canvas")
            return False
        
        if 'Scrollbar' in source:
            print("✅ تم العثور على Scrollbar في create_widgets")
        else:
            print("❌ لم يتم العثور على Scrollbar")
            return False
        
        if 'yview_scroll' in source:
            print("✅ تم العثور على دعم التمرير بالماوس")
        else:
            print("⚠️ لم يتم العثور على دعم التمرير بالماوس")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار شريط التمرير: {e}")
        return False

def test_html_generation():
    """اختبار إنشاء HTML للطباعة"""
    print("\n🧪 اختبار إنشاء HTML للطباعة...")
    
    try:
        # إنشاء مثيل وهمي لاختبار HTML
        class MockDailyReports:
            def __init__(self):
                self.selected_date = "2025-01-09"
                
                # إنشاء كائنات وهمية للبطاقات
                class MockCard:
                    def __init__(self, text):
                        self.text = text
                    def cget(self, prop):
                        return self.text
                
                self.total_filters_card = {"value": MockCard("5")}
                self.total_amount_card = {"value": MockCard("15,750.50")}
                self.avg_filter_card = {"value": MockCard("3,150.10")}
                self.active_users_card = {"value": MockCard("3")}
                
                # إنشاء جدول وهمي
                class MockTree:
                    def get_children(self):
                        return ["item1", "item2"]
                    def item(self, item_id):
                        if item_id == "item1":
                            return {'values': ["10:30", "كاشير 1", "2,500.00", "✅ مكتمل"]}
                        else:
                            return {'values': ["14:15", "كاشير 2", "3,250.50", "✅ مكتمل"]}
                
                self.daily_filters_tree = MockTree()
                self.user_activity_tree = MockTree()
            
            def generate_table_rows(self, tree_widget):
                rows_html = ""
                items = tree_widget.get_children()
                
                for item in items:
                    values = tree_widget.item(item)['values']
                    row_html = "<tr>"
                    for value in values:
                        row_html += f"<td>{value}</td>"
                    row_html += "</tr>"
                    rows_html += row_html
                
                return rows_html
            
            def generate_html_report(self):
                from datetime import datetime
                return f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير اليومي - {self.selected_date}</title>
</head>
<body>
    <h1>📈 التقرير اليومي</h1>
    <p>التاريخ: {self.selected_date}</p>
    <p>وقت الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    
    <h2>الإحصائيات العامة</h2>
    <p>إجمالي التصفيات: {self.total_filters_card["value"].cget("text")}</p>
    <p>إجمالي المبلغ: {self.total_amount_card["value"].cget("text")}</p>
    
    <h2>التصفيات اليومية</h2>
    <table border="1">
        <tr><th>الوقت</th><th>الكاشير</th><th>المبلغ</th><th>الحالة</th></tr>
        {self.generate_table_rows(self.daily_filters_tree)}
    </table>
</body>
</html>
                """.strip()
        
        # اختبار إنشاء HTML
        mock_reports = MockDailyReports()
        html_content = mock_reports.generate_html_report()
        
        # فحص محتوى HTML
        if "<!DOCTYPE html>" in html_content:
            print("✅ تم إنشاء HTML صحيح")
        else:
            print("❌ HTML غير صحيح")
            return False
        
        if "التقرير اليومي" in html_content:
            print("✅ العنوان موجود في HTML")
        else:
            print("❌ العنوان مفقود")
            return False
        
        if "<table" in html_content:
            print("✅ الجداول موجودة في HTML")
        else:
            print("❌ الجداول مفقودة")
            return False
        
        if "2025-01-09" in html_content:
            print("✅ التاريخ موجود في HTML")
        else:
            print("❌ التاريخ مفقود")
            return False
        
        print(f"✅ حجم HTML المُنشأ: {len(html_content)} حرف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار HTML: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_print_preview_components():
    """اختبار مكونات معاينة الطباعة"""
    print("\n🧪 اختبار مكونات معاينة الطباعة...")
    
    try:
        # فحص استيراد المكتبات المطلوبة
        import tempfile
        print("✅ مكتبة tempfile متاحة")
        
        import webbrowser
        print("✅ مكتبة webbrowser متاحة")
        
        try:
            from reportlab.lib.pagesizes import A4
            print("✅ مكتبة reportlab متاحة (PDF متقدم)")
            pdf_available = True
        except ImportError:
            print("⚠️ مكتبة reportlab غير متاحة (سيتم استخدام HTML)")
            pdf_available = False
        
        # فحص إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=True) as f:
            f.write("<html><body><h1>اختبار</h1></body></html>")
            print("✅ إنشاء الملفات المؤقتة يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مكونات الطباعة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🖨️ اختبار ميزة الطباعة وشريط التمرير")
    print("=" * 60)
    
    # تشغيل الاختبارات
    tests = [
        ("التحسينات الجديدة لنافذة التقارير", test_daily_reports_enhancements),
        ("وظيفة شريط التمرير", test_scrollbar_functionality),
        ("إنشاء HTML للطباعة", test_html_generation),
        ("مكونات معاينة الطباعة", test_print_preview_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print(f"\n📊 نتائج الاختبارات:")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 الإجمالي: {success_count}/{total_count} اختبار نجح")
    
    if success_count == total_count:
        print("\n🎉 تم تطوير ميزة الطباعة وشريط التمرير بنجاح!")
        print("\n✅ الميزات الجديدة المتاحة:")
        print("   🖨️ معاينة الطباعة المتقدمة")
        print("   📄 طباعة HTML في المتصفح")
        print("   📋 حفظ كـ PDF (إذا كانت المكتبات متاحة)")
        print("   📜 شريط التمرير للواجهة الطويلة")
        print("   🖱️ دعم التمرير بالماوس")
        
        print("\n🚀 لاختبار الميزات الجديدة:")
        print("   1. شغل التطبيق: python main.py")
        print("   2. اضغط على '📈 تقرير يومي'")
        print("   3. اضغط على '🖨️ طباعة' لمعاينة الطباعة")
        print("   4. استخدم شريط التمرير للتنقل")
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت، لكن الميزات الأساسية قد تعمل")

if __name__ == "__main__":
    main()
