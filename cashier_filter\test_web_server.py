#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار خادم الويب مع تقرير العملاء الآجل
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    print("🔄 بدء تشغيل خادم الويب...")
    print(f"📁 مسار المشروع: {BASE_DIR}")
    
    # التحقق من وجود قاعدة البيانات
    db_path = BASE_DIR / "db" / "cashier_filter.db"
    if db_path.exists():
        print(f"✅ قاعدة البيانات موجودة: {db_path}")
    else:
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        print("🔧 إنشاء قاعدة البيانات...")
        from db.init_db import init_db
        init_db()
        print("✅ تم إنشاء قاعدة البيانات")
    
    # استيراد خادم الويب
    print("📦 استيراد خادم الويب...")
    from web_server import app, run_server
    
    print("🌐 تشغيل الخادم...")
    print("=" * 60)
    print("🔗 الروابط المتاحة:")
    print("   الرئيسية: http://localhost:5000/")
    print("   التقارير: http://localhost:5000/reports")
    print("   العملاء الآجل: http://localhost:5000/credit-customers")
    print("   واجهة الهاتف: http://localhost:5000/mobile")
    print("=" * 60)
    
    # تشغيل الخادم
    run_server(host='0.0.0.0', port=5000, debug=True, open_browser_auto=False)
    
except Exception as e:
    print(f"❌ خطأ في تشغيل الخادم: {e}")
    import traceback
    traceback.print_exc()
    input("اضغط Enter للخروج...")
