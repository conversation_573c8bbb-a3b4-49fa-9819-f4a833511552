# متطلبات نظام تصفية الكاشير v3.5.0 - الإصدار الكامل
# Cashier Filter System v3.5.0 Complete Requirements

# ===== المتطلبات الأساسية =====

# واجهة المستخدم الرسومية
customtkinter>=5.2.0

# خادم التقارير والويب
flask>=2.3.0
flask-cors>=4.0.0

# طلبات HTTP والشبكة
requests>=2.31.0

# معالجة البيانات والتصدير
pandas>=2.0.0
openpyxl>=3.1.0

# إنشاء ملفات PDF
fpdf2>=2.7.0

# معالجة الصور والأيقونات
Pillow>=10.0.0

# ===== المتطلبات الاختيارية =====

# تقارير PDF متقدمة (اختياري)
reportlab>=4.0.0

# الرسوم البيانية والإحصائيات (اختياري)
matplotlib>=3.7.0
seaborn>=0.12.0

# تحسين الأداء (اختياري)
numpy>=1.24.0

# ===== ملاحظات =====
# المتطلبات التالية مدمجة مع Python ولا تحتاج تثبيت:
# - sqlite3 (قاعدة البيانات)
# - json (معالجة JSON)
# - datetime (التواريخ والأوقات)
# - os, sys, pathlib (نظام الملفات)
# - threading (المعالجة المتوازية)
# - tkinter (واجهة رسومية أساسية)

# ===== تعليمات التثبيت =====
# للتثبيت السريع:
# pip install -r requirements_complete.txt

# للتثبيت الأساسي فقط:
# pip install customtkinter flask requests pandas openpyxl fpdf2 Pillow

# للتثبيت مع الميزات المتقدمة:
# pip install -r requirements_complete.txt
