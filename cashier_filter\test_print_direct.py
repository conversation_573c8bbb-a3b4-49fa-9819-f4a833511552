#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لطباعة الموردين
Direct Test for Suppliers Print
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    from reports.html_print import generate_filter_report
    import sqlite3
    import json
    from datetime import datetime
    
    def test_direct_print():
        """اختبار مباشر للطباعة مع بيانات واضحة"""
        print("🖨️ اختبار مباشر لطباعة الموردين...")
        print("=" * 50)
        
        # بيانات واضحة ومباشرة
        test_data = {
            'sequence_number': 'DIRECT-TEST-001',
            'cashier_name': 'كاشير الاختبار المباشر',
            'cashier_number': '999',
            'admin_name': 'مسؤول الاختبار المباشر',
            'date': '2025-07-12',
            'credit_transactions': [],
            'client_transactions': [],
            'bank_transactions': [],
            'return_transactions': [],
            'suppliers_transactions': [
                {
                    'supplier_name': 'مورد الاختبار الأول',
                    'amount': 5000.0,
                    'payment_method': 'نقدي',
                    'notes': 'اختبار مباشر - دفعة نقدية'
                },
                {
                    'supplier_name': 'مورد الاختبار الثاني',
                    'amount': 7500.0,
                    'payment_method': 'شيك',
                    'notes': 'اختبار مباشر - شيك رقم 12345'
                },
                {
                    'supplier_name': 'مورد الاختبار الثالث',
                    'amount': 3200.0,
                    'payment_method': 'تحويل بنكي',
                    'notes': 'اختبار مباشر - تحويل بنكي'
                }
            ]
        }
        
        test_totals = {
            'bank': 2000,
            'cash': 1500,
            'credit': 3000,
            'client': 500,
            'return': 200
        }
        
        test_system_sales = 0
        
        print("📋 البيانات المرسلة للطباعة:")
        print(f"   عدد الموردين: {len(test_data['suppliers_transactions'])}")
        for i, supplier in enumerate(test_data['suppliers_transactions'], 1):
            print(f"   {i}. {supplier['supplier_name']}: {supplier['amount']} ريال ({supplier['payment_method']})")
        
        print("\n🖨️ محاولة إنشاء التقرير...")
        
        if generate_filter_report(test_data, test_totals, test_system_sales):
            print("✅ تم إنشاء التقرير بنجاح!")
            print("🔍 تحقق من المتصفح - يجب أن ترى:")
            print("   - قسم 'الموردين (للمتابعة فقط)'")
            print("   - جدول يحتوي على 3 موردين")
            print("   - إجمالي المدفوعات: 15,700.00 ريال")
            return True
        else:
            print("❌ فشل في إنشاء التقرير")
            return False
    
    def test_with_real_filter():
        """اختبار مع تصفية حقيقية من قاعدة البيانات"""
        print("\n📊 اختبار مع تصفية حقيقية...")
        print("=" * 50)
        
        try:
            db_path = BASE_DIR / "db" / "cashier_filter.db"
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # جلب تصفية تحتوي على موردين
            cursor.execute("""
                SELECT id, date, data, admin_name,
                       (SELECT name FROM cashiers WHERE id = filters.cashier_id) as cashier_name
                FROM filters 
                WHERE data IS NOT NULL 
                AND (data LIKE '%suppliers_transactions%' OR data LIKE '%"supplier_name"%')
                ORDER BY date DESC 
                LIMIT 1
            """)
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                print("❌ لم يتم العثور على تصفية تحتوي على موردين")
                return False
            
            filter_id, date, data_str, admin_name, cashier_name = result
            
            print(f"📋 تصفية {filter_id} - {date}:")
            print(f"   الكاشير: {cashier_name or 'غير محدد'}")
            print(f"   المسؤول: {admin_name or 'غير محدد'}")
            
            data = json.loads(data_str)
            
            # البحث عن بيانات الموردين
            suppliers_transactions = data.get('suppliers_transactions', [])
            if not suppliers_transactions:
                details = data.get('details', {})
                suppliers_transactions = details.get('suppliers_transactions', [])
            
            if not suppliers_transactions:
                print("❌ لا توجد بيانات موردين في هذه التصفية")
                return False
            
            print(f"   عدد الموردين: {len(suppliers_transactions)}")
            for i, supplier in enumerate(suppliers_transactions, 1):
                print(f"      {i}. {supplier.get('supplier_name', 'غير محدد')}: {supplier.get('amount', 0)} ريال")
            
            # تحضير البيانات للطباعة (محاكاة daily_filter.py)
            filter_data_for_print = {
                'sequence_number': f'REAL-{filter_id}',
                'cashier_name': cashier_name or 'غير محدد',
                'cashier_number': '001',
                'admin_name': admin_name or 'غير محدد',
                'date': date,
                'credit_transactions': data.get('credit_transactions', []),
                'client_transactions': data.get('client_transactions', []),
                'bank_transactions': data.get('bank_transactions', []),
                'return_transactions': data.get('return_transactions', []),
                'suppliers_transactions': suppliers_transactions
            }
            
            totals = data.get('totals', {})
            system_sales = data.get('system_sales', 0)
            
            print(f"\n🖨️ طباعة التصفية الحقيقية...")
            
            if generate_filter_report(filter_data_for_print, totals, system_sales):
                print("✅ تم إنشاء التقرير من البيانات الحقيقية!")
                print("🔍 تحقق من المتصفح لرؤية بيانات الموردين الحقيقية")
                return True
            else:
                print("❌ فشل في إنشاء التقرير من البيانات الحقيقية")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في اختبار البيانات الحقيقية: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def check_html_content():
        """فحص محتوى HTML المُنشأ"""
        print("\n🔍 فحص محتوى HTML...")
        print("=" * 50)
        
        try:
            # البحث عن ملفات HTML حديثة
            html_files = []
            for file in os.listdir('.'):
                if file.endswith('.html') and 'filter_report' in file:
                    html_files.append(file)
            
            if not html_files:
                print("❌ لم يتم العثور على ملفات HTML")
                return False
            
            # فحص أحدث ملف
            latest_file = max(html_files, key=os.path.getmtime)
            print(f"📄 فحص الملف: {latest_file}")
            
            with open(latest_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # البحث عن قسم الموردين
            if 'الموردين (للمتابعة فقط)' in content:
                print("✅ وجد قسم الموردين في HTML")
                
                # البحث عن بيانات الموردين
                if 'مورد' in content and 'ريال' in content:
                    print("✅ وجد بيانات الموردين في HTML")
                    
                    # عد عدد الصفوف
                    supplier_rows = content.count('<tr>') - 1  # طرح صف العناوين
                    print(f"📊 عدد صفوف الموردين: {supplier_rows}")
                    
                elif 'لا توجد مدفوعات للموردين' in content:
                    print("❌ HTML يحتوي على 'لا توجد مدفوعات للموردين'")
                    print("🔍 هذا يعني أن البيانات لا تصل بشكل صحيح")
                else:
                    print("⚠️ قسم الموردين موجود لكن لا توجد بيانات واضحة")
            else:
                print("❌ لم يتم العثور على قسم الموردين في HTML")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص HTML: {e}")
            return False
    
    def main():
        """تشغيل جميع الاختبارات"""
        print("🧪 اختبار مباشر لطباعة الموردين")
        print("=" * 60)
        
        # اختبار مباشر
        direct_test = test_direct_print()
        
        # اختبار مع بيانات حقيقية
        real_test = test_with_real_filter()
        
        # فحص محتوى HTML
        html_check = check_html_content()
        
        print("\n🎯 ملخص النتائج:")
        print("=" * 60)
        
        if direct_test:
            print("✅ الاختبار المباشر نجح")
        else:
            print("❌ الاختبار المباشر فشل")
        
        if real_test:
            print("✅ اختبار البيانات الحقيقية نجح")
        else:
            print("❌ اختبار البيانات الحقيقية فشل")
        
        if html_check:
            print("✅ فحص HTML مكتمل")
        else:
            print("❌ فحص HTML فشل")
        
        if direct_test and real_test:
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ طباعة الموردين تعمل بشكل صحيح")
            print("🔍 إذا كانت البيانات لا تظهر في التطبيق، المشكلة في الواجهة")
        else:
            print("\n⚠️ بعض الاختبارات فشلت")
            print("🔧 هناك مشكلة في دالة الطباعة تحتاج إصلاح")
        
        print("=" * 60)

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
