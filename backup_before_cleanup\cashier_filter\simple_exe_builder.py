#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة بناء EXE مبسطة لنظام تصفية الكاشير
Simple EXE Builder for Cashier Filter System
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    """طباعة العنوان"""
    print("=" * 60)
    print("🔧 أداة بناء EXE مبسطة - نظام تصفية الكاشير 2025")
    print("=" * 60)

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("\n📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller")
        return True
    except:
        print("❌ فشل تثبيت PyInstaller")
        return False

def build_simple_exe():
    """بناء ملف EXE بسيط"""
    print("\n🔨 بناء ملف EXE...")
    
    try:
        # الأمر المبسط
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed", 
            "--name=CashierSystem2025",
            "--add-data=db;db",
            "--add-data=ui;ui",
            "--add-data=utils;utils",
            "--hidden-import=customtkinter",
            "--hidden-import=tkinter",
            "--hidden-import=sqlite3",
            "main.py"
        ]
        
        print("تشغيل PyInstaller...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء EXE بنجاح!")
            
            exe_path = Path("dist/CashierSystem2025.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 الملف: {exe_path.absolute()}")
                print(f"📏 الحجم: {size_mb:.1f} MB")
                return True
            else:
                print("❌ الملف غير موجود")
                return False
        else:
            print("❌ فشل البناء")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من الملفات
    if not Path("main.py").exists():
        print("❌ ملف main.py غير موجود")
        return
    
    # تثبيت PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller متاح")
    except ImportError:
        if not install_pyinstaller():
            return
    
    # بناء EXE
    if build_simple_exe():
        print("\n🎉 تم إنشاء ملف EXE بنجاح!")
        print("\n📋 تعليمات الاستخدام:")
        print("1. انتقل لمجلد dist/")
        print("2. شغل ملف CashierSystem2025.exe")
        print("3. بيانات الدخول: admin / 123456")
    else:
        print("\n❌ فشل في إنشاء ملف EXE")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
