<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام تصفية الكاشير - التقارير{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .stats-card {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .filter-card {
            border-right: 4px solid #667eea;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .filter-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .mobile-optimized {
            padding: 10px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .card {
                margin: 10px 0;
            }
            
            .btn {
                width: 100%;
                margin: 5px 0;
            }
            
            .stats-card {
                margin: 5px 0;
            }
        }
        
        .loading {
            text-align: center;
            padding: 50px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .amount {
            font-weight: bold;
            color: #2e7d32;
        }
        
        .date {
            color: #666;
            font-size: 0.9em;
        }
        
        .cashier-name {
            color: #1976d2;
            font-weight: 600;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cash-register text-primary"></i>
                <strong>نظام تصفية الكاشير</strong>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/reports">
                            <i class="fas fa-file-alt"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/credit-customers">
                            <i class="fas fa-users"></i> العملاء الآجل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/mobile">
                            <i class="fas fa-mobile-alt"></i> واجهة الهاتف
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <span class="navbar-text">
                        <i class="fas fa-clock"></i>
                        <span id="current-time"></span>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 text-center text-white">
        <div class="container">
            <p>&copy; 2025 نظام تصفية الكاشير المتكامل - جميع الحقوق محفوظة</p>
            <p class="small">تطوير: محمد الكامل | الإصدار 3.0.0</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // تحديث الوقت الحالي
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();
        
        // تحسين الأداء للهاتف
        if (window.innerWidth <= 768) {
            document.body.classList.add('mobile-optimized');
        }
        
        // إضافة تأثيرات التحميل
        function showLoading(element) {
            element.innerHTML = '<div class="loading"><div class="spinner"></div><p>جاري التحميل...</p></div>';
        }
        
        // تنسيق الأرقام العربية
        function formatArabicNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }
        
        // تحديث تلقائي للبيانات كل 30 ثانية
        setInterval(function() {
            if (typeof refreshData === 'function') {
                refreshData();
            }
        }, 30000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
