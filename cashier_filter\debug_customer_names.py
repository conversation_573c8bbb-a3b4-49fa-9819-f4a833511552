#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة أسماء العملاء
Debug Customer Names Issue
"""

import sqlite3
import json
from pathlib import Path

DB_PATH = Path(__file__).parent / "db" / "cashier_filter.db"

def debug_customer_names():
    """تشخيص مشكلة أسماء العملاء"""
    print("🔍 تشخيص مشكلة أسماء العملاء...")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # جلب جميع التصفيات
        cursor.execute("""
            SELECT id, date, data, admin_name, sequence_number
            FROM filters 
            WHERE data IS NOT NULL AND data != ''
            ORDER BY id DESC 
        """)
        
        filters = cursor.fetchall()
        
        if not filters:
            print("❌ لا توجد تصفيات بها بيانات")
            return
        
        print(f"📋 فحص {len(filters)} تصفية...")
        
        for filter_row in filters:
            print(f"\n📋 التصفية #{filter_row['id']} (تسلسل: {filter_row['sequence_number']})")
            print(f"📅 التاريخ: {filter_row['date']}")
            
            try:
                data = json.loads(filter_row['data'])
                
                # فحص المعاملات المختلفة
                transaction_types = [
                    ('credit_transactions', 'المعاملات الآجلة'),
                    ('client_transactions', 'مقبوضات العملاء'),
                    ('return_transactions', 'المرتجعات'),
                    ('bank_transactions', 'المعاملات البنكية'),
                    ('cash_transactions', 'المعاملات النقدية')
                ]
                
                for trans_key, trans_name in transaction_types:
                    if trans_key in data:
                        transactions = data[trans_key]
                        print(f"\n  🔍 {trans_name}: {len(transactions)} معاملة")
                        
                        for i, transaction in enumerate(transactions[:3], 1):  # أول 3 فقط
                            print(f"    معاملة #{i}:")
                            
                            # طباعة جميع المفاتيح والقيم
                            for key, value in transaction.items():
                                print(f"      {key}: {value}")
                            
                            # البحث عن حقول العملاء
                            customer_fields = []
                            for key, value in transaction.items():
                                if any(word in key.lower() for word in ['customer', 'client', 'name', 'عميل']):
                                    customer_fields.append(f"{key}: {value}")
                            
                            if customer_fields:
                                print(f"      🔍 حقول العملاء الموجودة:")
                                for field in customer_fields:
                                    print(f"        - {field}")
                            else:
                                print(f"      ⚠️ لا توجد حقول عملاء واضحة")
                            
                            print()
                
            except json.JSONDecodeError as e:
                print(f"❌ خطأ في تحليل JSON: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {e}")

def suggest_customer_field_mapping():
    """اقتراح خريطة حقول العملاء"""
    print("\n💡 اقتراح خريطة حقول العملاء:")
    print("=" * 60)
    
    possible_mappings = {
        'customer_name': ['customer', 'client', 'name', 'customer_name', 'client_name', 'عميل', 'اسم_العميل'],
        'phone': ['phone', 'mobile', 'tel', 'هاتف', 'جوال', 'رقم_الهاتف'],
        'invoice': ['invoice', 'invoice_number', 'فاتورة', 'رقم_الفاتورة'],
        'amount': ['amount', 'total', 'value', 'مبلغ', 'قيمة', 'إجمالي']
    }
    
    print("🗺️ الحقول المحتملة لكل نوع بيانات:")
    for field_type, possible_names in possible_mappings.items():
        print(f"\n{field_type}:")
        for name in possible_names:
            print(f"  - {name}")

def create_sample_data():
    """إنشاء بيانات عينة لاختبار أسماء العملاء"""
    print("\n📝 بيانات عينة لاختبار أسماء العملاء:")
    print("=" * 60)
    
    sample_data = {
        "totals": {
            "bank": 5000.0,
            "cash": 2000.0,
            "credit": 1500.0,
            "client": 800.0,
            "return": 200.0
        },
        "system_sales": 9100.0,
        "bank_transactions": [
            {
                "type": "فيزا",
                "bank": "الأهلي",
                "amount": 2500.0,
                "ref": "REF001"
            },
            {
                "type": "مدى",
                "bank": "الراجحي",
                "amount": 2500.0,
                "ref": "REF002"
            }
        ],
        "cash_transactions": [],
        "credit_transactions": [
            {
                "invoice": "INV001",
                "customer": "أحمد محمد علي",
                "amount": 750.0,
                "phone": "**********",
                "notes": "عميل مميز"
            },
            {
                "invoice": "INV002",
                "customer_name": "سارة أحمد خالد",
                "amount": 750.0,
                "mobile": "**********",
                "notes": "دفع جزئي"
            }
        ],
        "client_transactions": [
            {
                "customer": "فاطمة خالد محمد",
                "invoice": "INV003",
                "amount": 400.0,
                "method": "cash",
                "phone": "**********"
            },
            {
                "customer_name": "عبدالله سعد أحمد",
                "invoice_number": "INV004",
                "amount": 400.0,
                "payment_method": "bank",
                "mobile": "**********"
            }
        ],
        "return_transactions": [
            {
                "invoice": "RET001",
                "original_invoice": "INV005",
                "customer": "خالد محمد سعد",
                "reason": "عيب في المنتج",
                "amount": 100.0
            },
            {
                "return_invoice": "RET002",
                "original_invoice": "INV006",
                "customer_name": "ليلى سعد علي",
                "reason": "تغيير الرأي",
                "amount": 100.0
            }
        ]
    }
    
    print("📋 بنية البيانات المقترحة:")
    print(json.dumps(sample_data, ensure_ascii=False, indent=2))

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص مشكلة أسماء العملاء")
    print("=" * 70)
    
    # فحص البيانات الحالية
    debug_customer_names()
    
    # اقتراح خريطة الحقول
    suggest_customer_field_mapping()
    
    # إنشاء بيانات عينة
    create_sample_data()
    
    print("\n" + "=" * 70)
    print("✅ انتهى التشخيص")
    print("💡 استخدم هذه المعلومات لإصلاح مشكلة أسماء العملاء")
    print("🔧 قد تحتاج لتحديث التطبيق الأساسي لحفظ أسماء العملاء")

if __name__ == "__main__":
    main()
