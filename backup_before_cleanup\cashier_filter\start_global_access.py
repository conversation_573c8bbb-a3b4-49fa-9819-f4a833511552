#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Global Access Launcher - Cashier Filter Reports
مشغل الوصول العالمي - تقارير نظام تصفية الكاشير

Simple and reliable global access setup
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

def print_header():
    """طباعة العنوان"""
    print("=" * 60)
    print("🌍 Global Access - Cashier Filter Reports")
    print("   الوصول العالمي - تقارير نظام تصفية الكاشير")
    print("=" * 60)
    print()

def check_local_server():
    """التحقق من الخادم المحلي"""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        return response.status_code == 200
    except:
        return False

def start_local_server():
    """بدء الخادم المحلي"""
    print("[1/4] Starting local server...")
    try:
        subprocess.Popen([
            sys.executable, "web_server.py"
        ], creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # انتظار بدء الخادم
        for i in range(10):
            time.sleep(1)
            if check_local_server():
                print("[OK] Local server is running")
                return True
            print(f"      Waiting... ({i+1}/10)")
        
        print("[ERROR] Failed to start local server")
        return False
        
    except Exception as e:
        print(f"[ERROR] Failed to start server: {e}")
        return False

def download_cloudflared():
    """تحميل cloudflared"""
    cloudflared_path = Path("cloudflared.exe")
    
    if cloudflared_path.exists():
        print("[OK] cloudflared.exe found")
        return True
    
    print("[2/4] Downloading Cloudflare Tunnel...")
    print("      This may take a few minutes...")
    
    try:
        import urllib.request
        
        url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        
        def show_progress(block_num, block_size, total_size):
            downloaded = block_num * block_size
            if total_size > 0:
                percent = min(100, (downloaded / total_size) * 100)
                print(f"\r      Progress: {percent:.1f}%", end="", flush=True)
        
        urllib.request.urlretrieve(url, cloudflared_path, show_progress)
        print("\n[OK] Download completed")
        return True
        
    except Exception as e:
        print(f"\n[ERROR] Download failed: {e}")
        print("\nManual download instructions:")
        print("1. Go to: https://github.com/cloudflare/cloudflared/releases")
        print("2. Download: cloudflared-windows-amd64.exe")
        print("3. Rename to: cloudflared.exe")
        print("4. Place in this folder")
        return False

def start_tunnel():
    """بدء النفق"""
    print("\n[3/4] Starting global tunnel...")
    print("=" * 40)
    print()
    print("🔗 COPY THE HTTPS URL BELOW:")
    print("   This URL works from anywhere in the world")
    print()
    print("📱 For mobile: Add /mobile to the URL")
    print("   Example: https://abc123.trycloudflare.com/mobile")
    print()
    print("=" * 40)
    print()
    
    try:
        # تشغيل cloudflared
        process = subprocess.run([
            "cloudflared.exe", "tunnel", "--url", "http://localhost:5000"
        ], check=False)
        
        print("\n[4/4] Tunnel ended")
        return True
        
    except FileNotFoundError:
        print("[ERROR] cloudflared.exe not found")
        return False
    except Exception as e:
        print(f"[ERROR] Tunnel failed: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من الخادم المحلي
    if not check_local_server():
        print("[INFO] Local server not running")
        if not start_local_server():
            print("\n[SOLUTION] Please run manually:")
            print("python web_server.py")
            input("\nPress Enter to exit...")
            return
    else:
        print("[OK] Local server is running")
    
    # تحميل cloudflared
    if not download_cloudflared():
        input("\nPress Enter to exit...")
        return
    
    # بدء النفق
    start_tunnel()
    
    print("\n" + "=" * 60)
    print("To restart: Run this script again")
    print("To stop: Close this window or press Ctrl+C")
    print("=" * 60)
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n[INFO] Stopped by user")
    except Exception as e:
        print(f"\n[ERROR] Unexpected error: {e}")
        input("Press Enter to exit...")
