#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التقارير المحسنة مع الميزات الجديدة
Test Enhanced Reports with New Features
"""

import sys
import os
from pathlib import Path

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_enhanced_reports():
    """اختبار التقارير المحسنة"""
    try:
        print("🧪 اختبار التقارير المحسنة مع الميزات الجديدة...")
        print("=" * 70)
        
        # استيراد WebReportServer
        from web_server import WebReportServer
        print("✅ تم استيراد WebReportServer")
        
        # إنشاء خادم التقارير
        web_server = WebReportServer()
        print("✅ تم إنشاء WebReportServer")
        
        # اختبار التصفية #18 التي تحتوي على بيانات
        filter_id = 18
        filter_data = web_server.get_filter_by_id(filter_id)
        print(f"📊 بيانات التصفية {filter_id}: {filter_data is not None}")
        
        if not filter_data:
            print(f"❌ التصفية رقم {filter_id} غير موجودة")
            return False
        
        # فحص التفاصيل المحسنة
        details = filter_data.get('details', {})
        print(f"\n💰 التفاصيل المالية:")
        print(f"   بنكي: {details.get('bank_total', 0):.2f}")
        print(f"   نقدي: {details.get('cash_total', 0):.2f}")
        print(f"   آجل: {details.get('credit_total', 0):.2f}")
        print(f"   عملاء: {details.get('client_total', 0):.2f}")
        print(f"   مرتجعات: {details.get('return_total', 0):.2f}")
        print(f"   موردين: {details.get('suppliers_total', 0):.2f} (لا يؤثر)")
        
        # اختبار مقبوضات العملاء المحسنة
        print(f"\n👥 اختبار مقبوضات العملاء المحسنة:")
        client_details = details.get('client_details', [])
        print(f"   عدد العملاء: {len(client_details)}")
        
        if client_details:
            for i, client in enumerate(client_details[:3], 1):  # أول 3 فقط
                customer_name = client.get('customer_name', 'غير محدد')
                payment_method = client.get('payment_method', 'نقدي')
                amount = client.get('amount', 0)
                ref = client.get('ref', '')
                
                print(f"   {i}. {customer_name}")
                print(f"      طريقة الدفع: {payment_method}")
                print(f"      المبلغ: {amount:.2f} ريال")
                if ref:
                    print(f"      رقم المرجع: {ref}")
                print()
        
        # اختبار الموردين
        print(f"🏭 اختبار جدول الموردين:")
        suppliers_details = details.get('suppliers_details', [])
        suppliers_total = details.get('suppliers_total', 0)
        print(f"   عدد الموردين: {len(suppliers_details)}")
        print(f"   إجمالي المدفوعات: {suppliers_total:.2f} ريال")
        
        if suppliers_details:
            for i, supplier in enumerate(suppliers_details[:3], 1):  # أول 3 فقط
                supplier_name = supplier.get('supplier_name', 'غير محدد')
                payment_method = supplier.get('payment_method', 'نقدي')
                amount = supplier.get('amount', 0)
                notes = supplier.get('notes', '')
                
                print(f"   {i}. {supplier_name}")
                print(f"      طريقة الدفع: {payment_method}")
                print(f"      المبلغ: {amount:.2f} ريال")
                if notes:
                    print(f"      ملاحظات: {notes}")
                print()
        else:
            print("   لا توجد مدفوعات للموردين")
        
        # اختبار الفارق
        if 'variance' in details:
            variance = details['variance']
            print(f"\n⚖️ تحليل الفارق:")
            print(f"   مبيعات النظام: {variance.get('system_sales', 0):.2f}")
            print(f"   المجموع الفعلي: {variance.get('actual_total', 0):.2f}")
            print(f"   الفارق: {variance.get('difference', 0):.2f}")
            print(f"   النسبة: {variance.get('percentage', 0):.2f}%")
            print(f"   الحالة: {variance.get('status', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير المحسنة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_html_report():
    """اختبار تقرير HTML"""
    try:
        print(f"\n📄 اختبار تقرير HTML...")
        
        from reports.html_print import generate_filter_report
        
        # بيانات تجريبية للاختبار
        filter_data = {
            'sequence_number': 18,
            'cashier_name': 'كاشير تجريبي',
            'cashier_number': '001',
            'admin_name': 'مدير تجريبي',
            'date': '2025-07-09',
            'credit_transactions': [
                {
                    'client': 'ثمر ثامر',
                    'invoice': '1',
                    'amount': 51.0,
                    'date': '2025-07-09'
                }
            ],
            'client_transactions': [
                {
                    'client': 'أحمد محمد',
                    'type': 'سداد فاتورة',
                    'payment_method': 'نقدي',
                    'amount': 1500.0,
                    'ref': ''
                },
                {
                    'client': 'سارة أحمد',
                    'type': 'دفعة مقدمة',
                    'payment_method': 'شبكة',
                    'amount': 2000.0,
                    'ref': 'REF123456'
                }
            ],
            'suppliers_transactions': [
                {
                    'supplier_name': 'شركة الأغذية',
                    'amount': 5000.0,
                    'payment_method': 'تحويل بنكي',
                    'notes': 'فاتورة رقم 123'
                },
                {
                    'supplier_name': 'مورد الخضار',
                    'amount': 800.0,
                    'payment_method': 'نقدي',
                    'notes': 'دفعة يومية'
                }
            ]
        }
        
        totals = {
            'bank': 3000.0,
            'cash': 1000.0,
            'credit': 51.0,
            'client': 3500.0,
            'return': 0.0
        }
        
        system_sales = 7500.0
        
        # إنشاء التقرير
        success = generate_filter_report(filter_data, totals, system_sales)
        
        if success:
            print("✅ تم إنشاء تقرير HTML بنجاح!")
            print("📄 يحتوي على:")
            print("   ✅ مقبوضات العملاء مع طريقة الدفع")
            print("   ✅ رقم المرجع للمعاملات البنكية")
            print("   ✅ جدول الموردين منفصل")
            print("   ✅ تنبيه أن الموردين لا يؤثرون على الحسابات")
            return True
        else:
            print("❌ فشل في إنشاء تقرير HTML")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تقرير HTML: {e}")
        return False

def test_comprehensive_report():
    """اختبار التقرير الشامل"""
    try:
        print(f"\n📊 اختبار التقرير الشامل...")
        
        # محاولة الوصول للتقرير الشامل
        import requests
        
        try:
            response = requests.get('http://localhost:5000/filter/18/comprehensive', timeout=5)
            if response.status_code == 200:
                content = response.text
                
                # فحص المحتوى للميزات الجديدة
                checks = [
                    ('طريقة الدفع', 'طريقة الدفع' in content),
                    ('رقم المرجع', 'رقم المرجع' in content),
                    ('الموردين', 'الموردين' in content or 'suppliers' in content),
                    ('لا يؤثر على الحسابات', 'لا يؤثر على الحسابات' in content),
                    ('نقدي', 'نقدي' in content),
                    ('شبكة', 'شبكة' in content)
                ]
                
                print("✅ تم الوصول للتقرير الشامل بنجاح!")
                print("🔍 فحص المحتوى:")
                
                all_passed = True
                for check_name, check_result in checks:
                    status = "✅" if check_result else "❌"
                    print(f"   {status} {check_name}: {'موجود' if check_result else 'مفقود'}")
                    if not check_result:
                        all_passed = False
                
                return all_passed
            else:
                print(f"❌ خطأ في الوصول للتقرير: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException:
            print("⚠️ خادم التقارير غير متاح - تأكد من تشغيله")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التقرير الشامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التقارير المحسنة مع الميزات الجديدة")
    print("=" * 70)
    
    # التحقق من المجلد الحالي
    if not Path('web_server.py').exists():
        print("❌ يجب تشغيل هذا الملف من مجلد cashier_filter")
        return
    
    # اختبار التقارير المحسنة
    test1 = test_enhanced_reports()
    
    # اختبار تقرير HTML
    test2 = test_html_report()
    
    # اختبار التقرير الشامل
    test3 = test_comprehensive_report()
    
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبارات:")
    print(f"   {'✅' if test1 else '❌'} التقارير المحسنة: {'نجح' if test1 else 'فشل'}")
    print(f"   {'✅' if test2 else '❌'} تقرير HTML: {'نجح' if test2 else 'فشل'}")
    print(f"   {'✅' if test3 else '❌'} التقرير الشامل: {'نجح' if test3 else 'فشل'}")
    
    if test1 and test2 and test3:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التقارير محسنة مع الميزات الجديدة:")
        print("   💳 طريقة الدفع في مقبوضات العملاء")
        print("   📄 رقم المرجع للمعاملات البنكية")
        print("   🏭 جدول الموردين منفصل")
        print("   ⚠️ تنبيه أن الموردين لا يؤثرون على الحسابات")
        print("\n🔗 جرب التقرير الشامل: http://localhost:5000/filter/18/comprehensive")
    else:
        print("\n⚠️ بعض الاختبارات فشلت!")
        print("💡 تأكد من:")
        print("   1. تشغيل خادم التقارير")
        print("   2. وجود بيانات في التصفية #18")
        print("   3. تطبيق جميع التحسينات")

if __name__ == "__main__":
    main()
