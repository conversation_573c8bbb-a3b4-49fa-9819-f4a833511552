#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت نظام تصفية الكاشير المتكامل 2025
Cashier Filter System 2025 - Easy Installer

تطوير: محمد الكامل
Developed by: <PERSON>
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

def print_banner():
    """طباعة شعار المثبت"""
    banner = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🏪 نظام تصفية الكاشير المتكامل 2025 - مثبت سهل                    ║
║         Cashier Filter System 2025 - Easy Installer                 ║
║                                                                      ║
║    الإصدار 3.0.0 - مع ذكاء اصطناعي متطور                          ║
║    Version 3.0.0 - With Advanced AI                                 ║
║                                                                      ║
║    تطوير: محمد الكامل | Developed by: <PERSON>              ║
║    البريد: <EMAIL>                                     ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_system():
    """فحص النظام والمت<PERSON>لب<PERSON>"""
    print("🔍 فحص النظام...")
    
    # فحص نظام التشغيل
    os_name = platform.system()
    print(f"   نظام التشغيل: {os_name} {platform.release()}")
    
    # فحص Python
    python_version = sys.version_info
    print(f"   إصدار Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("   يرجى تحديث Python من: https://python.org")
        return False
    
    # فحص pip
    try:
        import pip
        print(f"   pip متاح: ✅")
    except ImportError:
        print("❌ خطأ: pip غير متاح")
        return False
    
    print("✅ النظام متوافق")
    return True

def install_dependencies():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    # قائمة المتطلبات الأساسية
    requirements = [
        "customtkinter>=5.2.0",
        "fpdf2>=2.7.0", 
        "pandas>=1.5.0",
        "openpyxl>=3.1.0",
        "reportlab>=4.0.0",
        "numpy>=1.24.0"
    ]
    
    # المتطلبات الاختيارية
    optional_requirements = [
        "matplotlib>=3.5.0",
        "weasyprint>=60.0"
    ]
    
    success_count = 0
    total_count = len(requirements)
    
    for requirement in requirements:
        try:
            print(f"   تثبيت {requirement}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement, "--quiet"
            ])
            success_count += 1
            print(f"   ✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"   ❌ فشل تثبيت {requirement}")
    
    # تثبيت المتطلبات الاختيارية
    print("\n📦 تثبيت المتطلبات الاختيارية...")
    for requirement in optional_requirements:
        try:
            print(f"   تثبيت {requirement}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement, "--quiet"
            ])
            print(f"   ✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"   ⚠️ تخطي {requirement} (اختياري)")
    
    if success_count == total_count:
        print(f"\n✅ تم تثبيت جميع المتطلبات الأساسية ({success_count}/{total_count})")
        return True
    else:
        print(f"\n⚠️ تم تثبيت {success_count} من {total_count} متطلبات")
        return success_count > 0

def create_directory_structure():
    """إنشاء هيكل المجلدات"""
    print("\n📁 إنشاء هيكل المجلدات...")
    
    directories = [
        "db",
        "ui", 
        "utils",
        "reports",
        "reports/generated",
        "assets",
        "backups",
        "logs",
        "exports"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"   ✅ {directory}")
        except Exception as e:
            print(f"   ❌ فشل إنشاء {directory}: {e}")
    
    print("✅ تم إنشاء هيكل المجلدات")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        # تشغيل إعداد قاعدة البيانات
        if os.path.exists("db/init_db.py"):
            from db.init_db import init_db
            init_db()
            print("   ✅ تم إنشاء قاعدة البيانات")
        else:
            print("   ⚠️ ملف إعداد قاعدة البيانات غير موجود")
            return False
        
        # إضافة بيانات تجريبية
        add_sample_data()
        return True
        
    except Exception as e:
        print(f"   ❌ فشل إعداد قاعدة البيانات: {e}")
        return False

def add_sample_data():
    """إضافة بيانات تجريبية"""
    print("   📝 إضافة بيانات تجريبية...")
    
    try:
        import sqlite3
        import hashlib
        
        db_path = "db/cashier_filter.db"
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # إضافة كاشيرين تجريبيين
        sample_cashiers = [
            ("أحمد محمد", "001"),
            ("فاطمة علي", "002"),
            ("محمد أحمد", "003")
        ]
        
        for name, number in sample_cashiers:
            c.execute("""
                INSERT OR IGNORE INTO cashiers (name, number) 
                VALUES (?, ?)
            """, (name, number))
        
        # إضافة مسؤولين تجريبيين
        password_hash = hashlib.sha256("123456".encode()).hexdigest()
        sample_admins = [
            ("المدير العام", "admin"),
            ("المحاسب الرئيسي", "accountant"),
            ("مشرف النظام", "supervisor")
        ]
        
        for name, username in sample_admins:
            c.execute("""
                INSERT OR IGNORE INTO admins (name, username, password) 
                VALUES (?, ?, ?)
            """, (name, username, password_hash))
        
        conn.commit()
        conn.close()
        
        print("   ✅ تم إضافة البيانات التجريبية")
        
    except Exception as e:
        print(f"   ⚠️ تحذير: فشل إضافة البيانات التجريبية: {e}")

def create_shortcuts():
    """إنشاء اختصارات التشغيل"""
    print("\n🔗 إنشاء اختصارات التشغيل...")
    
    # اختصار Windows
    if platform.system() == "Windows":
        batch_content = """@echo off
chcp 65001 > nul
title نظام تصفية الكاشير المتكامل 2025
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║    تطوير: محمد الكامل - الإصدار 3.0.0                              ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 جاري تشغيل النظام...
echo.
cd /d "%~dp0"
python main.py
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo يرجى التأكد من تثبيت Python بشكل صحيح
    echo.
)
pause
"""
        
        with open("تشغيل_النظام.bat", "w", encoding="utf-8") as f:
            f.write(batch_content)
        print("   ✅ تم إنشاء: تشغيل_النظام.bat")
    
    # اختصار Linux/Mac
    else:
        shell_content = """#!/bin/bash
clear
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║"
echo "║    تطوير: محمد الكامل - الإصدار 3.0.0                              ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo ""
echo "🚀 جاري تشغيل النظام..."
echo ""

cd "$(dirname "$0")"
python3 main.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ حدث خطأ في تشغيل النظام"
    echo "يرجى التأكد من تثبيت Python بشكل صحيح"
    echo ""
fi

read -p "اضغط Enter للمتابعة..."
"""
        
        with open("تشغيل_النظام.sh", "w", encoding="utf-8") as f:
            f.write(shell_content)
        os.chmod("تشغيل_النظام.sh", 0o755)
        print("   ✅ تم إنشاء: تشغيل_النظام.sh")

def test_installation():
    """اختبار التثبيت"""
    print("\n🧪 اختبار التثبيت...")
    
    try:
        # اختبار استيراد الوحدات الأساسية
        import customtkinter
        print("   ✅ customtkinter")
        
        # اختبار قاعدة البيانات
        if os.path.exists("db/cashier_filter.db"):
            print("   ✅ قاعدة البيانات")
        else:
            print("   ❌ قاعدة البيانات غير موجودة")
            return False
        
        # اختبار الملف الرئيسي
        if os.path.exists("main.py"):
            print("   ✅ الملف الرئيسي")
        else:
            print("   ❌ الملف الرئيسي غير موجود")
            return False
        
        print("✅ اجتاز جميع الاختبارات")
        return True
        
    except ImportError as e:
        print(f"   ❌ فشل الاختبار: {e}")
        return False

def print_completion():
    """طباعة رسالة الإكمال"""
    completion_message = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🎉 تم تثبيت نظام تصفية الكاشير المتكامل 2025 بنجاح!              ║
║                                                                      ║
║    📋 خطوات التشغيل:                                                 ║
║    1. تشغيل الملف: python main.py                                   ║"""
    
    if platform.system() == "Windows":
        completion_message += """
║    2. أو النقر المزدوج على: تشغيل_النظام.bat                         ║"""
    else:
        completion_message += """
║    2. أو تشغيل الأمر: ./تشغيل_النظام.sh                             ║"""
    
    completion_message += """
║                                                                      ║
║    👤 بيانات الدخول الافتراضية:                                     ║
║    اسم المستخدم: admin                                              ║
║    كلمة المرور: 123456                                               ║
║                                                                      ║
║    📖 للمزيد من المعلومات، راجع ملف README.md                       ║
║                                                                      ║
║    💬 الدعم الفني: <EMAIL>                            ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    
    print(completion_message)

def main():
    """الدالة الرئيسية للمثبت"""
    print_banner()
    
    # فحص النظام
    if not check_system():
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
    
    # تثبيت المتطلبات
    if not install_dependencies():
        print("\n❌ فشل التثبيت: لا يمكن تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء هيكل المجلدات
    create_directory_structure()
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("\n⚠️ تحذير: قد تحتاج لإعداد قاعدة البيانات يدوياً")
    
    # إنشاء اختصارات التشغيل
    create_shortcuts()
    
    # اختبار التثبيت
    if test_installation():
        print_completion()
    else:
        print("\n⚠️ تحذير: قد تكون هناك مشاكل في التثبيت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
