{% extends "base.html" %}

{% block title %}تقرير الموردين - نظام تصفية الكاشير{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="card-title mb-2">
                            <i class="fas fa-industry"></i>
                            تقرير الموردين الشامل
                        </h1>
                        <p class="card-text mb-0">
                            عرض شامل لجميع المدفوعات للموردين من كافة التصفيات مع إمكانية الطباعة والتصدير
                        </p>
                        <small class="text-warning-light">
                            <i class="fas fa-exclamation-triangle"></i>
                            ملاحظة: هذه المدفوعات للمتابعة فقط ولا تؤثر على حسابات التصفية
                        </small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-light" onclick="printReport()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button type="button" class="btn btn-light" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="fas fa-industry fa-2x"></i>
                </div>
                <h3 class="card-title text-warning">{{ suppliers_data.total_suppliers }}</h3>
                <p class="card-text">إجمالي المدفوعات للموردين</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
                <h3 class="card-title text-success">{{ "{:,.2f}".format(suppliers_data.total_amount) }}</h3>
                <p class="card-text">إجمالي المبلغ (ريال)</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
                <h5 class="card-title text-info">{{ suppliers_data.last_update }}</h5>
                <p class="card-text">آخر تحديث</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلترة البيانات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="supplier-search" class="form-label">البحث بالمورد:</label>
                        <input type="text" class="form-control" id="supplier-search" placeholder="اسم المورد..." onkeyup="filterTable()">
                    </div>
                    <div class="col-md-3">
                        <label for="cashier-search" class="form-label">البحث بالكاشير:</label>
                        <input type="text" class="form-control" id="cashier-search" placeholder="اسم الكاشير..." onkeyup="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label for="date-from" class="form-label">من تاريخ:</label>
                        <input type="date" class="form-control" id="date-from" onchange="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label for="date-to" class="form-label">إلى تاريخ:</label>
                        <input type="date" class="form-control" id="date-to" onchange="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suppliers Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> جدول الموردين</h5>
                <span class="badge bg-warning" id="results-count">
                    {{ suppliers_data.total_suppliers }} مدفوعة
                </span>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> هذه المدفوعات للمتابعة فقط ولا تدخل في حسابات التصفية الرئيسية.
                </div>
                <div class="table-responsive">
                    <table class="table table-hover table-striped" id="suppliers-table">
                        <thead class="table-warning">
                            <tr>
                                <th>#</th>
                                <th>اسم المورد</th>
                                <th>المبلغ المسلم (ريال)</th>
                                <th>طريقة الدفع</th>
                                <th>الكاشير</th>
                                <th>المسؤول</th>
                                <th>تاريخ التصفية</th>
                                <th>الملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-tbody">
                            {% for supplier in suppliers_data.suppliers %}
                            <tr data-supplier-name="{{ supplier.supplier_name or '' }}" 
                                data-cashier="{{ supplier.cashier_name or '' }}" 
                                data-filter-date="{{ supplier.filter_date or '' }}">
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong class="text-warning">{{ supplier.supplier_name or 'مورد غير محدد' }}</strong>
                                </td>
                                <td class="text-end">
                                    <strong class="text-success">{{ "{:,.2f}".format(supplier.amount or 0) }}</strong>
                                </td>
                                <td>
                                    {% if supplier.payment_method == 'نقدي' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-money-bill"></i> {{ supplier.payment_method }}
                                        </span>
                                    {% elif supplier.payment_method == 'شيك' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-file-invoice"></i> {{ supplier.payment_method }}
                                        </span>
                                    {% elif 'تحويل' in supplier.payment_method or 'بنكي' in supplier.payment_method %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-university"></i> {{ supplier.payment_method }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ supplier.payment_method or 'غير محدد' }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ supplier.cashier_name or 'غير محدد' }}</td>
                                <td>{{ supplier.admin_name or 'غير محدد' }}</td>
                                <td>{{ supplier.filter_date or 'غير محدد' }}</td>
                                <td>
                                    {% if supplier.notes %}
                                        <span class="text-muted">{{ supplier.notes }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/filter/{{ supplier.filter_id }}" class="btn btn-outline-primary btn-sm" title="عرض التصفية">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/filter/{{ supplier.filter_id }}/comprehensive" class="btn btn-outline-info btn-sm" title="التقرير الشامل">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted py-4">
                                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                                    <br>لا توجد بيانات موردين متاحة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        {% if suppliers_data.suppliers %}
                        <tfoot class="table-warning">
                            <tr>
                                <th colspan="2" class="text-end">الإجمالي:</th>
                                <th class="text-end">{{ "{:,.2f}".format(suppliers_data.total_amount) }} ريال</th>
                                <th colspan="6">
                                    <small class="text-muted">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        للمتابعة فقط - لا يؤثر على الحسابات
                                    </small>
                                </th>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// فلترة الجدول
function filterTable() {
    const supplierSearch = document.getElementById('supplier-search').value.toLowerCase();
    const cashierSearch = document.getElementById('cashier-search').value.toLowerCase();
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    
    const rows = document.querySelectorAll('#suppliers-tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const supplierName = row.getAttribute('data-supplier-name').toLowerCase();
        const cashierName = row.getAttribute('data-cashier').toLowerCase();
        const filterDate = row.getAttribute('data-filter-date');
        
        let showRow = true;
        
        // فلترة بالمورد
        if (supplierSearch && !supplierName.includes(supplierSearch)) {
            showRow = false;
        }
        
        // فلترة بالكاشير
        if (cashierSearch && !cashierName.includes(cashierSearch)) {
            showRow = false;
        }
        
        // فلترة بالتاريخ
        if (dateFrom && filterDate < dateFrom) {
            showRow = false;
        }
        
        if (dateTo && filterDate > dateTo) {
            showRow = false;
        }
        
        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    document.getElementById('results-count').textContent = `${visibleCount} مدفوعة`;
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('supplier-search').value = '';
    document.getElementById('cashier-search').value = '';
    document.getElementById('date-from').value = '';
    document.getElementById('date-to').value = '';
    
    const rows = document.querySelectorAll('#suppliers-tbody tr');
    rows.forEach(row => {
        row.style.display = '';
    });
    
    document.getElementById('results-count').textContent = `{{ suppliers_data.total_suppliers }} مدفوعة`;
}

// طباعة التقرير
function printReport() {
    window.print();
}

// تصدير إلى Excel
function exportToExcel() {
    // جمع البيانات المرئية فقط
    const visibleRows = Array.from(document.querySelectorAll('#suppliers-tbody tr'))
        .filter(row => row.style.display !== 'none');
    
    if (visibleRows.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // إنشاء CSV
    let csvContent = "اسم المورد,المبلغ المسلم,طريقة الدفع,الكاشير,المسؤول,تاريخ التصفية,الملاحظات\n";
    
    visibleRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) { // تجاهل الصفوف الفارغة
            const rowData = [
                cells[1].textContent.trim(), // اسم المورد
                cells[2].textContent.trim(), // المبلغ المسلم
                cells[3].textContent.trim(), // طريقة الدفع
                cells[4].textContent.trim(), // الكاشير
                cells[5].textContent.trim(), // المسؤول
                cells[6].textContent.trim(), // تاريخ التصفية
                cells[7].textContent.trim()  // الملاحظات
            ];
            csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
        }
    });
    
    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `تقرير_الموردين_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث تلقائي كل 5 دقائق
setInterval(() => {
    location.reload();
}, 300000);
</script>

<style>
@media print {
    .btn, .card-header .btn-group, .filters-section, .alert {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th, .table td {
        padding: 4px !important;
    }
}

.table th {
    white-space: nowrap;
}

.badge {
    font-size: 0.8em;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.text-warning-light {
    color: rgba(255, 255, 255, 0.8) !important;
}
</style>
{% endblock %}
