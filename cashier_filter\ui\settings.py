# نافذة إعدادات التطبيق
import customtkinter as ctk
from tkinter import messagebox, filedialog, colorchooser
import json
import os
import subprocess
import threading
import time
import requests
import pyperclip
from pathlib import Path

SETTINGS_FILE = "settings.json"

class SettingsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("إعدادات التطبيق")
        self.geometry("800x700")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()
        
        # تحميل الإعدادات الحالية
        self.settings = self.load_settings()
        
        self.create_widgets()
        self.load_current_settings()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="⚙️ إعدادات التطبيق",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # إطار الإعدادات الرئيسي
        main_frame = ctk.CTkScrollableFrame(self, fg_color="#f2f3f7")
        main_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        # قسم الواجهة
        self.create_ui_settings(main_frame)
        
        # قسم قاعدة البيانات
        self.create_database_settings(main_frame)
        
        # قسم النسخ الاحتياطي
        self.create_backup_settings(main_frame)
        
        # قسم الطباعة والتصدير
        self.create_export_settings(main_frame)
        
        # قسم الأمان
        self.create_security_settings(main_frame)

        # قسم الوصول العالمي
        self.create_global_access_settings(main_frame)

        # أزرار الحفظ والإلغاء
        self.create_action_buttons()

        # معلومات التطوير
        self.create_developer_info(main_frame)

    def create_ui_settings(self, parent):
        """إنشاء إعدادات الواجهة"""
        ui_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        ui_frame.pack(pady=10, fill="x")
        
        title = ctk.CTkLabel(
            ui_frame,
            text="🎨 إعدادات الواجهة",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)
        
        content = ctk.CTkFrame(ui_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")
        
        # المظهر
        theme_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        theme_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(theme_row, text="المظهر:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.theme_combo = ctk.CTkComboBox(
            theme_row,
            values=["light", "dark", "system"],
            width=150
        )
        self.theme_combo.pack(side="left", padx=10)
        
        # حجم الخط مع حدود مستقرة
        font_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        font_row.pack(fill="x", pady=10)

        ctk.CTkLabel(font_row, text="حجم الخط:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.font_size_slider = ctk.CTkSlider(
            font_row,
            from_=12,  # حد أدنى مستقر
            to=18,     # حد أقصى معقول
            number_of_steps=6,  # خطوات أقل للاستقرار
            width=200
        )
        self.font_size_slider.pack(side="left", padx=10)
        
        self.font_size_label = ctk.CTkLabel(font_row, text="12", font=("Arial", 12))
        self.font_size_label.pack(side="left", padx=5)
        
        self.font_size_slider.configure(command=self.update_font_size_label)
        
        # اللغة
        lang_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        lang_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(lang_row, text="اللغة:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.language_combo = ctk.CTkComboBox(
            lang_row,
            values=["العربية", "English"],
            width=150
        )
        self.language_combo.pack(side="left", padx=10)

    def create_database_settings(self, parent):
        """إنشاء إعدادات قاعدة البيانات"""
        db_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        db_frame.pack(pady=10, fill="x")
        
        title = ctk.CTkLabel(
            db_frame,
            text="🗄️ إعدادات قاعدة البيانات",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)
        
        content = ctk.CTkFrame(db_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")
        
        # مسار قاعدة البيانات
        db_path_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        db_path_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(db_path_row, text="مسار قاعدة البيانات:", font=("Arial", 14, "bold")).pack(anchor="w", padx=10)
        
        path_frame = ctk.CTkFrame(db_path_row, fg_color="#f2f3f7", corner_radius=0)
        path_frame.pack(fill="x", padx=10, pady=5)
        
        self.db_path_entry = ctk.CTkEntry(path_frame, width=400)
        self.db_path_entry.pack(side="left", padx=5)
        
        browse_btn = ctk.CTkButton(
            path_frame,
            text="تصفح",
            command=self.browse_database_path,
            width=80,
            height=30
        )
        browse_btn.pack(side="left", padx=5)
        
        # مهلة الاتصال
        timeout_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        timeout_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(timeout_row, text="مهلة الاتصال (ثانية):", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.db_timeout_entry = ctk.CTkEntry(timeout_row, width=100)
        self.db_timeout_entry.pack(side="left", padx=10)

    def create_backup_settings(self, parent):
        """إنشاء إعدادات النسخ الاحتياطي"""
        backup_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        backup_frame.pack(pady=10, fill="x")
        
        title = ctk.CTkLabel(
            backup_frame,
            text="💾 إعدادات النسخ الاحتياطي",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)
        
        content = ctk.CTkFrame(backup_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")
        
        # تفعيل النسخ التلقائي
        auto_backup_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        auto_backup_row.pack(fill="x", pady=10)
        
        self.auto_backup_var = ctk.BooleanVar()
        auto_backup_check = ctk.CTkCheckBox(
            auto_backup_row,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_var,
            font=("Arial", 14, "bold")
        )
        auto_backup_check.pack(side="left", padx=10)
        
        # فترة النسخ الاحتياطي
        interval_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        interval_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(interval_row, text="فترة النسخ الاحتياطي (ساعة):", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.backup_interval_entry = ctk.CTkEntry(interval_row, width=100)
        self.backup_interval_entry.pack(side="left", padx=10)
        
        # عدد النسخ المحفوظة
        max_backups_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        max_backups_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(max_backups_row, text="عدد النسخ المحفوظة:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.max_backups_entry = ctk.CTkEntry(max_backups_row, width=100)
        self.max_backups_entry.pack(side="left", padx=10)

    def create_export_settings(self, parent):
        """إنشاء إعدادات الطباعة والتصدير"""
        export_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        export_frame.pack(pady=10, fill="x")
        
        title = ctk.CTkLabel(
            export_frame,
            text="🖨️ إعدادات الطباعة والتصدير",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)
        
        content = ctk.CTkFrame(export_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")
        
        # حجم الورق
        paper_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        paper_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(paper_row, text="حجم الورق:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.paper_size_combo = ctk.CTkComboBox(
            paper_row,
            values=["A4", "A3", "Letter"],
            width=150
        )
        self.paper_size_combo.pack(side="left", padx=10)
        
        # جودة PDF
        pdf_quality_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        pdf_quality_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(pdf_quality_row, text="جودة PDF:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.pdf_quality_combo = ctk.CTkComboBox(
            pdf_quality_row,
            values=["عالية", "متوسطة", "منخفضة"],
            width=150
        )
        self.pdf_quality_combo.pack(side="left", padx=10)
        
        # طباعة تلقائية
        auto_print_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        auto_print_row.pack(fill="x", pady=10)
        
        self.auto_print_var = ctk.BooleanVar()
        auto_print_check = ctk.CTkCheckBox(
            auto_print_row,
            text="فتح نافذة الطباعة تلقائياً",
            variable=self.auto_print_var,
            font=("Arial", 14, "bold")
        )
        auto_print_check.pack(side="left", padx=10)

    def create_security_settings(self, parent):
        """إنشاء إعدادات الأمان"""
        security_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        security_frame.pack(pady=10, fill="x")
        
        title = ctk.CTkLabel(
            security_frame,
            text="🔒 إعدادات الأمان",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)
        
        content = ctk.CTkFrame(security_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")
        
        # طول كلمة المرور الأدنى
        password_length_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        password_length_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(password_length_row, text="طول كلمة المرور الأدنى:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.min_password_length_entry = ctk.CTkEntry(password_length_row, width=100)
        self.min_password_length_entry.pack(side="left", padx=10)
        
        # عدد محاولات تسجيل الدخول
        login_attempts_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        login_attempts_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(login_attempts_row, text="عدد محاولات تسجيل الدخول:", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.max_login_attempts_entry = ctk.CTkEntry(login_attempts_row, width=100)
        self.max_login_attempts_entry.pack(side="left", padx=10)
        
        # مهلة الجلسة
        session_timeout_row = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        session_timeout_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(session_timeout_row, text="مهلة الجلسة (دقيقة):", font=("Arial", 14, "bold")).pack(side="left", padx=10)
        self.session_timeout_entry = ctk.CTkEntry(session_timeout_row, width=100)
        self.session_timeout_entry.pack(side="left", padx=10)

    def create_action_buttons(self):
        """إنشاء أزرار الحفظ والإلغاء"""
        buttons_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        buttons_frame.pack(pady=20, padx=20, fill="x")
        
        btn_container = ctk.CTkFrame(buttons_frame, fg_color="#e0e5ec", corner_radius=0)
        btn_container.pack(pady=15)
        
        save_btn = ctk.CTkButton(
            btn_container,
            text="💾 حفظ الإعدادات",
            command=self.save_settings,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        save_btn.pack(side="left", padx=10)
        
        reset_btn = ctk.CTkButton(
            btn_container,
            text="🔄 إعادة تعيين",
            command=self.reset_settings,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        reset_btn.pack(side="left", padx=10)
        
        cancel_btn = ctk.CTkButton(
            btn_container,
            text="❌ إلغاء",
            command=self.destroy,
            fg_color="#f44336",
            hover_color="#d32f2f",
            width=100,
            height=40,
            font=("Arial", 14, "bold")
        )
        cancel_btn.pack(side="left", padx=10)

    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        default_settings = {
            "ui": {
                "theme": "light",
                "font_size": 12,
                "language": "العربية"
            },
            "database": {
                "path": "db/cashier_filter.db",
                "timeout": 30
            },
            "backup": {
                "auto_backup": True,
                "interval_hours": 24,
                "max_backups": 30
            },
            "export": {
                "paper_size": "A4",
                "pdf_quality": "عالية",
                "auto_print": True
            },
            "security": {
                "min_password_length": 6,
                "max_login_attempts": 3,
                "session_timeout": 60
            }
        }
        
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # دمج الإعدادات المحملة مع الافتراضية
                    for category in default_settings:
                        if category in loaded_settings:
                            default_settings[category].update(loaded_settings[category])
                    return default_settings
            else:
                return default_settings
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإعدادات: {e}")
            return default_settings

    def load_current_settings(self):
        """تحميل الإعدادات الحالية في الواجهة"""
        try:
            # إعدادات الواجهة
            self.theme_combo.set(self.settings["ui"]["theme"])
            self.font_size_slider.set(self.settings["ui"]["font_size"])
            self.update_font_size_label(self.settings["ui"]["font_size"])
            self.language_combo.set(self.settings["ui"]["language"])
            
            # إعدادات قاعدة البيانات
            self.db_path_entry.insert(0, self.settings["database"]["path"])
            self.db_timeout_entry.insert(0, str(self.settings["database"]["timeout"]))
            
            # إعدادات النسخ الاحتياطي
            self.auto_backup_var.set(self.settings["backup"]["auto_backup"])
            self.backup_interval_entry.insert(0, str(self.settings["backup"]["interval_hours"]))
            self.max_backups_entry.insert(0, str(self.settings["backup"]["max_backups"]))
            
            # إعدادات التصدير
            self.paper_size_combo.set(self.settings["export"]["paper_size"])
            self.pdf_quality_combo.set(self.settings["export"]["pdf_quality"])
            self.auto_print_var.set(self.settings["export"]["auto_print"])
            
            # إعدادات الأمان
            self.min_password_length_entry.insert(0, str(self.settings["security"]["min_password_length"]))
            self.max_login_attempts_entry.insert(0, str(self.settings["security"]["max_login_attempts"]))
            self.session_timeout_entry.insert(0, str(self.settings["security"]["session_timeout"]))
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإعدادات الحالية: {e}")

    def update_font_size_label(self, value):
        """تحديث تسمية حجم الخط"""
        self.font_size_label.configure(text=str(int(float(value))))

    def browse_database_path(self):
        """تصفح مسار قاعدة البيانات"""
        filename = filedialog.askopenfilename(
            title="اختيار ملف قاعدة البيانات",
            filetypes=[("SQLite files", "*.db"), ("All files", "*.*")]
        )
        if filename:
            self.db_path_entry.delete(0, "end")
            self.db_path_entry.insert(0, filename)

    def save_settings(self):
        """حفظ الإعدادات مع ضمان استقرار حجم الخط"""
        try:
            # ضمان أن حجم الخط ضمن الحدود المسموحة
            font_size = int(self.font_size_slider.get())
            stable_font_size = max(min(font_size, 18), 12)  # بين 12 و 18

            # جمع الإعدادات من الواجهة
            new_settings = {
                "ui": {
                    "theme": self.theme_combo.get(),
                    "font_size": stable_font_size,
                    "language": self.language_combo.get()
                },
                "database": {
                    "path": self.db_path_entry.get(),
                    "timeout": int(self.db_timeout_entry.get())
                },
                "backup": {
                    "auto_backup": self.auto_backup_var.get(),
                    "interval_hours": int(self.backup_interval_entry.get()),
                    "max_backups": int(self.max_backups_entry.get())
                },
                "export": {
                    "paper_size": self.paper_size_combo.get(),
                    "pdf_quality": self.pdf_quality_combo.get(),
                    "auto_print": self.auto_print_var.get()
                },
                "security": {
                    "min_password_length": int(self.min_password_length_entry.get()),
                    "max_login_attempts": int(self.max_login_attempts_entry.get()),
                    "session_timeout": int(self.session_timeout_entry.get())
                }
            }
            
            # حفظ الإعدادات في الملف
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=2)

            # تطبيق إعدادات الواجهة فوراً
            self.apply_ui_settings(new_settings["ui"])

            messagebox.showinfo("نجح", "تم حفظ وتطبيق الإعدادات بنجاح!")
            self.destroy()
            
        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى التأكد من صحة القيم المدخلة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {e}")

    def apply_ui_settings(self, ui_settings):
        """تطبيق إعدادات الواجهة فوراً"""
        try:
            import customtkinter as ctk

            # تطبيق المظهر
            theme = ui_settings.get("theme", "light")
            ctk.set_appearance_mode(theme)

            # تطبيق حجم الخط على النافذة الحالية
            font_size = ui_settings.get("font_size", 12)
            self.update_font_sizes(font_size)

            # إشعار المستخدم
            print(f"تم تطبيق المظهر: {theme}, حجم الخط: {font_size}")

        except Exception as e:
            print(f"خطأ في تطبيق إعدادات الواجهة: {e}")

    def update_font_sizes(self, font_size):
        """تحديث أحجام الخطوط في النافذة الحالية"""
        try:
            # تحديث خطوط العناصر في النافذة الحالية
            for widget in self.winfo_children():
                self.update_widget_font(widget, font_size)
        except Exception as e:
            print(f"خطأ في تحديث أحجام الخطوط: {e}")

    def update_widget_font(self, widget, font_size):
        """تحديث خط عنصر واحد وأطفاله"""
        try:
            # تحديث خط العنصر الحالي إذا كان يدعم الخط
            if hasattr(widget, 'configure') and hasattr(widget, 'cget'):
                try:
                    current_font = widget.cget('font')
                    if current_font:
                        if isinstance(current_font, tuple):
                            # تحديث حجم الخط في tuple
                            new_font = (current_font[0], font_size) + current_font[2:]
                        else:
                            # إنشاء خط جديد
                            new_font = ("Arial", font_size)
                        widget.configure(font=new_font)
                except:
                    pass

            # تحديث خطوط الأطفال
            for child in widget.winfo_children():
                self.update_widget_font(child, font_size)

        except Exception as e:
            pass  # تجاهل الأخطاء في العناصر الفردية

    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            try:
                # حذف ملف الإعدادات
                if os.path.exists(SETTINGS_FILE):
                    os.remove(SETTINGS_FILE)
                
                # إعادة تحميل الإعدادات الافتراضية
                self.settings = self.load_settings()
                
                # مسح الواجهة وإعادة تحميلها
                self.clear_interface()
                self.load_current_settings()
                
                messagebox.showinfo("تم", "تم إعادة تعيين الإعدادات للقيم الافتراضية")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إعادة تعيين الإعدادات: {e}")

    def clear_interface(self):
        """مسح الواجهة"""
        # مسح حقول النص
        for entry in [self.db_path_entry, self.db_timeout_entry, self.backup_interval_entry, 
                     self.max_backups_entry, self.min_password_length_entry, 
                     self.max_login_attempts_entry, self.session_timeout_entry]:
            entry.delete(0, "end")
        
        # إعادة تعيين القوائم المنسدلة
        self.theme_combo.set("")
        self.language_combo.set("")
        self.paper_size_combo.set("")
        self.pdf_quality_combo.set("")
        
        # إعادة تعيين مربعات الاختيار
        self.auto_backup_var.set(False)
        self.auto_print_var.set(False)

    def create_developer_info(self, parent):
        """إنشاء قسم معلومات التطوير"""
        dev_frame = ctk.CTkFrame(parent, fg_color="#2c3e50", corner_radius=15)
        dev_frame.pack(pady=10, fill="x")

        title = ctk.CTkLabel(
            dev_frame,
            text="👨‍💻 معلومات التطوير",
            font=("Arial", 16, "bold"),
            text_color="#ecf0f1"
        )
        title.pack(pady=15)

        content = ctk.CTkFrame(dev_frame, fg_color="#34495e", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")

        # اسم المطور
        dev_name = ctk.CTkLabel(
            content,
            text="المطور: محمد الكامل",
            font=("Arial", 14, "bold"),
            text_color="#ecf0f1"
        )
        dev_name.pack(pady=10)

        # معلومات النظام
        system_info = ctk.CTkLabel(
            content,
            text="نظام تصفية الكاشير المتكامل 2025 - الإصدار 3.0.0",
            font=("Arial", 12),
            text_color="#bdc3c7"
        )
        system_info.pack(pady=5)

        # ميزات جديدة
        features_info = ctk.CTkLabel(
            content,
            text="✨ جديد: ذكاء اصطناعي | لوحة معلومات تفاعلية | تكامل سحابي",
            font=("Arial", 10),
            text_color="#3498db"
        )
        features_info.pack(pady=5)

        # حقوق النشر
        copyright_info = ctk.CTkLabel(
            content,
            text="© 2025 - جميع الحقوق محفوظة",
            font=("Arial", 11),
            text_color="#95a5a6"
        )
        copyright_info.pack(pady=10)

    def create_global_access_settings(self, parent):
        """إنشاء إعدادات الوصول العالمي"""
        global_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        global_frame.pack(pady=10, fill="x")

        title = ctk.CTkLabel(
            global_frame,
            text="🌍 الوصول العالمي للتقارير",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        title.pack(pady=15)

        content = ctk.CTkFrame(global_frame, fg_color="#f2f3f7", corner_radius=10)
        content.pack(pady=10, padx=20, fill="x")

        # وصف الميزة
        description = ctk.CTkLabel(
            content,
            text="اطلع على التقارير من أي مكان في العالم باستخدام رابط آمن ومشفر",
            font=("Arial", 12),
            text_color="#7f8c8d",
            wraplength=600
        )
        description.pack(pady=10)

        # حالة النفق
        self.tunnel_status_frame = ctk.CTkFrame(content, fg_color="#ecf0f1", corner_radius=10)
        self.tunnel_status_frame.pack(pady=10, fill="x", padx=10)

        self.tunnel_status_label = ctk.CTkLabel(
            self.tunnel_status_frame,
            text="🔴 النفق غير نشط",
            font=("Arial", 14, "bold"),
            text_color="#e74c3c"
        )
        self.tunnel_status_label.pack(pady=10)

        # إطار الرابط
        self.tunnel_url_frame = ctk.CTkFrame(content, fg_color="#ecf0f1", corner_radius=10)
        self.tunnel_url_frame.pack(pady=10, fill="x", padx=10)

        url_label = ctk.CTkLabel(
            self.tunnel_url_frame,
            text="الرابط العالمي:",
            font=("Arial", 12, "bold"),
            text_color="#2c3e50"
        )
        url_label.pack(pady=(10, 5))

        # حقل الرابط
        self.tunnel_url_entry = ctk.CTkEntry(
            self.tunnel_url_frame,
            placeholder_text="سيظهر الرابط هنا عند تشغيل النفق...",
            width=500,
            height=35,
            font=("Arial", 11)
        )
        self.tunnel_url_entry.pack(pady=5, padx=10)

        # أزرار الإجراءات
        buttons_frame = ctk.CTkFrame(content, fg_color="#f2f3f7", corner_radius=0)
        buttons_frame.pack(pady=15, fill="x")

        # زر تشغيل/إيقاف النفق
        self.tunnel_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 تشغيل النفق العالمي",
            font=("Arial", 14, "bold"),
            width=200,
            height=40,
            fg_color="#27ae60",
            hover_color="#2ecc71",
            command=self.toggle_tunnel
        )
        self.tunnel_button.pack(side="left", padx=10)

        # زر نسخ الرابط
        self.copy_button = ctk.CTkButton(
            buttons_frame,
            text="📋 نسخ الرابط",
            font=("Arial", 14, "bold"),
            width=150,
            height=40,
            fg_color="#3498db",
            hover_color="#2980b9",
            command=self.copy_tunnel_url,
            state="disabled"
        )
        self.copy_button.pack(side="left", padx=10)

        # زر فتح في المتصفح
        self.open_button = ctk.CTkButton(
            buttons_frame,
            text="🌐 فتح في المتصفح",
            font=("Arial", 14, "bold"),
            width=150,
            height=40,
            fg_color="#9b59b6",
            hover_color="#8e44ad",
            command=self.open_tunnel_url,
            state="disabled"
        )
        self.open_button.pack(side="left", padx=10)

        # معلومات إضافية
        info_frame = ctk.CTkFrame(content, fg_color="#d5dbdb", corner_radius=10)
        info_frame.pack(pady=10, fill="x", padx=10)

        info_text = """💡 معلومات مهمة:
• الرابط يعمل من أي مكان في العالم
• للهاتف: أضف /mobile في نهاية الرابط
• الاتصال مشفر وآمن (HTTPS)
• لا تشارك الرابط مع غير الموثوقين
• يمكن إيقاف النفق في أي وقت"""

        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=("Arial", 11),
            text_color="#2c3e50",
            justify="left"
        )
        info_label.pack(pady=10, padx=15)

        # متغيرات النفق
        self.tunnel_process = None
        self.tunnel_url = None
        self.tunnel_active = False

    def toggle_tunnel(self):
        """تشغيل أو إيقاف النفق"""
        if self.tunnel_active:
            self.stop_tunnel()
        else:
            self.start_tunnel()

    def start_tunnel(self):
        """بدء تشغيل النفق العالمي"""
        # التحقق من تشغيل الخادم المحلي
        if not self.check_local_server():
            response = messagebox.askyesno(
                "خادم التقارير غير مشغل",
                "يجب تشغيل خادم التقارير أولاً للوصول العالمي.\n\n" +
                "طرق التشغيل:\n" +
                "1️⃣ من الواجهة الرئيسية → زر '🌐 خادم التقارير'\n" +
                "2️⃣ ملف: تشغيل_خادم_التقارير.bat\n" +
                "3️⃣ أمر: python start_web_server.py\n\n" +
                "هل تريد محاولة تشغيله تلقائياً؟"
            )
            if response:
                self.try_start_web_server()
            return

        # التحقق من وجود cloudflared
        cloudflared_path = Path("cloudflared.exe")
        if not cloudflared_path.exists():
            response = messagebox.askyesno(
                "تحميل مطلوب",
                "cloudflared.exe غير موجود.\nهل تريد تحميله الآن؟\n(قد يستغرق بضع دقائق)"
            )
            if response:
                self.download_cloudflared()
            else:
                return

        # بدء النفق في خيط منفصل
        self.tunnel_button.configure(text="⏳ جاري التشغيل...", state="disabled")
        self.tunnel_status_label.configure(text="🟡 جاري تشغيل النفق...", text_color="#f39c12")

        threading.Thread(target=self._start_tunnel_thread, daemon=True).start()

    def _start_tunnel_thread(self):
        """تشغيل النفق في خيط منفصل"""
        try:
            # تشغيل cloudflared مع التقاط المخرجات
            self.tunnel_process = subprocess.Popen([
                "cloudflared.exe", "tunnel", "--url", "http://localhost:5000"
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1, universal_newlines=True)

            # قراءة المخرجات للبحث عن الرابط
            tunnel_url = None
            start_time = time.time()
            timeout = 30  # انتظار 30 ثانية كحد أقصى

            while time.time() - start_time < timeout:
                if self.tunnel_process.poll() is not None:
                    # العملية انتهت
                    break

                try:
                    # قراءة سطر من المخرجات
                    line = self.tunnel_process.stdout.readline()
                    if line:
                        print(f"Cloudflared output: {line.strip()}")  # للتشخيص

                        # البحث عن رابط trycloudflare
                        import re
                        url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', line)
                        if url_match:
                            tunnel_url = url_match.group(0)
                            print(f"Found tunnel URL: {tunnel_url}")
                            break

                        # البحث عن أنماط أخرى محتملة
                        if "trycloudflare.com" in line:
                            # محاولة استخراج الرابط بطريقة أخرى
                            parts = line.split()
                            for part in parts:
                                if "trycloudflare.com" in part:
                                    if part.startswith("http"):
                                        tunnel_url = part.strip()
                                        break
                            if tunnel_url:
                                break

                    time.sleep(0.5)  # انتظار قصير

                except Exception as e:
                    print(f"خطأ في قراءة مخرجات cloudflared: {e}")
                    time.sleep(1)

            # إذا لم نجد الرابط، محاولة أخيرة
            if not tunnel_url:
                tunnel_url = self.extract_url_from_logs()

            # تحديث الواجهة في الخيط الرئيسي
            self.after(0, lambda: self._tunnel_started(tunnel_url))

        except Exception as e:
            self.after(0, lambda: self._tunnel_failed(str(e)))

    def get_tunnel_url(self):
        """الحصول على رابط النفق من مخرجات cloudflared"""
        try:
            # قراءة مخرجات cloudflared للحصول على الرابط
            if self.tunnel_process and self.tunnel_process.poll() is None:
                # محاولة قراءة stderr حيث يظهر الرابط
                import select
                import time

                for attempt in range(10):  # محاولة لمدة 10 ثوان
                    time.sleep(1)

                    # قراءة المخرجات المتاحة
                    try:
                        # قراءة من stderr
                        if self.tunnel_process.stderr:
                            output = self.tunnel_process.stderr.readline()
                            if output:
                                # البحث عن رابط trycloudflare
                                import re
                                url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', output)
                                if url_match:
                                    return url_match.group(0)

                        # قراءة من stdout أيضاً
                        if self.tunnel_process.stdout:
                            output = self.tunnel_process.stdout.readline()
                            if output:
                                import re
                                url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', output)
                                if url_match:
                                    return url_match.group(0)
                    except:
                        continue

                # إذا لم نجد الرابط، نحاول طريقة أخرى
                return self.extract_url_from_logs()

        except Exception as e:
            print(f"خطأ في الحصول على رابط النفق: {e}")

        # إذا فشل كل شيء، إرجاع رابط تجريبي
        return None

    def extract_url_from_logs(self):
        """استخراج الرابط من ملفات السجل أو المخرجات"""
        try:
            # محاولة قراءة ملف سجل cloudflared إذا وُجد
            import glob
            import os

            # البحث عن ملفات السجل
            log_files = glob.glob("*.log") + glob.glob("cloudflared*.log")

            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        import re
                        url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', content)
                        if url_match:
                            return url_match.group(0)
                except:
                    continue

            # محاولة أخيرة: إنشاء رابط تجريبي مع تحذير
            return None

        except:
            return None

    def _tunnel_started(self, tunnel_url):
        """تحديث الواجهة عند بدء النفق"""
        self.tunnel_active = True
        self.tunnel_url = tunnel_url

        if tunnel_url:
            # تحديث الواجهة للنجاح
            self.tunnel_status_label.configure(text="🟢 النفق نشط", text_color="#27ae60")
            self.tunnel_button.configure(
                text="⏹️ إيقاف النفق",
                state="normal",
                fg_color="#e74c3c",
                hover_color="#c0392b"
            )

            # تحديث حقل الرابط
            self.tunnel_url_entry.delete(0, "end")
            self.tunnel_url_entry.insert(0, tunnel_url)

            # تفعيل الأزرار
            self.copy_button.configure(state="normal")
            self.open_button.configure(state="normal")

            # إشعار النجاح
            messagebox.showinfo(
                "نجح التشغيل",
                f"تم تشغيل النفق العالمي بنجاح!\n\n" +
                f"🔗 الرابط: {tunnel_url}\n" +
                f"📱 للهاتف: {tunnel_url}/mobile\n" +
                f"📊 التقارير: {tunnel_url}/reports\n\n" +
                "تم نسخ الرابط تلقائياً للحافظة!"
            )

            # نسخ الرابط تلقائياً
            try:
                import pyperclip
                pyperclip.copy(tunnel_url)
            except:
                try:
                    self.clipboard_clear()
                    self.clipboard_append(tunnel_url)
                except:
                    pass
        else:
            # النفق يعمل لكن لم نحصل على الرابط
            self.tunnel_status_label.configure(text="🟡 النفق نشط (جاري الحصول على الرابط)", text_color="#f39c12")
            self.tunnel_button.configure(
                text="⏹️ إيقاف النفق",
                state="normal",
                fg_color="#e74c3c",
                hover_color="#c0392b"
            )

            # تحديث حقل الرابط
            self.tunnel_url_entry.delete(0, "end")
            self.tunnel_url_entry.insert(0, "جاري الحصول على الرابط...")

            # تفعيل الأزرار جزئياً
            self.copy_button.configure(state="disabled")
            self.open_button.configure(state="disabled")

            # إشعار جزئي
            messagebox.showinfo(
                "النفق نشط",
                "تم تشغيل النفق العالمي!\n\n" +
                "⚠️ لم يتم الحصول على الرابط تلقائياً.\n\n" +
                "يرجى التحقق من نافذة cloudflared للحصول على الرابط،\n" +
                "أو إعادة تشغيل النفق.\n\n" +
                "الرابط عادة يكون بالشكل:\n" +
                "https://random-name.trycloudflare.com"
            )

            # محاولة الحصول على الرابط مرة أخرى بعد فترة
            self.after(10000, self.retry_get_tunnel_url)  # محاولة بعد 10 ثوان

    def retry_get_tunnel_url(self):
        """إعادة محاولة الحصول على رابط النفق"""
        if self.tunnel_active and not self.tunnel_url:
            tunnel_url = self.get_tunnel_url()
            if tunnel_url:
                self.tunnel_url = tunnel_url

                # تحديث الواجهة
                self.tunnel_status_label.configure(text="🟢 النفق نشط", text_color="#27ae60")
                self.tunnel_url_entry.delete(0, "end")
                self.tunnel_url_entry.insert(0, tunnel_url)

                # تفعيل الأزرار
                self.copy_button.configure(state="normal")
                self.open_button.configure(state="normal")

                # إشعار النجاح
                messagebox.showinfo(
                    "تم الحصول على الرابط",
                    f"تم الحصول على رابط النفق!\n\n" +
                    f"🔗 الرابط: {tunnel_url}\n" +
                    f"📱 للهاتف: {tunnel_url}/mobile"
                )

                # نسخ الرابط تلقائياً
                try:
                    import pyperclip
                    pyperclip.copy(tunnel_url)
                except:
                    pass

    def _tunnel_failed(self, error):
        """تحديث الواجهة عند فشل النفق"""
        self.tunnel_button.configure(text="🚀 تشغيل النفق العالمي", state="normal")
        self.tunnel_status_label.configure(text="🔴 فشل في التشغيل", text_color="#e74c3c")

        messagebox.showerror(
            "خطأ في التشغيل",
            f"فشل في تشغيل النفق:\n{error}\n\nتأكد من:\n• اتصال الإنترنت\n• عدم حجب Firewall\n• تشغيل الخادم المحلي"
        )

    def stop_tunnel(self):
        """إيقاف النفق"""
        if self.tunnel_process:
            self.tunnel_process.terminate()
            self.tunnel_process = None

        self.tunnel_active = False
        self.tunnel_url = None

        # تحديث الواجهة
        self.tunnel_status_label.configure(text="🔴 النفق غير نشط", text_color="#e74c3c")
        self.tunnel_button.configure(
            text="🚀 تشغيل النفق العالمي",
            fg_color="#27ae60",
            hover_color="#2ecc71"
        )

        # مسح حقل الرابط
        self.tunnel_url_entry.delete(0, "end")
        self.tunnel_url_entry.insert(0, "سيظهر الرابط هنا عند تشغيل النفق...")

        # تعطيل الأزرار
        self.copy_button.configure(state="disabled")
        self.open_button.configure(state="disabled")

        messagebox.showinfo("تم الإيقاف", "تم إيقاف النفق العالمي")

    def copy_tunnel_url(self):
        """نسخ رابط النفق"""
        if self.tunnel_url:
            try:
                pyperclip.copy(self.tunnel_url)
                messagebox.showinfo("تم النسخ", f"تم نسخ الرابط:\n{self.tunnel_url}")
            except:
                # إذا فشل pyperclip، استخدم الحافظة العادية
                self.clipboard_clear()
                self.clipboard_append(self.tunnel_url)
                messagebox.showinfo("تم النسخ", f"تم نسخ الرابط:\n{self.tunnel_url}")
        else:
            messagebox.showwarning("تحذير", "لا يوجد رابط للنسخ")

    def open_tunnel_url(self):
        """فتح رابط النفق في المتصفح"""
        if self.tunnel_url:
            import webbrowser
            webbrowser.open(self.tunnel_url)
            messagebox.showinfo("تم الفتح", "تم فتح الرابط في المتصفح")
        else:
            messagebox.showwarning("تحذير", "لا يوجد رابط للفتح")

    def check_local_server(self):
        """التحقق من تشغيل الخادم المحلي"""
        try:
            response = requests.get('http://localhost:5000', timeout=3)
            return response.status_code == 200
        except:
            return False

    def download_cloudflared(self):
        """تحميل cloudflared"""
        try:
            import urllib.request

            # إظهار نافذة تقدم
            progress_window = ctk.CTkToplevel(self)
            progress_window.title("تحميل cloudflared")
            progress_window.geometry("400x150")
            progress_window.transient(self)

            progress_label = ctk.CTkLabel(progress_window, text="جاري التحميل...")
            progress_label.pack(pady=20)

            progress_bar = ctk.CTkProgressBar(progress_window, width=300)
            progress_bar.pack(pady=10)
            progress_bar.set(0)

            def download_progress(block_num, block_size, total_size):
                downloaded = block_num * block_size
                if total_size > 0:
                    percent = min(1.0, downloaded / total_size)
                    progress_bar.set(percent)
                    progress_label.configure(text=f"تحميل: {percent*100:.1f}%")
                    progress_window.update()

            url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
            urllib.request.urlretrieve(url, "cloudflared.exe", download_progress)

            progress_window.destroy()
            messagebox.showinfo("تم التحميل", "تم تحميل cloudflared بنجاح!")

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("خطأ في التحميل", f"فشل في التحميل:\n{e}")

    def try_start_web_server(self):
        """محاولة تشغيل خادم التقارير تلقائياً"""
        try:
            # محاولة تشغيل الخادم في الخلفية (بدون نافذة)
            subprocess.Popen([
                sys.executable, "start_web_server.py"
            ], creationflags=subprocess.CREATE_NO_WINDOW)

            # انتظار قصير
            time.sleep(3)

            # التحقق من نجاح التشغيل
            if self.check_local_server():
                messagebox.showinfo(
                    "نجح التشغيل",
                    "تم تشغيل خادم التقارير بنجاح!\nيمكنك الآن تشغيل النفق العالمي."
                )
                # محاولة تشغيل النفق مرة أخرى
                self.start_tunnel()
            else:
                messagebox.showwarning(
                    "فشل التشغيل التلقائي",
                    "لم يتم تشغيل الخادم تلقائياً.\n\n" +
                    "يرجى تشغيله يدوياً:\n" +
                    "• من الواجهة الرئيسية → '🌐 خادم التقارير'\n" +
                    "• أو ملف: تشغيل_خادم_التقارير.bat\n" +
                    "• أو أمر: python web_server.py"
                )
        except Exception as e:
            messagebox.showerror(
                "خطأ في التشغيل",
                f"فشل في تشغيل خادم التقارير:\n{e}\n\n" +
                "يرجى تشغيله يدوياً من الواجهة الرئيسية."
            )

    def __del__(self):
        """تنظيف الموارد عند إغلاق النافذة"""
        if hasattr(self, 'tunnel_process') and self.tunnel_process:
            self.tunnel_process.terminate()
