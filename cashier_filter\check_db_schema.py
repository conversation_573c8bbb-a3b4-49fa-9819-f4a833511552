#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
Check Database Schema
"""

import sqlite3
import os

def check_database_schema():
    """فحص هيكل قاعدة البيانات"""
    
    # تحديد مسار قاعدة البيانات
    if os.path.exists("db/cashier_filter.db"):
        db_path = "db/cashier_filter.db"
    else:
        db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # الحصول على جميع الجداول
        c.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = c.fetchall()
        
        print("📊 الجداول الموجودة:")
        for table in tables:
            table_name = table[0]
            print(f"\n🔹 جدول: {table_name}")
            
            # الحصول على هيكل الجدول
            c.execute(f"PRAGMA table_info({table_name})")
            columns = c.fetchall()
            
            print("   الأعمدة:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = col
                print(f"     • {col_name} ({col_type})")
            
            # عرض عينة من البيانات
            c.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = c.fetchone()[0]
            print(f"   عدد السجلات: {count}")
            
            if count > 0:
                c.execute(f"SELECT * FROM {table_name} LIMIT 3")
                sample_data = c.fetchall()
                print("   عينة من البيانات:")
                for i, row in enumerate(sample_data, 1):
                    print(f"     {i}: {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_database_schema()
