#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام تصفية الكاشير المتكامل 2025
Simple Launcher for Cashier Filter System 2025

تطوير: محم<PERSON> الكامل
Developed by: <PERSON>
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
║         Cashier Filter System 2025                                   ║
║                                                                      ║
║    الإصدار 3.0.0 - مع ذكاء اصطناعي متطور                          ║
║    Version 3.0.0 - With Advanced AI                                 ║
║                                                                      ║
║    تطوير: محمد الكامل | Developed by: <PERSON>              ║
║    البريد: <EMAIL>                                     ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_python():
    """التحقق من Python"""
    print("🐍 التحقق من Python...")
    
    python_version = sys.version_info
    print(f"   إصدار Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print("   يرجى تحديث Python من: https://python.org")
        return False
    
    print("   ✅ Python متوافق")
    return True

def check_dependencies():
    """التحقق من المتطلبات"""
    print("\n📦 التحقق من المتطلبات...")
    
    required_modules = [
        ("customtkinter", "واجهة المستخدم"),
        ("sqlite3", "قاعدة البيانات"),
        ("json", "معالجة البيانات"),
        ("datetime", "التاريخ والوقت"),
        ("threading", "المعالجة المتوازية")
    ]
    
    missing_modules = []
    
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {description} ({module})")
        except ImportError:
            print(f"   ❌ {description} ({module}) - غير متاح")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ المتطلبات المفقودة: {', '.join(missing_modules)}")
        return False
    
    print("   ✅ جميع المتطلبات متاحة")
    return True

def check_files():
    """التحقق من الملفات المطلوبة"""
    print("\n📁 التحقق من الملفات...")
    
    required_files = [
        ("main.py", "الملف الرئيسي"),
        ("db/cashier_filter.db", "قاعدة البيانات"),
        ("ui/main_window.py", "واجهة المستخدم الرئيسية")
    ]
    
    missing_files = []
    
    for file_path, description in required_files:
        if Path(file_path).exists():
            print(f"   ✅ {description}")
        else:
            print(f"   ❌ {description} - غير موجود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    
    print("   ✅ جميع الملفات موجودة")
    return True

def install_missing_dependencies():
    """تثبيت المتطلبات المفقودة"""
    print("\n📦 تثبيت المتطلبات المفقودة...")
    
    try:
        if Path("requirements.txt").exists():
            print("   🔄 تثبيت من requirements.txt...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("   ✅ تم تثبيت المتطلبات")
            return True
        else:
            print("   🔄 تثبيت المتطلبات الأساسية...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "customtkinter"
            ])
            print("   ✅ تم تثبيت المتطلبات الأساسية")
            return True
            
    except subprocess.CalledProcessError as e:
        print(f"   ❌ فشل التثبيت: {e}")
        return False

def run_setup():
    """تشغيل الإعداد"""
    print("\n⚙️ تشغيل الإعداد...")
    
    try:
        if Path("setup.py").exists():
            subprocess.check_call([sys.executable, "setup.py"])
            return True
        elif Path("install.py").exists():
            subprocess.check_call([sys.executable, "install.py"])
            return True
        else:
            print("   ⚠️ ملف الإعداد غير موجود")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"   ❌ فشل الإعداد: {e}")
        return False

def launch_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل النظام...")
    
    try:
        # تغيير المجلد الحالي
        os.chdir(Path(__file__).parent)
        
        # تشغيل التطبيق
        subprocess.run([sys.executable, "main.py"])
        
    except FileNotFoundError:
        print("❌ خطأ: ملف main.py غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    
    return True

def show_help():
    """عرض المساعدة"""
    help_text = """
📖 دليل الاستخدام السريع:

🔐 تسجيل الدخول:
   اسم المستخدم: admin
   كلمة المرور: 123456

💰 إضافة تصفية جديدة:
   1. اضغط "➕ بدء تصفية جديدة"
   2. اختر الكاشير والتاريخ
   3. أدخل المبالغ واحفظ

📊 عرض التقارير:
   1. اضغط "📁 عرض تقارير التصفية"
   2. اختر نوع التقرير والفترة

🤖 التحليل الذكي:
   1. اضغط "🤖 التحليل الذكي بالذكاء الاصطناعي"
   2. اختر نوع التحليل وشغله

📈 لوحة المعلومات:
   1. اضغط "📊 لوحة المعلومات التفاعلية"
   2. راقب الإحصائيات في الوقت الفعلي

💬 الدعم الفني: <EMAIL>
"""
    print(help_text)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من Python
    if not check_python():
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
    
    # التحقق من المتطلبات
    if not check_dependencies():
        print("\n🔧 محاولة تثبيت المتطلبات...")
        if not install_missing_dependencies():
            print("\n❌ فشل في تثبيت المتطلبات")
            print("يرجى تشغيل: python install.py")
            input("اضغط Enter للخروج...")
            sys.exit(1)
    
    # التحقق من الملفات
    if not check_files():
        print("\n🔧 محاولة تشغيل الإعداد...")
        if not run_setup():
            print("\n❌ فشل في الإعداد")
            print("يرجى تشغيل: python setup.py")
            input("اضغط Enter للخروج...")
            sys.exit(1)
    
    # عرض المساعدة
    show_help()
    
    # تشغيل التطبيق
    if not launch_application():
        print("\n❌ فشل في تشغيل التطبيق")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إنهاء البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
