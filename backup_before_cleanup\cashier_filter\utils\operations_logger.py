#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تسجيل العمليات
Operations Logger System
"""

import sqlite3
import json
from datetime import datetime
import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# تحديد مسار قاعدة البيانات
if os.path.exists("db/cashier_filter.db"):
    DB_PATH = "db/cashier_filter.db"
else:
    DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class OperationsLogger:
    """فئة تسجيل العمليات"""
    
    def __init__(self):
        self.init_operations_table()
    
    def init_operations_table(self):
        """إنشاء جدول سجل العمليات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # إنشاء جدول سجل العمليات
            c.execute('''
                CREATE TABLE IF NOT EXISTS operations_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username TEXT,
                    operation_type TEXT,
                    operation_name TEXT,
                    description TEXT,
                    details TEXT,
                    ip_address TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT 1,
                    error_message TEXT,
                    session_id TEXT,
                    affected_records INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES admins (id)
                )
            ''')
            
            # إنشاء فهارس للبحث السريع
            c.execute('CREATE INDEX IF NOT EXISTS idx_operations_user ON operations_log(user_id)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_operations_type ON operations_log(operation_type)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_operations_timestamp ON operations_log(timestamp)')
            c.execute('CREATE INDEX IF NOT EXISTS idx_operations_success ON operations_log(success)')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إنشاء جدول سجل العمليات: {e}")
    
    def log_operation(self, user_id, username, operation_type, operation_name, 
                     description="", details=None, success=True, error_message=None,
                     affected_records=0, ip_address="localhost", session_id=None):
        """تسجيل عملية جديدة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # تحويل التفاصيل إلى JSON
            details_json = json.dumps(details, ensure_ascii=False) if details else None
            
            c.execute('''
                INSERT INTO operations_log 
                (user_id, username, operation_type, operation_name, description, 
                 details, ip_address, success, error_message, session_id, affected_records)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, username, operation_type, operation_name, description,
                  details_json, ip_address, success, error_message, session_id, affected_records))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"خطأ في تسجيل العملية: {e}")
            return False
    
    def get_operations_log(self, limit=100, offset=0, user_id=None, operation_type=None,
                          start_date=None, end_date=None, success_only=None):
        """استرجاع سجل العمليات مع فلترة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # بناء الاستعلام
            query = '''
                SELECT id, user_id, username, operation_type, operation_name, 
                       description, details, ip_address, timestamp, success, 
                       error_message, session_id, affected_records
                FROM operations_log
                WHERE 1=1
            '''
            params = []
            
            # إضافة الفلاتر
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if operation_type:
                query += " AND operation_type = ?"
                params.append(operation_type)
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date)
            
            if success_only is not None:
                query += " AND success = ?"
                params.append(success_only)
            
            # ترتيب وحد
            query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])
            
            c.execute(query, params)
            operations = c.fetchall()
            
            # تحويل النتائج إلى قاموس
            operations_list = []
            for op in operations:
                operation_dict = {
                    'id': op[0],
                    'user_id': op[1],
                    'username': op[2],
                    'operation_type': op[3],
                    'operation_name': op[4],
                    'description': op[5],
                    'details': json.loads(op[6]) if op[6] else None,
                    'ip_address': op[7],
                    'timestamp': op[8],
                    'success': bool(op[9]),
                    'error_message': op[10],
                    'session_id': op[11],
                    'affected_records': op[12]
                }
                operations_list.append(operation_dict)
            
            conn.close()
            return operations_list
            
        except Exception as e:
            print(f"خطأ في استرجاع سجل العمليات: {e}")
            return []
    
    def get_operations_count(self, user_id=None, operation_type=None, 
                           start_date=None, end_date=None, success_only=None):
        """حساب عدد العمليات مع الفلترة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            query = "SELECT COUNT(*) FROM operations_log WHERE 1=1"
            params = []
            
            # إضافة الفلاتر
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
            
            if operation_type:
                query += " AND operation_type = ?"
                params.append(operation_type)
            
            if start_date:
                query += " AND timestamp >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND timestamp <= ?"
                params.append(end_date)
            
            if success_only is not None:
                query += " AND success = ?"
                params.append(success_only)
            
            c.execute(query, params)
            count = c.fetchone()[0]
            
            conn.close()
            return count
            
        except Exception as e:
            print(f"خطأ في حساب العمليات: {e}")
            return 0
    
    def get_operations_statistics(self, days=30):
        """إحصائيات العمليات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # إحصائيات عامة
            stats = {}
            
            # العمليات في آخر فترة
            c.execute('''
                SELECT COUNT(*) FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days')
            '''.format(days))
            stats['total_operations'] = c.fetchone()[0]
            
            # العمليات الناجحة
            c.execute('''
                SELECT COUNT(*) FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days') AND success = 1
            '''.format(days))
            stats['successful_operations'] = c.fetchone()[0]
            
            # العمليات الفاشلة
            c.execute('''
                SELECT COUNT(*) FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days') AND success = 0
            '''.format(days))
            stats['failed_operations'] = c.fetchone()[0]
            
            # أكثر المستخدمين نشاطاً
            c.execute('''
                SELECT username, COUNT(*) as count
                FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY username
                ORDER BY count DESC
                LIMIT 10
            '''.format(days))
            stats['most_active_users'] = c.fetchall()
            
            # أكثر أنواع العمليات
            c.execute('''
                SELECT operation_type, COUNT(*) as count
                FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY operation_type
                ORDER BY count DESC
                LIMIT 10
            '''.format(days))
            stats['most_common_operations'] = c.fetchall()
            
            # العمليات حسب اليوم
            c.execute('''
                SELECT DATE(timestamp) as date, COUNT(*) as count
                FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            '''.format(days))
            stats['operations_by_day'] = c.fetchall()
            
            # العمليات حسب الساعة
            c.execute('''
                SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
                FROM operations_log 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY strftime('%H', timestamp)
                ORDER BY hour
            '''.format(days))
            stats['operations_by_hour'] = c.fetchall()
            
            conn.close()
            return stats
            
        except Exception as e:
            print(f"خطأ في إحصائيات العمليات: {e}")
            return {}

# إنشاء مثيل عام للاستخدام
operations_logger = OperationsLogger()

# دوال مساعدة للاستخدام السهل
def log_login(user_id, username, success=True, ip_address="localhost"):
    """تسجيل عملية تسجيل دخول"""
    return operations_logger.log_operation(
        user_id, username, "AUTH", "تسجيل دخول",
        "تسجيل دخول المستخدم", {"ip": ip_address}, success, ip_address=ip_address
    )

def log_logout(user_id, username, ip_address="localhost"):
    """تسجيل عملية تسجيل خروج"""
    return operations_logger.log_operation(
        user_id, username, "AUTH", "تسجيل خروج",
        "تسجيل خروج المستخدم", {"ip": ip_address}, True, ip_address=ip_address
    )

def log_filter_operation(user_id, username, operation, filter_id=None, details=None):
    """تسجيل عمليات التصفية"""
    return operations_logger.log_operation(
        user_id, username, "FILTER", operation,
        f"عملية تصفية: {operation}", 
        {"filter_id": filter_id, **details} if details else {"filter_id": filter_id}
    )

def log_report_operation(user_id, username, operation, report_type=None, details=None):
    """تسجيل عمليات التقارير"""
    return operations_logger.log_operation(
        user_id, username, "REPORT", operation,
        f"عملية تقرير: {operation}",
        {"report_type": report_type, **details} if details else {"report_type": report_type}
    )

def log_admin_operation(user_id, username, operation, target_user=None, details=None):
    """تسجيل العمليات الإدارية"""
    return operations_logger.log_operation(
        user_id, username, "ADMIN", operation,
        f"عملية إدارية: {operation}",
        {"target_user": target_user, **details} if details else {"target_user": target_user}
    )

def log_system_operation(user_id, username, operation, details=None):
    """تسجيل عمليات النظام"""
    return operations_logger.log_operation(
        user_id, username, "SYSTEM", operation,
        f"عملية نظام: {operation}", details
    )
