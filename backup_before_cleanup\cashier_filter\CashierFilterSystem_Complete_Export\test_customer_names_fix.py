#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح أسماء العملاء
Test Customer Names Fix
"""

import sys
import os
from pathlib import Path

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_customer_names_fix():
    """اختبار إصلاح أسماء العملاء"""
    try:
        print("🔍 اختبار إصلاح أسماء العملاء...")
        
        # استيراد WebReportServer
        from web_server import WebReportServer
        print("✅ تم استيراد WebReportServer")
        
        # إنشاء خادم التقارير
        web_server = WebReportServer()
        print("✅ تم إنشاء WebReportServer")
        
        # اختبار التصفية #18 التي تحتوي على أسماء عملاء
        filter_id = 18
        filter_data = web_server.get_filter_by_id(filter_id)
        print(f"📊 بيانات التصفية {filter_id}: {filter_data is not None}")
        
        if not filter_data:
            print(f"❌ التصفية رقم {filter_id} غير موجودة")
            return False
        
        # فحص التفاصيل
        details = filter_data.get('details', {})
        print(f"\n💰 التفاصيل المالية:")
        print(f"   بنكي: {details.get('bank_total', 0)}")
        print(f"   نقدي: {details.get('cash_total', 0)}")
        print(f"   آجل: {details.get('credit_total', 0)}")
        print(f"   عملاء: {details.get('client_total', 0)}")
        print(f"   مرتجعات: {details.get('return_total', 0)}")
        
        # فحص الفارق
        if 'variance' in details:
            variance = details['variance']
            print(f"\n⚖️ تحليل الفارق:")
            print(f"   مبيعات النظام: {variance.get('system_sales', 0)}")
            print(f"   المجموع الفعلي: {variance.get('actual_total', 0)}")
            print(f"   الفارق: {variance.get('difference', 0)}")
            print(f"   النسبة: {variance.get('percentage', 0)}%")
            print(f"   الحالة: {variance.get('status', 'غير محدد')}")
        
        # فحص أسماء العملاء
        print(f"\n👥 اختبار أسماء العملاء:")
        
        # المبيعات الآجلة
        credit_details = details.get('credit_details', [])
        print(f"\n📋 المبيعات الآجلة: {len(credit_details)} عميل")
        if credit_details:
            for i, credit in enumerate(credit_details[:5], 1):  # أول 5 فقط
                customer_name = credit.get('customer_name', 'غير محدد')
                amount = credit.get('amount', 0)
                invoice = credit.get('invoice_number', 'غير محدد')
                print(f"   {i}. {customer_name} - فاتورة {invoice} - {amount} ريال")
                
                if customer_name != 'عميل غير محدد' and customer_name != 'غير محدد':
                    print(f"      ✅ اسم العميل موجود: {customer_name}")
                else:
                    print(f"      ❌ اسم العميل مفقود")
        else:
            print("   لا توجد مبيعات آجلة")
        
        # مقبوضات العملاء
        client_details = details.get('client_details', [])
        print(f"\n💰 مقبوضات العملاء: {len(client_details)} عميل")
        if client_details:
            for i, client in enumerate(client_details[:5], 1):  # أول 5 فقط
                customer_name = client.get('customer_name', 'غير محدد')
                amount = client.get('amount', 0)
                invoice = client.get('invoice_number', 'غير محدد')
                print(f"   {i}. {customer_name} - فاتورة {invoice} - {amount} ريال")
                
                if customer_name != 'عميل غير محدد' and customer_name != 'غير محدد':
                    print(f"      ✅ اسم العميل موجود: {customer_name}")
                else:
                    print(f"      ❌ اسم العميل مفقود")
        else:
            print("   لا توجد مقبوضات عملاء")
        
        # المرتجعات
        return_details = details.get('return_details', [])
        print(f"\n🔄 المرتجعات: {len(return_details)} عميل")
        if return_details:
            for i, return_item in enumerate(return_details[:5], 1):  # أول 5 فقط
                customer_name = return_item.get('customer_name', 'غير محدد')
                amount = return_item.get('amount', 0)
                reason = return_item.get('reason', 'غير محدد')
                print(f"   {i}. {customer_name} - {amount} ريال - السبب: {reason}")
                
                if customer_name != 'عميل غير محدد' and customer_name != 'غير محدد':
                    print(f"      ✅ اسم العميل موجود: {customer_name}")
                else:
                    print(f"      ❌ اسم العميل مفقود")
        else:
            print("   لا توجد مرتجعات")
        
        # إحصائيات النجاح
        total_customers = len(credit_details) + len(client_details) + len(return_details)
        customers_with_names = 0
        
        for credit in credit_details:
            if credit.get('customer_name') not in ['عميل غير محدد', 'غير محدد']:
                customers_with_names += 1
        
        for client in client_details:
            if client.get('customer_name') not in ['عميل غير محدد', 'غير محدد']:
                customers_with_names += 1
        
        for return_item in return_details:
            if return_item.get('customer_name') not in ['عميل غير محدد', 'غير محدد']:
                customers_with_names += 1
        
        print(f"\n📊 إحصائيات أسماء العملاء:")
        print(f"   إجمالي المعاملات: {total_customers}")
        print(f"   معاملات بأسماء: {customers_with_names}")
        print(f"   معاملات بدون أسماء: {total_customers - customers_with_names}")
        
        if total_customers > 0:
            success_rate = (customers_with_names / total_customers) * 100
            print(f"   نسبة النجاح: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("   ✅ نسبة نجاح ممتازة!")
            elif success_rate >= 50:
                print("   ⚠️ نسبة نجاح مقبولة")
            else:
                print("   ❌ نسبة نجاح ضعيفة")
        
        return customers_with_names > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أسماء العملاء: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار إصلاح أسماء العملاء")
    print("=" * 60)
    
    # التحقق من المجلد الحالي
    if not Path('web_server.py').exists():
        print("❌ يجب تشغيل هذا الملف من مجلد cashier_filter")
        return
    
    # اختبار إصلاح أسماء العملاء
    success = test_customer_names_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 تم إصلاح أسماء العملاء بنجاح!")
        print("💡 أسماء العملاء تظهر الآن في التقارير")
        print("🔗 جرب: http://localhost:5000/filter/18/comprehensive")
    else:
        print("❌ لا تزال هناك مشاكل في أسماء العملاء!")
        print("💡 قد تحتاج لتحديث التطبيق الأساسي لحفظ أسماء العملاء")

if __name__ == "__main__":
    main()
