#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم ويب بسيط لعرض تقارير نظام تصفية الكاشير
Simple Web Server for Cashier Filter System Reports

يوفر واجهة ويب للاطلاع على التقارير من أي مكان ومن الهاتف
"""

from flask import Flask, render_template, jsonify, request, send_file
import sqlite3
import json
import os
import gc
import atexit
from datetime import datetime, timedelta
import threading
import webbrowser
from pathlib import Path

# إعداد المسارات
BASE_DIR = Path(__file__).parent
DB_PATH = BASE_DIR / "db" / "cashier_filter.db"
TEMPLATES_DIR = BASE_DIR / "web_templates"
STATIC_DIR = BASE_DIR / "web_static"

# إنشاء تطبيق Flask
app = Flask(__name__,
           template_folder=str(TEMPLATES_DIR),
           static_folder=str(STATIC_DIR))

# تحسين إعدادات Flask للذاكرة
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 300  # 5 minutes cache

def cleanup_web_resources():
    """تنظيف موارد خادم الويب"""
    try:
        # إغلاق اتصالات قاعدة البيانات
        if hasattr(app, '_db_connections'):
            for conn in app._db_connections:
                try:
                    conn.close()
                except:
                    pass
            app._db_connections.clear()

        # تنظيف الذاكرة
        gc.collect()
    except:
        pass

# تسجيل دالة التنظيف عند الخروج
atexit.register(cleanup_web_resources)

# إعدادات التطبيق
app.config['SECRET_KEY'] = 'cashier_filter_2025_secret_key'
app.config['JSON_AS_ASCII'] = False

class WebReportServer:
    def __init__(self):
        self.app = app
        self.host = '0.0.0.0'  # للوصول من أي مكان
        self.port = 5000
        self.debug = False
        
    def get_database_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        try:
            conn = sqlite3.connect(str(DB_PATH))
            conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return conn
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
    
    def get_all_filters(self, limit=50):
        """الحصول على جميع التصفيات مع جميع التفاصيل"""
        conn = self.get_database_connection()
        if not conn:
            return []

        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT f.*,
                       COALESCE(c.name, 'غير محدد') as cashier_name,
                       COALESCE(f.admin_name, a.name, 'غير محدد') as admin_name_full
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                LEFT JOIN admins a ON f.admin_id = a.id
                ORDER BY COALESCE(f.sequence_number, f.id) DESC
                LIMIT ?
            """, (limit,))

            filters = []
            for row in cursor.fetchall():
                filter_data = dict(row)

                # تحليل بيانات JSON مع معالجة أفضل للأخطاء
                try:
                    if filter_data['data']:
                        details = json.loads(filter_data['data'])
                        filter_data['details'] = details

                        # التأكد من وجود جميع الحقول المطلوبة
                        if 'totals' not in details:
                            details['totals'] = {}

                        # تحويل المجاميع إلى الشكل المطلوب للعرض
                        totals = details.get('totals', {})
                        filter_data['details'].update({
                            'bank_total': float(totals.get('bank', 0)),
                            'cash_total': float(totals.get('cash', 0)),
                            'credit_total': float(totals.get('credit', 0)),
                            'client_total': float(totals.get('client', 0)),
                            'return_total': float(totals.get('return', 0)),
                            'bank_transactions': details.get('bank_transactions', []),
                            'cash_details': details.get('cash_details', {}),
                        })

                        # معالجة تفاصيل المعاملات الآجلة مع أسماء العملاء
                        credit_transactions = details.get('credit_transactions', [])
                        credit_details = []
                        for transaction in credit_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            credit_detail = {
                                'invoice_number': transaction.get('invoice', transaction.get('invoice_number', 'غير محدد')),
                                'customer_name': customer_name,
                                'amount': float(transaction.get('amount', 0)),
                                'due_date': transaction.get('due_date', transaction.get('date', 'غير محدد')),
                                'phone': transaction.get('phone', transaction.get('mobile', transaction.get('tel', 'غير متاح'))),
                                'notes': transaction.get('notes', transaction.get('note', ''))
                            }
                            credit_details.append(credit_detail)
                        filter_data['details']['credit_details'] = credit_details

                        # معالجة تفاصيل مقبوضات العملاء مع الأسماء
                        client_transactions = details.get('client_transactions', [])
                        client_details = []
                        for transaction in client_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            client_detail = {
                                'customer_name': customer_name,
                                'invoice_number': transaction.get('invoice', transaction.get('invoice_number', 'غير محدد')),
                                'amount': float(transaction.get('amount', 0)),
                                'payment_method': transaction.get('payment_method', transaction.get('method', 'نقدي')),
                                'notes': transaction.get('notes', transaction.get('note', '')),
                                'phone': transaction.get('phone', transaction.get('mobile', transaction.get('tel', 'غير متاح'))),
                                'ref': transaction.get('ref', '')
                            }
                            client_details.append(client_detail)
                        filter_data['details']['client_details'] = client_details

                        # معالجة تفاصيل المرتجعات مع أسماء العملاء
                        return_transactions = details.get('return_transactions', [])
                        return_details = []
                        for transaction in return_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            return_detail = {
                                'return_invoice': transaction.get('return_invoice', transaction.get('invoice', 'غير محدد')),
                                'original_invoice': transaction.get('original_invoice', 'غير محدد'),
                                'customer_name': customer_name,
                                'reason': transaction.get('reason', 'غير محدد'),
                                'amount': float(transaction.get('amount', 0)),
                                'date': transaction.get('date', 'غير محدد')
                            }
                            return_details.append(return_detail)
                        filter_data['details']['return_details'] = return_details

                        # معالجة تفاصيل الموردين (للمتابعة فقط)
                        suppliers_transactions = details.get('suppliers_transactions', [])
                        suppliers_details = []
                        suppliers_total = 0
                        for transaction in suppliers_transactions:
                            supplier_detail = {
                                'supplier_name': transaction.get('supplier_name', 'مورد غير محدد'),
                                'amount': float(transaction.get('amount', 0)),
                                'payment_method': transaction.get('payment_method', 'نقدي'),
                                'notes': transaction.get('notes', '')
                            }
                            suppliers_details.append(supplier_detail)
                            suppliers_total += supplier_detail['amount']

                        filter_data['details']['suppliers_details'] = suppliers_details
                        filter_data['details']['suppliers_total'] = suppliers_total

                        # حساب المجموع الكلي
                        total = (filter_data['details']['bank_total'] +
                                filter_data['details']['cash_total'] +
                                filter_data['details']['credit_total'] -
                                filter_data['details']['return_total'])
                        filter_data['details']['grand_total'] = total

                        # حساب الفارق في التصفية
                        system_sales = float(details.get('system_sales', 0))
                        actual_total = total + filter_data['details']['client_total']  # إضافة مقبوضات العملاء
                        variance = actual_total - system_sales

                        # تحديد نوع الفارق بدقة
                        if abs(variance) < 0.01:
                            status = 'متوازن'
                            status_type = 'balanced'
                            status_icon = '⚖️'
                            status_color = 'success'
                        elif variance > 0:
                            status = 'فائض'
                            status_type = 'surplus'
                            status_icon = '📈'
                            status_color = 'info'
                        else:
                            status = 'عجز'
                            status_type = 'deficit'
                            status_icon = '📉'
                            status_color = 'warning'

                        # تحديد مستوى الخطورة
                        abs_percentage = abs((variance / system_sales * 100) if system_sales > 0 else 0)
                        if abs_percentage < 1:
                            severity = 'طبيعي'
                            severity_level = 'low'
                        elif abs_percentage < 5:
                            severity = 'مقبول'
                            severity_level = 'medium'
                        else:
                            severity = 'يحتاج مراجعة'
                            severity_level = 'high'

                        filter_data['details']['variance'] = {
                            'system_sales': system_sales,
                            'actual_total': actual_total,
                            'difference': variance,
                            'percentage': (variance / system_sales * 100) if system_sales > 0 else 0,
                            'status': status,
                            'status_type': status_type,
                            'status_icon': status_icon,
                            'status_color': status_color,
                            'severity': severity,
                            'severity_level': severity_level,
                            'abs_difference': abs(variance),
                            'abs_percentage': abs_percentage
                        }

                    else:
                        filter_data['details'] = {
                            'bank_total': 0,
                            'cash_total': 0,
                            'credit_total': 0,
                            'client_total': 0,
                            'return_total': 0,
                            'grand_total': 0,
                            'bank_transactions': [],
                            'cash_details': {},
                        }
                except Exception as e:
                    print(f"خطأ في تحليل بيانات التصفية {filter_data.get('id', 'غير معروف')}: {e}")
                    filter_data['details'] = {
                        'bank_total': 0,
                        'cash_total': 0,
                        'credit_total': 0,
                        'client_total': 0,
                        'return_total': 0,
                        'grand_total': 0,
                        'bank_transactions': [],
                        'cash_details': {},
                    }

                # استخدام admin_name_full إذا كان متاحاً
                if filter_data.get('admin_name_full'):
                    filter_data['admin_name'] = filter_data['admin_name_full']

                filters.append(filter_data)

            print(f"تم جلب {len(filters)} تصفية بنجاح")
            return filters

        except Exception as e:
            print(f"خطأ في جلب التصفيات: {e}")
            return []
        finally:
            conn.close()
    
    def get_filter_by_id(self, filter_id):
        """الحصول على تصفية محددة مع جميع التفاصيل"""
        conn = self.get_database_connection()
        if not conn:
            return None

        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT f.*,
                       COALESCE(c.name, 'غير محدد') as cashier_name,
                       COALESCE(f.admin_name, a.name, 'غير محدد') as admin_name_full
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                LEFT JOIN admins a ON f.admin_id = a.id
                WHERE f.id = ?
            """, (filter_id,))

            row = cursor.fetchone()
            if row:
                filter_data = dict(row)

                # تحليل بيانات JSON مع معالجة شاملة
                try:
                    if filter_data['data']:
                        details = json.loads(filter_data['data'])
                        filter_data['details'] = details

                        # التأكد من وجود جميع الحقول المطلوبة
                        if 'totals' not in details:
                            details['totals'] = {}

                        # تحويل المجاميع إلى الشكل المطلوب للعرض
                        totals = details.get('totals', {})
                        filter_data['details'].update({
                            'bank_total': float(totals.get('bank', 0)),
                            'cash_total': float(totals.get('cash', 0)),
                            'credit_total': float(totals.get('credit', 0)),
                            'client_total': float(totals.get('client', 0)),
                            'return_total': float(totals.get('return', 0)),
                            'bank_transactions': details.get('bank_transactions', []),
                            'cash_details': details.get('cash_details', {}),
                        })

                        # معالجة تفاصيل المعاملات الآجلة مع أسماء العملاء
                        credit_transactions = details.get('credit_transactions', [])
                        credit_details = []
                        for transaction in credit_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            credit_detail = {
                                'invoice_number': transaction.get('invoice', transaction.get('invoice_number', 'غير محدد')),
                                'customer_name': customer_name,
                                'amount': float(transaction.get('amount', 0)),
                                'due_date': transaction.get('due_date', transaction.get('date', 'غير محدد')),
                                'phone': transaction.get('phone', transaction.get('mobile', transaction.get('tel', 'غير متاح'))),
                                'notes': transaction.get('notes', transaction.get('note', ''))
                            }
                            credit_details.append(credit_detail)
                        filter_data['details']['credit_details'] = credit_details

                        # معالجة تفاصيل مقبوضات العملاء مع الأسماء
                        client_transactions = details.get('client_transactions', [])
                        client_details = []
                        for transaction in client_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            client_detail = {
                                'customer_name': customer_name,
                                'invoice_number': transaction.get('invoice', transaction.get('invoice_number', 'غير محدد')),
                                'amount': float(transaction.get('amount', 0)),
                                'payment_method': transaction.get('payment_method', transaction.get('method', 'نقدي')),
                                'notes': transaction.get('notes', transaction.get('note', '')),
                                'phone': transaction.get('phone', transaction.get('mobile', transaction.get('tel', 'غير متاح'))),
                                'ref': transaction.get('ref', '')
                            }
                            client_details.append(client_detail)
                        filter_data['details']['client_details'] = client_details

                        # معالجة تفاصيل المرتجعات مع أسماء العملاء
                        return_transactions = details.get('return_transactions', [])
                        return_details = []
                        for transaction in return_transactions:
                            # البحث عن اسم العميل في حقول مختلفة
                            customer_name = (
                                transaction.get('customer_name') or
                                transaction.get('customer') or
                                transaction.get('client') or
                                transaction.get('name') or
                                'عميل غير محدد'
                            )

                            return_detail = {
                                'return_invoice': transaction.get('return_invoice', transaction.get('invoice', 'غير محدد')),
                                'original_invoice': transaction.get('original_invoice', 'غير محدد'),
                                'customer_name': customer_name,
                                'reason': transaction.get('reason', 'غير محدد'),
                                'amount': float(transaction.get('amount', 0)),
                                'date': transaction.get('date', 'غير محدد')
                            }
                            return_details.append(return_detail)
                        filter_data['details']['return_details'] = return_details

                        # معالجة تفاصيل الموردين (للمتابعة فقط)
                        suppliers_transactions = details.get('suppliers_transactions', [])
                        suppliers_details = []
                        suppliers_total = 0
                        for transaction in suppliers_transactions:
                            supplier_detail = {
                                'supplier_name': transaction.get('supplier_name', 'مورد غير محدد'),
                                'amount': float(transaction.get('amount', 0)),
                                'payment_method': transaction.get('payment_method', 'نقدي'),
                                'notes': transaction.get('notes', '')
                            }
                            suppliers_details.append(supplier_detail)
                            suppliers_total += supplier_detail['amount']

                        filter_data['details']['suppliers_details'] = suppliers_details
                        filter_data['details']['suppliers_total'] = suppliers_total

                        # حساب المجموع الكلي
                        total = (filter_data['details']['bank_total'] +
                                filter_data['details']['cash_total'] +
                                filter_data['details']['credit_total'] -
                                filter_data['details']['return_total'])
                        filter_data['details']['grand_total'] = total

                        # حساب الفارق في التصفية
                        system_sales = float(details.get('system_sales', 0))
                        actual_total = total + filter_data['details']['client_total']  # إضافة مقبوضات العملاء
                        variance = actual_total - system_sales

                        # تحديد نوع الفارق بدقة
                        if abs(variance) < 0.01:
                            status = 'متوازن'
                            status_type = 'balanced'
                            status_icon = '⚖️'
                            status_color = 'success'
                        elif variance > 0:
                            status = 'فائض'
                            status_type = 'surplus'
                            status_icon = '📈'
                            status_color = 'info'
                        else:
                            status = 'عجز'
                            status_type = 'deficit'
                            status_icon = '📉'
                            status_color = 'warning'

                        # تحديد مستوى الخطورة
                        abs_percentage = abs((variance / system_sales * 100) if system_sales > 0 else 0)
                        if abs_percentage < 1:
                            severity = 'طبيعي'
                            severity_level = 'low'
                        elif abs_percentage < 5:
                            severity = 'مقبول'
                            severity_level = 'medium'
                        else:
                            severity = 'يحتاج مراجعة'
                            severity_level = 'high'

                        filter_data['details']['variance'] = {
                            'system_sales': system_sales,
                            'actual_total': actual_total,
                            'difference': variance,
                            'percentage': (variance / system_sales * 100) if system_sales > 0 else 0,
                            'status': status,
                            'status_type': status_type,
                            'status_icon': status_icon,
                            'status_color': status_color,
                            'severity': severity,
                            'severity_level': severity_level,
                            'abs_difference': abs(variance),
                            'abs_percentage': abs_percentage
                        }

                        # إضافة تفاصيل إضافية إذا كانت متاحة
                        additional_fields = [
                            'expenses', 'adjustments', 'notes_details',
                            'credit_details', 'client_details', 'return_details',
                            'invoice_details', 'payment_methods', 'discounts'
                        ]
                        for key in additional_fields:
                            if key in details:
                                filter_data['details'][key] = details[key]

                        # إضافة معلومات إضافية للعرض
                        filter_data['details']['total_transactions'] = (
                            len(filter_data['details']['bank_transactions']) +
                            len(filter_data['details'].get('credit_details', [])) +
                            len(filter_data['details'].get('client_details', [])) +
                            len(filter_data['details'].get('return_details', []))
                        )

                    else:
                        filter_data['details'] = {
                            'bank_total': 0,
                            'cash_total': 0,
                            'credit_total': 0,
                            'client_total': 0,
                            'return_total': 0,
                            'grand_total': 0,
                            'bank_transactions': [],
                            'cash_details': {},
                        }

                except Exception as e:
                    print(f"خطأ في تحليل بيانات التصفية {filter_id}: {e}")
                    print(f"البيانات الخام: {filter_data.get('data', 'لا توجد بيانات')}")
                    filter_data['details'] = {
                        'bank_total': 0,
                        'cash_total': 0,
                        'credit_total': 0,
                        'client_total': 0,
                        'return_total': 0,
                        'grand_total': 0,
                        'bank_transactions': [],
                        'cash_details': {},
                        'error': f"خطأ في تحليل البيانات: {e}"
                    }

                # استخدام admin_name_full إذا كان متاحاً
                if filter_data.get('admin_name_full'):
                    filter_data['admin_name'] = filter_data['admin_name_full']

                print(f"تم جلب تفاصيل التصفية {filter_id} بنجاح")
                return filter_data

            return None

        except Exception as e:
            print(f"خطأ في جلب التصفية {filter_id}: {e}")
            return None
        finally:
            conn.close()
    
    def get_statistics(self):
        """الحصول على إحصائيات عامة"""
        conn = self.get_database_connection()
        if not conn:
            return {}
        
        try:
            cursor = conn.cursor()
            
            # إجمالي التصفيات
            cursor.execute("SELECT COUNT(*) as total FROM filters")
            total_filters = cursor.fetchone()['total']
            
            # التصفيات هذا الشهر
            current_month = datetime.now().strftime('%Y-%m')
            cursor.execute("SELECT COUNT(*) as monthly FROM filters WHERE date LIKE ?", (f"{current_month}%",))
            monthly_filters = cursor.fetchone()['monthly']
            
            # إجمالي المبالغ (تقريبي من البيانات المتاحة)
            cursor.execute("SELECT data FROM filters WHERE data IS NOT NULL")
            total_amount = 0
            for row in cursor.fetchall():
                try:
                    data = json.loads(row['data'])
                    # حساب تقريبي للمجموع
                    bank_total = data.get('bank_total', 0)
                    cash_total = data.get('cash_total', 0)
                    total_amount += float(bank_total) + float(cash_total)
                except:
                    continue
            
            return {
                'total_filters': total_filters,
                'monthly_filters': monthly_filters,
                'total_amount': round(total_amount, 2),
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات: {e}")
            return {}
        finally:
            conn.close()

    def get_all_credit_customers(self):
        """جلب جميع بيانات العملاء الآجل من كافة التصفيات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row

        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, date, data, admin_name,
                       (SELECT name FROM cashiers WHERE id = filters.cashier_id) as cashier_name
                FROM filters
                ORDER BY date DESC
            """)

            all_credit_customers = []
            total_credit_amount = 0

            for row in cursor.fetchall():
                try:
                    data = json.loads(row['data'])
                    details = data.get('details', {})

                    # معالجة تفاصيل المعاملات الآجلة مع أسماء العملاء
                    credit_transactions = details.get('credit_transactions', [])

                    for transaction in credit_transactions:
                        # البحث عن اسم العميل في حقول مختلفة
                        customer_name = (
                            transaction.get('customer_name') or
                            transaction.get('customer') or
                            transaction.get('client') or
                            transaction.get('name') or
                            'عميل غير محدد'
                        )

                        # البحث عن رقم الهاتف في حقول مختلفة
                        phone = (
                            transaction.get('phone') or
                            transaction.get('mobile') or
                            transaction.get('tel') or
                            ''
                        )

                        amount = float(transaction.get('amount', 0))
                        total_credit_amount += amount

                        credit_customer = {
                            'filter_id': row['id'],
                            'filter_date': row['date'],
                            'cashier_name': row['cashier_name'] or 'غير محدد',
                            'admin_name': row['admin_name'] or 'غير محدد',
                            'customer_name': customer_name,
                            'phone': phone,
                            'invoice_number': transaction.get('invoice') or transaction.get('invoice_number') or 'غير محدد',
                            'amount': amount,
                            'due_date': transaction.get('due_date') or transaction.get('date') or '',
                            'notes': transaction.get('notes') or transaction.get('note') or '',
                            'payment_method': transaction.get('payment_method') or 'آجل'
                        }

                        all_credit_customers.append(credit_customer)

                except (json.JSONDecodeError, KeyError) as e:
                    print(f"خطأ في معالجة بيانات التصفية {row['id']}: {e}")
                    continue

            # ترتيب العملاء حسب التاريخ (الأحدث أولاً)
            all_credit_customers.sort(key=lambda x: x['filter_date'], reverse=True)

            return {
                'customers': all_credit_customers,
                'total_customers': len(all_credit_customers),
                'total_amount': round(total_credit_amount, 2),
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            print(f"خطأ في جلب بيانات العملاء الآجل: {e}")
            return {
                'customers': [],
                'total_customers': 0,
                'total_amount': 0,
                'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        finally:
            conn.close()

# إنشاء مثيل الخادم
web_server = WebReportServer()

# المسارات (Routes)
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    filters = web_server.get_all_filters(10)  # آخر 10 تصفيات
    stats = web_server.get_statistics()
    return render_template('index.html', filters=filters, stats=stats)

@app.route('/reports')
def reports():
    """صفحة التقارير"""
    page = request.args.get('page', 1, type=int)
    limit = 20
    filters = web_server.get_all_filters(limit * page)
    return render_template('reports.html', filters=filters, page=page)

@app.route('/filter/<int:filter_id>')
def filter_detail(filter_id):
    """تفاصيل تصفية محددة"""
    filter_data = web_server.get_filter_by_id(filter_id)
    if not filter_data:
        return "التصفية غير موجودة", 404
    return render_template('filter_detail.html', filter=filter_data)

@app.route('/filter/<int:filter_id>/comprehensive')
def comprehensive_report(filter_id):
    """عرض التقرير الشامل لتصفية محددة"""
    # إنشاء خادم التقارير
    web_server = WebReportServer()
    filter_data = web_server.get_filter_by_id(filter_id)
    if not filter_data:
        return "التصفية غير موجودة", 404

    return render_template('comprehensive_report.html', filter=filter_data)

@app.route('/api/filters')
def api_filters():
    """API للحصول على التصفيات"""
    limit = request.args.get('limit', 50, type=int)
    filters = web_server.get_all_filters(limit)
    return jsonify(filters)

@app.route('/api/filter/<int:filter_id>')
def api_filter(filter_id):
    """API للحصول على تصفية محددة"""
    filter_data = web_server.get_filter_by_id(filter_id)
    if not filter_data:
        return jsonify({'error': 'التصفية غير موجودة'}), 404
    return jsonify(filter_data)

@app.route('/api/stats')
def api_stats():
    """API للحصول على الإحصائيات"""
    stats = web_server.get_statistics()
    return jsonify(stats)

@app.route('/mobile')
def mobile():
    """واجهة محسنة للهاتف"""
    filters = web_server.get_all_filters(20)
    stats = web_server.get_statistics()
    return render_template('mobile.html', filters=filters, stats=stats)

@app.route('/credit-customers')
def credit_customers_report():
    """تقرير العملاء الآجل الشامل"""
    # جلب جميع التصفيات التي تحتوي على عملاء آجل
    credit_data = web_server.get_all_credit_customers()
    return render_template('credit_customers_report.html', credit_data=credit_data)

@app.route('/api/credit-customers')
def api_credit_customers():
    """API للحصول على بيانات العملاء الآجل"""
    credit_data = web_server.get_all_credit_customers()
    return jsonify(credit_data)

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    TEMPLATES_DIR.mkdir(exist_ok=True)
    STATIC_DIR.mkdir(exist_ok=True)
    (STATIC_DIR / 'css').mkdir(exist_ok=True)
    (STATIC_DIR / 'js').mkdir(exist_ok=True)

def open_browser():
    """فتح المتصفح تلقائياً"""
    import time
    time.sleep(1.5)  # انتظار حتى يبدأ الخادم
    webbrowser.open(f'http://localhost:{web_server.port}')

def run_server(host='0.0.0.0', port=5000, debug=False, open_browser_auto=True):
    """تشغيل الخادم"""
    web_server.host = host
    web_server.port = port
    web_server.debug = debug
    
    print("=" * 60)
    print("🌐 خادم تقارير نظام تصفية الكاشير")
    print("=" * 60)
    print(f"🔗 الرابط المحلي: http://localhost:{port}")
    print(f"🔗 الرابط الشبكي: http://{host}:{port}")
    print(f"📱 واجهة الهاتف: http://localhost:{port}/mobile")
    print("=" * 60)
    print("💡 للوصول من الهاتف:")
    print("   1. تأكد من أن الهاتف والكمبيوتر على نفس الشبكة")
    print("   2. استخدم عنوان IP الكمبيوتر بدلاً من localhost")
    print("   3. مثال: http://*************:5000")
    print("=" * 60)
    print("⏹️  للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    
    # فتح المتصفح تلقائياً
    if open_browser_auto:
        threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        app.run(host=host, port=port, debug=debug, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    run_server()
