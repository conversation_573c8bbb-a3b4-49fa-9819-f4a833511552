# نظام الصلاحيات المتقدم
import sqlite3
import json
from enum import Enum
from functools import wraps
from tkinter import messagebox

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class Permission(Enum):
    """تعداد الصلاحيات المختلفة"""
    # صلاحيات التصفية
    CREATE_FILTER = "create_filter"
    VIEW_FILTER = "view_filter"
    EDIT_FILTER = "edit_filter"
    DELETE_FILTER = "delete_filter"
    
    # صلاحيات إدارة المستخدمين
    MANAGE_CASHIERS = "manage_cashiers"
    MANAGE_ADMINS = "manage_admins"
    VIEW_USERS = "view_users"
    
    # صلاحيات التقارير
    VIEW_REPORTS = "view_reports"
    EXPORT_REPORTS = "export_reports"
    ADVANCED_REPORTS = "advanced_reports"
    
    # صلاحيات النظام
    MANAGE_BACKUPS = "manage_backups"
    VIEW_STATISTICS = "view_statistics"
    MANAGE_SETTINGS = "manage_settings"
    VIEW_NOTIFICATIONS = "view_notifications"
    
    # صلاحيات إدارية
    SYSTEM_ADMIN = "system_admin"
    AUDIT_LOGS = "audit_logs"

class Role(Enum):
    """أدوار المستخدمين"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    CASHIER = "cashier"
    VIEWER = "viewer"

class PermissionManager:
    """مدير الصلاحيات"""
    
    def __init__(self):
        self.current_user = None
        self.current_role = None
        self.user_permissions = set()
        self.init_permissions_table()
        self.load_default_roles()
    
    def init_permissions_table(self):
        """تهيئة جدول الصلاحيات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # إنشاء جدول الأدوار
            c.execute('''
                CREATE TABLE IF NOT EXISTS roles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    display_name TEXT NOT NULL,
                    permissions TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول صلاحيات المستخدمين
            c.execute('''
                CREATE TABLE IF NOT EXISTS user_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL,
                    role TEXT NOT NULL,
                    custom_permissions TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تهيئة جدول الصلاحيات: {e}")
    
    def load_default_roles(self):
        """تحميل الأدوار الافتراضية"""
        default_roles = {
            Role.SUPER_ADMIN.value: {
                "display_name": "مدير النظام",
                "permissions": [p.value for p in Permission]  # جميع الصلاحيات
            },
            Role.ADMIN.value: {
                "display_name": "مدير",
                "permissions": [
                    Permission.CREATE_FILTER.value,
                    Permission.VIEW_FILTER.value,
                    Permission.EDIT_FILTER.value,
                    Permission.DELETE_FILTER.value,
                    Permission.MANAGE_CASHIERS.value,
                    Permission.VIEW_USERS.value,
                    Permission.VIEW_REPORTS.value,
                    Permission.EXPORT_REPORTS.value,
                    Permission.ADVANCED_REPORTS.value,
                    Permission.MANAGE_BACKUPS.value,
                    Permission.VIEW_STATISTICS.value,
                    Permission.VIEW_NOTIFICATIONS.value
                ]
            },
            Role.MANAGER.value: {
                "display_name": "مشرف",
                "permissions": [
                    Permission.CREATE_FILTER.value,
                    Permission.VIEW_FILTER.value,
                    Permission.EDIT_FILTER.value,
                    Permission.VIEW_USERS.value,
                    Permission.VIEW_REPORTS.value,
                    Permission.EXPORT_REPORTS.value,
                    Permission.VIEW_STATISTICS.value,
                    Permission.VIEW_NOTIFICATIONS.value
                ]
            },
            Role.CASHIER.value: {
                "display_name": "كاشير",
                "permissions": [
                    Permission.CREATE_FILTER.value,
                    Permission.VIEW_FILTER.value,
                    Permission.VIEW_NOTIFICATIONS.value
                ]
            },
            Role.VIEWER.value: {
                "display_name": "مشاهد",
                "permissions": [
                    Permission.VIEW_FILTER.value,
                    Permission.VIEW_REPORTS.value,
                    Permission.VIEW_NOTIFICATIONS.value
                ]
            }
        }
        
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            for role_name, role_data in default_roles.items():
                c.execute('''
                    INSERT OR REPLACE INTO roles (name, display_name, permissions)
                    VALUES (?, ?, ?)
                ''', (
                    role_name,
                    role_data["display_name"],
                    json.dumps(role_data["permissions"])
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل الأدوار الافتراضية: {e}")
    
    def set_current_user(self, username, role=None):
        """تعيين المستخدم الحالي"""
        self.current_user = username
        
        if role:
            self.current_role = role
        else:
            # البحث عن دور المستخدم في قاعدة البيانات
            self.current_role = self.get_user_role(username)
        
        # تحميل صلاحيات المستخدم
        self.load_user_permissions()
    
    def get_user_role(self, username):
        """الحصول على دور المستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            c.execute('SELECT role FROM user_permissions WHERE username = ?', (username,))
            result = c.fetchone()
            
            conn.close()
            
            if result:
                return result[0]
            else:
                # إذا لم يكن للمستخدم دور محدد، تعيين دور افتراضي
                return Role.VIEWER.value
                
        except Exception as e:
            print(f"خطأ في الحصول على دور المستخدم: {e}")
            return Role.VIEWER.value
    
    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم الحالي"""
        if not self.current_role:
            return
        
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # الحصول على صلاحيات الدور
            c.execute('SELECT permissions FROM roles WHERE name = ?', (self.current_role,))
            result = c.fetchone()
            
            if result:
                role_permissions = json.loads(result[0])
                self.user_permissions = set(role_permissions)
            
            # الحصول على الصلاحيات المخصصة للمستخدم
            c.execute('SELECT custom_permissions FROM user_permissions WHERE username = ?', (self.current_user,))
            result = c.fetchone()
            
            if result and result[0]:
                custom_permissions = json.loads(result[0])
                self.user_permissions.update(custom_permissions.get('added', []))
                self.user_permissions.difference_update(custom_permissions.get('removed', []))
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل صلاحيات المستخدم: {e}")
            self.user_permissions = set()
    
    def has_permission(self, permission):
        """التحقق من وجود صلاحية معينة"""
        if isinstance(permission, Permission):
            permission = permission.value
        
        return permission in self.user_permissions
    
    def assign_role_to_user(self, username, role):
        """تعيين دور لمستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            c.execute('''
                INSERT OR REPLACE INTO user_permissions (username, role, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (username, role))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"خطأ في تعيين الدور: {e}")
            return False
    
    def add_custom_permission(self, username, permission):
        """إضافة صلاحية مخصصة لمستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # الحصول على الصلاحيات المخصصة الحالية
            c.execute('SELECT custom_permissions FROM user_permissions WHERE username = ?', (username,))
            result = c.fetchone()
            
            if result and result[0]:
                custom_permissions = json.loads(result[0])
            else:
                custom_permissions = {"added": [], "removed": []}
            
            # إضافة الصلاحية الجديدة
            if permission not in custom_permissions["added"]:
                custom_permissions["added"].append(permission)
            
            # إزالة من قائمة المحذوفة إذا كانت موجودة
            if permission in custom_permissions["removed"]:
                custom_permissions["removed"].remove(permission)
            
            # تحديث قاعدة البيانات
            c.execute('''
                UPDATE user_permissions 
                SET custom_permissions = ?, updated_at = CURRENT_TIMESTAMP
                WHERE username = ?
            ''', (json.dumps(custom_permissions), username))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"خطأ في إضافة الصلاحية المخصصة: {e}")
            return False
    
    def remove_custom_permission(self, username, permission):
        """إزالة صلاحية مخصصة من مستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            # الحصول على الصلاحيات المخصصة الحالية
            c.execute('SELECT custom_permissions FROM user_permissions WHERE username = ?', (username,))
            result = c.fetchone()
            
            if result and result[0]:
                custom_permissions = json.loads(result[0])
            else:
                custom_permissions = {"added": [], "removed": []}
            
            # إضافة للقائمة المحذوفة
            if permission not in custom_permissions["removed"]:
                custom_permissions["removed"].append(permission)
            
            # إزالة من قائمة المضافة إذا كانت موجودة
            if permission in custom_permissions["added"]:
                custom_permissions["added"].remove(permission)
            
            # تحديث قاعدة البيانات
            c.execute('''
                UPDATE user_permissions 
                SET custom_permissions = ?, updated_at = CURRENT_TIMESTAMP
                WHERE username = ?
            ''', (json.dumps(custom_permissions), username))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"خطأ في إزالة الصلاحية المخصصة: {e}")
            return False
    
    def get_all_roles(self):
        """الحصول على جميع الأدوار"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            c.execute('SELECT name, display_name FROM roles ORDER BY display_name')
            roles = c.fetchall()
            
            conn.close()
            
            return roles
            
        except Exception as e:
            print(f"خطأ في الحصول على الأدوار: {e}")
            return []
    
    def get_role_permissions(self, role_name):
        """الحصول على صلاحيات دور معين"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            
            c.execute('SELECT permissions FROM roles WHERE name = ?', (role_name,))
            result = c.fetchone()
            
            conn.close()
            
            if result:
                return json.loads(result[0])
            else:
                return []
                
        except Exception as e:
            print(f"خطأ في الحصول على صلاحيات الدور: {e}")
            return []

# مثيل عام لمدير الصلاحيات
permission_manager = PermissionManager()

def require_permission(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if permission_manager.has_permission(permission):
                return func(*args, **kwargs)
            else:
                messagebox.showerror(
                    "غير مصرح",
                    f"ليس لديك صلاحية للوصول إلى هذه الميزة.\nالصلاحية المطلوبة: {permission.value if isinstance(permission, Permission) else permission}"
                )
                return None
        return wrapper
    return decorator

def set_current_user(username, role=None):
    """تعيين المستخدم الحالي"""
    permission_manager.set_current_user(username, role)

def has_permission(permission):
    """التحقق من وجود صلاحية"""
    return permission_manager.has_permission(permission)

def get_current_user():
    """الحصول على المستخدم الحالي"""
    return permission_manager.current_user

def get_current_role():
    """الحصول على دور المستخدم الحالي"""
    return permission_manager.current_role

# نافذة إدارة الصلاحيات
import customtkinter as ctk
from tkinter import ttk, messagebox

class PermissionsManagerWindow(ctk.CTkToplevel):
    """نافذة إدارة الصلاحيات"""

    def __init__(self, master=None):
        super().__init__(master)
        self.title("إدارة الصلاحيات والأدوار")
        self.geometry("1000x700")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        self.create_widgets()
        self.load_data()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""

        # العنوان
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")

        title_label = ctk.CTkLabel(
            header_frame,
            text="🔐 إدارة الصلاحيات والأدوار",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # دفتر التبويبات
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(pady=10, padx=20, fill="both", expand=True)

        # تبويب المستخدمين
        self.users_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.users_frame, text="المستخدمين والأدوار")

        # تبويب الأدوار
        self.roles_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.roles_frame, text="إدارة الأدوار")

        # تبويب الصلاحيات
        self.permissions_frame = ctk.CTkFrame(self.notebook, fg_color="#f2f3f7")
        self.notebook.add(self.permissions_frame, text="الصلاحيات المخصصة")

        self.create_users_tab()
        self.create_roles_tab()
        self.create_permissions_tab()

    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""

        # جدول المستخدمين
        users_container = ctk.CTkFrame(self.users_frame, fg_color="#f2f3f7", corner_radius=0)
        users_container.pack(fill="both", expand=True, padx=10, pady=10)

        users_title = ctk.CTkLabel(
            users_container,
            text="👥 المستخدمين والأدوار المعينة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        users_title.pack(pady=15)

        # أزرار التحكم
        controls_frame = ctk.CTkFrame(users_container, fg_color="#f2f3f7", corner_radius=0)
        controls_frame.pack(fill="x", padx=20, pady=10)

        assign_role_btn = ctk.CTkButton(
            controls_frame,
            text="تعيين دور",
            command=self.assign_role_dialog,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=120,
            height=35
        )
        assign_role_btn.pack(side="left", padx=10)

        edit_permissions_btn = ctk.CTkButton(
            controls_frame,
            text="✏️ تعديل الصلاحيات",
            command=self.edit_user_permissions,
            fg_color="#3498db",
            hover_color="#2980b9",
            width=150,
            height=35
        )
        edit_permissions_btn.pack(side="left", padx=5)

        view_permissions_btn = ctk.CTkButton(
            controls_frame,
            text="👁️ عرض الصلاحيات",
            command=self.view_user_permissions,
            fg_color="#9C27B0",
            hover_color="#7B1FA2",
            width=150,
            height=35
        )
        view_permissions_btn.pack(side="left", padx=5)

        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث",
            command=self.load_users_data,
            fg_color="#27ae60",
            hover_color="#229954",
            width=100,
            height=35
        )
        refresh_btn.pack(side="right", padx=5)

        # جدول المستخدمين
        columns = ("اسم المستخدم", "نوع المستخدم", "الاسم الكامل", "الدور المعين", "تاريخ آخر تعديل")
        self.users_tree = ttk.Treeview(users_container, columns=columns, show="headings", height=15)

        # تخصيص عرض الأعمدة
        column_widths = {
            "اسم المستخدم": 150,
            "نوع المستخدم": 100,
            "الاسم الكامل": 180,
            "الدور المعين": 120,
            "تاريخ آخر تعديل": 200
        }

        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=column_widths.get(col, 150), anchor="center")

        scrollbar_users = ttk.Scrollbar(users_container, orient="vertical", command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=scrollbar_users.set)

        self.users_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_users.pack(side="right", fill="y", pady=10)

    def create_roles_tab(self):
        """إنشاء تبويب الأدوار"""

        roles_container = ctk.CTkFrame(self.roles_frame, fg_color="#f2f3f7", corner_radius=0)
        roles_container.pack(fill="both", expand=True, padx=10, pady=10)

        roles_title = ctk.CTkLabel(
            roles_container,
            text="🎭 الأدوار وصلاحياتها",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        roles_title.pack(pady=15)

        # جدول الأدوار
        columns = ("اسم الدور", "الاسم المعروض", "عدد الصلاحيات", "تاريخ الإنشاء")
        self.roles_tree = ttk.Treeview(roles_container, columns=columns, show="headings", height=15)

        for col in columns:
            self.roles_tree.heading(col, text=col)
            self.roles_tree.column(col, width=150, anchor="center")

        scrollbar_roles = ttk.Scrollbar(roles_container, orient="vertical", command=self.roles_tree.yview)
        self.roles_tree.configure(yscrollcommand=scrollbar_roles.set)

        self.roles_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_roles.pack(side="right", fill="y", pady=10)

    def create_permissions_tab(self):
        """إنشاء تبويب الصلاحيات المخصصة"""

        permissions_container = ctk.CTkFrame(self.permissions_frame, fg_color="#f2f3f7", corner_radius=0)
        permissions_container.pack(fill="both", expand=True, padx=10, pady=10)

        permissions_title = ctk.CTkLabel(
            permissions_container,
            text="🔑 الصلاحيات المخصصة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        permissions_title.pack(pady=15)

        # قائمة الصلاحيات المتاحة
        available_permissions = [
            ("إنشاء تصفية", Permission.CREATE_FILTER.value),
            ("عرض التصفيات", Permission.VIEW_FILTER.value),
            ("تعديل التصفيات", Permission.EDIT_FILTER.value),
            ("حذف التصفيات", Permission.DELETE_FILTER.value),
            ("إدارة الكاشيرين", Permission.MANAGE_CASHIERS.value),
            ("إدارة المسؤولين", Permission.MANAGE_ADMINS.value),
            ("عرض التقارير", Permission.VIEW_REPORTS.value),
            ("تصدير التقارير", Permission.EXPORT_REPORTS.value),
            ("التقارير المتقدمة", Permission.ADVANCED_REPORTS.value),
            ("إدارة النسخ الاحتياطي", Permission.MANAGE_BACKUPS.value),
            ("عرض الإحصائيات", Permission.VIEW_STATISTICS.value),
            ("إدارة الإعدادات", Permission.MANAGE_SETTINGS.value),
            ("عرض الإشعارات", Permission.VIEW_NOTIFICATIONS.value),
            ("إدارة النظام", Permission.SYSTEM_ADMIN.value)
        ]

        permissions_info = ctk.CTkLabel(
            permissions_container,
            text="الصلاحيات المتاحة في النظام:",
            font=("Arial", 14, "bold"),
            text_color="#34495e"
        )
        permissions_info.pack(pady=10)

        # عرض الصلاحيات في شبكة
        permissions_grid = ctk.CTkFrame(permissions_container, fg_color="#ffffff", corner_radius=10)
        permissions_grid.pack(fill="both", expand=True, padx=20, pady=10)

        row = 0
        col = 0
        for display_name, permission_value in available_permissions:
            permission_label = ctk.CTkLabel(
                permissions_grid,
                text=f"• {display_name}",
                font=("Arial", 12),
                text_color="#2c3e50"
            )
            permission_label.grid(row=row, column=col, padx=20, pady=5, sticky="w")

            col += 1
            if col >= 2:
                col = 0
                row += 1

    def load_data(self):
        """تحميل البيانات"""
        self.load_users_data()
        self.load_roles_data()

    def load_users_data(self):
        """تحميل بيانات المستخدمين من جداول المسؤولين والكاشيرين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # جلب المسؤولين
            c.execute('''
                SELECT
                    a.username,
                    'مسؤول' as user_type,
                    a.name,
                    COALESCE(up.role, 'admin') as role,
                    COALESCE(r.display_name, 'مسؤول') as role_display,
                    up.updated_at
                FROM admins a
                LEFT JOIN user_permissions up ON a.username = up.username
                LEFT JOIN roles r ON up.role = r.name
            ''')

            admins_data = c.fetchall()

            # جلب الكاشيرين
            c.execute('''
                SELECT
                    c.name as username,
                    'كاشير' as user_type,
                    c.name,
                    COALESCE(up.role, 'cashier') as role,
                    COALESCE(r.display_name, 'كاشير') as role_display,
                    up.updated_at
                FROM cashiers c
                LEFT JOIN user_permissions up ON c.name = up.username
                LEFT JOIN roles r ON up.role = r.name
            ''')

            cashiers_data = c.fetchall()

            # دمج البيانات وعرضها
            all_users = []

            # إضافة المسؤولين
            for username, user_type, name, role, role_display, updated_at in admins_data:
                all_users.append((
                    username,
                    user_type,
                    name,
                    role,
                    role_display,
                    updated_at or "لم يتم تعديل الصلاحيات"
                ))

            # إضافة الكاشيرين
            for username, user_type, name, role, role_display, updated_at in cashiers_data:
                all_users.append((
                    username,
                    user_type,
                    name,
                    role,
                    role_display,
                    updated_at or "لم يتم تعديل الصلاحيات"
                ))

            # ترتيب المستخدمين حسب النوع ثم الاسم
            all_users.sort(key=lambda x: (x[1], x[0]))

            # إضافة المستخدمين للجدول
            for username, user_type, name, role, role_display, updated_at in all_users:
                self.users_tree.insert("", "end", values=(
                    username,
                    user_type,
                    name,
                    role_display,
                    updated_at
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستخدمين: {e}")

    def load_roles_data(self):
        """تحميل بيانات الأدوار"""
        try:
            # مسح البيانات الحالية
            for item in self.roles_tree.get_children():
                self.roles_tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            c.execute('SELECT name, display_name, permissions, created_at FROM roles ORDER BY display_name')
            roles_data = c.fetchall()

            for name, display_name, permissions_json, created_at in roles_data:
                try:
                    permissions = json.loads(permissions_json)
                    permissions_count = len(permissions)
                except:
                    permissions_count = 0

                self.roles_tree.insert("", "end", values=(
                    name,
                    display_name,
                    permissions_count,
                    created_at
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الأدوار: {e}")

    def assign_role_dialog(self):
        """حوار تعيين دور لمستخدم"""
        dialog = AssignRoleDialog(self)
        if dialog.result:
            self.load_users_data()

    def edit_user_permissions(self):
        """تعديل صلاحيات مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتعديل صلاحياته")
            return

        username = self.users_tree.item(selected[0])["values"][0]
        dialog = EditPermissionsDialog(self, username)
        if dialog.result:
            self.load_users_data()

    def view_user_permissions(self):
        """عرض صلاحيات مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لعرض صلاحياته")
            return

        username = self.users_tree.item(selected[0])["values"][0]
        dialog = ViewPermissionsDialog(self, username)

class AssignRoleDialog(ctk.CTkToplevel):
    """حوار تعيين الأدوار"""

    def __init__(self, parent):
        super().__init__(parent)
        self.title("تعيين دور")
        self.geometry("400x300")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        self.result = None
        self.create_widgets()

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

    def create_widgets(self):
        """إنشاء عناصر الحوار"""

        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text="تعيين دور لمستخدم",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # اختيار المستخدم
        username_frame = ctk.CTkFrame(self, fg_color="#f2f3f7", corner_radius=0)
        username_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(username_frame, text="اختر المستخدم:", font=("Arial", 12, "bold")).pack(anchor="w")

        # تحميل المستخدمين المتاحين
        self.load_available_users()

        self.username_combo = ctk.CTkComboBox(
            username_frame,
            values=self.available_users,
            width=300,
            state="readonly"
        )
        self.username_combo.pack(fill="x", pady=5)

        # الدور
        role_frame = ctk.CTkFrame(self, fg_color="#f2f3f7", corner_radius=0)
        role_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(role_frame, text="الدور:", font=("Arial", 12, "bold")).pack(anchor="w")

        # تحميل الأدوار المتاحة
        roles = permission_manager.get_all_roles()
        role_values = [f"{display_name} ({name})" for name, display_name in roles]

        self.role_combo = ctk.CTkComboBox(role_frame, values=role_values, width=300)
        self.role_combo.pack(fill="x", pady=5)

        # أزرار الحوار
        buttons_frame = ctk.CTkFrame(self, fg_color="#f2f3f7", corner_radius=0)
        buttons_frame.pack(fill="x", padx=20, pady=20)

        save_btn = ctk.CTkButton(
            buttons_frame,
            text="حفظ",
            command=self.save_assignment,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=100
        )
        save_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="إلغاء",
            command=self.destroy,
            fg_color="#f44336",
            hover_color="#d32f2f",
            width=100
        )
        cancel_btn.pack(side="right", padx=10)

    def load_available_users(self):
        """تحميل المستخدمين المتاحين من جداول المسؤولين والكاشيرين"""
        self.available_users = []
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # جلب المسؤولين
            c.execute("SELECT username, name FROM admins ORDER BY name")
            admins = c.fetchall()

            # جلب الكاشيرين
            c.execute("SELECT name, name FROM cashiers ORDER BY name")
            cashiers = c.fetchall()

            # تجميع المستخدمين
            for username, name in admins:
                self.available_users.append(f"{name} ({username}) - مسؤول")

            for username, name in cashiers:
                self.available_users.append(f"{name} ({username}) - كاشير")

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")
            self.available_users = ["لا توجد مستخدمين متاحين"]

    def save_assignment(self):
        """حفظ تعيين الدور"""
        user_selection = self.username_combo.get()
        role_selection = self.role_combo.get()

        if not user_selection or not role_selection:
            messagebox.showwarning("تحذير", "يرجى اختيار المستخدم والدور")
            return

        if user_selection == "لا توجد مستخدمين متاحين":
            messagebox.showwarning("تحذير", "لا توجد مستخدمين متاحين")
            return

        # استخراج اسم المستخدم من النص
        try:
            # تنسيق: "الاسم (اسم_المستخدم) - النوع"
            username = user_selection.split("(")[1].split(")")[0]
        except:
            messagebox.showerror("خطأ", "تنسيق اسم المستخدم غير صحيح")
            return

        # استخراج اسم الدور من النص
        try:
            role_name = role_selection.split("(")[1].split(")")[0]
        except:
            messagebox.showerror("خطأ", "تنسيق الدور غير صحيح")
            return

        if permission_manager.assign_role_to_user(username, role_name):
            messagebox.showinfo("نجح", f"تم تعيين الدور '{role_name}' للمستخدم '{username}' بنجاح")
            self.result = True
            self.destroy()
        else:
            messagebox.showerror("خطأ", "فشل في تعيين الدور")

class EditPermissionsDialog(ctk.CTkToplevel):
    """حوار تعديل صلاحيات المستخدم"""

    def __init__(self, parent, username):
        super().__init__(parent)
        self.username = username
        self.title(f"تعديل صلاحيات المستخدم: {username}")
        self.geometry("600x700")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        self.result = None
        self.permission_vars = {}
        self.create_widgets()
        self.load_user_permissions()

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

    def create_widgets(self):
        """إنشاء عناصر الحوار"""

        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text=f"تعديل صلاحيات المستخدم: {self.username}",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # إطار التمرير
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self,
            width=550,
            height=500,
            fg_color="#ffffff",
            corner_radius=15
        )
        self.scrollable_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # عنوان الصلاحيات
        permissions_title = ctk.CTkLabel(
            self.scrollable_frame,
            text="🔐 الصلاحيات المتاحة",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        permissions_title.pack(pady=15)

        # تجميع الصلاحيات حسب الفئة
        permission_groups = {
            "صلاحيات التصفية": [
                ("إنشاء تصفية", Permission.CREATE_FILTER.value),
                ("عرض التصفية", Permission.VIEW_FILTER.value),
                ("تعديل التصفية", Permission.EDIT_FILTER.value),
                ("حذف التصفية", Permission.DELETE_FILTER.value),
            ],
            "صلاحيات إدارة المستخدمين": [
                ("إدارة الكاشيرين", Permission.MANAGE_CASHIERS.value),
                ("إدارة المسؤولين", Permission.MANAGE_ADMINS.value),
                ("عرض المستخدمين", Permission.VIEW_USERS.value),
            ],
            "صلاحيات التقارير": [
                ("عرض التقارير", Permission.VIEW_REPORTS.value),
                ("تصدير التقارير", Permission.EXPORT_REPORTS.value),
                ("التقارير المتقدمة", Permission.ADVANCED_REPORTS.value),
            ],
            "صلاحيات النظام": [
                ("إدارة النسخ الاحتياطية", Permission.MANAGE_BACKUPS.value),
                ("عرض الإحصائيات", Permission.VIEW_STATISTICS.value),
                ("إدارة الإعدادات", Permission.MANAGE_SETTINGS.value),
                ("عرض الإشعارات", Permission.VIEW_NOTIFICATIONS.value),
            ],
            "صلاحيات إدارية": [
                ("إدارة النظام", Permission.SYSTEM_ADMIN.value),
                ("سجلات المراجعة", Permission.AUDIT_LOGS.value),
            ]
        }

        # إنشاء مجموعات الصلاحيات
        for group_name, permissions in permission_groups.items():
            self.create_permission_group(group_name, permissions)

        # إطار الأزرار
        buttons_frame = ctk.CTkFrame(self, fg_color="#ecf0f1", corner_radius=15)
        buttons_frame.pack(pady=15, padx=20, fill="x")

        # زر الحفظ
        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ التغييرات",
            command=self.save_permissions,
            fg_color="#27ae60",
            hover_color="#229954",
            width=150,
            height=40,
            font=("Arial", 14, "bold")
        )
        save_btn.pack(side="left", padx=10, pady=15)

        # زر الإلغاء
        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.destroy,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=100,
            height=40,
            font=("Arial", 14, "bold")
        )
        cancel_btn.pack(side="right", padx=10, pady=15)

    def create_permission_group(self, group_name, permissions):
        """إنشاء مجموعة صلاحيات"""

        # إطار المجموعة
        group_frame = ctk.CTkFrame(self.scrollable_frame, fg_color="#f8f9fa", corner_radius=10)
        group_frame.pack(fill="x", padx=10, pady=10)

        # عنوان المجموعة
        group_title = ctk.CTkLabel(
            group_frame,
            text=group_name,
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        group_title.pack(pady=10)

        # الصلاحيات
        for display_name, permission_value in permissions:
            permission_frame = ctk.CTkFrame(group_frame, fg_color="transparent")
            permission_frame.pack(fill="x", padx=20, pady=5)

            # متغير الصلاحية
            var = ctk.BooleanVar()
            self.permission_vars[permission_value] = var

            # مربع الاختيار
            checkbox = ctk.CTkCheckBox(
                permission_frame,
                text=display_name,
                variable=var,
                font=("Arial", 12),
                text_color="#34495e"
            )
            checkbox.pack(side="left")

    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم الحالية"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # الحصول على دور المستخدم والصلاحيات المخصصة
            c.execute('''
                SELECT role, custom_permissions
                FROM user_permissions
                WHERE username = ?
            ''', (self.username,))

            result = c.fetchone()
            if result:
                role, custom_permissions_json = result

                # تحميل صلاحيات الدور
                if role:
                    c.execute('SELECT permissions FROM roles WHERE name = ?', (role,))
                    role_result = c.fetchone()
                    if role_result:
                        role_permissions = json.loads(role_result[0])
                        for permission in role_permissions:
                            if permission in self.permission_vars:
                                self.permission_vars[permission].set(True)

                # تحميل الصلاحيات المخصصة
                if custom_permissions_json:
                    custom_permissions = json.loads(custom_permissions_json)

                    # إضافة الصلاحيات المضافة
                    for permission in custom_permissions.get("added", []):
                        if permission in self.permission_vars:
                            self.permission_vars[permission].set(True)

                    # إزالة الصلاحيات المحذوفة
                    for permission in custom_permissions.get("removed", []):
                        if permission in self.permission_vars:
                            self.permission_vars[permission].set(False)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل صلاحيات المستخدم: {e}")

    def save_permissions(self):
        """حفظ الصلاحيات المحدثة"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # الحصول على الصلاحيات الحالية للمستخدم
            c.execute('''
                SELECT role, custom_permissions
                FROM user_permissions
                WHERE username = ?
            ''', (self.username,))

            result = c.fetchone()
            current_role = None
            current_custom = {"added": [], "removed": []}

            if result:
                current_role = result[0]
                if result[1]:
                    current_custom = json.loads(result[1])

            # الحصول على صلاحيات الدور الأساسية
            role_permissions = set()
            if current_role:
                c.execute('SELECT permissions FROM roles WHERE name = ?', (current_role,))
                role_result = c.fetchone()
                if role_result:
                    role_permissions = set(json.loads(role_result[0]))

            # تحديد الصلاحيات المختارة حالياً
            selected_permissions = set()
            for permission, var in self.permission_vars.items():
                if var.get():
                    selected_permissions.add(permission)

            # حساب الصلاحيات المخصصة الجديدة
            new_custom = {"added": [], "removed": []}

            # الصلاحيات المضافة (موجودة في المختارة وليست في الدور)
            for permission in selected_permissions:
                if permission not in role_permissions:
                    new_custom["added"].append(permission)

            # الصلاحيات المحذوفة (موجودة في الدور وليست في المختارة)
            for permission in role_permissions:
                if permission not in selected_permissions:
                    new_custom["removed"].append(permission)

            # تحديث قاعدة البيانات
            if result:
                # تحديث المستخدم الموجود
                c.execute('''
                    UPDATE user_permissions
                    SET custom_permissions = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE username = ?
                ''', (json.dumps(new_custom), self.username))
            else:
                # إنشاء سجل جديد للمستخدم
                c.execute('''
                    INSERT INTO user_permissions (username, role, custom_permissions, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (self.username, None, json.dumps(new_custom)))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم تحديث صلاحيات المستخدم '{self.username}' بنجاح!")
            self.result = True
            self.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الصلاحيات: {e}")

class ViewPermissionsDialog(ctk.CTkToplevel):
    """حوار عرض صلاحيات المستخدم"""

    def __init__(self, parent, username):
        super().__init__(parent)
        self.username = username
        self.title(f"صلاحيات المستخدم: {username}")
        self.geometry("500x600")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)

        self.create_widgets()
        self.load_user_permissions()

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

    def create_widgets(self):
        """إنشاء عناصر الحوار"""

        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text=f"صلاحيات المستخدم: {self.username}",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # إطار المعلومات
        info_frame = ctk.CTkFrame(self, fg_color="#3498db", corner_radius=15)
        info_frame.pack(pady=10, padx=20, fill="x")

        self.role_label = ctk.CTkLabel(
            info_frame,
            text="الدور: غير محدد",
            font=("Arial", 14, "bold"),
            text_color="white"
        )
        self.role_label.pack(pady=10)

        # إطار التمرير للصلاحيات
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self,
            width=450,
            height=400,
            fg_color="#ffffff",
            corner_radius=15
        )
        self.scrollable_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # عنوان الصلاحيات
        permissions_title = ctk.CTkLabel(
            self.scrollable_frame,
            text="🔐 الصلاحيات الممنوحة",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        permissions_title.pack(pady=15)

        # إطار قائمة الصلاحيات
        self.permissions_list_frame = ctk.CTkFrame(
            self.scrollable_frame,
            fg_color="#f8f9fa",
            corner_radius=10
        )
        self.permissions_list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # زر الإغلاق
        close_btn = ctk.CTkButton(
            self,
            text="❌ إغلاق",
            command=self.destroy,
            fg_color="#95a5a6",
            hover_color="#7f8c8d",
            width=100,
            height=40,
            font=("Arial", 14, "bold")
        )
        close_btn.pack(pady=15)

    def load_user_permissions(self):
        """تحميل وعرض صلاحيات المستخدم"""
        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # الحصول على دور المستخدم والصلاحيات المخصصة
            c.execute('''
                SELECT role, custom_permissions
                FROM user_permissions
                WHERE username = ?
            ''', (self.username,))

            result = c.fetchone()
            user_permissions = set()
            user_role = "غير محدد"

            if result:
                role, custom_permissions_json = result
                user_role = role or "غير محدد"

                # تحديث عرض الدور
                self.role_label.configure(text=f"الدور: {user_role}")

                # تحميل صلاحيات الدور
                if role:
                    c.execute('SELECT permissions FROM roles WHERE name = ?', (role,))
                    role_result = c.fetchone()
                    if role_result:
                        role_permissions = json.loads(role_result[0])
                        user_permissions.update(role_permissions)

                # تحميل الصلاحيات المخصصة
                if custom_permissions_json:
                    custom_permissions = json.loads(custom_permissions_json)

                    # إضافة الصلاحيات المضافة
                    user_permissions.update(custom_permissions.get("added", []))

                    # إزالة الصلاحيات المحذوفة
                    for permission in custom_permissions.get("removed", []):
                        user_permissions.discard(permission)

            # عرض الصلاحيات
            self.display_permissions(user_permissions)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل صلاحيات المستخدم: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل صلاحيات المستخدم: {e}")

    def display_permissions(self, permissions):
        """عرض قائمة الصلاحيات"""

        # مسح المحتوى السابق
        for widget in self.permissions_list_frame.winfo_children():
            widget.destroy()

        if not permissions:
            no_permissions_label = ctk.CTkLabel(
                self.permissions_list_frame,
                text="❌ لا توجد صلاحيات ممنوحة",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            no_permissions_label.pack(pady=20)
            return

        # تجميع الصلاحيات حسب الفئة
        permission_names = {
            Permission.CREATE_FILTER.value: "إنشاء تصفية",
            Permission.VIEW_FILTER.value: "عرض التصفية",
            Permission.EDIT_FILTER.value: "تعديل التصفية",
            Permission.DELETE_FILTER.value: "حذف التصفية",
            Permission.MANAGE_CASHIERS.value: "إدارة الكاشيرين",
            Permission.MANAGE_ADMINS.value: "إدارة المسؤولين",
            Permission.VIEW_USERS.value: "عرض المستخدمين",
            Permission.VIEW_REPORTS.value: "عرض التقارير",
            Permission.EXPORT_REPORTS.value: "تصدير التقارير",
            Permission.ADVANCED_REPORTS.value: "التقارير المتقدمة",
            Permission.MANAGE_BACKUPS.value: "إدارة النسخ الاحتياطية",
            Permission.VIEW_STATISTICS.value: "عرض الإحصائيات",
            Permission.MANAGE_SETTINGS.value: "إدارة الإعدادات",
            Permission.VIEW_NOTIFICATIONS.value: "عرض الإشعارات",
            Permission.SYSTEM_ADMIN.value: "إدارة النظام",
            Permission.AUDIT_LOGS.value: "سجلات المراجعة",
        }

        # عرض الصلاحيات
        for permission in sorted(permissions):
            permission_name = permission_names.get(permission, permission)

            permission_frame = ctk.CTkFrame(
                self.permissions_list_frame,
                fg_color="#ffffff",
                corner_radius=8
            )
            permission_frame.pack(fill="x", padx=10, pady=5)

            # أيقونة وتسمية الصلاحية
            permission_label = ctk.CTkLabel(
                permission_frame,
                text=f"✅ {permission_name}",
                font=("Arial", 12),
                text_color="#27ae60"
            )
            permission_label.pack(side="left", padx=15, pady=10)

def show_permissions_manager(parent=None):
    """عرض نافذة إدارة الصلاحيات"""
    PermissionsManagerWindow(parent)
