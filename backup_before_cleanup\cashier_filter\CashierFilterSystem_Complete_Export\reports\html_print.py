# توليد تقرير HTML محسن للطباعة عبر المتصفح
import json
import webbrowser
import os
from datetime import datetime

def generate_html_report(filter_data, totals, system_sales, filename):
    # استخراج معلومات التصفية
    filter_info = filter_data if isinstance(filter_data, dict) else {}
    if not isinstance(totals, dict):
        totals = {}
    if not isinstance(system_sales, (int, float)):
        system_sales = 0

    html = f'''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير تصفية الكاشير</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}

            @page {{
                size: A4;
                margin: 15mm;
            }}

            body {{
                font-family: 'Tajawal', Arial, sans-serif;
                background: white;
                color: #2c3e50;
                line-height: 1.4;
                font-size: 12px;
            }}

            .container {{
                max-width: 100%;
                margin: 0 auto;
                background: white;
            }}

            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px;
                text-align: center;
                margin-bottom: 15px;
            }}

            .header h1 {{
                font-size: 1.8em;
                margin-bottom: 5px;
                font-weight: 700;
            }}

            .header .subtitle {{
                font-size: 1em;
                opacity: 0.9;
            }}

            .info-section {{
                background: #ecf0f1;
                padding: 10px;
                margin-bottom: 10px;
            }}

            .info-grid {{
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 10px;
                margin-bottom: 10px;
            }}

            .info-item {{
                background: white;
                padding: 8px;
                border-radius: 5px;
                font-size: 11px;
            }}

            .info-label {{
                font-weight: bold;
                color: #34495e;
                margin-bottom: 3px;
            }}

            .info-value {{
                color: #2c3e50;
                font-size: 1em;
            }}

            .section {{
                padding: 8px;
                margin-bottom: 8px;
            }}

            .section:last-child {{
                border-bottom: none;
            }}

            .section-title {{
                background: #3498db;
                color: white;
                padding: 6px 10px;
                border-radius: 4px;
                margin-bottom: 8px;
                font-size: 1.1em;
                font-weight: bold;
                display: inline-block;
            }}

            .table-container {{
                overflow-x: auto;
                margin: 8px 0;
            }}

            table {{
                width: 100%;
                border-collapse: collapse;
                background: white;
                font-size: 10px;
            }}

            th {{
                background: #34495e;
                color: white;
                padding: 6px 4px;
                text-align: center;
                font-weight: bold;
                font-size: 10px;
            }}

            td {{
                padding: 4px;
                text-align: center;
                border: 1px solid #ddd;
                font-size: 10px;
            }}

            tr:nth-child(even) {{
                background: #f8f9fa;
            }}

            tr:hover {{
                background: #e3f2fd;
            }}

            .cash-grid {{
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
                margin: 20px 0;
            }}

            .cash-item {{
                background: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                text-align: center;
            }}

            .cash-denomination {{
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 8px;
            }}

            .cash-count {{
                font-size: 1.2em;
                color: #27ae60;
            }}

            .summary-section {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                text-align: center;
            }}

            .summary-title {{
                font-size: 2em;
                margin-bottom: 25px;
                font-weight: bold;
            }}

            .summary-grid {{
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                margin-bottom: 25px;
            }}

            .summary-item {{
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 10px;
                backdrop-filter: blur(10px);
            }}

            .summary-label {{
                font-size: 1.1em;
                margin-bottom: 8px;
                opacity: 0.9;
            }}

            .summary-value {{
                font-size: 1.5em;
                font-weight: bold;
            }}

            .final-result {{
                background: rgba(255,255,255,0.2);
                padding: 25px;
                border-radius: 15px;
                margin-top: 20px;
            }}

            .difference {{
                font-size: 2.2em;
                font-weight: bold;
                margin-bottom: 10px;
            }}

            .status {{
                font-size: 1.4em;
                font-weight: bold;
            }}

            .status.surplus {{
                color: #2ecc71;
            }}

            .status.deficit {{
                color: #e74c3c;
            }}

            .status.balanced {{
                color: #f39c12;
            }}

            .footer {{
                background: #2c3e50;
                color: white;
                padding: 20px;
                text-align: center;
            }}

            @media print {{
                body {{
                    background: white;
                    padding: 0;
                }}

                .container {{
                    box-shadow: none;
                    border-radius: 0;
                }}

                .section {{
                    page-break-inside: avoid;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📊 تقرير تصفية الكاشير - رقم {filter_info.get('sequence_number', 'غير محدد')}</h1>
                <div class="subtitle">نظام إدارة التصفية اليومية</div>
            </div>

            <div class="info-section">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">🔢 الرقم التسلسلي:</div>
                        <div class="info-value">{filter_info.get('sequence_number', 'غير محدد')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">👤 اسم الكاشير:</div>
                        <div class="info-value">{filter_info.get('cashier_name', 'غير محدد')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">🆔 رقم الكاشير:</div>
                        <div class="info-value">{filter_info.get('cashier_number', filter_info.get('cashier_id', 'غير محدد'))}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">🧑‍💼 المسؤول:</div>
                        <div class="info-value">{filter_info.get('admin_name', 'غير محدد')}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">📅 تاريخ التصفية:</div>
                        <div class="info-value">{filter_info.get('date', datetime.now().strftime('%Y-%m-%d'))}</div>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label">🕐 وقت إنشاء التقرير:</div>
                    <div class="info-value">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
                </div>
            </div>
    '''

    # الأقسام المطلوبة فقط

    # 1. قسم فواتير عملاء الآجل
    html += '''
        <div class="section">
            <div class="section-title">📋 فواتير عملاء الآجل</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>رقم الفاتورة</th>
                            <th>مبلغ الفاتورة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
    '''

    # بيانات فواتير الآجل من التصفية
    credit_invoices = filter_info.get('credit_transactions', [])
    credit_total = 0

    if credit_invoices:
        for invoice in credit_invoices:
            amount = float(invoice.get('amount', 0))
            credit_total += amount
            html += f'''
                <tr>
                    <td>{invoice.get('client', '')}</td>
                    <td>{invoice.get('invoice', '')}</td>
                    <td>{amount:.2f}</td>
                    <td>{invoice.get('date', '')}</td>
                </tr>
            '''
    else:
        html += '''
            <tr>
                <td colspan="4" style="text-align: center; color: #7f8c8d; font-style: italic;">لا توجد فواتير آجل</td>
            </tr>
        '''

    html += f'''
                    </tbody>
                </table>
            </div>
            <div style="text-align: left; font-weight: bold; color: #27ae60; margin-top: 8px;">
                إجمالي الآجل: {totals.get('credit', credit_total):.2f} ريال
            </div>
        </div>
    '''

    # 2. قسم المقبوضات من العملاء
    html += '''
        <div class="section">
            <div class="section-title">👥 المقبوضات من العملاء</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>نوع المقبوض</th>
                            <th>طريقة الدفع</th>
                            <th>المبلغ المدفوع</th>
                            <th>رقم المرجع</th>
                        </tr>
                    </thead>
                    <tbody>
    '''

    # بيانات المقبوضات من العملاء من التصفية
    client_payments = filter_info.get('client_transactions', [])
    client_total = 0

    if client_payments:
        for payment in client_payments:
            amount = float(payment.get('amount', 0))
            client_total += amount

            # تحديد طريقة الدفع مع التلوين
            payment_method = payment.get('payment_method', 'نقدي')
            payment_style = 'color: #27ae60; font-weight: bold;' if payment_method == 'نقدي' else 'color: #3498db; font-weight: bold;'

            html += f'''
                <tr>
                    <td>{payment.get('client', '')}</td>
                    <td>{payment.get('type', '')}</td>
                    <td style="{payment_style}">{payment_method}</td>
                    <td>{amount:.2f}</td>
                    <td>{payment.get('ref', '-')}</td>
                </tr>
            '''
    else:
        html += '''
            <tr>
                <td colspan="5" style="text-align: center; color: #7f8c8d; font-style: italic;">لا توجد مقبوضات من العملاء</td>
            </tr>
        '''

    html += f'''
                    </tbody>
                </table>
            </div>
            <div style="text-align: left; font-weight: bold; color: #27ae60; margin-top: 8px;">
                إجمالي المقبوضات: {totals.get('client', client_total):.2f} ريال
            </div>
        </div>
    '''

    # 3. قسم الموردين (للمتابعة فقط - لا يؤثر على الحسابات)
    html += '''
        <div class="section">
            <div class="section-title">🏭 الموردين (للمتابعة فقط)</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم المورد</th>
                            <th>المبلغ المسلم</th>
                            <th>طريقة الدفع</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
    '''

    # بيانات الموردين من التصفية
    suppliers_transactions = filter_info.get('suppliers_transactions', [])
    suppliers_total = 0

    if suppliers_transactions:
        for supplier in suppliers_transactions:
            amount = float(supplier.get('amount', 0))
            suppliers_total += amount

            # تحديد طريقة الدفع مع التلوين
            payment_method = supplier.get('payment_method', 'نقدي')
            if payment_method == 'نقدي':
                payment_style = 'color: #27ae60; font-weight: bold;'
            elif payment_method == 'شيك':
                payment_style = 'color: #e67e22; font-weight: bold;'
            else:  # تحويل بنكي
                payment_style = 'color: #3498db; font-weight: bold;'

            html += f'''
                <tr>
                    <td>{supplier.get('supplier_name', '')}</td>
                    <td>{amount:.2f}</td>
                    <td style="{payment_style}">{payment_method}</td>
                    <td>{supplier.get('notes', '-')}</td>
                </tr>
            '''
    else:
        html += '''
            <tr>
                <td colspan="4" style="text-align: center; color: #7f8c8d; font-style: italic;">لا توجد مدفوعات للموردين</td>
            </tr>
        '''

    html += f'''
                    </tbody>
                </table>
            </div>
            <div style="text-align: left; font-weight: bold; color: #795548; margin-top: 8px;">
                إجمالي المدفوعات للموردين: {suppliers_total:.2f} ريال (لا يؤثر على الحسابات)
            </div>
            <div style="text-align: center; color: #e67e22; font-style: italic; margin-top: 5px; font-size: 11px;">
                ⚠️ ملاحظة: هذا القسم للمتابعة فقط ولا يدخل في حسابات التصفية
            </div>
        </div>
    '''

    # تم إزالة الأقسام الأخرى - الاحتفاظ بالأقسام المطلوبة فقط

    # إزالة الأقسام غير المطلوبة
    if False:  # تعطيل جميع الأقسام الأخرى
        html += '''
            <div class="section">
                <div class="section-title">💳 المقبوضات البنكية</div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>نوع العملية</th>
                                <th>اسم البنك</th>
                                <th>المبلغ (ريال)</th>
                            </tr>
                        </thead>
                        <tbody>
        '''
        for row in bank_data:
            html += f'<tr><td>{row[0]}</td><td>{row[1]}</td><td>{row[2]}</td></tr>'
        html += f'''
                        </tbody>
                    </table>
                </div>
                <div style="text-align: left; font-weight: bold; color: #27ae60; font-size: 1.2em; margin-top: 15px;">
                    الإجمالي: {totals.get('bank', 0):.2f} ريال
                </div>
            </div>
        '''

    # تم إزالة قسم المقبوضات النقدية - غير مطلوب

    # قسم المقبوضات النقدية (مؤقتاً فارغ)
    cash_data_old = {}
    if False:  # تعطيل القسم القديم
        html += '''
            <div class="section">
                <div class="section-title">💰 المقبوضات النقدية</div>
                <div class="cash-grid">
        '''
        cash_types = [
            (500, "خمس مئة"),
            (100, "مئة"),
            (50, "خمسون"),
            (10, "عشرة"),
            (5, "خمسة"),
            (1, "ريال"),
            (0.5, "نصف"),
            (0.25, "ربع")
        ]

        for value, label in cash_types:
            count = cash_data.get(str(value), '0')
            total_value = float(count) * value
            html += f'''
                <div class="cash-item">
                    <div class="cash-denomination">{label}</div>
                    <div class="cash-count">{count} × {value} = {total_value:.2f}</div>
                </div>
            '''

        html += f'''
                </div>
                <div style="text-align: left; font-weight: bold; color: #27ae60; font-size: 1.2em; margin-top: 15px;">
                    الإجمالي: {totals.get('cash', 0):.2f} ريال
                </div>
            </div>
        '''

    # تم إزالة قسم المرتجعات - غير مطلوب

    # باقي الأقسام (تعطيل)
    sections = []

    for section_key, section_title, columns in sections:
        section_data = []  # مؤقتاً فارغ
        if False:  # تعطيل
            html += f'''
                <div class="section">
                    <div class="section-title">{section_title}</div>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
            '''
            for col in columns:
                html += f'<th>{col}</th>'
            html += '''
                                </tr>
                            </thead>
                            <tbody>
            '''
            for row in section_data:
                html += '<tr>'
                for cell in row:
                    html += f'<td>{cell}</td>'
                html += '</tr>'
            html += f'''
                            </tbody>
                        </table>
                    </div>
                    <div style="text-align: left; font-weight: bold; color: #27ae60; font-size: 1.2em; margin-top: 15px;">
                        الإجمالي: {totals.get(section_key, 0):.2f} ريال
                    </div>
                </div>
            '''

    # ملخص التصفية النهائي
    html += '''
        <div class="section" style="background: #f8f9fa; border: 2px solid #3498db; border-radius: 8px; margin-top: 15px;">
            <div class="section-title" style="background: #2c3e50; margin-bottom: 10px;">📊 ملخص التصفية النهائي</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div>
                    <table style="width: 100%; font-size: 11px;">
                        <thead>
                            <tr style="background: #34495e;">
                                <th style="color: white; padding: 6px;">البند</th>
                                <th style="color: white; padding: 6px;">المبلغ (ريال)</th>
                            </tr>
                        </thead>
                        <tbody>
    '''

    # حساب المجاميع الصحيح لجميع الأقسام
    bank_total = totals.get('bank', 0)      # المقبوضات البنكية
    cash_total = totals.get('cash', 0)      # المقبوضات النقدية
    credit_total = totals.get('credit', 0)  # فواتير عملاء الآجل
    client_total = totals.get('client', 0)  # المقبوضات من العملاء
    return_total = totals.get('return', 0)  # المرتجعات

    # إجمالي المقبوضات = البنكية + النقدية + الآجل + المرتجعات - المدفوع للعملاء
    total_receipts = bank_total + cash_total + credit_total + return_total - client_total

    try:
        system_sales_value = float(system_sales)
    except:
        system_sales_value = 0.0

    difference = total_receipts - system_sales_value

    # جدول المجاميع لجميع الأقسام
    summary_items = [
        ('المقبوضات البنكية', bank_total, '#3498db'),
        ('المقبوضات النقدية', cash_total, '#2ecc71'),
        ('فواتير عملاء الآجل', credit_total, '#f39c12'),
        ('المرتجعات', return_total, '#9b59b6'),
        ('المقبوضات من العملاء', client_total, '#e74c3c')
    ]

    for item_name, amount, color in summary_items:
        html += f'''
            <tr>
                <td style="padding: 4px; text-align: right; font-weight: bold;">{item_name}</td>
                <td style="padding: 4px; text-align: center; color: {color}; font-weight: bold;">{amount:.2f}</td>
            </tr>
        '''

    html += f'''
                            <tr style="background: #ecf0f1; border-top: 2px solid #34495e;">
                                <td style="padding: 6px; text-align: right; font-weight: bold; font-size: 12px;">إجمالي المقبوضات</td>
                                <td style="padding: 6px; text-align: center; font-weight: bold; font-size: 12px; color: #2c3e50;">{total_receipts:.2f}</td>
                            </tr>
                            <tr style="background: #e8f4f8;">
                                <td style="padding: 6px; text-align: right; font-weight: bold; font-size: 12px;">مبيعات النظام</td>
                                <td style="padding: 6px; text-align: center; font-weight: bold; font-size: 12px; color: #2c3e50;">{system_sales_value:.2f}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 20px;">
    '''

    # تحديد حالة التصفية
    if difference > 0:
        status = "فائض"
        status_color = "#27ae60"
        status_icon = "📈"
    elif difference < 0:
        status = "عجز"
        status_color = "#e74c3c"
        status_icon = "📉"
    else:
        status = "متوازن"
        status_color = "#f39c12"
        status_icon = "⚖️"

    html += f'''
                    <div style="text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 10px;">{status_icon}</div>
                        <div style="font-size: 16px; font-weight: bold; color: {status_color}; margin-bottom: 8px;">
                            حالة التصفية: {status}
                        </div>
                        <div style="font-size: 18px; font-weight: bold; color: {status_color};
                                   background: white; padding: 10px; border-radius: 8px; border: 2px solid {status_color};">
                            {abs(difference):.2f} ريال
                        </div>
                    </div>
                </div>
            </div>
        </div>
    '''

    # إضافة التذييل مع حقوق التطوير
    html += '''
        <div class="footer">
            <p><strong>تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير المتكامل 2025</strong></p>
            <p><strong>تطوير: محمد الكامل - الإصدار 3.0.0</strong></p>
            <p>© 2025 - جميع الحقوق محفوظة</p>
        </div>
        </div>

        <style media="print">
            @page {
                size: A4;
                margin: 15mm;
            }

            body {
                font-size: 11px;
                line-height: 1.3;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .header {
                background: #667eea !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .section-title {
                background: #3498db !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            table {
                page-break-inside: avoid;
            }

            .section {
                page-break-inside: avoid;
                margin-bottom: 8px;
            }
        </style>

        <script>
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 1000);
            };
        </script>
    </body>
    </html>
    '''

    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html)

    webbrowser.open('file://' + os.path.abspath(filename))
    return True

def generate_filter_report(filter_data, totals, system_sales):
    """إنشاء تقرير HTML وفتحه في المتصفح"""
    try:
        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = "reports/generated"
        os.makedirs(reports_dir, exist_ok=True)

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{reports_dir}/filter_report_{timestamp}.html"

        # إنشاء التقرير
        if generate_html_report(filter_data, totals, system_sales, filename):
            return True
        else:
            return False

    except Exception as e:
        print(f"خطأ في إنشاء تقرير التصفية: {e}")
        return False
