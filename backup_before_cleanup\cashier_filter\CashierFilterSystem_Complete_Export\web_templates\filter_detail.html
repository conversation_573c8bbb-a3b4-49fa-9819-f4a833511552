{% extends "base.html" %}

{% block title %}تفاصيل التصفية - {{ filter.cashier_name or 'غير محدد' }} - {{ filter.date }}{% endblock %}

{% block content %}
<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="card-title">
                            <i class="fas fa-file-invoice text-primary"></i>
                            تفاصيل التصفية
                        </h2>
                        <p class="card-text">
                            الكاشير: <strong>{{ filter.cashier_name or 'غير محدد' }}</strong> |
                            التاريخ: <strong>{{ filter.date }}</strong>
                            {% if filter.sequence_number %}
                            | رقم التسلسل: <strong>#{{ filter.sequence_number }}</strong>
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <a href="/reports" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Basic Info -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات أساسية</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>الكاشير:</strong></td>
                        <td>{{ filter.cashier_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>المسؤول:</strong></td>
                        <td>{{ filter.admin_name or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <td><strong>التاريخ:</strong></td>
                        <td>{{ filter.date }}</td>
                    </tr>
                    {% if filter.sequence_number %}
                    <tr>
                        <td><strong>رقم التسلسل:</strong></td>
                        <td>#{{ filter.sequence_number }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> ملخص المبالغ</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>المقبوضات البنكية:</strong></td>
                        <td class="amount">{{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال</td>
                    </tr>
                    <tr>
                        <td><strong>المقبوضات النقدية:</strong></td>
                        <td class="amount">{{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال</td>
                    </tr>
                    <tr>
                        <td><strong>المبيعات الآجلة:</strong></td>
                        <td class="amount">{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</td>
                    </tr>
                    <tr class="table-primary">
                        <td><strong>المجموع الكلي:</strong></td>
                        <td class="amount">
                            <strong>{{ "{:,.2f}".format((filter.details.bank_total or 0) + (filter.details.cash_total or 0) + (filter.details.credit_total or 0)) }} ريال</strong>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Bank Transactions -->
{% if filter.details.bank_transactions %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-credit-card"></i> المقبوضات البنكية</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>نوع العملية</th>
                                <th>البنك</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in filter.details.bank_transactions %}
                            <tr>
                                <td>
                                    <i class="fas fa-credit-card text-primary"></i>
                                    {{ transaction.operation_type or 'غير محدد' }}
                                </td>
                                <td>
                                    <i class="fas fa-university text-info"></i>
                                    {{ transaction.bank_name or 'غير محدد' }}
                                </td>
                                <td class="amount">{{ "{:,.2f}".format(transaction.amount or 0) }} ريال</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="2">المجموع</th>
                                <th class="amount">{{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Cash Details -->
{% if filter.details.cash_details %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-money-bill"></i> تفاصيل المقبوضات النقدية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for denomination, details in filter.details.cash_details.items() %}
                    <div class="col-md-3 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>{{ denomination }}</h6>
                                <p class="mb-1">العدد: <strong>{{ details.count or 0 }}</strong></p>
                                <p class="amount mb-0">{{ "{:,.2f}".format(details.total or 0) }} ريال</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h5>إجمالي النقدي: {{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Additional Details -->
<div class="row mb-4">
    <div class="col-md-4">
        {% if filter.details.credit_total and filter.details.credit_total > 0 %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-clock"></i> المبيعات الآجلة</h6>
            </div>
            <div class="card-body text-center">
                <h4 class="amount">{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</h4>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="col-md-4">
        {% if filter.details.client_total and filter.details.client_total > 0 %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-users"></i> المقبوضات من العملاء</h6>
            </div>
            <div class="card-body text-center">
                <h4 class="amount">{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</h4>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="col-md-4">
        {% if filter.details.return_total and filter.details.return_total > 0 %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-undo"></i> فواتير المرتجعات</h6>
            </div>
            <div class="card-body text-center">
                <h4 class="amount text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</h4>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Comprehensive Financial Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-chart-pie"></i> الملخص المالي الشامل</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">📊 تفصيل الإيرادات:</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><i class="fas fa-credit-card text-primary"></i> المقبوضات البنكية:</td>
                                <td class="amount text-end">{{ "{:,.2f}".format(filter.details.bank_total or 0) }} ريال</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-money-bill text-success"></i> المقبوضات النقدية:</td>
                                <td class="amount text-end">{{ "{:,.2f}".format(filter.details.cash_total or 0) }} ريال</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-clock text-warning"></i> المبيعات الآجلة:</td>
                                <td class="amount text-end">{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-users text-info"></i> مقبوضات العملاء:</td>
                                <td class="amount text-end">{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</td>
                            </tr>
                            <tr class="table-danger">
                                <td><i class="fas fa-undo text-danger"></i> فواتير المرتجعات:</td>
                                <td class="amount text-end text-danger">-{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">💰 المجاميع النهائية:</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                {% set total_income = (filter.details.bank_total or 0) + (filter.details.cash_total or 0) + (filter.details.credit_total or 0) + (filter.details.client_total or 0) %}
                                {% set net_total = total_income - (filter.details.return_total or 0) %}

                                <div class="row mb-2">
                                    <div class="col-8">إجمالي الإيرادات:</div>
                                    <div class="col-4 text-end amount">{{ "{:,.2f}".format(total_income) }} ريال</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-8">إجمالي المرتجعات:</div>
                                    <div class="col-4 text-end amount text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-8"><strong>صافي المبلغ:</strong></div>
                                    <div class="col-4 text-end amount"><strong class="text-success">{{ "{:,.2f}".format(net_total) }} ريال</strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Credit Sales -->
{% if filter.details.credit_total and filter.details.credit_total > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> تفاصيل المبيعات الآجلة</h5>
            </div>
            <div class="card-body">
                {% if filter.details.credit_details %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>اسم العميل</th>
                                <th>المبلغ</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for credit in filter.details.credit_details %}
                            <tr>
                                <td>{{ credit.invoice_number or 'غير محدد' }}</td>
                                <td>{{ credit.customer_name or 'غير محدد' }}</td>
                                <td class="amount">{{ "{:,.2f}".format(credit.amount or 0) }} ريال</td>
                                <td>{{ credit.due_date or 'غير محدد' }}</td>
                                <td>
                                    <span class="badge bg-warning">آجل</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-warning">
                            <tr>
                                <th colspan="2">إجمالي المبيعات الآجلة</th>
                                <th class="amount">{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    إجمالي المبيعات الآجلة: <strong>{{ "{:,.2f}".format(filter.details.credit_total or 0) }} ريال</strong>
                    <br><small>لا توجد تفاصيل إضافية متاحة</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Client Collections Details -->
{% if filter.details.client_total and filter.details.client_total > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> تفاصيل المقبوضات من العملاء</h5>
            </div>
            <div class="card-body">
                {% if filter.details.client_details %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الفاتورة المسددة</th>
                                <th>المبلغ المسدد</th>
                                <th>طريقة السداد</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in filter.details.client_details %}
                            <tr>
                                <td>{{ client.customer_name or 'غير محدد' }}</td>
                                <td>{{ client.invoice_number or 'غير محدد' }}</td>
                                <td class="amount">{{ "{:,.2f}".format(client.amount or 0) }} ريال</td>
                                <td>
                                    {% if client.payment_method == 'cash' %}
                                    <span class="badge bg-success">نقدي</span>
                                    {% elif client.payment_method == 'bank' %}
                                    <span class="badge bg-primary">بنكي</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ client.payment_method or 'غير محدد' }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ client.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-info">
                            <tr>
                                <th colspan="2">إجمالي المقبوضات من العملاء</th>
                                <th class="amount">{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    إجمالي المقبوضات من العملاء: <strong>{{ "{:,.2f}".format(filter.details.client_total or 0) }} ريال</strong>
                    <br><small>لا توجد تفاصيل إضافية متاحة</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Return Details -->
{% if filter.details.return_total and filter.details.return_total > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-undo"></i> تفاصيل فواتير المرتجعات</h5>
            </div>
            <div class="card-body">
                {% if filter.details.return_details %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم فاتورة الإرجاع</th>
                                <th>رقم الفاتورة الأصلية</th>
                                <th>سبب الإرجاع</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for return_item in filter.details.return_details %}
                            <tr>
                                <td>{{ return_item.return_invoice or 'غير محدد' }}</td>
                                <td>{{ return_item.original_invoice or 'غير محدد' }}</td>
                                <td>{{ return_item.reason or 'غير محدد' }}</td>
                                <td class="amount text-danger">{{ "{:,.2f}".format(return_item.amount or 0) }} ريال</td>
                                <td>{{ return_item.date or 'غير محدد' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-danger">
                            <tr>
                                <th colspan="3">إجمالي المرتجعات</th>
                                <th class="amount text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    إجمالي فواتير المرتجعات: <strong class="text-danger">{{ "{:,.2f}".format(filter.details.return_total or 0) }} ريال</strong>
                    <br><small>لا توجد تفاصيل إضافية متاحة</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Expenses and Adjustments -->
{% if filter.details.expenses or filter.details.adjustments %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> المصروفات والتعديلات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if filter.details.expenses %}
                    <div class="col-md-6">
                        <h6 class="text-warning">💸 المصروفات:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>البيان</th>
                                        <th>المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for expense in filter.details.expenses %}
                                    <tr>
                                        <td>{{ expense.description or 'غير محدد' }}</td>
                                        <td class="amount text-warning">{{ "{:,.2f}".format(expense.amount or 0) }} ريال</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-warning">
                                        <th>إجمالي المصروفات</th>
                                        <th class="amount">{{ "{:,.2f}".format(filter.details.expenses | sum(attribute='amount') or 0) }} ريال</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    {% if filter.details.adjustments %}
                    <div class="col-md-6">
                        <h6 class="text-info">⚖️ التعديلات:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>نوع التعديل</th>
                                        <th>المبلغ</th>
                                        <th>السبب</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for adjustment in filter.details.adjustments %}
                                    <tr>
                                        <td>{{ adjustment.type or 'غير محدد' }}</td>
                                        <td class="amount {% if adjustment.amount > 0 %}text-success{% else %}text-danger{% endif %}">
                                            {{ "{:+,.2f}".format(adjustment.amount or 0) }} ريال
                                        </td>
                                        <td>{{ adjustment.reason or 'غير محدد' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th>صافي التعديلات</th>
                                        <th class="amount">{{ "{:+,.2f}".format(filter.details.adjustments | sum(attribute='amount') or 0) }} ريال</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Final Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-chart-line"></i> الملخص النهائي للتصفية</h5>
            </div>
            <div class="card-body">
                {% set total_income = (filter.details.bank_total or 0) + (filter.details.cash_total or 0) + (filter.details.credit_total or 0) + (filter.details.client_total or 0) %}
                {% set total_deductions = (filter.details.return_total or 0) + (filter.details.expenses | sum(attribute='amount') or 0) %}
                {% set total_adjustments = filter.details.adjustments | sum(attribute='amount') or 0 %}
                {% set final_total = total_income - total_deductions + total_adjustments %}

                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>إجمالي الإيرادات:</strong></td>
                                <td class="amount text-end text-success">{{ "{:,.2f}".format(total_income) }} ريال</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي الخصومات:</strong></td>
                                <td class="amount text-end text-danger">{{ "{:,.2f}".format(total_deductions) }} ريال</td>
                            </tr>
                            {% if total_adjustments != 0 %}
                            <tr>
                                <td><strong>صافي التعديلات:</strong></td>
                                <td class="amount text-end {% if total_adjustments > 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ "{:+,.2f}".format(total_adjustments) }} ريال
                                </td>
                            </tr>
                            {% endif %}
                            <tr class="table-success">
                                <td><strong>المبلغ النهائي:</strong></td>
                                <td class="amount text-end"><strong class="text-success fs-5">{{ "{:,.2f}".format(final_total) }} ريال</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>إحصائيات سريعة</h6>
                                <p class="mb-1">عدد المعاملات البنكية: <strong>{{ filter.details.bank_transactions | length }}</strong></p>
                                <p class="mb-1">عدد فئات النقدي: <strong>{{ filter.details.cash_details | length }}</strong></p>
                                {% if filter.details.credit_details %}
                                <p class="mb-1">عدد الفواتير الآجلة: <strong>{{ filter.details.credit_details | length }}</strong></p>
                                {% endif %}
                                {% if filter.details.return_details %}
                                <p class="mb-1">عدد فواتير الإرجاع: <strong>{{ filter.details.return_details | length }}</strong></p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notes -->
{% if filter.notes %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-sticky-note"></i> ملاحظات</h5>
            </div>
            <div class="card-body">
                <p>{{ filter.notes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Print Styles -->
<style>
    @media print {
        .btn, .navbar, footer {
            display: none !important;
        }
        
        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
        }
        
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
        
        body {
            background: white !important;
        }
        
        .amount {
            font-weight: bold !important;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.title = `تصفية_${document.querySelector('h2').textContent.replace(/[^\w\s]/gi, '_')}_${new Date().toISOString().split('T')[0]}`;
    });
    
    // إضافة معلومات إضافية للطباعة
    window.addEventListener('afterprint', function() {
        document.title = 'تفاصيل التصفية - نظام تصفية الكاشير';
    });
</script>
{% endblock %}
