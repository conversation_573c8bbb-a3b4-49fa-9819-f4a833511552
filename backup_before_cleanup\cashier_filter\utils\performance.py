# تحسينات الأداء والميزات المتقدمة
import sqlite3
import threading
import time
from datetime import datetime, timedelta
import json
import os
from functools import lru_cache
import logging

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")
CACHE_SIZE = 128
CACHE_TIMEOUT = 300  # 5 دقائق

# إعداد نظام السجلات
def setup_logging():
    """إعداد نظام السجلات مع إنشاء المجلد إذا لم يكن موجوداً"""
    try:
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
        os.makedirs(logs_dir, exist_ok=True)

        log_file = os.path.join(logs_dir, "performance.log")

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        print(f"✅ تم إعداد نظام السجلات: {log_file}")
    except Exception as e:
        print(f"⚠️ تحذير: فشل في إعداد ملف السجلات: {e}")
        # إعداد السجلات للوحة التحكم فقط
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )

# تشغيل إعداد السجلات
setup_logging()

class DatabasePool:
    """مجموعة اتصالات قاعدة البيانات لتحسين الأداء"""
    
    def __init__(self, max_connections=5):
        self.max_connections = max_connections
        self.connections = []
        self.lock = threading.Lock()
        
    def get_connection(self):
        """الحصول على اتصال من المجموعة"""
        with self.lock:
            if self.connections:
                return self.connections.pop()
            else:
                return sqlite3.connect(DB_PATH, check_same_thread=False)
    
    def return_connection(self, conn):
        """إرجاع الاتصال للمجموعة"""
        with self.lock:
            if len(self.connections) < self.max_connections:
                self.connections.append(conn)
            else:
                conn.close()
    
    def close_all(self):
        """إغلاق جميع الاتصالات"""
        with self.lock:
            for conn in self.connections:
                conn.close()
            self.connections.clear()

# مثيل مجموعة الاتصالات
db_pool = DatabasePool()

class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self):
        self.cache = {}
        self.cache_times = {}
        self.lock = threading.Lock()
    
    def get(self, key):
        """الحصول على قيمة من التخزين المؤقت"""
        with self.lock:
            if key in self.cache:
                # التحقق من انتهاء صلاحية التخزين المؤقت
                if time.time() - self.cache_times[key] < CACHE_TIMEOUT:
                    return self.cache[key]
                else:
                    # إزالة البيانات المنتهية الصلاحية
                    del self.cache[key]
                    del self.cache_times[key]
            return None
    
    def set(self, key, value):
        """تعيين قيمة في التخزين المؤقت"""
        with self.lock:
            self.cache[key] = value
            self.cache_times[key] = time.time()
            
            # تنظيف التخزين المؤقت إذا تجاوز الحد الأقصى
            if len(self.cache) > CACHE_SIZE:
                self.cleanup_old_entries()
    
    def cleanup_old_entries(self):
        """تنظيف الإدخالات القديمة"""
        current_time = time.time()
        keys_to_remove = []
        
        for key, cache_time in self.cache_times.items():
            if current_time - cache_time >= CACHE_TIMEOUT:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.cache[key]
            del self.cache_times[key]
    
    def clear(self):
        """مسح جميع البيانات المؤقتة"""
        with self.lock:
            self.cache.clear()
            self.cache_times.clear()

# مثيل مدير التخزين المؤقت
cache_manager = CacheManager()

class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.operation_times = {}
        self.operation_counts = {}
        self.lock = threading.Lock()
    
    def start_operation(self, operation_name):
        """بدء مراقبة عملية"""
        return time.time()
    
    def end_operation(self, operation_name, start_time):
        """انتهاء مراقبة عملية"""
        duration = time.time() - start_time
        
        with self.lock:
            if operation_name not in self.operation_times:
                self.operation_times[operation_name] = []
                self.operation_counts[operation_name] = 0
            
            self.operation_times[operation_name].append(duration)
            self.operation_counts[operation_name] += 1
            
            # الاحتفاظ بآخر 100 عملية فقط
            if len(self.operation_times[operation_name]) > 100:
                self.operation_times[operation_name] = self.operation_times[operation_name][-100:]
        
        # تسجيل العمليات البطيئة
        if duration > 1.0:  # أكثر من ثانية واحدة
            logging.warning(f"عملية بطيئة: {operation_name} استغرقت {duration:.2f} ثانية")
    
    def get_statistics(self):
        """الحصول على إحصائيات الأداء"""
        stats = {}
        
        with self.lock:
            for operation_name in self.operation_times:
                times = self.operation_times[operation_name]
                if times:
                    stats[operation_name] = {
                        'count': self.operation_counts[operation_name],
                        'avg_time': sum(times) / len(times),
                        'min_time': min(times),
                        'max_time': max(times),
                        'total_time': sum(times)
                    }
        
        return stats

# مثيل مراقب الأداء
performance_monitor = PerformanceMonitor()

def performance_decorator(operation_name):
    """ديكوريتر لمراقبة أداء الدوال"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = performance_monitor.start_operation(operation_name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                performance_monitor.end_operation(operation_name, start_time)
        return wrapper
    return decorator

@lru_cache(maxsize=128)
def get_cached_cashiers():
    """الحصول على قائمة الكاشيرين مع التخزين المؤقت"""
    cache_key = "cashiers_list"
    cached_result = cache_manager.get(cache_key)
    
    if cached_result is not None:
        return cached_result
    
    try:
        conn = db_pool.get_connection()
        c = conn.cursor()
        
        c.execute("SELECT id, name, number FROM cashiers ORDER BY name")
        cashiers = c.fetchall()
        
        db_pool.return_connection(conn)
        
        # تخزين النتيجة مؤقتاً
        cache_manager.set(cache_key, cashiers)
        
        return cashiers
        
    except Exception as e:
        logging.error(f"خطأ في الحصول على الكاشيرين: {e}")
        return []

@lru_cache(maxsize=128)
def get_cached_admins():
    """الحصول على قائمة المسؤولين مع التخزين المؤقت"""
    cache_key = "admins_list"
    cached_result = cache_manager.get(cache_key)
    
    if cached_result is not None:
        return cached_result
    
    try:
        conn = db_pool.get_connection()
        c = conn.cursor()
        
        c.execute("SELECT id, name FROM admins ORDER BY name")
        admins = c.fetchall()
        
        db_pool.return_connection(conn)
        
        # تخزين النتيجة مؤقتاً
        cache_manager.set(cache_key, admins)
        
        return admins
        
    except Exception as e:
        logging.error(f"خطأ في الحصول على المسؤولين: {e}")
        return []

@performance_decorator("get_filters_with_pagination")
def get_filters_with_pagination(page=1, page_size=50, filters=None):
    """الحصول على التصفيات مع التصفح المقسم"""
    try:
        conn = db_pool.get_connection()
        c = conn.cursor()
        
        # بناء الاستعلام مع الفلاتر
        base_query = """
            SELECT f.id, f.date, c.name as cashier_name, f.admin_name, f.data, f.date as created_at, f.sequence_number
            FROM filters f
            LEFT JOIN cashiers c ON f.cashier_id = c.id
        """
        
        where_conditions = []
        params = []
        
        if filters:
            if filters.get('start_date'):
                where_conditions.append("f.date >= ?")
                params.append(filters['start_date'])
            
            if filters.get('end_date'):
                where_conditions.append("f.date <= ?")
                params.append(filters['end_date'])
            
            if filters.get('cashier_name'):
                where_conditions.append("c.name LIKE ?")
                params.append(f"%{filters['cashier_name']}%")
            
            if filters.get('admin_name'):
                where_conditions.append("f.admin_name LIKE ?")
                params.append(f"%{filters['admin_name']}%")
        
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة الترتيب والتصفح المقسم
        base_query += " ORDER BY f.sequence_number DESC LIMIT ? OFFSET ?"
        params.extend([page_size, (page - 1) * page_size])
        
        c.execute(base_query, params)
        results = c.fetchall()
        
        # الحصول على العدد الإجمالي
        count_query = "SELECT COUNT(*) FROM filters f LEFT JOIN cashiers c ON f.cashier_id = c.id"
        if where_conditions:
            count_query += " WHERE " + " AND ".join(where_conditions)
        
        c.execute(count_query, params[:-2])  # استبعاد LIMIT و OFFSET
        total_count = c.fetchone()[0]
        
        db_pool.return_connection(conn)
        
        return {
            'data': results,
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_pages': (total_count + page_size - 1) // page_size
        }
        
    except Exception as e:
        logging.error(f"خطأ في الحصول على التصفيات: {e}")
        return {'data': [], 'total_count': 0, 'page': 1, 'page_size': page_size, 'total_pages': 0}

class AdvancedSearch:
    """البحث المتقدم"""
    
    @staticmethod
    @performance_decorator("advanced_search")
    def search_filters(search_criteria):
        """البحث المتقدم في التصفيات"""
        try:
            print(f"🔍 بدء البحث المتقدم مع المعايير: {search_criteria}")

            # التحقق من وجود قاعدة البيانات
            if not os.path.exists(DB_PATH):
                print(f"❌ قاعدة البيانات غير موجودة: {DB_PATH}")
                return []

            import sqlite3
            conn = sqlite3.connect(DB_PATH, timeout=10.0)
            c = conn.cursor()

            # التحقق من وجود الجداول المطلوبة
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='filters'")
            if not c.fetchone():
                print("❌ جدول التصفيات غير موجود")
                conn.close()
                return []

            query = """
                SELECT f.id, f.date,
                       COALESCE(c.name, 'غير محدد') as cashier_name,
                       COALESCE(f.admin_name, 'غير محدد') as admin_name,
                       f.data, f.date as created_at, f.sequence_number
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                WHERE 1=1
            """

            params = []

            # البحث في التاريخ
            if search_criteria.get('date_from'):
                query += " AND f.date >= ?"
                params.append(search_criteria['date_from'])
                print(f"✅ إضافة معيار التاريخ من: {search_criteria['date_from']}")

            if search_criteria.get('date_to'):
                query += " AND f.date <= ?"
                params.append(search_criteria['date_to'])
                print(f"✅ إضافة معيار التاريخ إلى: {search_criteria['date_to']}")

            # البحث في اسم الكاشير
            if search_criteria.get('cashier_name'):
                query += " AND c.name LIKE ?"
                params.append(f"%{search_criteria['cashier_name']}%")
                print(f"✅ إضافة معيار اسم الكاشير: {search_criteria['cashier_name']}")

            # البحث في اسم المسؤول
            if search_criteria.get('admin_name'):
                query += " AND f.admin_name LIKE ?"
                params.append(f"%{search_criteria['admin_name']}%")
                print(f"✅ إضافة معيار اسم المسؤول: {search_criteria['admin_name']}")

            # البحث النصي في الملاحظات
            if search_criteria.get('notes'):
                query += " AND f.notes LIKE ?"
                params.append(f"%{search_criteria['notes']}%")
                print(f"✅ إضافة معيار الملاحظات: {search_criteria['notes']}")

            query += " ORDER BY f.sequence_number DESC LIMIT 1000"

            print(f"🔍 تنفيذ الاستعلام: {query}")
            print(f"📊 المعاملات: {params}")

            c.execute(query, params)
            results = c.fetchall()

            print(f"✅ تم العثور على {len(results)} نتيجة أولية")
            conn.close()

            # فلترة النتائج حسب المبلغ إذا لزم الأمر
            if search_criteria.get('amount_min') or search_criteria.get('amount_max'):
                print("🔍 بدء فلترة النتائج حسب المبلغ...")
                filtered_results = []
                amount_min = search_criteria.get('amount_min')
                amount_max = search_criteria.get('amount_max')

                for i, result in enumerate(results):
                    try:
                        import json
                        data = json.loads(result[4])  # البيانات في العمود الخامس
                        totals = data.get('totals', {})

                        # حساب المبلغ الإجمالي
                        total_amount = (
                            float(totals.get('bank', 0) or 0) +
                            float(totals.get('cash', 0) or 0) +
                            float(totals.get('credit', 0) or 0) +
                            float(totals.get('return', 0) or 0) -
                            float(totals.get('client', 0) or 0)
                        )

                        # فحص الحد الأدنى
                        if amount_min is not None and total_amount < amount_min:
                            continue

                        # فحص الحد الأقصى
                        if amount_max is not None and total_amount > amount_max:
                            continue

                        filtered_results.append(result)

                    except Exception as e:
                        print(f"⚠️ خطأ في تحليل النتيجة {i}: {e}")
                        # في حالة خطأ في تحليل JSON، أضف النتيجة
                        filtered_results.append(result)

                print(f"✅ تم فلترة النتائج: {len(filtered_results)} من أصل {len(results)}")
                return filtered_results

            print(f"✅ إرجاع جميع النتائج: {len(results)}")
            return results

        except Exception as e:
            print(f"❌ خطأ خطير في البحث المتقدم: {e}")
            import traceback
            traceback.print_exc()

            # محاولة تسجيل الخطأ بأمان
            try:
                logging.error(f"خطأ في البحث المتقدم: {e}")
            except:
                print("⚠️ فشل في تسجيل الخطأ في ملف السجلات")

            return []

class DataExporter:
    """مصدر البيانات المحسن"""
    
    @staticmethod
    @performance_decorator("export_to_excel")
    def export_to_excel_optimized(data, filename):
        """تصدير محسن إلى Excel"""
        try:
            import pandas as pd
            
            # تحويل البيانات إلى DataFrame
            df = pd.DataFrame(data)
            
            # تحسين الذاكرة
            for col in df.select_dtypes(include=['object']):
                df[col] = df[col].astype('category')
            
            # كتابة الملف
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='التصفيات', index=False)
            
            logging.info(f"تم تصدير {len(data)} سجل إلى {filename}")
            return True
            
        except Exception as e:
            logging.error(f"خطأ في تصدير Excel: {e}")
            return False

def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        conn = db_pool.get_connection()
        c = conn.cursor()
        
        # إنشاء فهارس لتحسين الأداء
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_filters_date ON filters(date)",
            "CREATE INDEX IF NOT EXISTS idx_filters_cashier_id ON filters(cashier_id)",
            "CREATE INDEX IF NOT EXISTS idx_filters_admin_name ON filters(admin_name)",
            "CREATE INDEX IF NOT EXISTS idx_filters_created_at ON filters(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_cashiers_name ON cashiers(name)",
            "CREATE INDEX IF NOT EXISTS idx_admins_name ON admins(name)"
        ]
        
        for index_sql in indexes:
            c.execute(index_sql)
        
        # تحليل الجداول لتحسين الاستعلامات
        c.execute("ANALYZE")
        
        # تنظيف قاعدة البيانات
        c.execute("VACUUM")
        
        conn.commit()
        db_pool.return_connection(conn)
        
        logging.info("تم تحسين قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        logging.error(f"خطأ في تحسين قاعدة البيانات: {e}")
        return False

def cleanup_old_data(days=365):
    """تنظيف البيانات القديمة"""
    try:
        cutoff_date = datetime.now() - timedelta(days=days)
        
        conn = db_pool.get_connection()
        c = conn.cursor()
        
        # حذف التصفيات القديمة
        c.execute("DELETE FROM filters WHERE created_at < ?", (cutoff_date.isoformat(),))
        deleted_count = c.rowcount
        
        conn.commit()
        db_pool.return_connection(conn)
        
        logging.info(f"تم حذف {deleted_count} تصفية قديمة")
        return deleted_count
        
    except Exception as e:
        logging.error(f"خطأ في تنظيف البيانات القديمة: {e}")
        return 0

def get_performance_report():
    """الحصول على تقرير الأداء"""
    stats = performance_monitor.get_statistics()
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'cache_size': len(cache_manager.cache),
        'db_pool_size': len(db_pool.connections),
        'operations': stats
    }
    
    return report

# تشغيل تحسين قاعدة البيانات عند التحميل
if __name__ == "__main__":
    optimize_database()
