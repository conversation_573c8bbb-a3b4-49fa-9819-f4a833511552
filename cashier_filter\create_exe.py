#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إنشاء ملف تنفيذي EXE لنظام تصفية الكاشير المتكامل 2025
EXE Builder for Cashier Filter System 2025

تطوير: محمد الكامل
Developed by: <PERSON>
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path
from datetime import datetime

def print_header():
    """طباعة رأس أداة البناء"""
    header = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🔧 أداة إنشاء ملف تنفيذي EXE - نظام تصفية الكاشير 2025           ║
║       EXE Builder - Cashier Filter System 2025                      ║
║                                                                      ║
║    الإصدار 3.0.0 - تطوير: محمد الكامل                              ║
║    Version 3.0.0 - Developed by: <PERSON>                   ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(header)

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ خطأ: يتطلب Python 3.8 أو أحدث (الحالي: {python_version.major}.{python_version.minor})")
        return False
    print(f"   ✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # التحقق من PyInstaller
    try:
        import PyInstaller
        print(f"   ✅ PyInstaller متاح")
    except ImportError:
        print("   ❌ PyInstaller غير متاح")
        print("   📦 جاري تثبيت PyInstaller...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller"
            ])
            print("   ✅ تم تثبيت PyInstaller بنجاح")
        except subprocess.CalledProcessError:
            print("   ❌ فشل تثبيت PyInstaller")
            return False
    
    # التحقق من الملفات المطلوبة
    required_files = ["main.py", "db/cashier_filter.db"]
    for file in required_files:
        if not Path(file).exists():
            print(f"   ❌ ملف مطلوب مفقود: {file}")
            return False
    print("   ✅ جميع الملفات المطلوبة موجودة")
    
    return True

def create_icon():
    """إنشاء أيقونة للتطبيق"""
    print("\n🎨 إنشاء أيقونة التطبيق...")
    
    # إنشاء مجلد assets إذا لم يكن موجوداً
    assets_dir = Path("assets")
    assets_dir.mkdir(exist_ok=True)
    
    # إنشاء أيقونة بسيطة باستخدام PIL إذا كانت متاحة
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # إنشاء صورة 256x256
        img = Image.new('RGBA', (256, 256), (52, 152, 219, 255))
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة
        draw.ellipse([20, 20, 236, 236], fill=(255, 255, 255, 255))
        
        # رسم رمز الكاشير (مربع صغير)
        draw.rectangle([80, 80, 176, 176], fill=(52, 152, 219, 255))
        
        # حفظ كـ ICO
        icon_path = assets_dir / "icon.ico"
        img.save(icon_path, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        
        print(f"   ✅ تم إنشاء أيقونة: {icon_path}")
        return True
        
    except ImportError:
        print("   ⚠️ PIL غير متاح، سيتم تخطي إنشاء الأيقونة")
        return False

def build_exe():
    """بناء ملف EXE"""
    print("\n🔨 بناء ملف EXE...")
    print("   ⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # تحديد الأيقونة
        icon_path = Path("assets/icon.ico")
        icon_arg = f"--icon={icon_path}" if icon_path.exists() else ""
        
        # تحديد ملف معلومات الإصدار
        version_path = Path("version_info.txt")
        version_arg = f"--version-file={version_path}" if version_path.exists() else ""
        
        # بناء الأمر
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",                    # ملف واحد
            "--windowed",                   # بدون console
            "--name=CashierFilterSystem2025", # اسم الملف
            "--clean",                      # تنظيف الملفات المؤقتة
            "--noconfirm",                  # عدم طلب تأكيد
        ]
        
        # إضافة البيانات المطلوبة
        data_files = [
            ("db", "db"),
            ("ui", "ui"),
            ("utils", "utils")
        ]
        
        for src, dst in data_files:
            if Path(src).exists():
                cmd.extend(["--add-data", f"{src};{dst}"])
        
        # إضافة المكتبات المخفية
        hidden_imports = [
            "customtkinter",
            "tkinter",
            "tkinter.ttk",
            "tkinter.messagebox",
            "sqlite3",
            "json",
            "datetime",
            "threading",
            "hashlib",
            "webbrowser",
            "tempfile",
            "shutil",
            "pathlib"
        ]
        
        for module in hidden_imports:
            cmd.extend(["--hidden-import", module])
        
        # إضافة الأيقونة إذا كانت موجودة
        if icon_arg:
            cmd.append(icon_arg)
        
        # إضافة معلومات الإصدار إذا كانت موجودة
        if version_arg:
            cmd.append(version_arg)
        
        # إضافة الملف الرئيسي
        cmd.append("main.py")
        
        # تشغيل PyInstaller
        print(f"   🔄 تشغيل: {' '.join(cmd[:5])}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ تم بناء ملف EXE بنجاح")
            
            # التحقق من وجود الملف
            exe_path = Path("dist/CashierFilterSystem2025.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"   📏 حجم الملف: {size_mb:.1f} MB")
                print(f"   📁 مسار الملف: {exe_path.absolute()}")
                return True
            else:
                print("   ❌ الملف التنفيذي غير موجود")
                return False
        else:
            print("   ❌ فشل في بناء ملف EXE")
            print(f"   خطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في البناء: {e}")
        return False

def create_installer_package():
    """إنشاء حزمة التثبيت"""
    print("\n📦 إنشاء حزمة التثبيت...")
    
    # إنشاء مجلد الحزمة
    package_name = f"CashierFilterSystem2025_Installer"
    package_dir = Path(package_name)
    
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    package_dir.mkdir()
    
    try:
        # نسخ الملف التنفيذي
        exe_path = Path("dist/CashierFilterSystem2025.exe")
        if exe_path.exists():
            shutil.copy2(exe_path, package_dir / "CashierFilterSystem2025.exe")
            print(f"   ✅ نسخ الملف التنفيذي")
        else:
            print(f"   ❌ الملف التنفيذي غير موجود")
            return False
        
        # نسخ ملفات التوثيق
        doc_files = ["README.md", "USER_GUIDE.md", "LICENSE"]
        for doc_file in doc_files:
            if Path(doc_file).exists():
                shutil.copy2(doc_file, package_dir / doc_file)
                print(f"   ✅ نسخ {doc_file}")
        
        # إنشاء ملف تشغيل مبسط
        run_script = f"""@echo off
chcp 65001 > nul
title نظام تصفية الكاشير المتكامل 2025

echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║    تطوير: محمد الكامل - الإصدار 3.0.0                              ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 جاري تشغيل النظام...
echo.

CashierFilterSystem2025.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo.
)
pause
"""
        
        with open(package_dir / "تشغيل_النظام.bat", "w", encoding="utf-8") as f:
            f.write(run_script)
        
        print("   ✅ تم إنشاء ملف التشغيل")
        
        # إنشاء ملف معلومات
        info_content = f"""# نظام تصفية الكاشير المتكامل 2025

## معلومات الحزمة
- الإصدار: 3.0.0
- تاريخ البناء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- نظام التشغيل: {platform.system()} {platform.release()}
- المطور: محمد الكامل
- البريد الإلكتروني: <EMAIL>

## طريقة التشغيل
1. النقر المزدوج على: CashierFilterSystem2025.exe
2. أو النقر المزدوج على: تشغيل_النظام.bat

## بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: 123456

## الميزات
- ✅ لا يحتاج تثبيت Python
- ✅ يعمل على أي كمبيوتر Windows
- ✅ جميع المكتبات مدمجة
- ✅ قاعدة بيانات مدمجة
- ✅ واجهة عربية كاملة

## الدعم الفني
للدعم الفني والاستفسارات:
البريد الإلكتروني: <EMAIL>

© 2025 محمد الكامل - جميع الحقوق محفوظة
"""
        
        with open(package_dir / "معلومات_التطبيق.txt", "w", encoding="utf-8") as f:
            f.write(info_content)
        
        print("   ✅ تم إنشاء ملف المعلومات")
        print(f"   📁 مجلد الحزمة: {package_dir.absolute()}")
        
        return package_dir
        
    except Exception as e:
        print(f"   ❌ فشل في إنشاء الحزمة: {e}")
        return None

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_items = [
        "build",
        "__pycache__",
        "*.spec"
    ]
    
    for item in temp_items:
        if item == "*.spec":
            # حذف ملفات .spec
            for spec_file in Path(".").glob("*.spec"):
                try:
                    spec_file.unlink()
                    print(f"   ✅ حذف ملف: {spec_file}")
                except Exception as e:
                    print(f"   ⚠️ تعذر حذف {spec_file}: {e}")
        else:
            item_path = Path(item)
            try:
                if item_path.is_file():
                    item_path.unlink()
                    print(f"   ✅ حذف ملف: {item}")
                elif item_path.is_dir():
                    shutil.rmtree(item_path)
                    print(f"   ✅ حذف مجلد: {item}")
            except Exception as e:
                print(f"   ⚠️ تعذر حذف {item}: {e}")

def print_completion(package_dir):
    """طباعة رسالة الإكمال"""
    exe_path = Path("dist/CashierFilterSystem2025.exe")
    
    completion = f"""
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🎉 تم إنشاء ملف EXE بنجاح!                                       ║
║                                                                      ║
║    📁 الملف التنفيذي: {exe_path.name:<45} ║
║    📍 المسار: {str(exe_path.absolute())[:55]:<55} ║
║                                                                      ║
║    📦 حزمة التثبيت: {package_dir.name:<45} ║
║                                                                      ║
║    ✨ الميزات:                                                       ║
║    • لا يحتاج تثبيت Python                                          ║
║    • يعمل على أي كمبيوتر Windows                                   ║
║    • جميع المكتبات مدمجة                                            ║
║    • قاعدة بيانات مدمجة                                             ║
║                                                                      ║
║    🚀 طريقة التشغيل:                                                ║
║    النقر المزدوج على الملف التنفيذي                                ║
║                                                                      ║
║    💬 الدعم الفني: <EMAIL>                            ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(completion)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء أيقونة
    create_icon()
    
    # بناء ملف EXE
    if not build_exe():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء حزمة التثبيت
    package_dir = create_installer_package()
    if not package_dir:
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    # طباعة رسالة الإكمال
    print_completion(package_dir)
    
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
