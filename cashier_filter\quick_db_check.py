#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

try:
    # تحديد مسار قاعدة البيانات
    if os.path.exists("db/cashier_filter.db"):
        db_path = "db/cashier_filter.db"
    else:
        db_path = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"
    
    print(f"مسار قاعدة البيانات: {db_path}")
    print(f"موجودة: {os.path.exists(db_path)}")
    
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        # فحص جدول filters
        c.execute("PRAGMA table_info(filters)")
        columns = c.fetchall()
        print("\nأعمدة جدول filters:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        conn.close()
    
except Exception as e:
    print(f"خطأ: {e}")
    import traceback
    traceback.print_exc()
