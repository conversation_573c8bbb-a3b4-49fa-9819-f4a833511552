# واجهة التقارير التفاعلية المتطورة
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
from datetime import datetime, timedelta
import calendar
from collections import defaultdict
import webbrowser
import os
from pathlib import Path
import threading
import time

# استيراد matplotlib للرسوم البيانية التفاعلية
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    import matplotlib.dates as mdates
    import numpy as np
    import seaborn as sns
    from matplotlib.animation import FuncAnimation

    plt.rcParams['font.family'] = ['Tahoma', 'Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10
    sns.set_style("whitegrid")
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

# استيراد plotly للرسوم التفاعلية المتقدمة
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

# استخدام مسار نسبي
BASE_DIR = Path(__file__).parent.parent
DB_PATH = BASE_DIR / "db" / "cashier_filter.db"

class InteractiveReportsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("🎮 التقارير التفاعلية المتطورة")
        self.geometry("1800x1200")
        self.configure(bg="#0f1419")
        self.resizable(True, True)

        # متغيرات البيانات
        self.filters_data = []
        self.real_time_data = []
        self.animation_running = False

        # إعداد الألوان والثيم
        self.setup_theme()

        self.create_widgets()
        self.load_data()
        self.start_real_time_updates()

    def setup_theme(self):
        """إعداد ثيم التطبيق المتطور"""
        self.colors = {
            'primary': '#1e3a8a',
            'secondary': '#7c3aed',
            'success': '#059669',
            'warning': '#d97706',
            'danger': '#dc2626',
            'dark': '#111827',
            'light': '#f9fafb',
            'accent': '#06b6d4'
        }

    def create_widgets(self):
        """إنشاء عناصر الواجهة التفاعلية"""

        # الشريط العلوي المتطور
        self.create_advanced_header()

        # لوحة التحكم التفاعلية
        self.create_control_panel()

        # المحتوى الرئيسي مع التبويبات التفاعلية
        self.create_interactive_content()

        # شريط الحالة المتقدم
        self.create_advanced_status_bar()

    def create_advanced_header(self):
        """إنشاء الشريط العلوي المتطور"""
        header_frame = ctk.CTkFrame(self, fg_color=self.colors['dark'], corner_radius=0, height=100)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)

        # العنوان مع تأثيرات بصرية
        title_container = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_container.pack(side="left", padx=30, pady=20)

        title_label = ctk.CTkLabel(
            title_container,
            text="🎮 التقارير التفاعلية المتطورة",
            font=("Arial", 32, "bold"),
            text_color="#ffffff"
        )
        title_label.pack()

        subtitle_label = ctk.CTkLabel(
            title_container,
            text="تحليلات في الوقت الفعلي مع ذكاء اصطناعي متقدم",
            font=("Arial", 14),
            text_color="#94a3b8"
        )
        subtitle_label.pack()

        # أدوات التحكم السريع
        controls_container = ctk.CTkFrame(header_frame, fg_color="transparent")
        controls_container.pack(side="right", padx=30, pady=20)

        # زر التحديث المباشر
        self.live_update_btn = ctk.CTkButton(
            controls_container,
            text="🔴 مباشر",
            command=self.toggle_live_updates,
            fg_color=self.colors['danger'],
            hover_color="#b91c1c",
            width=100,
            height=35
        )
        self.live_update_btn.pack(side="top", pady=2)

        # زر ملء الشاشة
        fullscreen_btn = ctk.CTkButton(
            controls_container,
            text="⛶ ملء الشاشة",
            command=self.toggle_fullscreen,
            fg_color=self.colors['secondary'],
            hover_color="#6d28d9",
            width=100,
            height=35
        )
        fullscreen_btn.pack(side="top", pady=2)

    def create_control_panel(self):
        """إنشاء لوحة التحكم التفاعلية"""
        control_frame = ctk.CTkFrame(self, fg_color=self.colors['dark'], corner_radius=0, height=80)
        control_frame.pack(fill="x", padx=0, pady=0)
        control_frame.pack_propagate(False)

        # أدوات التحكم في الوقت الفعلي
        time_controls = ctk.CTkFrame(control_frame, fg_color="#1f2937", corner_radius=10)
        time_controls.pack(side="left", padx=20, pady=15)

        ctk.CTkLabel(time_controls, text="⏱️ الفترة الزمنية:",
                    font=("Arial", 12, "bold"), text_color="#ffffff").pack(side="left", padx=10)

        self.time_range_combo = ctk.CTkComboBox(
            time_controls,
            values=["آخر ساعة", "آخر 6 ساعات", "آخر 24 ساعة", "آخر أسبوع", "آخر شهر"],
            width=150
        )
        self.time_range_combo.set("آخر 24 ساعة")
        self.time_range_combo.pack(side="left", padx=5)

        # أدوات التحكم في العرض
        display_controls = ctk.CTkFrame(control_frame, fg_color="#1f2937", corner_radius=10)
        display_controls.pack(side="left", padx=10, pady=15)

        ctk.CTkLabel(display_controls, text="📊 نوع العرض:",
                    font=("Arial", 12, "bold"), text_color="#ffffff").pack(side="left", padx=10)

        self.display_type_combo = ctk.CTkComboBox(
            display_controls,
            values=["رسوم بيانية", "جداول تفاعلية", "خرائط حرارية", "رسوم ثلاثية الأبعاد"],
            width=150
        )
        self.display_type_combo.set("رسوم بيانية")
        self.display_type_combo.pack(side="left", padx=5)

        # أزرار الإجراءات السريعة
        actions_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        actions_frame.pack(side="right", padx=20, pady=15)

        refresh_btn = ctk.CTkButton(
            actions_frame,
            text="🔄 تحديث",
            command=self.refresh_data,
            fg_color=self.colors['accent'],
            hover_color="#0891b2",
            width=80,
            height=30
        )
        refresh_btn.pack(side="left", padx=5)

        export_btn = ctk.CTkButton(
            actions_frame,
            text="📤 تصدير",
            command=self.export_interactive_report,
            fg_color=self.colors['success'],
            hover_color="#047857",
            width=80,
            height=30
        )
        export_btn.pack(side="left", padx=5)

    def create_interactive_content(self):
        """إنشاء المحتوى التفاعلي الرئيسي"""

        # دفتر التبويبات التفاعلي
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=10)

        # تبويب الرسوم البيانية المباشرة
        self.live_charts_frame = ctk.CTkFrame(self.notebook, fg_color="#0f172a")
        self.notebook.add(self.live_charts_frame, text="📈 الرسوم المباشرة")

        # تبويب لوحة المعلومات التفاعلية
        self.dashboard_frame = ctk.CTkFrame(self.notebook, fg_color="#0f172a")
        self.notebook.add(self.dashboard_frame, text="🎛️ لوحة المعلومات")

        # تبويب التحليلات المتقدمة
        self.analytics_frame = ctk.CTkFrame(self.notebook, fg_color="#0f172a")
        self.notebook.add(self.analytics_frame, text="🧠 التحليلات المتقدمة")

        # تبويب الخرائط الحرارية
        self.heatmap_frame = ctk.CTkFrame(self.notebook, fg_color="#0f172a")
        self.notebook.add(self.heatmap_frame, text="🔥 الخرائط الحرارية")

        # تبويب التوقعات الذكية
        self.predictions_frame = ctk.CTkFrame(self.notebook, fg_color="#0f172a")
        self.notebook.add(self.predictions_frame, text="🔮 التوقعات الذكية")

        # إنشاء محتوى التبويبات
        self.create_live_charts_tab()
        self.create_dashboard_tab()
        self.create_analytics_tab()
        self.create_heatmap_tab()
        self.create_predictions_tab()

    def create_advanced_status_bar(self):
        """إنشاء شريط الحالة المتقدم"""
        status_frame = ctk.CTkFrame(self, fg_color=self.colors['dark'], corner_radius=0, height=40)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)

        # حالة الاتصال
        self.connection_status = ctk.CTkLabel(
            status_frame,
            text="🟢 متصل",
            font=("Arial", 10, "bold"),
            text_color="#10b981"
        )
        self.connection_status.pack(side="left", padx=20, pady=10)

        # عداد البيانات
        self.data_counter = ctk.CTkLabel(
            status_frame,
            text="📊 البيانات: 0",
            font=("Arial", 10),
            text_color="#ffffff"
        )
        self.data_counter.pack(side="left", padx=20, pady=10)

        # آخر تحديث
        self.last_update = ctk.CTkLabel(
            status_frame,
            text="🕐 آخر تحديث: --",
            font=("Arial", 10),
            text_color="#94a3b8"
        )
        self.last_update.pack(side="right", padx=20, pady=10)

    def create_live_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية المباشرة"""

        if MATPLOTLIB_AVAILABLE:
            # إطار الرسوم البيانية
            charts_container = ctk.CTkFrame(self.live_charts_frame, fg_color="#1e293b")
            charts_container.pack(fill="both", expand=True, padx=10, pady=10)

            # الرسم البياني المباشر
            self.create_live_chart(charts_container)
        else:
            no_charts_label = ctk.CTkLabel(
                self.live_charts_frame,
                text="📊 الرسوم البيانية المباشرة غير متاحة\n(يتطلب تثبيت matplotlib)",
                font=("Arial", 16),
                text_color="#94a3b8"
            )
            no_charts_label.pack(expand=True)

    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة المعلومات التفاعلية"""

        dashboard_container = ctk.CTkScrollableFrame(self.dashboard_frame, fg_color="#1e293b")
        dashboard_container.pack(fill="both", expand=True, padx=10, pady=10)

        # بطاقات المؤشرات الرئيسية
        self.create_kpi_cards(dashboard_container)

        # الرسوم البيانية الصغيرة
        self.create_mini_charts(dashboard_container)

    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات المتقدمة"""

        analytics_container = ctk.CTkScrollableFrame(self.analytics_frame, fg_color="#1e293b")
        analytics_container.pack(fill="both", expand=True, padx=10, pady=10)

        # تحليلات متقدمة
        self.create_advanced_analytics(analytics_container)

    def create_heatmap_tab(self):
        """إنشاء تبويب الخرائط الحرارية"""

        heatmap_container = ctk.CTkFrame(self.heatmap_frame, fg_color="#1e293b")
        heatmap_container.pack(fill="both", expand=True, padx=10, pady=10)

        # خريطة حرارية للأداء
        self.create_performance_heatmap(heatmap_container)

    def create_predictions_tab(self):
        """إنشاء تبويب التوقعات الذكية"""

        predictions_container = ctk.CTkScrollableFrame(self.predictions_frame, fg_color="#1e293b")
        predictions_container.pack(fill="both", expand=True, padx=10, pady=10)

        # توقعات ذكية
        self.create_smart_predictions(predictions_container)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(str(DB_PATH))
            c = conn.cursor()

            # تحميل بيانات التصفيات مع JOIN للحصول على اسم الكاشير
            c.execute("""
                SELECT f.date, f.data, COALESCE(c.name, f.admin_name, 'غير محدد') as cashier_name
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                ORDER BY f.date DESC
                LIMIT 1000
            """)
            self.filters_data = c.fetchall()

            conn.close()

            # تحديث العدادات
            self.data_counter.configure(text=f"📊 البيانات: {len(self.filters_data)}")
            self.last_update.configure(text=f"🕐 آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")

    def start_real_time_updates(self):
        """بدء التحديثات في الوقت الفعلي"""
        def update_loop():
            while True:
                if self.animation_running:
                    self.refresh_data()
                time.sleep(5)  # تحديث كل 5 ثوان

        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()

    def toggle_live_updates(self):
        """تبديل التحديثات المباشرة"""
        self.animation_running = not self.animation_running

        if self.animation_running:
            self.live_update_btn.configure(text="🔴 مباشر", fg_color=self.colors['danger'])
            self.connection_status.configure(text="🔴 مباشر", text_color="#ef4444")
        else:
            self.live_update_btn.configure(text="⏸️ متوقف", fg_color="#6b7280")
            self.connection_status.configure(text="⏸️ متوقف", text_color="#6b7280")

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        current_state = self.attributes('-fullscreen')
        self.attributes('-fullscreen', not current_state)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()

    def export_interactive_report(self):
        """تصدير التقرير التفاعلي"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("PDF files", "*.pdf")],
                title="تصدير التقرير التفاعلي"
            )
            if filename:
                if filename.endswith('.html'):
                    self.export_html_interactive(filename)
                else:
                    self.export_pdf_interactive(filename)

                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير: {e}")

    # الوظائف المساعدة (سيتم تنفيذها في الإصدارات القادمة)
    def create_live_chart(self, parent):
        """إنشاء رسم بياني مباشر"""
        pass

    def create_kpi_cards(self, parent):
        """إنشاء بطاقات المؤشرات الرئيسية"""
        pass

    def create_mini_charts(self, parent):
        """إنشاء الرسوم البيانية الصغيرة"""
        pass

    def create_advanced_analytics(self, parent):
        """إنشاء التحليلات المتقدمة"""
        pass

    def create_performance_heatmap(self, parent):
        """إنشاء خريطة حرارية للأداء"""
        pass

    def create_smart_predictions(self, parent):
        """إنشاء التوقعات الذكية"""
        pass

    def export_html_interactive(self, filename):
        """تصدير HTML تفاعلي"""
        pass

    def export_pdf_interactive(self, filename):
        """تصدير PDF تفاعلي"""
        pass