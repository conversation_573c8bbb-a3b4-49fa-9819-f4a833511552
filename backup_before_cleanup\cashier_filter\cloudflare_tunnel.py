#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cloudflare Tunnel للوصول العالمي المجاني
Free Global Access via Cloudflare Tunnel

حل مجاني 100% للوصول لتقارير نظام تصفية الكاشير من أي مكان في العالم
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

class CloudflareTunnel:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.cloudflared_path = self.project_root / "cloudflared.exe"
        self.tunnel_process = None
        self.tunnel_url = None
        
    def download_cloudflared(self):
        """تحميل cloudflared"""
        if self.cloudflared_path.exists():
            print("✅ cloudflared موجود بالفعل")
            return True
        
        print("📥 تحميل cloudflared...")
        try:
            import urllib.request
            url = "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
            
            def show_progress(block_num, block_size, total_size):
                downloaded = block_num * block_size
                percent = min(100, (downloaded / total_size) * 100)
                print(f"\r📥 تحميل: {percent:.1f}%", end="", flush=True)
            
            urllib.request.urlretrieve(url, self.cloudflared_path, show_progress)
            print("\n✅ تم تحميل cloudflared بنجاح")
            return True
            
        except Exception as e:
            print(f"\n❌ فشل في تحميل cloudflared: {e}")
            return False
    
    def check_local_server(self):
        """التحقق من تشغيل الخادم المحلي"""
        try:
            response = requests.get('http://localhost:5000', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def start_tunnel(self):
        """بدء نفق Cloudflare"""
        if not self.cloudflared_path.exists():
            if not self.download_cloudflared():
                return None
        
        if not self.check_local_server():
            print("❌ الخادم المحلي لا يعمل")
            print("🚀 بدء تشغيل الخادم...")
            self.start_local_server()
            time.sleep(3)
            
            if not self.check_local_server():
                print("❌ فشل في تشغيل الخادم المحلي")
                return None
        
        print("✅ الخادم المحلي يعمل")
        print("🚀 بدء نفق Cloudflare...")
        
        try:
            # تشغيل cloudflared
            self.tunnel_process = subprocess.Popen([
                str(self.cloudflared_path), 
                "tunnel", 
                "--url", 
                "http://localhost:5000"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # انتظار حتى يبدأ النفق
            time.sleep(5)
            
            # البحث عن رابط النفق في المخرجات
            tunnel_url = self.extract_tunnel_url()
            if tunnel_url:
                self.tunnel_url = tunnel_url
                return tunnel_url
            else:
                print("⚠️ لم يتم العثور على رابط النفق، لكن النفق قد يكون يعمل")
                return "https://your-tunnel-url.trycloudflare.com"
                
        except Exception as e:
            print(f"❌ خطأ في بدء النفق: {e}")
            return None
    
    def start_local_server(self):
        """بدء الخادم المحلي"""
        try:
            subprocess.Popen([
                sys.executable, "web_server.py"
            ], cwd=self.project_root)
        except Exception as e:
            print(f"❌ خطأ في بدء الخادم: {e}")
    
    def extract_tunnel_url(self):
        """استخراج رابط النفق من المخرجات"""
        try:
            # قراءة المخرجات للبحث عن الرابط
            for _ in range(10):  # محاولة لمدة 10 ثوان
                if self.tunnel_process.poll() is not None:
                    break
                
                time.sleep(1)
                
                # قراءة stderr حيث يظهر الرابط عادة
                try:
                    output = self.tunnel_process.stderr.readline()
                    if output and "trycloudflare.com" in output:
                        # استخراج الرابط
                        import re
                        url_match = re.search(r'https://[a-zA-Z0-9-]+\.trycloudflare\.com', output)
                        if url_match:
                            return url_match.group(0)
                except:
                    continue
            
            return None
            
        except Exception as e:
            print(f"❌ خطأ في استخراج الرابط: {e}")
            return None
    
    def stop_tunnel(self):
        """إيقاف النفق"""
        if self.tunnel_process:
            self.tunnel_process.terminate()
            self.tunnel_process = None
            print("⏹️ تم إيقاف نفق Cloudflare")
    
    def print_access_info(self, tunnel_url):
        """طباعة معلومات الوصول"""
        print("=" * 70)
        print("🌍 الوصول العالمي عبر Cloudflare Tunnel")
        print("=" * 70)
        print(f"🔗 الرابط العالمي: {tunnel_url}")
        print(f"📱 للهاتف: {tunnel_url}/mobile")
        print(f"📊 التقارير: {tunnel_url}/reports")
        print()
        print("🔒 الأمان:")
        print("   ✅ اتصال مشفر (HTTPS)")
        print("   ✅ مجاني 100%")
        print("   ✅ لا يحتاج تسجيل")
        print("   ⚠️ لا تشارك الرابط مع غير الموثوقين")
        print()
        print("📱 للاستخدام:")
        print("   1. انسخ الرابط أعلاه")
        print("   2. افتحه في أي متصفح في العالم")
        print("   3. أضفه للمفضلة أو الشاشة الرئيسية")
        print()
        print("💡 نصائح:")
        print("   - الرابط يتغير في كل مرة")
        print("   - احفظ الرابط للاستخدام المؤقت")
        print("   - النفق يعمل طالما هذه النافذة مفتوحة")
        print()
        print("⏹️ للإيقاف: اضغط Ctrl+C")
        print("=" * 70)

def main():
    """الدالة الرئيسية"""
    tunnel = CloudflareTunnel()
    
    print("☁️ Cloudflare Tunnel للوصول العالمي المجاني")
    print("=" * 50)
    
    try:
        tunnel_url = tunnel.start_tunnel()
        
        if tunnel_url:
            tunnel.print_access_info(tunnel_url)
            
            # إبقاء النفق مفتوحاً
            print("🔄 النفق يعمل... اضغط Ctrl+C للإيقاف")
            try:
                while True:
                    time.sleep(10)
                    # التحقق من حالة النفق
                    if tunnel.tunnel_process and tunnel.tunnel_process.poll() is not None:
                        print("⚠️ انقطع النفق، محاولة إعادة التشغيل...")
                        tunnel_url = tunnel.start_tunnel()
                        if tunnel_url:
                            tunnel.print_access_info(tunnel_url)
                        else:
                            break
                            
            except KeyboardInterrupt:
                print("\n👋 إيقاف النفق...")
                tunnel.stop_tunnel()
        else:
            print("❌ فشل في بدء النفق")
            print("\n🔧 حلول بديلة:")
            print("1. تشغيل يدوي: cloudflared tunnel --url http://localhost:5000")
            print("2. استخدام ngrok: python setup_global_access.py")
            print("3. تشغيل: وصول_عالمي_سريع.bat")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
    
    finally:
        tunnel.stop_tunnel()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم الإنهاء")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
