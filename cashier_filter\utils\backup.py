# نظام النسخ الاحتياطي التلقائي
import os
import shutil
import sqlite3
import json
import zipfile
from datetime import datetime, timedelta
from pathlib import Path
import threading
import time

class BackupManager:
    def __init__(self, db_path, backup_dir="backups"):
        self.db_path = db_path
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
        # إعدادات النسخ الاحتياطي
        self.auto_backup_enabled = True
        self.backup_interval_hours = 24
        self.max_backups = 30
        self.backup_thread = None
        self.stop_backup = False

    def create_backup(self, backup_type="manual"):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{backup_type}_{timestamp}"
            backup_path = self.backup_dir / f"{backup_name}.zip"
            
            # إنشاء ملف مضغوط للنسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ قاعدة البيانات
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "database.db")
                
                # نسخ ملفات الإعدادات
                config_files = [
                    "config.py",
                    "requirements.txt"
                ]
                
                for config_file in config_files:
                    if os.path.exists(config_file):
                        zipf.write(config_file, config_file)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    "timestamp": datetime.now().isoformat(),
                    "type": backup_type,
                    "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    "version": "2.0.0"
                }
                
                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return backup_path
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None

    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError("ملف النسخة الاحتياطية غير موجود")
            
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup = self.create_backup("pre_restore")
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # استعادة قاعدة البيانات
                if "database.db" in zipf.namelist():
                    # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                    if os.path.exists(self.db_path):
                        backup_current_db = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copy2(self.db_path, backup_current_db)
                    
                    # استعادة قاعدة البيانات
                    zipf.extract("database.db", path="temp_restore")
                    shutil.move("temp_restore/database.db", self.db_path)
                    
                    # تنظيف المجلد المؤقت
                    if os.path.exists("temp_restore"):
                        shutil.rmtree("temp_restore")
                
                # قراءة معلومات النسخة الاحتياطية
                if "backup_info.json" in zipf.namelist():
                    backup_info_content = zipf.read("backup_info.json").decode('utf-8')
                    backup_info = json.loads(backup_info_content)
                    return backup_info
            
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.zip"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # حذف النسخ الزائدة
            if len(backup_files) > self.max_backups:
                for old_backup in backup_files[self.max_backups:]:
                    old_backup.unlink()
                    print(f"تم حذف النسخة الاحتياطية القديمة: {old_backup.name}")
                    
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")

    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backup_files = list(self.backup_dir.glob("backup_*.zip"))
            backup_list = []
            
            for backup_file in backup_files:
                try:
                    # قراءة معلومات النسخة الاحتياطية
                    with zipfile.ZipFile(backup_file, 'r') as zipf:
                        if "backup_info.json" in zipf.namelist():
                            backup_info_content = zipf.read("backup_info.json").decode('utf-8')
                            backup_info = json.loads(backup_info_content)
                        else:
                            # معلومات افتراضية للنسخ القديمة
                            backup_info = {
                                "timestamp": datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat(),
                                "type": "unknown",
                                "database_size": 0,
                                "version": "unknown"
                            }
                    
                    backup_info["file_path"] = str(backup_file)
                    backup_info["file_size"] = backup_file.stat().st_size
                    backup_list.append(backup_info)
                    
                except Exception as e:
                    print(f"خطأ في قراءة معلومات النسخة الاحتياطية {backup_file}: {e}")
            
            # ترتيب حسب التاريخ (الأحدث أولاً)
            backup_list.sort(key=lambda x: x["timestamp"], reverse=True)
            return backup_list
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []

    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if self.auto_backup_enabled and not self.backup_thread:
            self.stop_backup = False
            self.backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
            self.backup_thread.start()
            print("تم بدء النسخ الاحتياطي التلقائي")

    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.stop_backup = True
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
            self.backup_thread = None
        print("تم إيقاف النسخ الاحتياطي التلقائي")

    def _auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        while not self.stop_backup:
            try:
                # انتظار الفترة المحددة
                for _ in range(self.backup_interval_hours * 3600):  # تحويل الساعات إلى ثوان
                    if self.stop_backup:
                        return
                    time.sleep(1)
                
                # إنشاء نسخة احتياطية تلقائية
                if not self.stop_backup:
                    backup_path = self.create_backup("auto")
                    if backup_path:
                        print(f"تم إنشاء نسخة احتياطية تلقائية: {backup_path}")
                    
            except Exception as e:
                print(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
                time.sleep(3600)  # انتظار ساعة قبل المحاولة مرة أخرى

    def export_data_to_json(self, output_file):
        """تصدير البيانات إلى ملف JSON"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            
            data = {}
            
            # تصدير جداول قاعدة البيانات
            tables = ['cashiers', 'admins', 'filters']
            
            for table in tables:
                cursor = conn.execute(f"SELECT * FROM {table}")
                rows = cursor.fetchall()
                data[table] = [dict(row) for row in rows]
            
            conn.close()
            
            # حفظ البيانات في ملف JSON
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير البيانات: {e}")
            return False

    def import_data_from_json(self, input_file):
        """استيراد البيانات من ملف JSON"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            conn = sqlite3.connect(self.db_path)
            
            # استيراد البيانات لكل جدول
            for table, rows in data.items():
                if rows:
                    # حذف البيانات الحالية
                    conn.execute(f"DELETE FROM {table}")
                    
                    # إدراج البيانات الجديدة
                    columns = list(rows[0].keys())
                    placeholders = ','.join(['?' for _ in columns])
                    
                    for row in rows:
                        values = [row[col] for col in columns]
                        conn.execute(f"INSERT INTO {table} ({','.join(columns)}) VALUES ({placeholders})", values)
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"خطأ في استيراد البيانات: {e}")
            return False

# مثيل عام لمدير النسخ الاحتياطي
backup_manager = None

def initialize_backup_manager(db_path):
    """تهيئة مدير النسخ الاحتياطي"""
    global backup_manager
    backup_manager = BackupManager(db_path)
    backup_manager.start_auto_backup()
    return backup_manager

def get_backup_manager():
    """الحصول على مدير النسخ الاحتياطي"""
    return backup_manager
