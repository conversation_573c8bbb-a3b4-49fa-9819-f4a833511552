# نافذة البحث المتقدم
import customtkinter as ctk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import json

class AdvancedSearchWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("البحث المتقدم")
        self.geometry("1200x800")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)
        
        self.search_results = []

        # إعداد تركيز النافذة
        self.setup_window_focus()

        self.create_widgets()

    def setup_window_focus(self):
        """إعداد النافذة لتظهر في المقدمة"""
        try:
            self.deiconify()
            self.lift()
            self.attributes('-topmost', True)
            self.focus_force()
            self.wm_state('normal')
            self.tkraise()

            def remove_topmost():
                try:
                    self.attributes('-topmost', False)
                    self.focus_set()
                except:
                    pass

            self.after(300, remove_topmost)
            print("✅ تم إعداد تركيز نافذة البحث المتقدم")
        except Exception as e:
            print(f"خطأ في إعداد تركيز نافذة البحث المتقدم: {e}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        header_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="🔍 البحث المتقدم في التصفيات",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # إطار معايير البحث
        search_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        search_frame.pack(pady=10, padx=20, fill="x")
        
        search_title = ctk.CTkLabel(
            search_frame,
            text="🎯 معايير البحث",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        search_title.pack(pady=10)
        
        # حقول البحث
        search_controls = ctk.CTkFrame(search_frame, fg_color="#f2f3f7", corner_radius=10)
        search_controls.pack(pady=10, padx=20, fill="x")
        
        # الصف الأول - التواريخ
        date_row = ctk.CTkFrame(search_controls, fg_color="#f2f3f7", corner_radius=0)
        date_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(date_row, text="من تاريخ:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky="e")
        self.date_from_entry = ctk.CTkEntry(date_row, width=120, placeholder_text="YYYY-MM-DD")
        self.date_from_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ctk.CTkLabel(date_row, text="إلى تاريخ:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=10, pady=5, sticky="e")
        self.date_to_entry = ctk.CTkEntry(date_row, width=120, placeholder_text="YYYY-MM-DD")
        self.date_to_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # أزرار التواريخ السريعة
        quick_dates_frame = ctk.CTkFrame(date_row, fg_color="#f2f3f7", corner_radius=0)
        quick_dates_frame.grid(row=0, column=4, padx=20, pady=5)
        
        today_btn = ctk.CTkButton(
            quick_dates_frame,
            text="اليوم",
            command=self.set_today,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=60,
            height=25,
            font=("Arial", 10)
        )
        today_btn.pack(side="left", padx=2)
        
        week_btn = ctk.CTkButton(
            quick_dates_frame,
            text="هذا الأسبوع",
            command=self.set_this_week,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=80,
            height=25,
            font=("Arial", 10)
        )
        week_btn.pack(side="left", padx=2)
        
        month_btn = ctk.CTkButton(
            quick_dates_frame,
            text="هذا الشهر",
            command=self.set_this_month,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=80,
            height=25,
            font=("Arial", 10)
        )
        month_btn.pack(side="left", padx=2)
        
        # الصف الثاني - الأسماء
        names_row = ctk.CTkFrame(search_controls, fg_color="#f2f3f7", corner_radius=0)
        names_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(names_row, text="اسم الكاشير:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky="e")
        self.cashier_name_entry = ctk.CTkEntry(names_row, width=150, placeholder_text="اسم الكاشير")
        self.cashier_name_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ctk.CTkLabel(names_row, text="اسم المسؤول:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=10, pady=5, sticky="e")
        self.admin_name_entry = ctk.CTkEntry(names_row, width=150, placeholder_text="اسم المسؤول")
        self.admin_name_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # الصف الثالث - المبالغ
        amounts_row = ctk.CTkFrame(search_controls, fg_color="#f2f3f7", corner_radius=0)
        amounts_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(amounts_row, text="المبلغ من:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky="e")
        self.amount_min_entry = ctk.CTkEntry(amounts_row, width=100, placeholder_text="0.00")
        self.amount_min_entry.grid(row=0, column=1, padx=5, pady=5)
        
        ctk.CTkLabel(amounts_row, text="المبلغ إلى:", font=("Arial", 12, "bold")).grid(row=0, column=2, padx=10, pady=5, sticky="e")
        self.amount_max_entry = ctk.CTkEntry(amounts_row, width=100, placeholder_text="0.00")
        self.amount_max_entry.grid(row=0, column=3, padx=5, pady=5)
        
        # الصف الرابع - الملاحظات
        notes_row = ctk.CTkFrame(search_controls, fg_color="#f2f3f7", corner_radius=0)
        notes_row.pack(fill="x", pady=10)
        
        ctk.CTkLabel(notes_row, text="البحث في الملاحظات:", font=("Arial", 12, "bold")).grid(row=0, column=0, padx=10, pady=5, sticky="e")
        self.notes_entry = ctk.CTkEntry(notes_row, width=300, placeholder_text="نص للبحث في الملاحظات")
        self.notes_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # أزرار البحث
        search_buttons = ctk.CTkFrame(search_controls, fg_color="#f2f3f7", corner_radius=0)
        search_buttons.pack(fill="x", pady=15)
        
        search_btn = ctk.CTkButton(
            search_buttons,
            text="🔍 بحث",
            command=self.perform_search,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=120,
            height=40,
            font=("Arial", 14, "bold")
        )
        search_btn.pack(side="left", padx=10)
        
        clear_btn = ctk.CTkButton(
            search_buttons,
            text="🗑️ مسح",
            command=self.clear_search,
            fg_color="#FF9800",
            hover_color="#F57C00",
            width=100,
            height=40,
            font=("Arial", 14, "bold")
        )
        clear_btn.pack(side="left", padx=5)
        
        export_btn = ctk.CTkButton(
            search_buttons,
            text="📄 تصدير النتائج",
            command=self.export_results,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=130,
            height=40,
            font=("Arial", 14, "bold")
        )
        export_btn.pack(side="left", padx=5)

        print_btn = ctk.CTkButton(
            search_buttons,
            text="🖨️ طباعة النتائج",
            command=self.print_results,
            fg_color="#9C27B0",
            hover_color="#7B1FA2",
            width=130,
            height=40,
            font=("Arial", 14, "bold")
        )
        print_btn.pack(side="left", padx=5)
        
        # إطار النتائج
        results_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        results_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        results_title = ctk.CTkLabel(
            results_frame,
            text="📋 نتائج البحث",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        results_title.pack(pady=10)
        
        # جدول النتائج
        table_frame = ctk.CTkFrame(results_frame, fg_color="#f2f3f7", corner_radius=10)
        table_frame.pack(pady=10, padx=20, fill="both", expand=True)
        
        columns = ("الرقم التسلسلي", "ID", "التاريخ", "الكاشير", "المسؤول", "إجمالي المبلغ", "الحالة", "تاريخ الإنشاء")
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar_results = ttk.Scrollbar(table_frame, orient="vertical", command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar_results.set)
        
        self.results_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_results.pack(side="right", fill="y", pady=10)
        
        # ربط النقر المزدوج لعرض التفاصيل
        self.results_tree.bind("<Double-1>", self.show_filter_details)
        
        # شريط الحالة
        self.status_label = ctk.CTkLabel(
            results_frame,
            text="جاهز للبحث",
            font=("Arial", 12),
            text_color="#7f8c8d"
        )
        self.status_label.pack(pady=10)

    def set_today(self):
        """تعيين تاريخ اليوم"""
        today = datetime.now().strftime("%Y-%m-%d")
        self.date_from_entry.delete(0, "end")
        self.date_from_entry.insert(0, today)
        self.date_to_entry.delete(0, "end")
        self.date_to_entry.insert(0, today)

    def set_this_week(self):
        """تعيين هذا الأسبوع"""
        today = datetime.now()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        
        self.date_from_entry.delete(0, "end")
        self.date_from_entry.insert(0, start_of_week.strftime("%Y-%m-%d"))
        self.date_to_entry.delete(0, "end")
        self.date_to_entry.insert(0, end_of_week.strftime("%Y-%m-%d"))

    def set_this_month(self):
        """تعيين هذا الشهر"""
        today = datetime.now()
        start_of_month = today.replace(day=1)
        
        # آخر يوم في الشهر
        if today.month == 12:
            end_of_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_of_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        self.date_from_entry.delete(0, "end")
        self.date_from_entry.insert(0, start_of_month.strftime("%Y-%m-%d"))
        self.date_to_entry.delete(0, "end")
        self.date_to_entry.insert(0, end_of_month.strftime("%Y-%m-%d"))

    def perform_search(self):
        """تنفيذ البحث"""
        try:
            # جمع معايير البحث
            search_criteria = {}
            
            if self.date_from_entry.get():
                search_criteria['date_from'] = self.date_from_entry.get()
            
            if self.date_to_entry.get():
                search_criteria['date_to'] = self.date_to_entry.get()
            
            if self.cashier_name_entry.get():
                search_criteria['cashier_name'] = self.cashier_name_entry.get()
            
            if self.admin_name_entry.get():
                search_criteria['admin_name'] = self.admin_name_entry.get()
            
            if self.amount_min_entry.get():
                try:
                    search_criteria['amount_min'] = float(self.amount_min_entry.get())
                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح في الحد الأدنى")
                    return
            
            if self.amount_max_entry.get():
                try:
                    search_criteria['amount_max'] = float(self.amount_max_entry.get())
                except ValueError:
                    messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح في الحد الأقصى")
                    return
            
            if self.notes_entry.get():
                search_criteria['notes'] = self.notes_entry.get()
            
            # تحديث شريط الحالة
            self.status_label.configure(text="جاري البحث...")
            self.update()
            
            # تنفيذ البحث
            try:
                print("🔍 بدء تنفيذ البحث المتقدم...")
                from utils.performance import AdvancedSearch
                self.search_results = AdvancedSearch.search_filters(search_criteria)
                print(f"✅ تم الحصول على {len(self.search_results)} نتيجة")

                # عرض النتائج
                self.display_results()

                # تحديث شريط الحالة
                self.status_label.configure(text=f"تم العثور على {len(self.search_results)} نتيجة")

            except ImportError as e:
                print(f"❌ خطأ في الاستيراد: {e}")
                messagebox.showerror("خطأ", f"فشل في تحميل وحدة البحث: {e}")
                self.status_label.configure(text="فشل في تحميل وحدة البحث")
            except Exception as e:
                print(f"❌ خطأ في البحث: {e}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("خطأ", f"فشل في تنفيذ البحث: {e}")
                self.status_label.configure(text="فشل في البحث")

        except Exception as e:
            print(f"❌ خطأ عام في البحث المتقدم: {e}")
            messagebox.showerror("خطأ", f"خطأ في معايير البحث: {e}")
            self.status_label.configure(text="خطأ في معايير البحث")

    def display_results(self):
        """عرض نتائج البحث"""
        try:
            print(f"📊 بدء عرض {len(self.search_results)} نتيجة...")

            # مسح النتائج الحالية
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            if not self.search_results:
                print("ℹ️ لا توجد نتائج للعرض")
                return

            # عرض النتائج الجديدة
            successful_displays = 0
            for i, result in enumerate(self.search_results):
                try:
                    # التحقق من صحة البيانات
                    if len(result) < 7:
                        print(f"⚠️ بيانات ناقصة في النتيجة {i}: {result}")
                        continue

                    filter_id, date, cashier_name, admin_name, data_json, created_at, sequence_number = result

                    # حساب إجمالي المبلغ
                    data = json.loads(data_json) if data_json else {}
                    totals = data.get('totals', {})

                    total_amount = (
                        float(totals.get('bank', 0) or 0) +
                        float(totals.get('cash', 0) or 0) +
                        float(totals.get('credit', 0) or 0) +
                        float(totals.get('return', 0) or 0) -
                        float(totals.get('client', 0) or 0)
                    )

                    # تحديد الحالة
                    system_sales = float(data.get('system_sales', 0) or 0)
                    difference = total_amount - system_sales

                    if difference > 0:
                        status = "فائض"
                    elif difference < 0:
                        status = "عجز"
                    else:
                        status = "متوازن"

                    # إدراج النتيجة في الجدول
                    self.results_tree.insert("", "end", values=(
                        sequence_number or "غير محدد",
                        filter_id or "غير محدد",
                        date or "غير محدد",
                        cashier_name or "غير محدد",
                        admin_name or "غير محدد",
                        f"{total_amount:.2f}",
                        status,
                        created_at or "غير محدد"
                    ))

                    successful_displays += 1

                except json.JSONDecodeError as e:
                    print(f"⚠️ خطأ في تحليل JSON للنتيجة {i}: {e}")
                    # عرض النتيجة بدون تحليل JSON
                    self.results_tree.insert("", "end", values=(
                        result[6] if len(result) > 6 else "غير محدد",  # sequence_number
                        result[0] if len(result) > 0 else "غير محدد",  # filter_id
                        result[1] if len(result) > 1 else "غير محدد",  # date
                        result[2] if len(result) > 2 else "غير محدد",  # cashier_name
                        result[3] if len(result) > 3 else "غير محدد",  # admin_name
                        "0.00",  # total_amount
                        "غير محدد",  # status
                        result[5] if len(result) > 5 else "غير محدد"   # created_at
                    ))
                except Exception as e:
                    print(f"❌ خطأ في عرض النتيجة {i}: {e}")
                    continue

            print(f"✅ تم عرض {successful_displays} نتيجة بنجاح من أصل {len(self.search_results)}")

        except Exception as e:
            print(f"❌ خطأ خطير في عرض النتائج: {e}")
            import traceback
            traceback.print_exc()

    def clear_search(self):
        """مسح معايير البحث"""
        self.date_from_entry.delete(0, "end")
        self.date_to_entry.delete(0, "end")
        self.cashier_name_entry.delete(0, "end")
        self.admin_name_entry.delete(0, "end")
        self.amount_min_entry.delete(0, "end")
        self.amount_max_entry.delete(0, "end")
        self.notes_entry.delete(0, "end")
        
        # مسح النتائج
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self.search_results = []
        self.status_label.configure(text="تم مسح البحث")

    def export_results(self):
        """تصدير نتائج البحث"""
        if not self.search_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return
        
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")],
                title="حفظ نتائج البحث"
            )
            
            if filename:
                from utils.performance import DataExporter
                if DataExporter.export_to_excel_optimized(self.search_results, filename):
                    messagebox.showinfo("نجح", f"تم تصدير النتائج إلى: {filename}")
                else:
                    messagebox.showerror("خطأ", "فشل في تصدير النتائج")
                    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النتائج: {e}")

    def print_results(self):
        """طباعة نتائج البحث"""
        if not self.search_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للطباعة")
            return

        try:
            # إنشاء تقرير HTML لنتائج البحث
            html_content = self.generate_search_results_html()

            # حفظ التقرير في ملف مؤقت
            import os
            reports_dir = "reports/generated"
            os.makedirs(reports_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{reports_dir}/search_results_{timestamp}.html"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح التقرير في المتصفح
            import webbrowser
            webbrowser.open(f'file://{os.path.abspath(filename)}')

            messagebox.showinfo("نجح", "تم فتح نتائج البحث في المتصفح للطباعة!")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة النتائج: {e}")

    def generate_search_results_html(self):
        """إنشاء تقرير HTML لنتائج البحث"""

        # جمع معايير البحث المستخدمة
        search_criteria = self.get_current_search_criteria()

        html = f'''
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>نتائج البحث المتقدم - نظام تصفية الكاشير</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap');

                @page {{
                    size: A4 landscape;
                    margin: 15mm;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', Arial, sans-serif;
                    background: white;
                    color: #2c3e50;
                    line-height: 1.6;
                    font-size: 12px;
                }}

                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    margin-bottom: 20px;
                    border-radius: 8px;
                }}

                .header h1 {{
                    font-size: 24px;
                    margin-bottom: 10px;
                }}

                .header .subtitle {{
                    font-size: 16px;
                    opacity: 0.9;
                }}

                .search-criteria {{
                    background: #f8f9fa;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }}

                .criteria-title {{
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }}

                .criteria-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 10px;
                }}

                .criteria-item {{
                    background: white;
                    padding: 8px;
                    border-radius: 4px;
                    border: 1px solid #ddd;
                }}

                .criteria-label {{
                    font-weight: bold;
                    color: #34495e;
                    margin-bottom: 3px;
                }}

                .criteria-value {{
                    color: #2c3e50;
                }}

                .results-summary {{
                    background: #e8f4f8;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 20px 0;
                    text-align: center;
                }}

                .summary-title {{
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }}

                .summary-stats {{
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 15px;
                }}

                .stat-item {{
                    background: white;
                    padding: 10px;
                    border-radius: 5px;
                    border: 1px solid #bdc3c7;
                }}

                .stat-label {{
                    font-weight: bold;
                    color: #34495e;
                    margin-bottom: 5px;
                }}

                .stat-value {{
                    font-size: 16px;
                    color: #2c3e50;
                    font-weight: bold;
                }}

                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    font-size: 10px;
                }}

                th {{
                    background: #34495e;
                    color: white;
                    padding: 8px 4px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 9px;
                }}

                td {{
                    padding: 6px 4px;
                    text-align: center;
                    border: 1px solid #ddd;
                    font-size: 9px;
                }}

                tr:nth-child(even) {{
                    background: #f8f9fa;
                }}

                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding: 15px;
                    background: #ecf0f1;
                    border-radius: 5px;
                    font-size: 10px;
                    color: #7f8c8d;
                }}

                @media print {{
                    .header {{
                        background: #667eea !important;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}

                    th {{
                        background: #34495e !important;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🔍 نتائج البحث المتقدم</h1>
                <div class="subtitle">نظام تصفية الكاشير المتكامل</div>
            </div>
        '''

        # إضافة معايير البحث
        html += self.generate_search_criteria_section(search_criteria)

        # إضافة ملخص النتائج
        html += self.generate_results_summary()

        # إضافة جدول النتائج
        html += self.generate_results_table()

        # إضافة التذييل
        html += f'''
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير المتكامل 2025</p>
                <p>تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - عدد النتائج: {len(self.search_results)}</p>
                <p><strong>تطوير: محمد الكامل - الإصدار 3.0.0</strong></p>
                <p>© 2025 - جميع الحقوق محفوظة</p>
            </div>

            <script>
                window.onload = function() {{
                    setTimeout(function() {{
                        window.print();
                    }}, 1000);
                }};
            </script>
        </body>
        </html>
        '''

        return html

    def get_current_search_criteria(self):
        """الحصول على معايير البحث الحالية"""
        criteria = {}

        if self.date_from_entry.get():
            criteria['date_from'] = self.date_from_entry.get()
        if self.date_to_entry.get():
            criteria['date_to'] = self.date_to_entry.get()
        if self.cashier_name_entry.get():
            criteria['cashier_name'] = self.cashier_name_entry.get()
        if self.admin_name_entry.get():
            criteria['admin_name'] = self.admin_name_entry.get()
        if self.amount_min_entry.get():
            criteria['amount_min'] = self.amount_min_entry.get()
        if self.amount_max_entry.get():
            criteria['amount_max'] = self.amount_max_entry.get()
        if self.notes_entry.get():
            criteria['notes'] = self.notes_entry.get()

        return criteria

    def generate_search_criteria_section(self, criteria):
        """إنشاء قسم معايير البحث"""
        if not criteria:
            return '<div class="search-criteria"><div class="criteria-title">معايير البحث: جميع التصفيات</div></div>'

        html = '''
            <div class="search-criteria">
                <div class="criteria-title">🔍 معايير البحث المستخدمة</div>
                <div class="criteria-grid">
        '''

        criteria_labels = {
            'date_from': 'من تاريخ',
            'date_to': 'إلى تاريخ',
            'cashier_name': 'اسم الكاشير',
            'admin_name': 'اسم المسؤول',
            'amount_min': 'الحد الأدنى للمبلغ',
            'amount_max': 'الحد الأقصى للمبلغ',
            'notes': 'الملاحظات'
        }

        for key, value in criteria.items():
            label = criteria_labels.get(key, key)
            html += f'''
                    <div class="criteria-item">
                        <div class="criteria-label">{label}</div>
                        <div class="criteria-value">{value}</div>
                    </div>
            '''

        html += '''
                </div>
            </div>
        '''

        return html

    def generate_results_summary(self):
        """إنشاء ملخص النتائج"""
        if not self.search_results:
            return ''

        # حساب الإحصائيات
        total_count = len(self.search_results)
        total_amount = 0
        cashiers = set()
        admins = set()

        for result in self.search_results:
            try:
                # تحليل البيانات من JSON
                import json
                data = json.loads(result[4])  # البيانات في العمود الخامس
                totals = data.get('totals', {})
                amount = (totals.get('bank', 0) + totals.get('cash', 0) +
                         totals.get('credit', 0) + totals.get('return', 0) -
                         totals.get('client', 0))
                total_amount += amount

                if result[2]:  # اسم الكاشير
                    cashiers.add(result[2])
                if result[3]:  # اسم المسؤول
                    admins.add(result[3])
            except:
                continue

        avg_amount = total_amount / total_count if total_count > 0 else 0

        html = f'''
            <div class="results-summary">
                <div class="summary-title">📊 ملخص النتائج</div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-label">عدد التصفيات</div>
                        <div class="stat-value">{total_count}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">إجمالي المبالغ</div>
                        <div class="stat-value">{total_amount:.2f} ريال</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">متوسط التصفية</div>
                        <div class="stat-value">{avg_amount:.2f} ريال</div>
                    </div>
                </div>
                <div class="summary-stats" style="margin-top: 10px;">
                    <div class="stat-item">
                        <div class="stat-label">عدد الكاشيرين</div>
                        <div class="stat-value">{len(cashiers)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">عدد المسؤولين</div>
                        <div class="stat-value">{len(admins)}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">تاريخ البحث</div>
                        <div class="stat-value">{datetime.now().strftime('%Y-%m-%d')}</div>
                    </div>
                </div>
            </div>
        '''

        return html

    def generate_results_table(self):
        """إنشاء جدول النتائج"""
        if not self.search_results:
            return '<div style="text-align: center; padding: 20px;">لا توجد نتائج للعرض</div>'

        html = '''
            <table>
                <thead>
                    <tr>
                        <th>رقم التصفية</th>
                        <th>التاريخ</th>
                        <th>الكاشير</th>
                        <th>المسؤول</th>
                        <th>البنكي</th>
                        <th>النقدي</th>
                        <th>الآجل</th>
                        <th>العملاء</th>
                        <th>المرتجعات</th>
                        <th>الإجمالي</th>
                        <th>مبيعات النظام</th>
                        <th>الفرق</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
        '''

        for result in self.search_results:
            try:
                import json
                data = json.loads(result[4])
                totals = data.get('totals', {})
                system_sales = data.get('system_sales', 0)

                bank = totals.get('bank', 0)
                cash = totals.get('cash', 0)
                credit = totals.get('credit', 0)
                client = totals.get('client', 0)
                return_amount = totals.get('return', 0)

                total = bank + cash + credit + return_amount - client
                difference = total - system_sales

                if difference > 0:
                    status = "فائض"
                    status_color = "#27ae60"
                elif difference < 0:
                    status = "عجز"
                    status_color = "#e74c3c"
                else:
                    status = "متوازن"
                    status_color = "#3498db"

                html += f'''
                    <tr>
                        <td>{result[0]}</td>
                        <td>{result[1]}</td>
                        <td>{result[2] or 'غير محدد'}</td>
                        <td>{result[3] or 'غير محدد'}</td>
                        <td>{bank:.2f}</td>
                        <td>{cash:.2f}</td>
                        <td>{credit:.2f}</td>
                        <td>{client:.2f}</td>
                        <td>{return_amount:.2f}</td>
                        <td><strong>{total:.2f}</strong></td>
                        <td>{system_sales:.2f}</td>
                        <td style="color: {status_color};">{difference:.2f}</td>
                        <td style="color: {status_color}; font-weight: bold;">{status}</td>
                    </tr>
                '''
            except:
                # في حالة خطأ في تحليل البيانات
                html += f'''
                    <tr>
                        <td>{result[0]}</td>
                        <td>{result[1]}</td>
                        <td>{result[2] or 'غير محدد'}</td>
                        <td>{result[3] or 'غير محدد'}</td>
                        <td colspan="9">خطأ في تحليل البيانات</td>
                    </tr>
                '''

        html += '''
                </tbody>
            </table>
        '''

        return html

    def show_filter_details(self, event):
        """عرض تفاصيل التصفية المحددة"""
        selected_item = self.results_tree.selection()
        if not selected_item:
            return
        
        item_values = self.results_tree.item(selected_item[0])['values']
        filter_id = item_values[0]
        
        # هنا يمكن فتح نافذة تفاصيل التصفية
        messagebox.showinfo("تفاصيل التصفية", f"عرض تفاصيل التصفية رقم: {filter_id}")

def show_advanced_search(parent=None):
    """عرض نافذة البحث المتقدم"""
    try:
        print("🔍 إنشاء نافذة البحث المتقدم...")
        window = AdvancedSearchWindow(parent)
        print("✅ تم إنشاء نافذة البحث المتقدم بنجاح")
        return window
    except Exception as e:
        print(f"❌ خطأ في إنشاء نافذة البحث المتقدم: {e}")
        import traceback
        traceback.print_exc()
        return None
