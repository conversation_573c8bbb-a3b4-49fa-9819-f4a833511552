# شاشة عرض تقارير التصفية
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class ReportsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("تقارير التصفية")
        self.geometry("1000x700")
        self.configure(bg="#f2f3f7")

        # جعل النافذة تظهر في المقدمة
        self.setup_window_focus()

        self.create_widgets()
        self.load_reports()

    def setup_window_focus(self):
        """إعداد النافذة لتظهر في المقدمة"""
        try:
            # التأكد من أن النافذة مرئية
            self.deiconify()

            # رفع النافذة للمقدمة
            self.lift()

            # جعل النافذة في المقدمة مؤقتاً
            self.attributes('-topmost', True)

            # إعطاء التركيز للنافذة
            self.focus_force()

            # محاولة جعل النافذة نشطة
            try:
                self.wm_state('normal')
                self.tkraise()
            except:
                pass

            # إزالة خاصية البقاء في المقدمة بعد 300 مللي ثانية
            def remove_topmost():
                try:
                    self.attributes('-topmost', False)
                    self.focus_set()
                except:
                    pass

            self.after(300, remove_topmost)

        except Exception as e:
            print(f"خطأ في إعداد تركيز النافذة: {e}")

    def create_widgets(self):
        title = ctk.CTkLabel(self, text="تقارير التصفية", font=("Tajawal", 26, "bold"), text_color="#444", bg_color="#f2f3f7")
        title.pack(pady=20)

        # إطار البحث
        search_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        search_frame.pack(padx=20, pady=10, fill="x")

        # عنوان البحث
        search_title = ctk.CTkLabel(
            search_frame,
            text="🔍 البحث والتصفية",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        search_title.pack(pady=10)

        # إطار عناصر البحث
        search_controls_frame = ctk.CTkFrame(search_frame, fg_color="transparent")
        search_controls_frame.pack(fill="x", padx=20, pady=10)

        # الصف الأول - البحث النصي
        search_row1 = ctk.CTkFrame(search_controls_frame, fg_color="transparent")
        search_row1.pack(fill="x", pady=5)

        # حقل البحث
        ctk.CTkLabel(search_row1, text="البحث:", font=("Arial", 12, "bold")).pack(side="right", padx=5)
        self.search_entry = ctk.CTkEntry(
            search_row1,
            placeholder_text="ابحث بالرقم التسلسلي، اسم الكاشير، المسؤول أو التاريخ...",
            width=300,
            font=("Arial", 12)
        )
        self.search_entry.pack(side="right", padx=10)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        # نوع البحث
        ctk.CTkLabel(search_row1, text="البحث في:", font=("Arial", 12, "bold")).pack(side="right", padx=5)
        self.search_type = ctk.CTkComboBox(
            search_row1,
            values=["الكل", "الرقم التسلسلي", "اسم الكاشير", "اسم المسؤول", "التاريخ"],
            width=150,
            font=("Arial", 12)
        )
        self.search_type.set("الكل")
        self.search_type.pack(side="right", padx=10)

        # الصف الثاني - أزرار التحكم
        search_row2 = ctk.CTkFrame(search_controls_frame, fg_color="transparent")
        search_row2.pack(fill="x", pady=5)

        # أزرار البحث
        search_btn = ctk.CTkButton(
            search_row2,
            text="🔍 بحث",
            command=self.search_filters,
            width=100,
            font=("Arial", 12, "bold"),
            fg_color="#3498db",
            hover_color="#2980b9"
        )
        search_btn.pack(side="right", padx=5)

        clear_btn = ctk.CTkButton(
            search_row2,
            text="🗑️ مسح",
            command=self.clear_search,
            width=100,
            font=("Arial", 12, "bold"),
            fg_color="#95a5a6",
            hover_color="#7f8c8d"
        )
        clear_btn.pack(side="right", padx=5)

        refresh_btn = ctk.CTkButton(
            search_row2,
            text="🔄 تحديث",
            command=self.load_reports,
            width=100,
            font=("Arial", 12, "bold"),
            fg_color="#27ae60",
            hover_color="#229954"
        )
        refresh_btn.pack(side="right", padx=5)

        # عداد النتائج
        self.results_label = ctk.CTkLabel(
            search_row2,
            text="إجمالي التصفيات: 0",
            font=("Arial", 12),
            text_color="#7f8c8d"
        )
        self.results_label.pack(side="left", padx=10)

        # الجدول
        self.tree = ttk.Treeview(self, columns=("sequence", "id", "cashier", "admin", "date"), show="headings", height=15)
        self.tree.heading("sequence", text="الرقم التسلسلي")
        self.tree.heading("id", text="رقم التصفية")
        self.tree.heading("cashier", text="اسم الكاشير")
        self.tree.heading("admin", text="اسم المسؤول")
        self.tree.heading("date", text="تاريخ التصفية")

        # تحسين عرض الأعمدة
        self.tree.column("sequence", width=120, anchor="center")
        self.tree.column("id", width=100, anchor="center")
        self.tree.column("cashier", width=150, anchor="center")
        self.tree.column("admin", width=150, anchor="center")
        self.tree.column("date", width=120, anchor="center")

        self.tree.pack(padx=20, pady=10, fill="both", expand=True)
        self.tree.bind('<Double-1>', self.on_double_click)

    def load_reports(self):
        """تحميل جميع التقارير"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute('''SELECT f.sequence_number, f.id, ca.name,
                     COALESCE(f.admin_name, ad.name, 'غير محدد') as admin_name,
                     f.date FROM filters f
                     LEFT JOIN cashiers ca ON f.cashier_id=ca.id
                     LEFT JOIN admins ad ON f.admin_id=ad.id
                     ORDER BY f.sequence_number DESC''')

        rows = c.fetchall()
        for row in rows:
            self.tree.insert('', 'end', values=row)

        # تحديث عداد النتائج
        self.results_label.configure(text=f"إجمالي التصفيات: {len(rows)}")
        conn.close()

    def on_search_change(self, event):
        """البحث التلقائي عند الكتابة"""
        # تأخير البحث قليلاً لتحسين الأداء
        if hasattr(self, 'search_timer'):
            self.after_cancel(self.search_timer)
        self.search_timer = self.after(300, self.search_filters)

    def search_filters(self):
        """البحث في التصفيات"""
        search_text = self.search_entry.get().strip()
        search_type = self.search_type.get()

        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()

        # بناء الاستعلام حسب نوع البحث
        base_query = '''SELECT f.sequence_number, f.id, ca.name,
                       COALESCE(f.admin_name, ad.name, 'غير محدد') as admin_name,
                       f.date FROM filters f
                       LEFT JOIN cashiers ca ON f.cashier_id=ca.id
                       LEFT JOIN admins ad ON f.admin_id=ad.id'''

        if not search_text:
            # إذا لم يكن هناك نص بحث، عرض جميع النتائج
            query = base_query + " ORDER BY f.sequence_number DESC"
            c.execute(query)
        else:
            # تحديد شروط البحث
            if search_type == "الكل":
                query = base_query + '''
                    WHERE (CAST(f.sequence_number AS TEXT) LIKE ? OR
                           CAST(f.id AS TEXT) LIKE ? OR
                           ca.name LIKE ? OR
                           COALESCE(f.admin_name, ad.name, '') LIKE ? OR
                           f.date LIKE ?)
                    ORDER BY f.sequence_number DESC'''
                search_param = f"%{search_text}%"
                c.execute(query, (search_param, search_param, search_param, search_param, search_param))

            elif search_type == "الرقم التسلسلي":
                query = base_query + " WHERE CAST(f.sequence_number AS TEXT) LIKE ? ORDER BY f.sequence_number DESC"
                c.execute(query, (f"%{search_text}%",))

            elif search_type == "اسم الكاشير":
                query = base_query + " WHERE ca.name LIKE ? ORDER BY f.sequence_number DESC"
                c.execute(query, (f"%{search_text}%",))

            elif search_type == "اسم المسؤول":
                query = base_query + " WHERE COALESCE(f.admin_name, ad.name, '') LIKE ? ORDER BY f.sequence_number DESC"
                c.execute(query, (f"%{search_text}%",))

            elif search_type == "التاريخ":
                query = base_query + " WHERE f.date LIKE ? ORDER BY f.sequence_number DESC"
                c.execute(query, (f"%{search_text}%",))

        rows = c.fetchall()
        for row in rows:
            self.tree.insert('', 'end', values=row)

        # تحديث عداد النتائج
        if search_text:
            self.results_label.configure(text=f"نتائج البحث: {len(rows)} من إجمالي التصفيات")
        else:
            self.results_label.configure(text=f"إجمالي التصفيات: {len(rows)}")

        conn.close()

    def clear_search(self):
        """مسح البحث وإعادة تحميل جميع التقارير"""
        self.search_entry.delete(0, 'end')
        self.search_type.set("الكل")
        self.load_reports()

    def on_double_click(self, event):
        item = self.tree.selection()
        if item:
            filter_id = self.tree.item(item[0])["values"][1]  # الآن filter_id في العمود الثاني
            self.show_filter_details(filter_id)

    def show_filter_details(self, filter_id):
        # نافذة تفاصيل التصفية مع أزرار تصدير
        details_win = ctk.CTkToplevel(self)
        details_win.title(f"تفاصيل التصفية رقم {filter_id}")
        details_win.geometry("900x700")
        details_win.configure(bg="#f2f3f7")

        # الحصول على بيانات التصفية
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute("""
            SELECT f.data, f.date,
                   COALESCE(f.admin_name, a.name, 'غير محدد') as admin_name,
                   c.name as cashier_name, c.number as cashier_number, f.sequence_number
            FROM filters f
            LEFT JOIN cashiers c ON f.cashier_id = c.id
            LEFT JOIN admins a ON f.admin_id = a.id
            WHERE f.id=?
        """, (filter_id,))
        result = c.fetchone()
        conn.close()

        if result:
            data_json, filter_date, admin_name, cashier_name, cashier_number, sequence_number = result
            details = json.loads(data_json)

            # إنشاء عرض منسق بالعربية
            formatted_text = self.format_filter_details(details, filter_date, admin_name, cashier_name, filter_id)

            # عرض التفاصيل المنسقة
            txt = ctk.CTkTextbox(details_win, font=("Arial", 14), width=800, height=500)
            txt.pack(pady=20, padx=20)
            txt.insert("end", formatted_text)
            txt.configure(state="disabled")

            # أزرار التصدير والطباعة
            def export_pdf():
                try:
                    from reports.export_utils import export_filter_to_pdf
                    import tkinter.filedialog as fd
                    filename = fd.asksaveasfilename(
                        defaultextension=".pdf",
                        filetypes=[("PDF files", "*.pdf")],
                        title="حفظ تقرير PDF"
                    )
                    if filename:
                        # تحضير البيانات للتصدير
                        filter_data = {
                            'sequence_number': sequence_number,
                            'cashier_name': cashier_name,
                            'cashier_number': cashier_number or 'غير محدد',
                            'admin_name': admin_name,
                            'date': filter_date
                        }
                        totals = details.get('totals', {})
                        system_sales = details.get('system_sales', 0)

                        if export_filter_to_pdf(filter_data, totals, system_sales, filename):
                            messagebox.showinfo("نجح", f"تم تصدير PDF إلى: {filename}")
                        else:
                            messagebox.showerror("خطأ", "فشل في تصدير PDF")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في تصدير PDF: {e}")

            def export_excel():
                try:
                    from reports.export_utils import export_filter_to_excel
                    import tkinter.filedialog as fd
                    filename = fd.asksaveasfilename(
                        defaultextension=".xlsx",
                        filetypes=[("Excel files", "*.xlsx")],
                        title="حفظ تقرير Excel"
                    )
                    if filename:
                        # تحضير البيانات للتصدير
                        filter_data = {
                            'cashier_name': cashier_name,
                            'admin_name': admin_name,
                            'date': filter_date
                        }
                        totals = details.get('totals', {})
                        system_sales = details.get('system_sales', 0)

                        if export_filter_to_excel(filter_data, totals, system_sales, filename):
                            messagebox.showinfo("نجح", f"تم تصدير Excel إلى: {filename}")
                        else:
                            messagebox.showerror("خطأ", "فشل في تصدير Excel")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في تصدير Excel: {e}")

            def print_html():
                try:
                    from reports.html_print import generate_filter_report

                    # تحضير البيانات للطباعة
                    credit_transactions = details.get('credit_transactions', [])
                    client_transactions = details.get('client_transactions', [])
                    suppliers_transactions = details.get('suppliers_transactions', [])

                    # إذا لم تكن التفاصيل موجودة، أنشئ بيانات تجريبية من المجاميع
                    totals = details.get('totals', {})

                    if not credit_transactions and totals.get('credit', 0) > 0:
                        credit_transactions = [{
                            'client': 'عميل آجل',
                            'invoice': 'INV-001',
                            'amount': totals.get('credit', 0),
                            'date': filter_date
                        }]

                    if not client_transactions and totals.get('client', 0) > 0:
                        client_transactions = [{
                            'client': 'عميل',
                            'type': 'نقدي',
                            'amount': totals.get('client', 0),
                            'ref': 'REF-001'
                        }]

                    filter_data = {
                        'sequence_number': sequence_number,
                        'cashier_name': cashier_name,
                        'cashier_number': cashier_number or 'غير محدد',
                        'admin_name': admin_name,
                        'date': filter_date,
                        'credit_transactions': credit_transactions,
                        'client_transactions': client_transactions,
                        'suppliers_transactions': suppliers_transactions
                    }

                    system_sales = details.get('system_sales', 0)

                    if generate_filter_report(filter_data, totals, system_sales):
                        messagebox.showinfo("نجح", "تم فتح التقرير في المتصفح للطباعة")
                    else:
                        messagebox.showerror("خطأ", "فشل في إنشاء التقرير")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في الطباعة: {e}")

            # إطار الأزرار
            btn_frame = ctk.CTkFrame(details_win, fg_color="#f2f3f7")
            btn_frame.pack(pady=10)

            # أزرار التصدير والطباعة
            print_btn = ctk.CTkButton(
                btn_frame,
                text="🖨️ طباعة",
                command=print_html,
                fg_color="#2196f3",
                text_color="#fff",
                width=120,
                font=("Arial", 12, "bold")
            )
            print_btn.pack(side="left", padx=10)

            excel_btn = ctk.CTkButton(
                btn_frame,
                text="📊 Excel",
                command=export_excel,
                fg_color="#4caf50",
                text_color="#fff",
                width=120,
                font=("Arial", 12, "bold")
            )
            excel_btn.pack(side="left", padx=10)

            pdf_btn = ctk.CTkButton(
                btn_frame,
                text="📄 PDF",
                command=export_pdf,
                fg_color="#e57373",
                text_color="#fff",
                width=120,
                font=("Arial", 12, "bold")
            )
            pdf_btn.pack(side="left", padx=10)

    def format_filter_details(self, details, filter_date, admin_name, cashier_name, filter_id):
        """تنسيق تفاصيل التصفية بالعربية"""

        formatted = f"""
═══════════════════════════════════════════════════════════════
                    📋 تفاصيل التصفية رقم {filter_id}
═══════════════════════════════════════════════════════════════

📅 معلومات التصفية:
────────────────────────────────────────────────────────────────
• التاريخ: {filter_date or 'غير محدد'}
• اسم الكاشير: {cashier_name or 'غير محدد'}
• اسم المسؤول: {admin_name or 'غير محدد'}

💰 ملخص المجاميع:
────────────────────────────────────────────────────────────────
"""

        # عرض المجاميع
        totals = details.get('totals', {})
        if totals:
            formatted += f"• المقبوضات البنكية: {totals.get('bank', 0):.2f} ريال\n"
            formatted += f"• المقبوضات النقدية: {totals.get('cash', 0):.2f} ريال\n"
            formatted += f"• فواتير الآجل: {totals.get('credit', 0):.2f} ريال\n"
            formatted += f"• المقبوضات من العملاء: {totals.get('client', 0):.2f} ريال\n"
            formatted += f"• المرتجعات: {totals.get('return', 0):.2f} ريال\n"

            total_receipts = (totals.get('bank', 0) + totals.get('cash', 0) +
                            totals.get('credit', 0) + totals.get('return', 0) -
                            totals.get('client', 0))
            formatted += f"\n📊 إجمالي المقبوضات: {total_receipts:.2f} ريال\n"

        # مبيعات النظام
        system_sales = details.get('system_sales', 0)
        if system_sales:
            formatted += f"🖥️ مبيعات النظام: {system_sales:.2f} ريال\n"

            if totals:
                difference = total_receipts - float(system_sales)
                if difference > 0:
                    formatted += f"📈 فائض: {difference:.2f} ريال\n"
                elif difference < 0:
                    formatted += f"📉 عجز: {abs(difference):.2f} ريال\n"
                else:
                    formatted += f"⚖️ متوازن: 0.00 ريال\n"

        # فواتير عملاء الآجل
        credit_transactions = details.get('credit_transactions', [])
        if credit_transactions:
            formatted += f"\n📋 فواتير عملاء الآجل ({len(credit_transactions)} فاتورة):\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            for i, transaction in enumerate(credit_transactions, 1):
                formatted += f"{i}. العميل: {transaction.get('client', 'غير محدد')}\n"
                formatted += f"   رقم الفاتورة: {transaction.get('invoice', 'غير محدد')}\n"
                formatted += f"   المبلغ: {transaction.get('amount', 0):.2f} ريال\n"
                formatted += f"   التاريخ: {transaction.get('date', 'غير محدد')}\n\n"

        # المقبوضات من العملاء (مع طريقة الدفع المحسنة)
        client_transactions = details.get('client_transactions', [])
        if client_transactions:
            formatted += f"👥 المقبوضات من العملاء ({len(client_transactions)} عملية):\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            for i, transaction in enumerate(client_transactions, 1):
                payment_method = transaction.get('payment_method', 'نقدي')
                payment_icon = "💵" if payment_method == 'نقدي' else "💳"

                formatted += f"{i}. العميل: {transaction.get('client', 'غير محدد')}\n"
                formatted += f"   نوع المقبوض: {transaction.get('type', 'غير محدد')}\n"
                formatted += f"   طريقة الدفع: {payment_icon} {payment_method}\n"
                formatted += f"   المبلغ: {transaction.get('amount', 0):.2f} ريال\n"
                if transaction.get('ref'):
                    formatted += f"   رقم المرجع: {transaction.get('ref')}\n"
                formatted += "\n"

        # المقبوضات البنكية
        bank_transactions = details.get('bank_transactions', [])
        if bank_transactions:
            formatted += f"💳 المقبوضات البنكية ({len(bank_transactions)} عملية):\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            for i, transaction in enumerate(bank_transactions, 1):
                formatted += f"{i}. نوع العملية: {transaction.get('type', 'غير محدد')}\n"
                formatted += f"   البنك: {transaction.get('bank', 'غير محدد')}\n"
                formatted += f"   المبلغ: {transaction.get('amount', 0):.2f} ريال\n"
                formatted += f"   رقم المرجع: {transaction.get('ref', 'غير محدد')}\n\n"

        # المقبوضات النقدية
        cash_data = details.get('cash_data', {})
        if cash_data:
            formatted += f"💰 المقبوضات النقدية:\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            cash_types = [
                (500, "500 ريال"), (100, "100 ريال"), (50, "50 ريال"),
                (10, "10 ريال"), (5, "5 ريال"), (1, "1 ريال"),
                (0.5, "50 هللة"), (0.25, "25 هللة")
            ]

            for value, label in cash_types:
                count = cash_data.get(str(value), 0)
                if count and float(count) > 0:
                    total_value = float(count) * value
                    formatted += f"• {label}: {count} × {value:.2f} = {total_value:.2f} ريال\n"

        # المرتجعات
        return_transactions = details.get('return_transactions', [])
        if return_transactions:
            formatted += f"\n↩️ المرتجعات ({len(return_transactions)} عملية):\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            for i, transaction in enumerate(return_transactions, 1):
                formatted += f"{i}. رقم الفاتورة: {transaction.get('invoice', 'غير محدد')}\n"
                formatted += f"   العميل: {transaction.get('client', 'غير محدد')}\n"
                formatted += f"   المبلغ: {transaction.get('amount', 0):.2f} ريال\n"
                formatted += f"   السبب: {transaction.get('reason', 'غير محدد')}\n\n"

        # الموردين (للمتابعة فقط)
        suppliers_transactions = details.get('suppliers_transactions', [])
        if suppliers_transactions:
            formatted += f"\n🏭 الموردين - للمتابعة فقط ({len(suppliers_transactions)} مورد):\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            formatted += "⚠️ ملاحظة: هذا القسم لا يؤثر على حسابات التصفية\n"
            formatted += "────────────────────────────────────────────────────────────────\n"

            suppliers_total = 0
            for i, transaction in enumerate(suppliers_transactions, 1):
                amount = float(transaction.get('amount', 0))
                suppliers_total += amount
                payment_method = transaction.get('payment_method', 'نقدي')

                # أيقونة طريقة الدفع
                if payment_method == 'نقدي':
                    payment_icon = "💵"
                elif payment_method == 'شيك':
                    payment_icon = "📄"
                else:  # تحويل بنكي
                    payment_icon = "🏦"

                formatted += f"{i}. المورد: {transaction.get('supplier_name', 'غير محدد')}\n"
                formatted += f"   المبلغ المسلم: {amount:.2f} ريال\n"
                formatted += f"   طريقة الدفع: {payment_icon} {payment_method}\n"
                if transaction.get('notes'):
                    formatted += f"   ملاحظات: {transaction.get('notes')}\n"
                formatted += "\n"

            formatted += f"📊 إجمالي المدفوعات للموردين: {suppliers_total:.2f} ريال\n"
            formatted += "⚠️ لا يدخل في حسابات التصفية\n\n"

        # الملاحظات
        notes = details.get('notes', '')
        if notes:
            formatted += f"📝 ملاحظات:\n"
            formatted += "────────────────────────────────────────────────────────────────\n"
            formatted += f"{notes}\n"

        formatted += "\n═══════════════════════════════════════════════════════════════\n"
        formatted += "                        انتهى التقرير\n"
        formatted += "═══════════════════════════════════════════════════════════════\n"

        return formatted
