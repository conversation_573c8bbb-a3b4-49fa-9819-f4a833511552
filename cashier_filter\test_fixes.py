#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات المطبقة
Test Applied Fixes
"""

import sqlite3
import json
from pathlib import Path

DB_PATH = Path(__file__).parent / "db" / "cashier_filter.db"

def test_statistics_calculation():
    """اختبار حساب الإحصائيات المصحح"""
    print("🧮 اختبار حساب إجمالي المبالغ...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار الطريقة المصححة
        cursor.execute("SELECT data FROM filters WHERE data IS NOT NULL")
        total_amount = 0
        filter_count = 0
        
        for row in cursor.fetchall():
            try:
                data = json.loads(row['data'])
                totals = data.get('totals', {})
                
                bank_total = float(totals.get('bank', 0))
                cash_total = float(totals.get('cash', 0))
                credit_total = float(totals.get('credit', 0))
                client_total = float(totals.get('client', 0))
                return_total = float(totals.get('return', 0))
                
                filter_total = bank_total + cash_total + credit_total + return_total - client_total
                total_amount += filter_total
                filter_count += 1
                
                print(f"تصفية {filter_count}: {filter_total:.2f} ريال")
                print(f"  - بنكي: {bank_total:.2f}")
                print(f"  - نقدي: {cash_total:.2f}")
                print(f"  - آجل: {credit_total:.2f}")
                print(f"  - مقبوضات: {client_total:.2f}")
                print(f"  - مرتجعات: {return_total:.2f}")
                
            except Exception as e:
                print(f"خطأ في معالجة تصفية: {e}")
                continue
        
        print(f"\n✅ إجمالي المبالغ المحسوب: {total_amount:.2f} ريال")
        print(f"📊 عدد التصفيات المعالجة: {filter_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإحصائيات: {e}")

def test_cashier_name_join():
    """اختبار JOIN لأسماء الكاشيرين"""
    print("\n👥 اختبار JOIN لأسماء الكاشيرين...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار الاستعلام المصحح
        cursor.execute("""
            SELECT f.date, f.data, COALESCE(c.name, f.admin_name, 'غير محدد') as cashier_name
            FROM filters f
            LEFT JOIN cashiers c ON f.cashier_id = c.id
            ORDER BY f.date DESC 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        print(f"✅ تم جلب {len(results)} تصفية بنجاح")
        
        for i, row in enumerate(results, 1):
            print(f"{i}. التاريخ: {row['date']} | الكاشير: {row['cashier_name']}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار JOIN: {e}")

def test_web_server_statistics():
    """اختبار إحصائيات خادم الويب"""
    print("\n🌐 اختبار إحصائيات خادم الويب...")
    print("=" * 50)
    
    try:
        # محاكاة دالة get_statistics المصححة
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # إجمالي التصفيات
        cursor.execute("SELECT COUNT(*) as total FROM filters")
        total_filters = cursor.fetchone()['total']
        
        # إجمالي المبالغ المصحح
        cursor.execute("SELECT data FROM filters WHERE data IS NOT NULL")
        total_amount = 0
        
        for row in cursor.fetchall():
            try:
                data = json.loads(row['data'])
                totals = data.get('totals', {})
                bank_total = float(totals.get('bank', 0))
                cash_total = float(totals.get('cash', 0))
                credit_total = float(totals.get('credit', 0))
                client_total = float(totals.get('client', 0))
                return_total = float(totals.get('return', 0))
                
                filter_total = bank_total + cash_total + credit_total + return_total - client_total
                total_amount += filter_total
            except:
                continue
        
        print(f"✅ إجمالي التصفيات: {total_filters}")
        print(f"✅ إجمالي المبالغ: {total_amount:.2f} ريال")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إحصائيات الويب: {e}")

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("\n🗄️ اختبار هيكل قاعدة البيانات...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ الجداول الموجودة: {[table[0] for table in tables]}")
        
        # فحص أعمدة جدول filters
        cursor.execute("PRAGMA table_info(filters)")
        filters_columns = cursor.fetchall()
        print(f"✅ أعمدة جدول filters:")
        for col in filters_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # فحص أعمدة جدول cashiers
        cursor.execute("PRAGMA table_info(cashiers)")
        cashiers_columns = cursor.fetchall()
        print(f"✅ أعمدة جدول cashiers:")
        for col in cashiers_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص هيكل قاعدة البيانات: {e}")

def main():
    """تشغيل جميع الاختبارات"""
    print("🔧 اختبار الإصلاحات المطبقة")
    print("=" * 60)
    
    test_database_structure()
    test_cashier_name_join()
    test_statistics_calculation()
    test_web_server_statistics()
    
    print("\n🎉 انتهى اختبار الإصلاحات!")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
