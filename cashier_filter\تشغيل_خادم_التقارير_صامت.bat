@echo off
title تشغيل خادم التقارير في الخلفية

echo.
echo ========================================
echo تشغيل خادم التقارير في الخلفية
echo ========================================
echo.

cd /d "%~dp0"

echo [1/3] التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo [OK] Python متاح

echo.
echo [2/3] التحقق من قاعدة البيانات...
if not exist "db\cashier_filter.db" (
    echo خطأ: قاعدة البيانات غير موجودة
    echo يرجى تشغيل التطبيق الرئيسي أولاً
    pause
    exit /b 1
)

echo [OK] قاعدة البيانات موجودة

echo.
echo [3/3] تشغيل الخادم في الخلفية...

REM تشغيل الخادم في الخلفية بدون نافذة
start /B python start_web_server_silent.py

echo.
echo تم بدء تشغيل خادم التقارير في الخلفية!
echo.
echo معلومات الوصول:
echo ==================
echo الرابط المحلي: http://localhost:5000
echo واجهة الهاتف: http://localhost:5000/mobile
echo.
echo ملاحظة: الخادم يعمل الآن في الخلفية بدون نوافذ إضافية
echo لإيقاف الخادم: أغلق التطبيق الرئيسي أو أعد تشغيل الكمبيوتر
echo.

REM انتظار قصير ثم فتح المتصفح
timeout /t 3 > nul
start http://localhost:5000

echo تم فتح المتصفح على الرابط
echo.
pause
