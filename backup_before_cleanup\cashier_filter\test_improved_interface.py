#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الواجهة المحسنة مع ترتيب الأزرار
Test Improved Interface with Button Organization
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interface_import():
    """اختبار استيراد الواجهة المحسنة"""
    print("🧪 اختبار استيراد الواجهة المحسنة...")
    
    try:
        from ui.main_window import MainWindow
        print("✅ تم استيراد MainWindow بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_interface_creation():
    """اختبار إنشاء الواجهة المحسنة"""
    print("🧪 اختبار إنشاء الواجهة المحسنة...")
    
    try:
        from ui.main_window import MainWindow
        
        # إنشاء النافذة
        app = MainWindow("مستخدم تجريبي")
        print("✅ تم إنشاء النافذة بنجاح")
        
        # فحص الخصائص
        print(f"📏 حجم النافذة: {app.geometry()}")
        print(f"📝 عنوان النافذة: {app.title()}")
        print(f"👤 المستخدم الحالي: {app.current_user}")
        
        # فحص الدوال الجديدة
        new_methods = [
            'create_button_groups',
            'create_button_group',
            'create_info_bar'
        ]
        
        working_methods = []
        for method_name in new_methods:
            if hasattr(app, method_name):
                working_methods.append(method_name)
        
        print(f"🔧 الدوال الجديدة: {', '.join(working_methods)}")
        
        app.destroy()
        print("✅ تم إغلاق النافذة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_features():
    """اختبار ميزات الواجهة المحسنة"""
    print("🧪 اختبار ميزات الواجهة المحسنة...")
    
    try:
        from ui.main_window import MainWindow
        
        app = MainWindow("مستخدم تجريبي")
        
        print("🔍 فحص التحسينات:")
        
        # فحص مجموعات الأزرار
        print("📊 مجموعات الأزرار المتوقعة:")
        expected_groups = [
            "🚀 العمليات الأساسية",
            "📊 التقارير والتحليلات", 
            "👥 الإدارة والصلاحيات",
            "🌐 الخدمات المتقدمة"
        ]
        
        for group in expected_groups:
            print(f"   ✅ {group}")
        
        # فحص الأزرار في كل مجموعة
        print("\n🔘 الأزرار في كل مجموعة:")
        
        print("   🚀 العمليات الأساسية:")
        print("      ➕ بدء تصفية جديدة")
        print("      📁 عرض تقارير التصفية")
        print("      🔍 البحث المتقدم")
        print("      📝 تعديل تصفية محفوظة")
        
        print("   📊 التقارير والتحليلات:")
        print("      📈 التقارير المتقدمة")
        print("      📊 الإحصائيات والتحليلات")
        print("      📊 لوحة المعلومات التفاعلية")
        print("      🤖 التحليل الذكي")
        
        print("   👥 الإدارة والصلاحيات:")
        print("      👤 إدارة الكاشير")
        print("      🧑‍💼 إدارة المسؤولين")
        print("      🔐 إدارة الصلاحيات")
        print("      💾 إدارة النسخ الاحتياطي")
        
        print("   🌐 الخدمات المتقدمة:")
        print("      🌐 التكامل السحابي")
        print("      🌐 خادم التقارير")
        print("      🔔 الإشعارات والتنبيهات")
        print("      ⚙️ إعدادات التطبيق")
        
        app.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات: {e}")
        return False

def test_interface_display():
    """اختبار عرض الواجهة المحسنة"""
    print("🧪 اختبار عرض الواجهة المحسنة...")
    
    try:
        from ui.main_window import MainWindow
        
        print("🚀 تشغيل الواجهة المحسنة...")
        print("\n🎯 التحسينات المطبقة:")
        print("   🎨 تصميم أنيق مع ألوان متناسقة")
        print("   📊 ترتيب الأزرار في 4 مجموعات منطقية")
        print("   🔲 تخطيط شبكي 2x2 لكل مجموعة")
        print("   📱 شريط معلومات علوي محسن")
        print("   👤 معلومات المستخدم مع الحالة")
        print("   🔔 أزرار سريعة للإشعارات والإعدادات")
        print("   📏 أحجام أزرار متناسقة ومريحة")
        print("   🎭 تأثيرات بصرية عند التمرير")
        
        print(f"\n📊 مجموعات الأزرار:")
        print("   1️⃣ العمليات الأساسية (أخضر)")
        print("   2️⃣ التقارير والتحليلات (أزرق)")
        print("   3️⃣ الإدارة والصلاحيات (برتقالي)")
        print("   4️⃣ الخدمات المتقدمة (أخضر فاتح)")
        
        print(f"\n🎨 نظام الألوان:")
        print("   🟢 الأساسية: #28a745 (أخضر)")
        print("   🔵 التقارير: #007bff (أزرق)")
        print("   🟠 الإدارة: #ffc107 (برتقالي)")
        print("   🌐 الخدمات: #17a2b8 (أزرق فاتح)")
        
        print(f"\n🚀 تشغيل النافذة...")
        
        # إنشاء وتشغيل النافذة
        app = MainWindow("مستخدم تجريبي")
        
        print("✅ تم إنشاء النافذة بنجاح")
        print("🎉 الواجهة المحسنة جاهزة للاستخدام!")
        
        # تشغيل النافذة
        app.mainloop()
        
        print("✅ تم إغلاق النافذة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عرض النافذة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🎨 اختبار الواجهة المحسنة مع ترتيب الأزرار")
    print("=" * 70)
    print("🌟 تم تحسين الواجهة الأساسية بالميزات التالية:")
    print("   • ترتيب الأزرار في 4 مجموعات منطقية")
    print("   • تخطيط شبكي 2x2 لكل مجموعة")
    print("   • شريط معلومات علوي محسن")
    print("   • نظام ألوان متناسق ومنظم")
    print("   • تصميم أنيق بدون تبويبات")
    print("   • أحجام أزرار متناسقة ومريحة")
    print("   • تأثيرات بصرية عند التمرير")
    print("   • معلومات المستخدم والحالة")
    print("=" * 70)
    
    # تشغيل الاختبارات
    tests = [
        ("استيراد الواجهة", test_interface_import),
        ("إنشاء النافذة", test_interface_creation),
        ("اختبار الميزات", test_interface_features),
        ("عرض الواجهة", test_interface_display)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print(f"\n📊 نتائج الاختبارات:")
    print("=" * 40)
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 الإجمالي: {success_count}/{total_count} اختبار نجح")
    
    if success_count == total_count:
        print("🎉 جميع الاختبارات نجحت! الواجهة المحسنة جاهزة للاستخدام")
        print("\n🚀 لتشغيل النظام مع الواجهة المحسنة:")
        print("   python main.py")
        print("\n💡 الميزات الجديدة:")
        print("   • أزرار منظمة في 4 مجموعات")
        print("   • تخطيط شبكي مريح")
        print("   • شريط معلومات محسن")
        print("   • ألوان متناسقة ومنظمة")
    else:
        print("⚠️ بعض الاختبارات فشلت، لكن الواجهة قد تعمل")

if __name__ == "__main__":
    main()
