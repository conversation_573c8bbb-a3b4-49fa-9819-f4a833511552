@echo off
chcp 65001 > nul
title نظام تصفية الكاشير v3.5.0 - الإصدار التنفيذي
color 0A
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██        🏪 نظام تصفية الكاشير v3.5.0 - الإصدار التنفيذي      ██
echo ██                                                            ██
echo ██  🎉 جميع الميزات الجديدة مدمجة:                            ██
echo ██  💳 طريقة الدفع في مقبوضات العملاء                       ██
echo ██  🏭 جدول الموردين منفصل                                   ██
echo ██  👥 أسماء العملاء في التقارير                            ██
echo ██  ⚖️ حساب الفارق الدقيق                                   ██
echo ██  📊 التقرير الشامل المحسن                                ██
echo ██  🌐 الوصول العالمي                                       ██
echo ██                                                            ██
echo ██  ⚡ لا يحتاج Python - يعمل فوراً!                        ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo 🚀 بدء تشغيل النظام...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🎯 بيانات تسجيل الدخول الافتراضية:                        │
echo │                                                             │
echo │     👤 اسم المستخدم: admin                                │
echo │     🔐 كلمة المرور: 123456                                │
echo │                                                             │
echo │  💡 يمكنك تغيير كلمة المرور من الإعدادات                  │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM تشغيل النظام
CashierFilterSystem_v3.5.0.exe

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 💡 تأكد من وجود ملف CashierFilterSystem_v3.5.0.exe
    echo.
    pause
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo 👋 شكراً لاستخدام نظام تصفية الكاشير!
)

pause
