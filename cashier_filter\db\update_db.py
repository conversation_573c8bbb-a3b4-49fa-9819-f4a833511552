# تحديث قاعدة البيانات لإضافة الحقول المفقودة
import sqlite3
import os

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

def update_database():
    """تحديث قاعدة البيانات لإضافة الحقول المفقودة"""
    try:
        if not os.path.exists(DB_PATH):
            print("قاعدة البيانات غير موجودة")
            return False
            
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        
        # التحقق من وجود الحقول المطلوبة في جدول filters
        c.execute("PRAGMA table_info(filters)")
        columns = [column[1] for column in c.fetchall()]
        
        # إضافة حقل admin_name إذا لم يكن موجوداً
        if 'admin_name' not in columns:
            print("إضافة حقل admin_name...")
            c.execute("ALTER TABLE filters ADD COLUMN admin_name TEXT")
            
        # إضافة حقل notes إذا لم يكن موجوداً
        if 'notes' not in columns:
            print("إضافة حقل notes...")
            c.execute("ALTER TABLE filters ADD COLUMN notes TEXT")

        # إضافة حقل الترقيم التسلسلي إذا لم يكن موجوداً
        if 'sequence_number' not in columns:
            print("إضافة حقل الترقيم التسلسلي...")
            c.execute("ALTER TABLE filters ADD COLUMN sequence_number INTEGER")

        # تحديث البيانات الموجودة لملء حقل admin_name من جدول admins
        print("تحديث البيانات الموجودة...")
        c.execute("""
            UPDATE filters
            SET admin_name = (
                SELECT a.name
                FROM admins a
                WHERE a.id = filters.admin_id
            )
            WHERE admin_name IS NULL AND admin_id IS NOT NULL
        """)

        # تحديث الترقيم التسلسلي للبيانات الموجودة
        print("تحديث الترقيم التسلسلي...")
        c.execute("""
            UPDATE filters
            SET sequence_number = (
                SELECT COUNT(*) FROM filters f2
                WHERE f2.id <= filters.id
            )
            WHERE sequence_number IS NULL
        """)
        
        conn.commit()
        conn.close()
        
        print("تم تحديث قاعدة البيانات بنجاح!")
        return True

    except Exception as e:
        print(f"خطأ في تحديث قاعدة البيانات: {e}")
        return False

def get_next_sequence_number():
    """الحصول على الرقم التسلسلي التالي"""
    try:
        if not os.path.exists(DB_PATH):
            return 1

        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()

        # الحصول على أعلى رقم تسلسلي
        c.execute("SELECT MAX(sequence_number) FROM filters")
        result = c.fetchone()
        max_sequence = result[0] if result[0] is not None else 0

        conn.close()
        return max_sequence + 1

    except Exception as e:
        print(f"خطأ في الحصول على الرقم التسلسلي: {e}")
        return 1

if __name__ == "__main__":
    update_database()
