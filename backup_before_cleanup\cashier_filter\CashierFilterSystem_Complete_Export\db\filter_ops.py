# دوال التعامل مع قاعدة البيانات للتصفية
import sqlite3
import json
import os

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

def get_next_sequence_number():
    """الحصول على الرقم التسلسلي التالي"""
    try:
        if not os.path.exists(DB_PATH):
            return 1

        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()

        # الحصول على أعلى رقم تسلسلي
        c.execute("SELECT MAX(sequence_number) FROM filters")
        result = c.fetchone()
        max_sequence = result[0] if result[0] is not None else 0

        conn.close()
        return max_sequence + 1

    except Exception as e:
        print(f"خطأ في الحصول على الرقم التسلسلي: {e}")
        return 1

def save_filter(filter_data, details):
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()

    try:
        # الحصول على الرقم التسلسلي التالي
        sequence_number = get_next_sequence_number()
        print(f"🔢 الرقم التسلسلي للتصفية الجديدة: {sequence_number}")

        # إضافة الكاشير إذا لم يكن موجودًا
        c.execute("SELECT id FROM cashiers WHERE number=?", (filter_data["cashier_id"],))
        cashier = c.fetchone()
        if not cashier:
            c.execute("INSERT INTO cashiers (name, number) VALUES (?, ?)", (filter_data["cashier_name"], filter_data["cashier_id"]))
            cashier_id = c.lastrowid
        else:
            cashier_id = cashier[0]

        # إضافة المسؤول إذا لم يكن موجودًا
        c.execute("SELECT id FROM admins WHERE name=?", (filter_data["admin_name"],))
        admin = c.fetchone()
        if not admin:
            c.execute("INSERT INTO admins (name, username, password) VALUES (?, ?, ?)", (filter_data["admin_name"], filter_data["admin_name"], "1234"))
            admin_id = c.lastrowid
        else:
            admin_id = admin[0]

        # حفظ التصفية مع الرقم التسلسلي
        c.execute("""INSERT INTO filters
                     (cashier_id, admin_id, date, data, admin_name, sequence_number)
                     VALUES (?, ?, ?, ?, ?, ?)""",
                  (cashier_id, admin_id, filter_data["date"],
                   json.dumps(details, ensure_ascii=False),
                   filter_data["admin_name"], sequence_number))

        filter_id = c.lastrowid
        conn.commit()
        conn.close()

        print(f"✅ تم حفظ التصفية بنجاح - المعرف: {filter_id}, الرقم التسلسلي: {sequence_number}")
        return True

    except Exception as e:
        print(f"❌ خطأ في حفظ التصفية: {e}")
        conn.rollback()
        conn.close()
        return False
