# UI Main Window (Enhanced Neumorphic Design)
import customtkinter as ctk
from tkinter import messagebox
import json
import os
import sys
from pathlib import Path

# إضافة مسار utils للوصول لمدير الخطوط
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))

try:
    from utils.font_manager import font_manager, get_stable_font, get_stable_font_size
except ImportError:
    # في حالة عدم توفر مدير الخطوط، استخدم دوال بديلة
    def get_stable_font(size_type="normal", weight="normal"):
        sizes = {"small": 12, "normal": 14, "medium": 16, "large": 18, "button": 16, "title": 20}
        size = sizes.get(size_type, 14)
        return ("Arial", size, weight)

    def get_stable_font_size(font_size):
        try:
            size = int(font_size) if font_size else 14
            return max(min(size, 18), 12)
        except:
            return 14

# تهيئة قاعدة البيانات عند بدء التطبيق
from db.init_db import init_db
init_db()

# دالة مساعدة لجعل النوافذ تظهر في المقدمة
def bring_window_to_front(window):
    """جعل النافذة تظهر في المقدمة"""
    try:
        # التأكد من أن النافذة مرئية
        window.deiconify()

        # رفع النافذة للمقدمة
        window.lift()

        # جعل النافذة في المقدمة مؤقتاً
        window.attributes('-topmost', True)

        # إعطاء التركيز للنافذة
        window.focus_force()

        # محاولة جعل النافذة نشطة
        try:
            window.wm_state('normal')
            window.tkraise()
        except:
            pass

        # إزالة خاصية البقاء في المقدمة بعد 300 مللي ثانية
        def remove_topmost():
            try:
                window.attributes('-topmost', False)
                window.focus_set()
            except:
                pass

        window.after(300, remove_topmost)

    except Exception as e:
        print(f"خطأ في إظهار النافذة في المقدمة: {e}")

# دالة مساعدة لضمان استقرار حجم الخط
def ensure_stable_font_size(font_size, min_size=12, max_size=18):
    """ضمان أن حجم الخط ضمن الحدود المسموحة"""
    try:
        size = int(font_size) if font_size else 14
        return max(min(size, max_size), min_size)
    except (ValueError, TypeError):
        return 14  # حجم افتراضي آمن

# تحميل وتطبيق الإعدادات مع ضمان استقرار حجم الخط
def load_and_apply_settings():
    """تحميل وتطبيق إعدادات الواجهة مع ضمان استقرار حجم الخط"""
    settings_file = "settings.json"
    default_settings = {
        "ui": {
            "theme": "light",
            "font_size": 14,  # حجم افتراضي مستقر
            "language": "العربية"
        }
    }

    try:
        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                ui_settings = settings.get("ui", default_settings["ui"])

                # تطبيق المظهر
                theme = ui_settings.get("theme", "light")
                ctk.set_appearance_mode(theme)

                # تطبيق حجم الخط مع ضمان الاستقرار
                font_size = ensure_stable_font_size(ui_settings.get("font_size", 14))
                ui_settings["font_size"] = font_size  # تحديث القيمة المستقرة

                return ui_settings
        else:
            # استخدام الإعدادات الافتراضية
            ctk.set_appearance_mode("light")
            return default_settings["ui"]
    except Exception as e:
        print(f"خطأ في تحميل الإعدادات: {e}")
        ctk.set_appearance_mode("light")
        return default_settings["ui"]

# تحميل الإعدادات عند بدء التطبيق
ui_settings = load_and_apply_settings()
ctk.set_default_color_theme("blue")

class MainWindow(ctk.CTk):
    def __init__(self, current_user="مستخدم"):
        super().__init__()
        self.title("نظام تصفية الكاشير - إصدار محسن")
        self.geometry("1000x800")
        self.configure(bg="#f2f3f7")
        self.resizable(True, True)
        self.minsize(900, 700)

        # التعامل مع current_user سواء كان نص أو قاموس
        if isinstance(current_user, dict):
            self.current_user = current_user.get('name', 'مستخدم')
            self.current_user_data = current_user
        else:
            self.current_user = current_user
            self.current_user_data = None

        # تحميل الإعدادات الحالية وضمان استقرار حجم الخط باستخدام مدير الخطوط
        self.ui_settings = ui_settings
        self.font_size = get_stable_font_size(self.ui_settings.get("font_size", 14))
        self.min_font_size = 12  # حد أدنى لحجم الخط
        self.max_font_size = 18  # حد أقصى لحجم الخط

        # تحديث مدير الخطوط بالحجم الحالي
        try:
            font_manager.update_font_sizes(self.font_size)
        except:
            pass

        # تعيين أيقونة التطبيق إذا كانت متوفرة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass

        self.create_widgets()

        # إضافة شريط تسجيل الخروج
        self.create_logout_bar()

        # بدء تحديث الإشعارات
        self.start_notifications_update()

        # إضافة إشعارات تجريبية
        self.add_sample_notifications()

    def create_widgets(self):
        # إطار رئيسي بتأثير Neumorphic
        main_frame = ctk.CTkFrame(
            self,
            fg_color="#f2f3f7",
            corner_radius=0
        )
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان الرئيسي مع تأثير ظليل
        title_frame = ctk.CTkFrame(
            main_frame,
            fg_color="#e0e5ec",
            corner_radius=25,
            height=100
        )
        title_frame.pack(pady=20, padx=20, fill="x")
        title_frame.pack_propagate(False)

        self.title_label = ctk.CTkLabel(
            title_frame,
            text="🏪 نظام تصفية الكاشير المتكامل 2025",
            font=get_stable_font("title", "bold"),
            text_color="#2c3e50"
        )
        self.title_label.pack(expand=True)

        subtitle_label = ctk.CTkLabel(
            title_frame,
            text="الإصدار الثالث - نظام متطور لإدارة وتصفية حسابات الكاشير مع ذكاء اصطناعي",
            font=get_stable_font("normal"),
            text_color="#7f8c8d"
        )
        subtitle_label.pack()

        # شريط المعلومات العلوي
        self.create_info_bar()

        # إطار الأزرار قابل للتمرير
        buttons_frame = ctk.CTkScrollableFrame(
            main_frame,
            fg_color="#f2f3f7",
            corner_radius=0
        )
        buttons_frame.pack(pady=20, fill="both", expand=True)

        # تصميم الأزرار بتأثير Neumorphic محسن مع خط مستقر من مدير الخطوط
        btn_style = {
            "font": get_stable_font("button", "bold"),
            "corner_radius": 20,
            "height": 70,
            "width": 400,
            "border_width": 0
        }

        # أزرار بألوان مختلفة وتأثيرات ظليلة
        buttons_config = [
            ("➕ بدء تصفية جديدة", self.start_new, "#4CAF50", "#45a049"),
            ("📁 عرض تقارير التصفية", self.show_reports, "#2196F3", "#1976D2"),
            ("🔍 البحث المتقدم", self.show_advanced_search, "#00BCD4", "#0097A7"),
            ("📈 التقارير المتقدمة", self.show_advanced_reports, "#673AB7", "#512DA8"),
            ("🤖 التحليل الذكي بالذكاء الاصطناعي", self.show_ai_analysis, "#FF6B35", "#E55A2B"),
            ("📊 لوحة المعلومات التفاعلية", self.show_dashboard, "#6C5CE7", "#5A4FCF"),
            ("📝 تعديل تصفية محفوظة", self.edit_filter, "#FF9800", "#F57C00"),
            ("👤 إدارة الكاشير", self.manage_cashier, "#9C27B0", "#7B1FA2"),
            ("🧑‍💼 إدارة المسؤولين", self.manage_admins, "#607D8B", "#455A64"),
            ("🔐 إدارة الصلاحيات", self.manage_permissions, "#8BC34A", "#689F38"),
            ("💾 إدارة النسخ الاحتياطي", self.manage_backups, "#E91E63", "#C2185B"),
            ("📊 الإحصائيات والتحليلات", self.show_statistics, "#795548", "#5D4037"),
            ("🌐 التكامل السحابي", self.show_cloud_integration, "#00D2D3", "#00B8B9"),
            ("🌐 خادم التقارير", self.start_web_server, "#1ABC9C", "#16A085"),
            ("🔔 الإشعارات والتنبيهات", self.show_notifications, "#FF5722", "#D84315"),
            ("⚙️ إعدادات التطبيق", self.show_settings, "#37474F", "#263238")
        ]

        for text, command, color, hover_color in buttons_config:
            btn = ctk.CTkButton(
                buttons_frame,
                text=text,
                command=command,
                fg_color=color,
                hover_color=hover_color,
                text_color="white",
                **btn_style
            )
            btn.pack(pady=12)





    def start_new(self):
        from ui.filter_entry import FilterEntryWindow
        window = FilterEntryWindow(self)
        bring_window_to_front(window)
        # سيتم فتح نافذة إدخال بيانات التصفية

    def show_reports(self):
        from ui.reports import ReportsWindow
        window = ReportsWindow(self)
        bring_window_to_front(window)

    def show_advanced_search(self):
        try:
            print("🔍 فتح نافذة البحث المتقدم...")
            from ui.advanced_search import show_advanced_search
            window = show_advanced_search(self)
            if window:
                bring_window_to_front(window)
                print("✅ تم فتح نافذة البحث المتقدم بنجاح")
        except Exception as e:
            print(f"❌ خطأ في فتح البحث المتقدم: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح البحث المتقدم: {e}")

    def show_advanced_reports(self):
        try:
            from ui.advanced_reports import AdvancedReportsWindow
            window = AdvancedReportsWindow(self)
            bring_window_to_front(window)
        except ImportError as e:
            if "matplotlib" in str(e):
                messagebox.showwarning("تحذير", "matplotlib غير مثبت. سيتم فتح التقارير بدون الرسوم البيانية.")
                # محاولة فتح النافذة بدون matplotlib
                try:
                    from ui.advanced_reports_simple import AdvancedReportsSimpleWindow
                    window = AdvancedReportsSimpleWindow(self)
                    bring_window_to_front(window)
                except:
                    messagebox.showerror("خطأ", "فشل في فتح التقارير المتقدمة")
            else:
                messagebox.showerror("خطأ", f"فشل في تحميل المكتبات: {e}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح التقارير المتقدمة: {e}")

    def show_ai_analysis(self):
        """عرض نافذة التحليل الذكي بالذكاء الاصطناعي"""
        try:
            from ui.ai_analysis import show_ai_analysis
            show_ai_analysis(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح التحليل الذكي: {e}")

    def edit_filter(self):
        from ui.edit_filter import EditFilterWindow
        window = EditFilterWindow(self)
        bring_window_to_front(window)

    def manage_cashier(self):
        from ui.manage_cashier import ManageCashierWindow
        window = ManageCashierWindow(self)
        bring_window_to_front(window)

    def manage_admins(self):
        from ui.manage_admins import ManageAdminsWindow
        window = ManageAdminsWindow(self)
        bring_window_to_front(window)

    def manage_permissions(self):
        try:
            from utils.permissions import show_permissions_manager, require_permission, Permission
            # التحقق من الصلاحية
            if require_permission(Permission.SYSTEM_ADMIN):
                show_permissions_manager(self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح إدارة الصلاحيات: {e}")

    def manage_backups(self):
        from ui.backup_manager import BackupManagerWindow
        window = BackupManagerWindow(self)
        bring_window_to_front(window)

    def show_statistics(self):
        from ui.statistics import StatisticsWindow
        window = StatisticsWindow(self)
        bring_window_to_front(window)

    def start_web_server(self):
        """تشغيل خادم التقارير"""
        try:
            import subprocess
            import sys
            import requests
            import time

            # التحقق من تشغيل الخادم بالفعل
            try:
                response = requests.get('http://localhost:5000', timeout=3)
                if response.status_code == 200:
                    result = messagebox.askyesno(
                        "خادم التقارير يعمل بالفعل",
                        "خادم التقارير يعمل بالفعل على http://localhost:5000\n\n" +
                        "هل تريد فتحه في المتصفح؟"
                    )
                    if result:
                        import webbrowser
                        webbrowser.open('http://localhost:5000')
                    return
            except:
                pass

            # تشغيل الخادم في نافذة منفصلة
            messagebox.showinfo(
                "تشغيل خادم التقارير",
                "سيتم تشغيل خادم التقارير في نافذة منفصلة.\n\n" +
                "انتظر قليلاً حتى يبدأ الخادم، ثم يمكنك:\n" +
                "• الوصول من الكمبيوتر: http://localhost:5000\n" +
                "• الوصول من الهاتف: http://[IP]/5000/mobile\n" +
                "• استخدام الوصول العالمي من الإعدادات"
            )

            # تشغيل الخادم في الخلفية (بدون نافذة)
            subprocess.Popen([
                sys.executable, "start_web_server_silent.py"
            ], creationflags=subprocess.CREATE_NO_WINDOW)

            # انتظار قصير ثم التحقق
            def check_server():
                time.sleep(5)  # انتظار أطول للتأكد من بدء الخادم

                # قراءة معلومات الخادم من الملف
                server_info_file = Path("server_info.txt")
                error_file = Path("server_error.txt")

                if error_file.exists():
                    # قراءة رسالة الخطأ
                    try:
                        with open(error_file, 'r', encoding='utf-8') as f:
                            error_msg = f.read()
                        error_file.unlink()  # حذف ملف الخطأ
                        self.after(0, lambda: messagebox.showerror(
                            "خطأ في تشغيل الخادم", error_msg
                        ))
                    except:
                        self.after(0, lambda: messagebox.showerror(
                            "خطأ", "فشل في تشغيل خادم التقارير"
                        ))
                    return

                if server_info_file.exists():
                    # قراءة معلومات الخادم
                    try:
                        with open(server_info_file, 'r', encoding='utf-8') as f:
                            server_info = f.read()

                        # استخراج المنفذ من المعلومات
                        import re
                        port_match = re.search(r'localhost:(\d+)', server_info)
                        port = port_match.group(1) if port_match else "5000"

                        # التحقق من عمل الخادم
                        try:
                            response = requests.get(f'http://localhost:{port}', timeout=5)
                            if response.status_code == 200:
                                self.after(0, lambda: messagebox.showinfo(
                                    "نجح التشغيل",
                                    f"تم تشغيل خادم التقارير بنجاح في الخلفية!\n\n" +
                                    f"🔗 الرابط: http://localhost:{port}\n" +
                                    f"📱 للهاتف: http://localhost:{port}/mobile\n\n" +
                                    "يمكنك الآن استخدام الوصول العالمي من الإعدادات.\n" +
                                    "الخادم يعمل في الخلفية بدون نوافذ إضافية."
                                ))

                                # فتح المتصفح تلقائياً
                                import webbrowser
                                webbrowser.open(f'http://localhost:{port}')
                            else:
                                self.after(0, lambda: messagebox.showwarning(
                                    "تحذير",
                                    f"تم تشغيل الخادم لكن قد يحتاج وقت إضافي.\n" +
                                    f"جرب الرابط: http://localhost:{port}"
                                ))
                        except:
                            self.after(0, lambda: messagebox.showwarning(
                                "تحذير",
                                f"تم تشغيل الخادم في الخلفية.\n" +
                                f"جرب الرابط: http://localhost:{port}"
                            ))
                    except:
                        self.after(0, lambda: messagebox.showinfo(
                            "تم التشغيل",
                            "تم تشغيل خادم التقارير في الخلفية.\n" +
                            "جرب الرابط: http://localhost:5000"
                        ))
                else:
                    # لم يتم إنشاء ملف المعلومات بعد
                    self.after(0, lambda: messagebox.showinfo(
                        "جاري التشغيل",
                        "تم بدء تشغيل خادم التقارير في الخلفية.\n" +
                        "قد يحتاج دقيقة للبدء.\n" +
                        "جرب الرابط: http://localhost:5000"
                    ))

            # تشغيل التحقق في خيط منفصل
            import threading
            threading.Thread(target=check_server, daemon=True).start()

        except Exception as e:
            messagebox.showerror(
                "خطأ في تشغيل خادم التقارير",
                f"فشل في تشغيل خادم التقارير:\n{e}\n\n" +
                "يمكنك تشغيله يدوياً:\n" +
                "• ملف: تشغيل_خادم_التقارير.bat\n" +
                "• أمر: python start_web_server.py"
            )

    def show_notifications(self):
        from utils.notifications import show_notifications_window
        show_notifications_window(self)

    def show_settings(self):
        from ui.settings import SettingsWindow
        settings_window = SettingsWindow(self)
        bring_window_to_front(settings_window)

        # ربط إغلاق نافذة الإعدادات بتحديث الإعدادات
        def on_settings_close():
            self.reload_settings()

        # ربط الحدث (سيتم استدعاؤه عند إغلاق النافذة)
        settings_window.protocol("WM_DELETE_WINDOW", lambda: [settings_window.destroy(), on_settings_close()])

    def reload_settings(self):
        """إعادة تحميل وتطبيق الإعدادات مع ضمان استقرار حجم الخط"""
        try:
            global ui_settings
            ui_settings = load_and_apply_settings()
            self.ui_settings = ui_settings

            # ضمان استقرار حجم الخط ضمن الحدود المسموحة
            new_font_size = self.ui_settings.get("font_size", 14)
            self.font_size = max(min(new_font_size, self.max_font_size), self.min_font_size)

            # تطبيق حجم الخط على النافذة الحالية
            self.update_font_sizes(self.font_size)

            # إعادة إنشاء الواجهة بحجم الخط الجديد
            self.recreate_interface()

        except Exception as e:
            print(f"خطأ في إعادة تحميل الإعدادات: {e}")

    def update_font_sizes(self, font_size):
        """تحديث أحجام الخطوط في النافذة الرئيسية مع ضمان الاستقرار"""
        try:
            # ضمان أن حجم الخط ضمن الحدود المسموحة
            stable_font_size = max(min(font_size, self.max_font_size), self.min_font_size)

            # تحديث خطوط العناصر في النافذة
            for widget in self.winfo_children():
                self.update_widget_font(widget, stable_font_size)
        except Exception as e:
            print(f"خطأ في تحديث أحجام الخطوط: {e}")

    def update_widget_font(self, widget, font_size):
        """تحديث خط عنصر واحد وأطفاله مع ضمان الاستقرار"""
        try:
            # تحديث خط العنصر الحالي إذا كان يدعم الخط
            if hasattr(widget, 'configure') and hasattr(widget, 'cget'):
                try:
                    current_font = widget.cget('font')
                    if current_font:
                        if isinstance(current_font, tuple):
                            # الحفاظ على نوع الخط والوزن، تحديث الحجم فقط
                            font_family = current_font[0] if len(current_font) > 0 else "Arial"
                            font_weight = current_font[2] if len(current_font) > 2 else "normal"

                            # تحديد حجم الخط المناسب حسب نوع العنصر
                            if "bold" in str(font_weight):
                                # للعناصر العريضة، استخدم حجم أكبر قليلاً
                                adjusted_size = max(font_size + 2, self.min_font_size + 2)
                            else:
                                adjusted_size = max(font_size, self.min_font_size)

                            new_font = (font_family, adjusted_size, font_weight)
                        else:
                            # إنشاء خط جديد مع حجم مستقر
                            new_font = ("Arial", max(font_size, self.min_font_size))

                        widget.configure(font=new_font)
                except Exception as font_error:
                    # في حالة فشل تحديث الخط، استخدم خط افتراضي
                    try:
                        widget.configure(font=("Arial", max(font_size, self.min_font_size)))
                    except:
                        pass

            # تحديث خطوط الأطفال
            for child in widget.winfo_children():
                self.update_widget_font(child, font_size)

        except Exception as e:
            pass  # تجاهل الأخطاء في العناصر الفردية

    def recreate_interface(self):
        """إعادة إنشاء الواجهة بحجم الخط الجديد"""
        try:
            # حفظ حالة النافذة الحالية
            current_geometry = self.geometry()

            # مسح المحتوى الحالي
            for widget in self.winfo_children():
                widget.destroy()

            # إعادة إنشاء الواجهة
            self.create_widgets()

            # استعادة حجم النافذة
            self.geometry(current_geometry)

        except Exception as e:
            print(f"خطأ في إعادة إنشاء الواجهة: {e}")
            # في حالة الفشل، أعد إنشاء الواجهة الأساسية
            try:
                self.create_widgets()
            except:
                pass

    def show_ai_analysis(self):
        """عرض نافذة التحليل الذكي بالذكاء الاصطناعي"""
        try:
            from ui.ai_analysis import AIAnalysisWindow
            AIAnalysisWindow(self)
        except ImportError:
            messagebox.showinfo("قريباً", "ميزة التحليل الذكي بالذكاء الاصطناعي ستكون متاحة قريباً في التحديث القادم!")

    def show_dashboard(self):
        """عرض لوحة المعلومات التفاعلية"""
        try:
            from ui.dashboard import show_dashboard
            show_dashboard(self)
        except Exception as e:
            print(f"خطأ في فتح لوحة المعلومات: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح لوحة المعلومات: {e}")

    def show_cloud_integration(self):
        """عرض نافذة التكامل السحابي المتقدم"""
        try:
            # محاولة استخدام النسخة الكاملة أولاً
            try:
                from ui.cloud_integration import CloudIntegrationWindow
                window = CloudIntegrationWindow(self)
                bring_window_to_front(window)
                print("✅ تم فتح نافذة التكامل السحابي الكاملة")
            except ImportError:
                # استخدام النسخة المبسطة إذا فشلت النسخة الكاملة
                from ui.cloud_integration_simple import CloudIntegrationSimpleWindow
                window = CloudIntegrationSimpleWindow(self)
                bring_window_to_front(window)
                print("✅ تم فتح نافذة التكامل السحابي المبسطة")
        except Exception as e:
            print(f"❌ خطأ في فتح التكامل السحابي: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التكامل السحابي: {e}")

    def create_info_bar(self):
        """إنشاء شريط المعلومات العلوي"""
        info_bar = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        info_bar.pack(pady=10, padx=20, fill="x")

        # معلومات المستخدم
        user_frame = ctk.CTkFrame(info_bar, fg_color="#e0e5ec", corner_radius=0)
        user_frame.pack(side="left", padx=20, pady=10)

        user_label = ctk.CTkLabel(
            user_frame,
            text=f"{self.current_user}",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        user_label.pack(side="left")

        # إشعارات غير مقروءة
        notifications_frame = ctk.CTkFrame(info_bar, fg_color="#e0e5ec", corner_radius=0)
        notifications_frame.pack(side="right", padx=20, pady=10)

        self.notifications_btn = ctk.CTkButton(
            notifications_frame,
            text="🔔 الإشعارات",
            command=self.show_notifications,
            fg_color="#FF5722",
            hover_color="#D84315",
            width=120,
            height=30,
            font=("Arial", 12, "bold")
        )
        self.notifications_btn.pack(side="left")

        self.unread_count_label = ctk.CTkLabel(
            notifications_frame,
            text="",
            font=("Arial", 10, "bold"),
            text_color="#FF5722"
        )
        self.unread_count_label.pack(side="left", padx=5)

    def update_notifications_count(self):
        """تحديث عداد الإشعارات غير المقروءة"""
        try:
            from utils.notifications import get_unread_notifications_count
            unread_count = get_unread_notifications_count()

            if unread_count > 0:
                self.unread_count_label.configure(text=f"({unread_count})")
                self.notifications_btn.configure(text=f"🔔 الإشعارات ({unread_count})")
            else:
                self.unread_count_label.configure(text="")
                self.notifications_btn.configure(text="🔔 الإشعارات")
        except Exception as e:
            print(f"خطأ في تحديث عداد الإشعارات: {e}")

    def start_notifications_update(self):
        """بدء تحديث دوري للإشعارات"""
        self.update_notifications_count()
        # تحديث كل 30 ثانية
        self.after(30000, self.start_notifications_update)

    def add_sample_notifications(self):
        """إضافة إشعارات تجريبية"""
        try:
            from utils.notifications import notify_info, notify_warning, notify_success

            # إشعارات ترحيبية
            notify_success("مرحباً بك!", f"تم تسجيل دخولك بنجاح كـ {self.current_user}")
            notify_info("نظام محدث", "تم تحديث النظام بميزات جديدة ومحسنة")
            notify_warning("تذكير", "يرجى عمل نسخة احتياطية من البيانات بانتظام")

        except Exception as e:
            print(f"خطأ في إضافة الإشعارات التجريبية: {e}")

    def create_logout_bar(self):
        """إنشاء شريط تسجيل الخروج السفلي"""
        try:
            # إضافة شريط معلومات المستخدم
            user_frame = ctk.CTkFrame(self, fg_color="#34495e", corner_radius=0, height=40)
            user_frame.pack(side="bottom", fill="x")
            user_frame.pack_propagate(False)

            # تكوين الشبكة
            user_frame.grid_columnconfigure(0, weight=1)  # العمود الأيسر
            user_frame.grid_columnconfigure(1, weight=2)  # العمود الأوسط (أوسع)
            user_frame.grid_columnconfigure(2, weight=1)  # العمود الأيمن

            # معلومات المستخدم (يسار)
            if self.current_user_data and isinstance(self.current_user_data, dict):
                user_name = self.current_user_data.get('name', 'مستخدم')
                login_time = self.current_user_data.get('login_time')
                if login_time:
                    time_str = login_time.strftime('%H:%M:%S')
                    user_info = f"👤 {user_name} | 🕐 {time_str}"
                else:
                    user_info = f"👤 {user_name}"
            else:
                user_info = f"👤 {self.current_user}"

            user_label = ctk.CTkLabel(
                user_frame,
                text=user_info,
                font=("Arial", 12),
                text_color="white"
            )
            user_label.grid(row=0, column=0, padx=20, pady=10, sticky="w")

            # بيانات التطوير في الوسط
            developer_info = "تطوير: محمد الكامل | الإصدار 3.0.0 | جميع الحقوق محفوظة"
            developer_label = ctk.CTkLabel(
                user_frame,
                text=developer_info,
                font=("Arial", 11),
                text_color="#bdc3c7"
            )
            developer_label.grid(row=0, column=1, pady=10)

            # زر تسجيل الخروج (يمين)
            logout_btn = ctk.CTkButton(
                user_frame,
                text="تسجيل خروج",
                font=("Arial", 12),
                width=100,
                height=25,
                fg_color="#e74c3c",
                hover_color="#c0392b",
                command=self.logout
            )
            logout_btn.grid(row=0, column=2, padx=20, pady=7, sticky="e")

            # التأكد من تحديث النافذة
            user_frame.update_idletasks()
            self.update_idletasks()

            print("تم إنشاء شريط تسجيل الخروج بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء شريط تسجيل الخروج: {e}")
            import traceback
            traceback.print_exc()

    def logout(self):
        """تسجيل الخروج"""
        from tkinter import messagebox
        if messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج من النظام؟"):
            self.quit()

def run_app():
    app = MainWindow()
    app.mainloop()
