@echo off
chcp 65001 > nul
title واجهة الاتجاهات والتوقعات المتقدمة

echo ========================================
echo    📈 واجهة الاتجاهات والتوقعات المتقدمة
echo ========================================
echo.

echo 🔄 جاري تشغيل الواجهة...
echo.

REM التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود المتطلبات
echo 📦 التحقق من المتطلبات...
python -c "import matplotlib, numpy, pandas, seaborn" > nul 2>&1
if errorlevel 1 (
    echo 🔧 تثبيت المتطلبات المفقودة...
    pip install matplotlib numpy pandas seaborn
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo يرجى تشغيل: pip install matplotlib numpy pandas seaborn
        pause
        exit /b 1
    )
)

REM تشغيل الواجهة
echo ✅ تشغيل واجهة الاتجاهات والتوقعات...
echo.
python run_trends_predictions.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل الواجهة
    echo جرب تشغيل: python run_trends_predictions.py
    pause
)

echo.
echo 👋 تم إغلاق الواجهة
pause
