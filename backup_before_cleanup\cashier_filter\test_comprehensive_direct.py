#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر للتقرير الشامل
Direct Test for Comprehensive Report
"""

import sys
import os
from pathlib import Path
import traceback

# إضافة مجلد المشروع لمسار Python
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_comprehensive_report():
    """اختبار التقرير الشامل مباشرة"""
    try:
        print("🔍 اختبار التقرير الشامل...")
        
        # استيراد DatabaseManager
        from web_server import DatabaseManager
        print("✅ تم استيراد DatabaseManager")
        
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager()
        print("✅ تم إنشاء DatabaseManager")
        
        # جلب بيانات التصفية
        filter_id = 22
        filter_data = db_manager.get_filter_by_id(filter_id)
        print(f"📊 بيانات التصفية {filter_id}: {filter_data is not None}")
        
        if not filter_data:
            print(f"❌ التصفية رقم {filter_id} غير موجودة")
            
            # جرب تصفيات أخرى
            all_filters = db_manager.get_all_filters(10)
            print(f"📋 عدد التصفيات المتاحة: {len(all_filters)}")
            
            if all_filters:
                print("📋 التصفيات المتاحة:")
                for f in all_filters:
                    print(f"   - ID: {f['id']}, التاريخ: {f['date']}, الكاشير: {f.get('cashier_name', 'غير محدد')}")
                
                # جرب أول تصفية متاحة
                filter_data = all_filters[0]
                filter_id = filter_data['id']
                print(f"🔄 جرب التصفية {filter_id}")
            else:
                print("❌ لا توجد تصفيات في قاعدة البيانات")
                return
        
        # طباعة تفاصيل التصفية
        print(f"\n📋 تفاصيل التصفية {filter_id}:")
        print(f"   التاريخ: {filter_data.get('date', 'غير محدد')}")
        print(f"   الكاشير: {filter_data.get('cashier_name', 'غير محدد')}")
        print(f"   المسؤول: {filter_data.get('admin_name', 'غير محدد')}")
        
        # فحص التفاصيل
        details = filter_data.get('details', {})
        print(f"\n💰 التفاصيل المالية:")
        print(f"   بنكي: {details.get('bank_total', 0)}")
        print(f"   نقدي: {details.get('cash_total', 0)}")
        print(f"   آجل: {details.get('credit_total', 0)}")
        print(f"   مرتجعات: {details.get('return_total', 0)}")
        
        # فحص المعاملات البنكية
        bank_transactions = details.get('bank_transactions', [])
        print(f"   معاملات بنكية: {len(bank_transactions)}")
        
        # فحص النقدي
        cash_details = details.get('cash_details', {})
        print(f"   فئات نقدية: {len(cash_details)}")
        
        # اختبار عرض القالب
        print(f"\n🎨 اختبار القالب...")
        
        from flask import Flask, render_template
        app = Flask(__name__, template_folder='web_templates')
        
        with app.app_context():
            try:
                html_content = render_template('comprehensive_report.html', filter=filter_data)
                print("✅ تم عرض القالب بنجاح!")
                print(f"📄 طول المحتوى: {len(html_content)} حرف")
                
                # فحص وجود عناصر مهمة في HTML
                if 'التقرير الشامل' in html_content:
                    print("✅ العنوان موجود")
                else:
                    print("⚠️ العنوان مفقود")
                
                if 'الملخص التنفيذي' in html_content:
                    print("✅ الملخص التنفيذي موجود")
                else:
                    print("⚠️ الملخص التنفيذي مفقود")
                
                return True
                
            except Exception as e:
                print(f"❌ خطأ في عرض القالب: {e}")
                traceback.print_exc()
                return False
    
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار مباشر للتقرير الشامل")
    print("=" * 50)
    
    # التحقق من المجلد الحالي
    if not Path('web_server.py').exists():
        print("❌ يجب تشغيل هذا الملف من مجلد cashier_filter")
        return
    
    # تشغيل الاختبار
    success = test_comprehensive_report()
    
    if success:
        print("\n🎉 الاختبار نجح!")
        print("💡 المشكلة قد تكون في إعدادات الخادم أو route")
        print("🔗 جرب: http://localhost:5000/filter/22/comprehensive")
    else:
        print("\n❌ الاختبار فشل!")
        print("💡 راجع رسائل الخطأ أعلاه لحل المشكلة")

if __name__ == "__main__":
    main()
