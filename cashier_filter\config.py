# إعدادات التطبيق
import os

# مسارات الملفات
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, "db", "cashier_filter.db")
REPORTS_DIR = os.path.join(BASE_DIR, "reports", "generated")
ASSETS_DIR = os.path.join(BASE_DIR, "assets")

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    "path": DB_PATH,
    "timeout": 30,
    "check_same_thread": False
}

# إعدادات الواجهة مع حجم خط مستقر
UI_CONFIG = {
    "theme": "light",
    "color_theme": "blue",
    "font_family": "Arial",
    "font_size": 14,  # حجم افتراضي أكبر ومستقر
    "min_font_size": 12,  # حد أدنى
    "max_font_size": 18,  # حد أقصى
    "window_size": {
        "main": "800x700",
        "daily_filter": "1400x900",
        "manage_cashier": "800x600",
        "manage_admins": "850x650",
        "filter_entry": "900x750",
        "reports": "1000x700"
    }
}

# ألوان التصميم
COLORS = {
    "primary": "#4CAF50",
    "secondary": "#2196F3",
    "accent": "#FF9800",
    "background": "#f2f3f7",
    "surface": "#e0e5ec",
    "text_primary": "#2c3e50",
    "text_secondary": "#34495e",
    "success": "#27ae60",
    "warning": "#f39c12",
    "error": "#e74c3c"
}

# إعدادات البنوك
BANKS = [
    "الأهلي",
    "الراجحي", 
    "سامبا",
    "الرياض",
    "البلاد",
    "الإنماء",
    "الجزيرة",
    "ساب",
    "العربي الوطني",
    "الاستثمار",
    "الأول"
]

# أنواع العمليات البنكية
BANK_OPERATIONS = [
    "ماستر",
    "مدى",
    "ماستر كارد",
    "فيزا",
    "أمريكان إكسبريس",
    "دايرز كلوب"
]

# فئات العملة السعودية
CURRENCY_DENOMINATIONS = [
    (500, "خمس مئة"),
    (100, "مئة"),
    (50, "خمسون"),
    (10, "عشرة"),
    (5, "خمسة"),
    (1, "ريال"),
    (0.5, "نصف"),
    (0.25, "ربع")
]

# إعدادات التصدير
EXPORT_CONFIG = {
    "pdf": {
        "page_size": "A4",
        "margin": 15,
        "font_size": 12,
        "title_font_size": 16
    },
    "excel": {
        "engine": "openpyxl",
        "date_format": "YYYY-MM-DD",
        "number_format": "#,##0.00"
    },
    "html": {
        "encoding": "utf-8",
        "auto_print": True,
        "responsive": True
    }
}

# رسائل النظام
MESSAGES = {
    "success": {
        "save": "تم حفظ التصفية بنجاح!",
        "update": "تم تحديث التصفية بنجاح!",
        "delete": "تم الحذف بنجاح!",
        "export": "تم التصدير بنجاح!",
        "print": "تم فتح التقرير في المتصفح للطباعة"
    },
    "error": {
        "save": "حدث خطأ أثناء الحفظ",
        "update": "حدث خطأ أثناء التحديث",
        "delete": "حدث خطأ أثناء الحذف",
        "export": "حدث خطأ في التصدير",
        "print": "حدث خطأ في الطباعة",
        "database": "حدث خطأ في قاعدة البيانات",
        "validation": "يرجى التحقق من البيانات المدخلة"
    },
    "warning": {
        "empty_fields": "يرجى ملء جميع الحقول المطلوبة",
        "invalid_amount": "يرجى إدخال مبلغ صحيح",
        "duplicate_entry": "هذا الإدخال موجود مسبقاً",
        "no_selection": "يرجى اختيار عنصر أولاً"
    },
    "confirm": {
        "delete": "هل أنت متأكد من الحذف؟",
        "update": "هل تريد تحديث البيانات؟",
        "exit": "هل تريد الخروج من التطبيق؟"
    }
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "password_min_length": 6,
    "hash_algorithm": "sha256",
    "session_timeout": 3600,  # ساعة واحدة
    "max_login_attempts": 3
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    "auto_backup": True,
    "backup_interval": 24,  # ساعة
    "max_backups": 30,  # عدد النسخ الاحتياطية
    "backup_dir": os.path.join(BASE_DIR, "backups")
}

# إعدادات السجلات
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": os.path.join(BASE_DIR, "logs", "app.log"),
    "max_size": 10 * 1024 * 1024,  # 10 ميجابايت
    "backup_count": 5
}

# معلومات التطبيق
APP_INFO = {
    "name": "نظام تصفية الكاشير",
    "version": "2.0.0",
    "author": "فريق التطوير",
    "description": "نظام متكامل لإدارة وتصفية حسابات الكاشير اليومية",
    "copyright": "© 2024 - جميع الحقوق محفوظة"
}

# إنشاء المجلدات المطلوبة
def create_required_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    directories = [
        os.path.dirname(DB_PATH),
        REPORTS_DIR,
        ASSETS_DIR,
        BACKUP_CONFIG["backup_dir"],
        os.path.dirname(LOGGING_CONFIG["file"])
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

# تشغيل إنشاء المجلدات عند استيراد الملف
create_required_directories()
