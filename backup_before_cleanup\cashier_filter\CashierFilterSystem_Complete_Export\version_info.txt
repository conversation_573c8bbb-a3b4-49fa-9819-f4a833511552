# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(3,0,0,0),
    prodvers=(3,0,0,0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'محمد الكامل - <PERSON>'),
        StringStruct(u'FileDescription', u'نظام تصفية الكاشير المتكامل 2025 - Cashier Filter System 2025'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'CashierFilterSystem2025'),
        StringStruct(u'LegalCopyright', u'© 2025 محمد الكامل - Mohamed Al-Kamel. جميع الحقوق محفوظة.'),
        StringStruct(u'OriginalFilename', u'CashierFilterSystem2025.exe'),
        StringStruct(u'ProductName', u'نظام تصفية الكاشير المتكامل 2025'),
        StringStruct(u'ProductVersion', u'*******'),
        StringStruct(u'Comments', u'نظام إدارة مالية متقدم مع ذكاء اصطناعي ولوحة معلومات تفاعلية'),
        StringStruct(u'LegalTrademarks', u'محمد الكامل - Mohamed Al-Kamel')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
