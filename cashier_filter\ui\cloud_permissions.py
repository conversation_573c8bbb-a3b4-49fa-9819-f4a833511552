# نظام إدارة الأذونات السحابية المتقدم
import customtkinter as ctk
from tkinter import ttk, messagebox
import json
import os
import sqlite3
from datetime import datetime
from pathlib import Path

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class CloudPermissionsWindow(ctk.CTkToplevel):
    """نافذة إدارة الأذونات السحابية المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.title("🔐 إدارة الأذونات السحابية")
        self.geometry("900x700")
        self.configure(bg="#f0f2f5")
        self.resizable(True, True)
        
        # متغيرات الأذونات
        self.permissions_data = {
            "users": {},
            "roles": {
                "admin": {
                    "name": "مدير النظام",
                    "permissions": ["read", "write", "delete", "sync", "backup", "restore", "manage_users"]
                },
                "manager": {
                    "name": "مدير",
                    "permissions": ["read", "write", "sync", "backup", "restore"]
                },
                "cashier": {
                    "name": "كاشير",
                    "permissions": ["read", "sync"]
                },
                "viewer": {
                    "name": "مشاهد",
                    "permissions": ["read"]
                }
            },
            "cloud_providers": {
                "google_drive": {"enabled": True, "restricted_users": []},
                "dropbox": {"enabled": True, "restricted_users": []},
                "onedrive": {"enabled": True, "restricted_users": []},
                "aws_s3": {"enabled": False, "restricted_users": []},
                "azure": {"enabled": False, "restricted_users": []}
            }
        }
        
        self.create_widgets()
        self.load_permissions_data()
        self.load_users_from_db()

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        self.create_header()
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f0f2f5", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # إنشاء التبويبات
        self.create_tabs(main_container)

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = ctk.CTkFrame(self, fg_color="#1e3a8a", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            header_frame,
            text="🔐 إدارة الأذونات السحابية المتقدمة",
            font=("Arial", 20, "bold"),
            text_color="white"
        )
        title_label.pack(pady=15)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تبويب أذونات المستخدمين
        self.users_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.users_frame, text="👥 أذونات المستخدمين")
        self.create_users_tab()
        
        # تبويب الأدوار والصلاحيات
        self.roles_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.roles_frame, text="🎭 الأدوار والصلاحيات")
        self.create_roles_tab()
        
        # تبويب أذونات الموفرين
        self.providers_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.providers_frame, text="🌐 أذونات الموفرين")
        self.create_providers_tab()
        
        # تبويب سجل الأذونات
        self.audit_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.audit_frame, text="📋 سجل الأذونات")
        self.create_audit_tab()

    def create_users_tab(self):
        """إنشاء تبويب أذونات المستخدمين"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.users_frame,
            text="👥 إدارة أذونات المستخدمين",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.users_frame, fg_color="#f8fafc", corner_radius=15)
        toolbar_frame.pack(fill="x", padx=20, pady=10)
        
        toolbar_content = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        toolbar_content.pack(fill="x", padx=20, pady=15)
        
        # أزرار الأدوات
        add_user_btn = ctk.CTkButton(
            toolbar_content,
            text="➕ إضافة مستخدم",
            command=self.add_user_permissions,
            fg_color="#10b981",
            hover_color="#059669",
            width=120,
            height=35
        )
        add_user_btn.pack(side="left", padx=5)
        
        edit_user_btn = ctk.CTkButton(
            toolbar_content,
            text="✏️ تعديل أذونات",
            command=self.edit_user_permissions,
            fg_color="#3b82f6",
            hover_color="#2563eb",
            width=120,
            height=35
        )
        edit_user_btn.pack(side="left", padx=5)
        
        remove_user_btn = ctk.CTkButton(
            toolbar_content,
            text="🗑️ إزالة أذونات",
            command=self.remove_user_permissions,
            fg_color="#ef4444",
            hover_color="#dc2626",
            width=120,
            height=35
        )
        remove_user_btn.pack(side="left", padx=5)
        
        # جدول المستخدمين
        users_frame = ctk.CTkFrame(self.users_frame, fg_color="#f8fafc", corner_radius=15)
        users_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # أعمدة الجدول
        columns = ("المستخدم", "الدور", "الأذونات", "الموفرين المسموحين", "آخر نشاط")
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings", height=12)
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(users_frame, orient="vertical", command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        self.users_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        users_scrollbar.pack(side="right", fill="y", pady=10)
        
        # تحميل بيانات المستخدمين
        self.refresh_users_list()

    def create_roles_tab(self):
        """إنشاء تبويب الأدوار والصلاحيات"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.roles_frame,
            text="🎭 إدارة الأدوار والصلاحيات",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الأدوار
        roles_container = ctk.CTkScrollableFrame(
            self.roles_frame,
            fg_color="#f8fafc",
            corner_radius=15,
            height=400
        )
        roles_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء بطاقات الأدوار
        for role_id, role_info in self.permissions_data["roles"].items():
            self.create_role_card(roles_container, role_id, role_info)

    def create_providers_tab(self):
        """إنشاء تبويب أذونات الموفرين"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.providers_frame,
            text="🌐 إدارة أذونات الموفرين السحابيين",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # إطار الموفرين
        providers_container = ctk.CTkScrollableFrame(
            self.providers_frame,
            fg_color="#f8fafc",
            corner_radius=15,
            height=400
        )
        providers_container.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إنشاء بطاقات الموفرين
        provider_names = {
            "google_drive": "Google Drive 📁",
            "dropbox": "Dropbox 📦",
            "onedrive": "OneDrive ☁️",
            "aws_s3": "Amazon S3 🗄️",
            "azure": "Azure Storage 🔷"
        }
        
        for provider_id, provider_info in self.permissions_data["cloud_providers"].items():
            provider_name = provider_names.get(provider_id, provider_id)
            self.create_provider_card(providers_container, provider_id, provider_name, provider_info)

    def create_audit_tab(self):
        """إنشاء تبويب سجل الأذونات"""
        # العنوان
        title_label = ctk.CTkLabel(
            self.audit_frame,
            text="📋 سجل أنشطة الأذونات",
            font=("Arial", 16, "bold"),
            text_color="#1e3a8a"
        )
        title_label.pack(pady=20)
        
        # جدول السجل
        audit_frame = ctk.CTkFrame(self.audit_frame, fg_color="#f8fafc", corner_radius=15)
        audit_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # أعمدة الجدول
        columns = ("الوقت", "المستخدم", "النشاط", "التفاصيل", "النتيجة")
        self.audit_tree = ttk.Treeview(audit_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.audit_tree.heading(col, text=col)
            self.audit_tree.column(col, width=150, anchor="center")
        
        # شريط التمرير
        audit_scrollbar = ttk.Scrollbar(audit_frame, orient="vertical", command=self.audit_tree.yview)
        self.audit_tree.configure(yscrollcommand=audit_scrollbar.set)
        
        self.audit_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        audit_scrollbar.pack(side="right", fill="y", pady=10)
        
        # تحميل السجل
        self.load_audit_log()

    def create_role_card(self, parent, role_id, role_info):
        """إنشاء بطاقة دور"""
        # إطار البطاقة
        card_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=12)
        card_frame.pack(fill="x", pady=10, padx=10)
        
        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=15)
        
        # اسم الدور
        role_label = ctk.CTkLabel(
            content_frame,
            text=f"🎭 {role_info['name']} ({role_id})",
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        role_label.pack(anchor="w")
        
        # الأذونات
        permissions_text = ", ".join(role_info['permissions'])
        permissions_label = ctk.CTkLabel(
            content_frame,
            text=f"الأذونات: {permissions_text}",
            font=("Arial", 10),
            text_color="#6b7280",
            wraplength=600
        )
        permissions_label.pack(anchor="w", pady=(5, 0))

    def create_provider_card(self, parent, provider_id, provider_name, provider_info):
        """إنشاء بطاقة موفر خدمة"""
        # إطار البطاقة
        card_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=12)
        card_frame.pack(fill="x", pady=10, padx=10)
        
        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=15)
        
        # الجانب الأيسر - معلومات الموفر
        left_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        left_frame.pack(side="left", fill="x", expand=True)
        
        # اسم الموفر
        provider_label = ctk.CTkLabel(
            left_frame,
            text=provider_name,
            font=("Arial", 14, "bold"),
            text_color="#1e3a8a"
        )
        provider_label.pack(anchor="w")
        
        # حالة التفعيل
        status_text = "مفعل" if provider_info["enabled"] else "معطل"
        status_color = "#10b981" if provider_info["enabled"] else "#ef4444"
        status_label = ctk.CTkLabel(
            left_frame,
            text=f"الحالة: {status_text}",
            font=("Arial", 10),
            text_color=status_color
        )
        status_label.pack(anchor="w", pady=(5, 0))
        
        # الجانب الأيمن - أزرار التحكم
        right_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        right_frame.pack(side="right")
        
        # زر التفعيل/التعطيل
        toggle_btn = ctk.CTkButton(
            right_frame,
            text="تعطيل" if provider_info["enabled"] else "تفعيل",
            command=lambda p=provider_id: self.toggle_provider(p),
            fg_color="#ef4444" if provider_info["enabled"] else "#10b981",
            hover_color="#dc2626" if provider_info["enabled"] else "#059669",
            width=80,
            height=30
        )
        toggle_btn.pack(side="top", pady=2)

    def load_users_from_db(self):
        """تحميل المستخدمين من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # تحميل المستخدمين من جدول المستخدمين
            cursor.execute("SELECT username, role FROM users")
            users = cursor.fetchall()
            
            # تحميل الكاشيرين
            cursor.execute("SELECT name FROM cashiers")
            cashiers = cursor.fetchall()
            
            conn.close()
            
            # إضافة المستخدمين لبيانات الأذونات
            for username, role in users:
                if username not in self.permissions_data["users"]:
                    self.permissions_data["users"][username] = {
                        "role": role or "viewer",
                        "permissions": self.permissions_data["roles"].get(role or "viewer", {}).get("permissions", ["read"]),
                        "allowed_providers": ["google_drive", "dropbox", "onedrive"],
                        "last_activity": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
            
            # إضافة الكاشيرين
            for (cashier_name,) in cashiers:
                if cashier_name not in self.permissions_data["users"]:
                    self.permissions_data["users"][cashier_name] = {
                        "role": "cashier",
                        "permissions": ["read", "sync"],
                        "allowed_providers": ["google_drive", "dropbox"],
                        "last_activity": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
        except Exception as e:
            print(f"خطأ في تحميل المستخدمين من قاعدة البيانات: {e}")

    def refresh_users_list(self):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح القائمة الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # إضافة المستخدمين
            for username, user_info in self.permissions_data["users"].items():
                role_name = self.permissions_data["roles"].get(user_info["role"], {}).get("name", user_info["role"])
                permissions_text = ", ".join(user_info["permissions"][:3]) + ("..." if len(user_info["permissions"]) > 3 else "")
                providers_text = ", ".join(user_info["allowed_providers"][:2]) + ("..." if len(user_info["allowed_providers"]) > 2 else "")
                
                self.users_tree.insert("", "end", values=(
                    username,
                    role_name,
                    permissions_text,
                    providers_text,
                    user_info["last_activity"]
                ))
                
        except Exception as e:
            print(f"خطأ في تحديث قائمة المستخدمين: {e}")

    def load_audit_log(self):
        """تحميل سجل الأذونات"""
        try:
            # إضافة سجلات وهمية للعرض
            sample_logs = [
                (datetime.now().strftime('%H:%M:%S'), "admin", "تعديل أذونات", "تم تعديل أذونات المستخدم أحمد", "نجح"),
                ((datetime.now()).strftime('%H:%M:%S'), "manager", "وصول للسحابة", "وصول إلى Google Drive", "نجح"),
                ((datetime.now()).strftime('%H:%M:%S'), "cashier1", "محاولة وصول", "محاولة وصول إلى AWS S3", "فشل - غير مسموح"),
                ((datetime.now()).strftime('%H:%M:%S'), "admin", "تفعيل موفر", "تم تفعيل Azure Storage", "نجح"),
                ((datetime.now()).strftime('%H:%M:%S'), "viewer", "عرض ملفات", "عرض قائمة الملفات", "نجح"),
            ]
            
            for log_entry in sample_logs:
                self.audit_tree.insert("", "end", values=log_entry)
                
        except Exception as e:
            print(f"خطأ في تحميل سجل الأذونات: {e}")

    def load_permissions_data(self):
        """تحميل بيانات الأذونات"""
        try:
            permissions_file = Path("cloud_permissions.json")
            if permissions_file.exists():
                with open(permissions_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.permissions_data.update(data)
        except Exception as e:
            print(f"خطأ في تحميل بيانات الأذونات: {e}")

    def save_permissions_data(self):
        """حفظ بيانات الأذونات"""
        try:
            with open("cloud_permissions.json", 'w', encoding='utf-8') as f:
                json.dump(self.permissions_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ بيانات الأذونات: {e}")

    def add_user_permissions(self):
        """إضافة أذونات مستخدم جديد"""
        messagebox.showinfo("قريباً", "ميزة إضافة المستخدمين ستكون متاحة قريباً!")

    def edit_user_permissions(self):
        """تعديل أذونات المستخدم"""
        selected_item = self.users_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتعديل أذوناته")
            return
        
        username = self.users_tree.item(selected_item[0])['values'][0]
        messagebox.showinfo("قريباً", f"ميزة تعديل أذونات المستخدم '{username}' ستكون متاحة قريباً!")

    def remove_user_permissions(self):
        """إزالة أذونات المستخدم"""
        selected_item = self.users_tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لإزالة أذوناته")
            return
        
        username = self.users_tree.item(selected_item[0])['values'][0]
        
        if messagebox.askyesno("تأكيد الإزالة", f"هل أنت متأكد من إزالة أذونات المستخدم '{username}'؟"):
            try:
                if username in self.permissions_data["users"]:
                    del self.permissions_data["users"][username]
                    self.save_permissions_data()
                    self.refresh_users_list()
                    messagebox.showinfo("نجح", f"تم إزالة أذونات المستخدم '{username}' بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل إزالة أذونات المستخدم: {e}")

    def toggle_provider(self, provider_id):
        """تبديل حالة تفعيل الموفر"""
        try:
            current_status = self.permissions_data["cloud_providers"][provider_id]["enabled"]
            self.permissions_data["cloud_providers"][provider_id]["enabled"] = not current_status
            
            self.save_permissions_data()
            
            # إعادة إنشاء التبويب لتحديث الواجهة
            for widget in self.providers_frame.winfo_children():
                widget.destroy()
            self.create_providers_tab()
            
            status_text = "تم تفعيل" if not current_status else "تم تعطيل"
            messagebox.showinfo("نجح", f"{status_text} الموفر بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تغيير حالة الموفر: {e}")


# دالة لفتح نافذة إدارة الأذونات السحابية
def open_cloud_permissions(parent=None):
    """فتح نافذة إدارة الأذونات السحابية"""
    try:
        window = CloudPermissionsWindow(parent)
        return window
    except Exception as e:
        print(f"خطأ في فتح نافذة إدارة الأذونات: {e}")
        if parent:
            messagebox.showerror("خطأ", f"فشل فتح نافذة إدارة الأذونات: {e}")
        return None


if __name__ == "__main__":
    # اختبار النافذة
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    root = ctk.CTk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    app = CloudPermissionsWindow()
    app.mainloop()
