@echo off
title Global Access - Cashier Filter Reports

echo.
echo ========================================
echo Global Access - Cashier Filter Reports
echo ========================================
echo.

cd /d "%~dp0"

echo [1/4] Checking local server...
curl -s http://localhost:5000 > nul 2>&1
if errorlevel 1 (
    echo [INFO] Starting local server...
    start "Reports Server" /min python web_server.py
    echo [INFO] Waiting for server to start...
    timeout /t 5 > nul
    
    REM Check again
    curl -s http://localhost:5000 > nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Failed to start local server
        echo [INFO] Please run: python web_server.py
        pause
        exit /b 1
    )
)

echo [OK] Local server is running

echo.
echo [2/4] Checking Cloudflare Tunnel...
if not exist "cloudflared.exe" (
    echo [INFO] Downloading cloudflared.exe...
    echo [INFO] This may take a few minutes...
    
    curl -L -o cloudflared.exe https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe
    
    if errorlevel 1 (
        echo [ERROR] Download failed
        echo.
        echo Manual download instructions:
        echo 1. Go to: https://github.com/cloudflare/cloudflared/releases
        echo 2. Download: cloudflared-windows-amd64.exe
        echo 3. Rename to: cloudflared.exe
        echo 4. Place in this folder
        echo.
        pause
        exit /b 1
    )
    echo [OK] Download completed
) else (
    echo [OK] cloudflared.exe found
)

echo.
echo [3/4] Starting global tunnel...
echo ========================================
echo.
echo IMPORTANT: Copy the HTTPS URL that appears below
echo This URL will work from anywhere in the world
echo.
echo For mobile: Add /mobile to the end of the URL
echo Example: https://abc123.trycloudflare.com/mobile
echo.
echo ========================================

cloudflared.exe tunnel --url http://localhost:5000

echo.
echo [4/4] Tunnel ended
echo.
echo To restart: Run this file again
echo To stop: Close this window or press Ctrl+C
echo.
pause
