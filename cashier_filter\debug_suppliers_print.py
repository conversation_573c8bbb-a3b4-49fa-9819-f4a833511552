#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة طباعة الموردين
Debug Suppliers Print Issue
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
BASE_DIR = Path(__file__).parent
sys.path.insert(0, str(BASE_DIR))

try:
    import sqlite3
    import json
    from datetime import datetime
    
    def debug_suppliers_in_filters():
        """فحص بيانات الموردين في التصفيات المحفوظة"""
        print("🔍 فحص بيانات الموردين في التصفيات المحفوظة...")
        print("=" * 60)
        
        try:
            db_path = BASE_DIR / "db" / "cashier_filter.db"
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, date, data FROM filters WHERE data IS NOT NULL ORDER BY date DESC")
            filters = cursor.fetchall()
            
            suppliers_found = False
            
            for filter_id, date, data_str in filters:
                try:
                    data = json.loads(data_str)
                    
                    print(f"\n📋 تصفية {filter_id} - {date}:")
                    print(f"   هيكل البيانات: {list(data.keys())}")
                    
                    # فحص details
                    details = data.get('details', {})
                    if details:
                        print(f"   هيكل details: {list(details.keys())}")
                        
                        suppliers_transactions = details.get('suppliers_transactions', [])
                        if suppliers_transactions:
                            suppliers_found = True
                            print(f"   ✅ وجدت {len(suppliers_transactions)} مدفوعة موردين:")
                            for i, supplier in enumerate(suppliers_transactions, 1):
                                print(f"      {i}. {supplier.get('supplier_name', 'غير محدد')}: {supplier.get('amount', 0)} ريال")
                        else:
                            print("   ❌ لا توجد suppliers_transactions في details")
                    else:
                        print("   ❌ لا توجد details في البيانات")
                    
                    # فحص المستوى الأعلى
                    suppliers_transactions_root = data.get('suppliers_transactions', [])
                    if suppliers_transactions_root:
                        suppliers_found = True
                        print(f"   ✅ وجدت {len(suppliers_transactions_root)} مدفوعة موردين في المستوى الأعلى:")
                        for i, supplier in enumerate(suppliers_transactions_root, 1):
                            print(f"      {i}. {supplier.get('supplier_name', 'غير محدد')}: {supplier.get('amount', 0)} ريال")
                    
                except Exception as e:
                    print(f"   ❌ خطأ في معالجة تصفية {filter_id}: {e}")
                    continue
            
            conn.close()
            
            if suppliers_found:
                print("\n✅ تم العثور على بيانات موردين في قاعدة البيانات")
            else:
                print("\n❌ لم يتم العثور على أي بيانات موردين")
            
            return suppliers_found
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            return False
    
    def debug_print_function():
        """فحص دالة الطباعة"""
        print("\n🖨️ فحص دالة الطباعة...")
        print("=" * 60)
        
        try:
            # محاكاة البيانات التي يتم تمريرها من daily_filter.py
            test_filter_data = {
                'sequence_number': 'DEBUG-001',
                'cashier_name': 'كاشير تجريبي',
                'cashier_number': '001',
                'admin_name': 'مسؤول تجريبي',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'suppliers_transactions': [
                    {
                        'supplier_name': 'مورد تجريبي 1',
                        'amount': 1000.0,
                        'payment_method': 'نقدي',
                        'notes': 'اختبار 1'
                    },
                    {
                        'supplier_name': 'مورد تجريبي 2',
                        'amount': 2000.0,
                        'payment_method': 'شيك',
                        'notes': 'اختبار 2'
                    }
                ]
            }
            
            print("📋 البيانات المرسلة للطباعة:")
            print(f"   suppliers_transactions: {test_filter_data.get('suppliers_transactions', [])}")
            
            # اختبار دالة generate_html_report مباشرة
            from reports.html_print import generate_html_report
            
            test_totals = {'bank': 0, 'cash': 0, 'credit': 0, 'client': 0, 'return': 0}
            test_system_sales = 0
            
            # إنشاء ملف اختبار
            test_filename = "debug_suppliers_test.html"
            
            print("\n🔧 اختبار دالة generate_html_report...")
            
            if generate_html_report(test_filter_data, test_totals, test_system_sales, test_filename):
                print("✅ تم إنشاء ملف HTML بنجاح")
                
                # قراءة الملف للتحقق من محتوى الموردين
                if os.path.exists(test_filename):
                    with open(test_filename, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if 'مورد تجريبي 1' in content and 'مورد تجريبي 2' in content:
                        print("✅ بيانات الموردين موجودة في HTML")
                    elif 'لا توجد مدفوعات للموردين' in content:
                        print("❌ HTML يظهر 'لا توجد مدفوعات للموردين'")
                        print("🔍 هذا يعني أن البيانات لا تصل بشكل صحيح")
                    else:
                        print("⚠️ لا يمكن تحديد حالة بيانات الموردين في HTML")
                    
                    # حذف ملف الاختبار
                    os.remove(test_filename)
                else:
                    print("❌ لم يتم إنشاء ملف HTML")
            else:
                print("❌ فشل في إنشاء HTML")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار دالة الطباعة: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def debug_daily_filter_data():
        """فحص كيفية جمع البيانات في daily_filter.py"""
        print("\n📊 فحص جمع البيانات في daily_filter.py...")
        print("=" * 60)
        
        try:
            # محاكاة البيانات كما يتم جمعها من الجدول
            mock_suppliers_tree_data = [
                ('مورد محاكاة 1', '500.00', 'نقدي', 'ملاحظة 1'),
                ('مورد محاكاة 2', '750.00', 'شيك', 'ملاحظة 2'),
                ('مورد محاكاة 3', '1200.00', 'تحويل بنكي', 'ملاحظة 3')
            ]
            
            print("📋 بيانات الجدول المحاكاة:")
            for i, values in enumerate(mock_suppliers_tree_data, 1):
                print(f"   {i}. {values}")
            
            # محاكاة عملية التحويل كما في daily_filter.py
            suppliers_transactions = []
            for values in mock_suppliers_tree_data:
                suppliers_transactions.append({
                    'supplier_name': values[0],
                    'amount': float(values[1]),
                    'payment_method': values[2],
                    'notes': values[3] if len(values) > 3 else ''
                })
            
            print(f"\n🔄 البيانات بعد التحويل:")
            for i, supplier in enumerate(suppliers_transactions, 1):
                print(f"   {i}. {supplier}")
            
            # اختبار تمرير البيانات للطباعة
            filter_data_for_print = {
                'sequence_number': 'MOCK-001',
                'cashier_name': 'كاشير محاكاة',
                'cashier_number': '999',
                'admin_name': 'مسؤول محاكاة',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'suppliers_transactions': suppliers_transactions
            }
            
            print(f"\n📤 البيانات النهائية للطباعة:")
            print(f"   عدد الموردين: {len(filter_data_for_print.get('suppliers_transactions', []))}")
            print(f"   البيانات: {filter_data_for_print.get('suppliers_transactions', [])}")
            
            return filter_data_for_print
            
        except Exception as e:
            print(f"❌ خطأ في محاكاة البيانات: {e}")
            return None
    
    def main():
        """تشغيل جميع اختبارات التشخيص"""
        print("🔧 تشخيص مشكلة طباعة الموردين")
        print("=" * 70)
        
        # فحص قاعدة البيانات
        db_has_suppliers = debug_suppliers_in_filters()
        
        # فحص دالة الطباعة
        print_test_success = debug_print_function()
        
        # فحص جمع البيانات
        mock_data = debug_daily_filter_data()
        
        print("\n🎯 ملخص التشخيص:")
        print("=" * 70)
        
        if db_has_suppliers:
            print("✅ قاعدة البيانات تحتوي على بيانات موردين")
        else:
            print("❌ قاعدة البيانات لا تحتوي على بيانات موردين")
        
        if print_test_success:
            print("✅ دالة الطباعة تعمل بشكل صحيح")
        else:
            print("❌ مشكلة في دالة الطباعة")
        
        if mock_data:
            print("✅ عملية جمع البيانات تعمل بشكل صحيح")
        else:
            print("❌ مشكلة في عملية جمع البيانات")
        
        print("\n💡 التوصيات:")
        print("=" * 70)
        
        if not db_has_suppliers:
            print("1. أضف بعض بيانات الموردين في التصفية أولاً")
        
        if db_has_suppliers and not print_test_success:
            print("2. هناك مشكلة في دالة الطباعة تحتاج إصلاح")
        
        if db_has_suppliers and print_test_success:
            print("3. المشكلة قد تكون في تمرير البيانات من الواجهة")
            print("4. تحقق من أن الجدول يحتوي على بيانات عند الطباعة")
        
        print("=" * 70)

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()

input("\nاضغط Enter للخروج...")
