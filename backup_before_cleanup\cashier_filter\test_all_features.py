#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع ميزات نظام تصفية الكاشير 2025
Comprehensive Test for All Cashier Filter System 2025 Features
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🧪 اختبار استيراد الوحدات...")
    
    modules_to_test = [
        ("ui.main_window", "MainWindow"),
        ("ui.login", "run_login"),
        ("ui.dashboard", "show_dashboard"),
        ("ui.ai_analysis", "AIAnalysisWindow"),
        ("ui.advanced_search", "AdvancedSearchWindow"),
        ("ui.cloud_integration_simple", "CloudIntegrationSimpleWindow"),
        ("ui.reports", "ReportsWindow"),
        ("ui.statistics", "StatisticsWindow"),
        ("ui.settings", "SettingsWindow"),
        ("db.init_db", "init_db"),
        ("utils.export_utils", "export_to_excel"),
        ("utils.print_utils", "print_html")
    ]
    
    successful_imports = 0
    failed_imports = []
    
    for module_name, class_or_function in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_or_function])
            getattr(module, class_or_function)
            print(f"   ✅ {module_name}.{class_or_function}")
            successful_imports += 1
        except ImportError as e:
            print(f"   ❌ {module_name}.{class_or_function} - خطأ استيراد: {e}")
            failed_imports.append((module_name, str(e)))
        except AttributeError as e:
            print(f"   ⚠️ {module_name}.{class_or_function} - غير موجود: {e}")
            failed_imports.append((module_name, str(e)))
        except Exception as e:
            print(f"   ❌ {module_name}.{class_or_function} - خطأ عام: {e}")
            failed_imports.append((module_name, str(e)))
    
    print(f"\n📊 نتائج اختبار الاستيراد:")
    print(f"   ✅ نجح: {successful_imports}")
    print(f"   ❌ فشل: {len(failed_imports)}")
    
    if failed_imports:
        print(f"\n⚠️ الوحدات الفاشلة:")
        for module, error in failed_imports:
            print(f"   - {module}: {error}")
    
    return successful_imports, failed_imports

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from db.init_db import init_db
        init_db()
        print("   ✅ تهيئة قاعدة البيانات نجحت")
        
        # اختبار الاتصال
        import sqlite3
        db_path = os.path.join("db", "cashier_filter.db")
        
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # اختبار الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"   📋 عدد الجداول: {len(tables)}")
            for table in tables:
                print(f"      - {table[0]}")
            
            conn.close()
            print("   ✅ اختبار قاعدة البيانات نجح")
            return True
        else:
            print("   ❌ ملف قاعدة البيانات غير موجود")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui_components():
    """اختبار مكونات الواجهة"""
    print("\n🖥️ اختبار مكونات الواجهة...")
    
    try:
        import customtkinter as ctk
        print("   ✅ customtkinter متاح")
        
        # اختبار إنشاء نافذة بسيطة
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        root = ctk.CTk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار إنشاء عناصر واجهة
        frame = ctk.CTkFrame(root)
        label = ctk.CTkLabel(frame, text="اختبار")
        button = ctk.CTkButton(frame, text="زر اختبار")
        
        print("   ✅ إنشاء عناصر الواجهة نجح")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_export_functionality():
    """اختبار وظائف التصدير"""
    print("\n📤 اختبار وظائف التصدير...")
    
    try:
        # اختبار تصدير Excel
        try:
            import openpyxl
            print("   ✅ openpyxl متاح - تصدير Excel ممكن")
        except ImportError:
            print("   ⚠️ openpyxl غير متاح - تصدير Excel غير ممكن")
        
        # اختبار تصدير PDF
        try:
            import fpdf2
            print("   ✅ fpdf2 متاح - تصدير PDF ممكن")
        except ImportError:
            print("   ⚠️ fpdf2 غير متاح - تصدير PDF غير ممكن")
        
        # اختبار pandas
        try:
            import pandas
            print("   ✅ pandas متاح - معالجة البيانات ممكنة")
        except ImportError:
            print("   ⚠️ pandas غير متاح - معالجة البيانات محدودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التصدير: {e}")
        return False

def test_cloud_integration():
    """اختبار التكامل السحابي"""
    print("\n☁️ اختبار التكامل السحابي...")
    
    try:
        # اختبار النسخة المبسطة
        from ui.cloud_integration_simple import CloudIntegrationSimpleWindow
        print("   ✅ نافذة التكامل السحابي المبسطة متاحة")
        
        # اختبار النسخة الكاملة
        try:
            from ui.cloud_integration import CloudIntegrationWindow
            print("   ✅ نافذة التكامل السحابي الكاملة متاحة")
        except ImportError:
            print("   ⚠️ نافذة التكامل السحابي الكاملة غير متاحة (مكتبات مفقودة)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل السحابي: {e}")
        return False

def test_ai_features():
    """اختبار ميزات الذكاء الاصطناعي"""
    print("\n🤖 اختبار ميزات الذكاء الاصطناعي...")
    
    try:
        from ui.ai_analysis import AIAnalysisWindow
        print("   ✅ نافذة التحليل الذكي متاحة")
        
        # اختبار matplotlib للرسوم البيانية
        try:
            import matplotlib
            print("   ✅ matplotlib متاح - الرسوم البيانية ممكنة")
        except ImportError:
            print("   ⚠️ matplotlib غير متاح - الرسوم البيانية محدودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الذكاء الاصطناعي: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n📁 اختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "LICENSE",
        "db/init_db.py",
        "ui/main_window.py",
        "ui/login.py",
        "ui/cloud_integration_simple.py",
        "utils/export_utils.py",
        "utils/print_utils.py"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path} - مفقود")
    
    print(f"\n📊 نتائج فحص الملفات:")
    print(f"   ✅ موجود: {len(existing_files)}")
    print(f"   ❌ مفقود: {len(missing_files)}")
    
    return len(missing_files) == 0

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n" + "="*60)
    print("📋 تقرير الاختبار الشامل - نظام تصفية الكاشير 2025")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    successful_imports, failed_imports = test_imports()
    db_test = test_database()
    ui_test = test_ui_components()
    export_test = test_export_functionality()
    cloud_test = test_cloud_integration()
    ai_test = test_ai_features()
    files_test = test_file_structure()
    
    # حساب النتيجة الإجمالية
    total_tests = 7
    passed_tests = sum([
        1 if len(failed_imports) == 0 else 0,
        1 if db_test else 0,
        1 if ui_test else 0,
        1 if export_test else 0,
        1 if cloud_test else 0,
        1 if ai_test else 0,
        1 if files_test else 0
    ])
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n🎯 النتيجة الإجمالية:")
    print(f"   📊 الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print(f"   🎉 ممتاز! النظام جاهز للاستخدام")
    elif success_rate >= 70:
        print(f"   ✅ جيد! النظام يعمل مع بعض القيود")
    elif success_rate >= 50:
        print(f"   ⚠️ مقبول! يحتاج بعض التحسينات")
    else:
        print(f"   ❌ ضعيف! يحتاج إصلاحات كبيرة")
    
    print(f"\n💡 التوصيات:")
    if len(failed_imports) > 0:
        print(f"   - تثبيت المكتبات المفقودة: pip install -r requirements.txt")
    if not db_test:
        print(f"   - إعادة تهيئة قاعدة البيانات")
    if not files_test:
        print(f"   - التأكد من وجود جميع الملفات المطلوبة")
    
    print(f"\n📞 للدعم الفني: <EMAIL>")
    print("="*60)

def main():
    """الدالة الرئيسية"""
    try:
        generate_test_report()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
