#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات عينة للعملاء الآجل لاختبار التقرير
"""

import sqlite3
import json
from datetime import datetime, timedelta
from pathlib import Path

# مسار قاعدة البيانات
DB_PATH = Path(__file__).parent / "db" / "cashier_filter.db"

def create_sample_credit_data():
    """إنشاء بيانات عينة للعملاء الآجل"""
    
    # بيانات عينة للعملاء الآجل
    sample_filters = [
        {
            "cashier_id": 1,
            "admin_id": 1,
            "admin_name": "أحمد المدير",
            "date": (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            "data": {
                "totals": {
                    "bank": 5000.0,
                    "cash": 3000.0,
                    "credit": 2500.0,
                    "client": 800.0,
                    "return": 200.0
                },
                "details": {
                    "credit_transactions": [
                        {
                            "customer_name": "محمد أحمد علي",
                            "phone": "**********",
                            "invoice_number": "INV-001",
                            "amount": 1500.0,
                            "due_date": "2025-01-15",
                            "notes": "عميل مميز - دفع منتظم"
                        },
                        {
                            "customer": "سارة خالد محمد",
                            "mobile": "**********",
                            "invoice": "INV-002",
                            "amount": 1000.0,
                            "due_date": "2025-01-20",
                            "notes": "متابعة أسبوعية"
                        }
                    ]
                }
            },
            "notes": "تصفية يوم الأمس"
        },
        {
            "cashier_id": 2,
            "admin_id": 1,
            "admin_name": "أحمد المدير",
            "date": datetime.now().strftime('%Y-%m-%d'),
            "data": {
                "totals": {
                    "bank": 4500.0,
                    "cash": 2800.0,
                    "credit": 3200.0,
                    "client": 600.0,
                    "return": 150.0
                },
                "details": {
                    "credit_transactions": [
                        {
                            "customer_name": "عبدالله سعد الغامدي",
                            "phone": "**********",
                            "invoice_number": "INV-003",
                            "amount": 2200.0,
                            "due_date": "2025-01-25",
                            "notes": "عميل جديد"
                        },
                        {
                            "client": "فاطمة عبدالرحمن",
                            "tel": "**********",
                            "invoice": "INV-004",
                            "amount": 1000.0,
                            "date": "2025-01-18",
                            "note": "دفع جزئي متفق عليه"
                        }
                    ]
                }
            },
            "notes": "تصفية اليوم"
        },
        {
            "cashier_id": 1,
            "admin_id": 1,
            "admin_name": "سعد المشرف",
            "date": (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d'),
            "data": {
                "totals": {
                    "bank": 6000.0,
                    "cash": 2500.0,
                    "credit": 1800.0,
                    "client": 900.0,
                    "return": 100.0
                },
                "details": {
                    "credit_transactions": [
                        {
                            "name": "خالد محمد الأحمد",
                            "phone": "**********",
                            "invoice_number": "INV-005",
                            "amount": 800.0,
                            "due_date": "2025-01-12",
                            "notes": "متأخر في السداد"
                        },
                        {
                            "customer_name": "نورا سالم القحطاني",
                            "mobile": "**********",
                            "invoice": "INV-006",
                            "amount": 1000.0,
                            "due_date": "2025-01-30",
                            "notes": "عميلة منتظمة"
                        }
                    ]
                }
            },
            "notes": "تصفية أول أمس"
        }
    ]
    
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # إنشاء كاشيرين عينة إذا لم يكونوا موجودين
        cursor.execute("INSERT OR IGNORE INTO cashiers (id, name, number) VALUES (1, 'كاشير أول', '001')")
        cursor.execute("INSERT OR IGNORE INTO cashiers (id, name, number) VALUES (2, 'كاشير ثاني', '002')")
        
        # إنشاء مسؤولين عينة إذا لم يكونوا موجودين
        cursor.execute("INSERT OR IGNORE INTO admins (id, name, username, password) VALUES (1, 'أحمد المدير', 'admin', 'password')")
        
        # إضافة التصفيات العينة
        for filter_data in sample_filters:
            cursor.execute("""
                INSERT INTO filters (cashier_id, admin_id, admin_name, date, data, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                filter_data["cashier_id"],
                filter_data["admin_id"],
                filter_data["admin_name"],
                filter_data["date"],
                json.dumps(filter_data["data"], ensure_ascii=False),
                filter_data["notes"]
            ))
        
        conn.commit()
        print("✅ تم إضافة بيانات العينة بنجاح!")
        print(f"📊 تم إضافة {len(sample_filters)} تصفية تحتوي على عملاء آجل")
        
        # عرض ملخص البيانات المضافة
        total_customers = sum(len(f["data"]["details"]["credit_transactions"]) for f in sample_filters)
        total_amount = sum(
            sum(t["amount"] for t in f["data"]["details"]["credit_transactions"])
            for f in sample_filters
        )
        
        print(f"👥 إجمالي العملاء الآجل: {total_customers}")
        print(f"💰 إجمالي المبلغ: {total_amount:,.2f} ريال")
        print("\n🌐 يمكنك الآن زيارة التقرير على:")
        print("   http://localhost:5000/credit-customers")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

def clear_sample_data():
    """مسح بيانات العينة"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # مسح التصفيات العينة
        cursor.execute("DELETE FROM filters WHERE notes LIKE '%تصفية%'")
        
        conn.commit()
        print("✅ تم مسح بيانات العينة")
        
    except Exception as e:
        print(f"❌ خطأ في مسح البيانات: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("🔧 إدارة بيانات العينة للعملاء الآجل")
    print("=" * 50)
    print("1. إضافة بيانات عينة")
    print("2. مسح بيانات العينة")
    print("3. خروج")
    
    choice = input("\nاختر رقم العملية: ").strip()
    
    if choice == "1":
        create_sample_credit_data()
    elif choice == "2":
        clear_sample_data()
    elif choice == "3":
        print("👋 وداعاً!")
    else:
        print("❌ اختيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")
