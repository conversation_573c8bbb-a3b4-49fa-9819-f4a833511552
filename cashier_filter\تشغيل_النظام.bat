@echo off
chcp 65001 > nul
title نظام تصفية الكاشير المتكامل 2025

:: تعيين متغيرات البيئة
set PYTHONIOENCODING=utf-8
set PYTHONPATH=%~dp0

:: طباعة شعار النظام
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║         Cashier Filter System 2025                                   ║
echo ║                                                                      ║
echo ║    الإصدار 3.0.0 - مع ذكاء اصطناعي متطور                          ║
echo ║    Version 3.0.0 - With Advanced AI                                 ║
echo ║                                                                      ║
echo ║    تطوير: محمد الكامل ^| Developed by: <PERSON>              ║
echo ║    البريد: <EMAIL>                                     ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

:: الانتقال لمجلد التطبيق
cd /d "%~dp0"

:: التحقق من وجود Python
echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo    https://python.org
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

:: عرض إصدار Python
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% متاح

:: التحقق من وجود الملف الرئيسي
echo.
echo 📁 التحقق من ملفات النظام...
if not exist "main.py" (
    echo ❌ خطأ: ملف main.py غير موجود
    echo.
    echo 💡 تأكد من وجود جميع ملفات النظام في نفس المجلد
    echo.
    pause
    exit /b 1
)
echo ✅ الملف الرئيسي موجود

:: التحقق من قاعدة البيانات
if not exist "db\cashier_filter.db" (
    echo ⚠️ تحذير: قاعدة البيانات غير موجودة
    echo 🔧 محاولة إنشاء قاعدة البيانات...
    
    if exist "setup.py" (
        python setup.py
        if errorlevel 1 (
            echo ❌ فشل في إنشاء قاعدة البيانات
            echo.
            pause
            exit /b 1
        )
        echo ✅ تم إنشاء قاعدة البيانات
    ) else (
        echo ❌ ملف الإعداد غير موجود
        echo.
        pause
        exit /b 1
    )
) else (
    echo ✅ قاعدة البيانات موجودة
)

:: التحقق من المتطلبات
echo.
echo 📦 التحقق من المتطلبات...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: customtkinter غير مثبت
    echo 🔧 محاولة تثبيت المتطلبات...
    
    if exist "requirements.txt" (
        python -m pip install -r requirements.txt
        if errorlevel 1 (
            echo ❌ فشل في تثبيت المتطلبات
            echo.
            echo 💡 جرب تشغيل الأمر التالي يدوياً:
            echo    pip install customtkinter
            echo.
            pause
            exit /b 1
        )
        echo ✅ تم تثبيت المتطلبات
    ) else (
        echo 🔧 تثبيت المتطلبات الأساسية...
        python -m pip install customtkinter
        if errorlevel 1 (
            echo ❌ فشل في تثبيت customtkinter
            echo.
            pause
            exit /b 1
        )
        echo ✅ تم تثبيت المتطلبات الأساسية
    )
) else (
    echo ✅ جميع المتطلبات متاحة
)

:: عرض معلومات الدخول
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                        معلومات تسجيل الدخول                        ║
echo ║                                                                      ║
echo ║    👤 اسم المستخدم: admin                                           ║
echo ║    🔐 كلمة المرور: 123456                                            ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

:: تشغيل النظام
echo 🚀 جاري تشغيل نظام تصفية الكاشير...
echo.

python main.py

:: التحقق من حالة الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo.
    echo 🔧 خطوات حل المشكلة:
    echo 1. تأكد من تثبيت Python بشكل صحيح
    echo 2. تأكد من وجود جميع ملفات النظام
    echo 3. جرب تشغيل: python main.py
    echo 4. راجع ملف README.md للمزيد من المعلومات
    echo.
    echo 💬 للدعم الفني: <EMAIL>
    echo.
) else (
    echo.
    echo ✅ تم إغلاق النظام بنجاح
    echo.
    echo 👋 شكراً لاستخدام نظام تصفية الكاشير المتكامل 2025
    echo    تطوير: محمد الكامل
    echo.
)

pause
