# 📈 دليل واجهة الاتجاهات والتوقعات المتقدمة

## 🎯 نظرة عامة

واجهة الاتجاهات والتوقعات المتقدمة هي أحدث إضافة لنظام تصفية الكاشير 2025، وتوفر تحليلاً ذكياً شاملاً للبيانات المالية مع إمكانيات التنبؤ المستقبلي والرسوم البيانية التفاعلية.

## ✨ الميزات الرئيسية

### 📊 **مؤشرات الأداء الرئيسية (KPIs)**
- إجمالي الإيرادات للفترة المحددة
- إجمالي عدد المعاملات
- متوسط قيمة المعاملة
- معدل النمو مع الاتجاه العام

### 🔮 **التوقعات الذكية**
- توقعات الأسبوع القادم (إيرادات ومعاملات)
- توقعات الشهر القادم (إيرادات ومعاملات)
- مستوى الثقة في التوقعات
- اتجاه التوقع (صاعد/نازل/مستقر)

### 📈 **الرسوم البيانية التفاعلية**
- **رسم الاتجاهات**: عرض الإيرادات والمعاملات اليومية
- **رسم التوقعات**: البيانات الفعلية مع التوقعات ونطاق الثقة
- **التحليل الموسمي**: أداء أيام الأسبوع مع رسوم دائرية
- **رسم المقارنات**: أداء الكاشيرين المختلفين

### ⚠️ **التنبيهات الذكية**
- تنبيهات النمو (ممتاز/جيد/منخفض)
- تحذيرات التقلبات العالية
- توصيات تحسين الأداء
- تحليل أفضل وأضعف أيام الأسبوع

## 🚀 كيفية الاستخدام

### 1️⃣ **الوصول للواجهة**

#### من الواجهة الرئيسية:
```
الواجهة الرئيسية → التقارير والتحليلات المتقدمة → الاتجاهات والتوقعات
```

#### تشغيل مباشر:
```bash
python run_trends_predictions.py
```

### 2️⃣ **لوحة التحكم**

#### **فترة التحليل**
- اختر من 7، 15، 30، 60، 90، 180، أو 365 يوم
- الافتراضي: 30 يوم

#### **نوع التحليل**
- **تحليل شامل**: جميع الميزات
- **الاتجاهات فقط**: التركيز على الاتجاهات
- **التوقعات فقط**: التركيز على التنبؤات
- **مؤشرات الأداء**: KPIs فقط
- **تحليل الموسمية**: أنماط أيام الأسبوع
- **تحليل المخاطر**: تحليل التقلبات

#### **نوع البيانات**
- الإيرادات اليومية
- عدد المعاملات
- متوسط قيمة المعاملة
- أداء الكاشيرين
- توزيع العملاء

### 3️⃣ **قراءة المؤشرات**

#### **مؤشرات الأداء الرئيسية**
- 💰 **الإيرادات الإجمالية**: إجمالي الإيرادات للفترة
- 🧾 **إجمالي المعاملات**: عدد المعاملات الكلي
- 📈 **متوسط قيمة المعاملة**: الإيرادات ÷ المعاملات
- 📊 **معدل النمو**: النسبة المئوية للنمو

#### **ألوان معدل النمو**
- 🟢 **أخضر**: نمو إيجابي (> 1%)
- 🟡 **أصفر**: مستقر (-1% إلى 1%)
- 🔴 **أحمر**: انخفاض (< -1%)

### 4️⃣ **فهم التوقعات**

#### **مستويات الثقة**
- **عالي جداً (95%)**: تقلبات منخفضة جداً
- **عالي (85%)**: تقلبات منخفضة
- **متوسط (70%)**: تقلبات متوسطة
- **منخفض (55%)**: تقلبات عالية
- **منخفض جداً (40%)**: تقلبات عالية جداً

#### **اتجاه التوقع**
- **صاعد**: نمو متوقع > 2%
- **مستقر**: نمو متوقع بين -2% و 2%
- **نازل**: انخفاض متوقع > 2%

### 5️⃣ **قراءة الرسوم البيانية**

#### **رسم الاتجاهات**
- الخط الأخضر: الإيرادات اليومية
- الخط الأزرق: عدد المعاملات اليومية
- الشبكة: مساعدة في قراءة القيم

#### **رسم التوقعات**
- الخط المتصل: البيانات الفعلية
- الخط المتقطع: التوقعات
- المنطقة المظللة: نطاق الثقة

#### **التحليل الموسمي**
- الرسم العمودي: مقارنة أيام الأسبوع
- الرسم الدائري: توزيع الإيرادات

#### **رسم المقارنات**
- أداء الكاشيرين حسب الإيرادات
- أداء الكاشيرين حسب عدد المعاملات

## 🔧 المتطلبات التقنية

### **المكتبات المطلوبة**
```bash
pip install customtkinter matplotlib numpy pandas seaborn
```

### **متطلبات النظام**
- Python 3.8 أو أحدث
- ذاكرة: 4 جيجابايت على الأقل
- مساحة القرص: 100 ميجابايت للمكتبات

## 📊 تصدير التقارير

### **تنسيقات التصدير**
- **JSON**: بيانات منظمة للمعالجة
- **TXT**: تقرير نصي مقروء

### **محتويات التقرير**
- معلومات التقرير (التاريخ، الفترة، النوع)
- المؤشرات الرئيسية
- بيانات الاتجاهات
- التوقعات المستقبلية
- البيانات الخام

## ⚠️ استكشاف الأخطاء

### **مشاكل شائعة وحلولها**

#### **"المكتبات المطلوبة غير مثبتة"**
```bash
pip install matplotlib numpy pandas seaborn
```

#### **"لا توجد بيانات كافية"**
- تأكد من وجود بيانات في قاعدة البيانات
- جرب فترة تحليل أطول
- استخدم البيانات التجريبية: `python test_trends_predictions.py`

#### **"خطأ في تحميل الرسوم البيانية"**
- تأكد من تثبيت matplotlib
- أعد تشغيل التطبيق
- تحقق من صحة البيانات

#### **"التوقعات غير دقيقة"**
- التوقعات تعتمد على البيانات التاريخية
- كلما زادت البيانات، زادت الدقة
- التقلبات العالية تقلل من دقة التوقعات

## 🎯 نصائح للاستخدام الأمثل

### **لتحسين دقة التوقعات**
1. استخدم فترات تحليل أطول (60-90 يوم)
2. تأكد من اكتمال البيانات
3. راجع التنبيهات والتوصيات

### **لفهم الاتجاهات بشكل أفضل**
1. قارن فترات مختلفة
2. راجع التحليل الموسمي
3. انتبه للتقلبات والشذوذ

### **للاستفادة من التنبيهات**
1. اقرأ جميع التنبيهات بعناية
2. اتخذ إجراءات بناءً على التوصيات
3. راقب التحسن بعد التطبيق

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- ملفات السجل: مجلد `logs`
- ملف الاختبار: `test_trends_predictions.py`

---

**تطوير: محمد الكامل - نظام تصفية الكاشير 2025**  
**© 2025 - جميع الحقوق محفوظة**
