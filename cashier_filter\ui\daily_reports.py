#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير اليومية
Daily Reports Window
"""

import customtkinter as ctk
from tkinter import messagebox, ttk
import tkinter as tk
import sqlite3
from datetime import datetime, timedelta
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.font_manager import get_stable_font
    from utils.window_utils import bring_window_to_front
except ImportError:
    def get_stable_font(size_type="normal", weight="normal"):
        sizes = {"small": 12, "normal": 14, "medium": 16, "large": 18}
        size = sizes.get(size_type, 14)
        return ("Arial", size, weight)
    
    def bring_window_to_front(window):
        window.lift()
        window.focus_force()

# تحديد مسار قاعدة البيانات
if os.path.exists("db/cashier_filter.db"):
    DB_PATH = "db/cashier_filter.db"
else:
    DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class DailyReportsWindow(ctk.CTkToplevel):
    """نافذة التقارير اليومية"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title("📈 التقارير اليومية")
        self.geometry("1000x700")
        self.transient(parent)
        self.grab_set()
        
        # تعيين أيقونة
        try:
            self.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # متغيرات
        self.selected_date = datetime.now().strftime('%Y-%m-%d')
        
        self.create_widgets()
        self.load_daily_data()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # الإطار الرئيسي مع شريط التمرير
        main_canvas = tk.Canvas(self, bg="#f8f9fa", highlightthickness=0)
        main_scrollbar = ttk.Scrollbar(self, orient="vertical", command=main_canvas.yview)
        main_frame = ctk.CTkFrame(main_canvas, fg_color="#f8f9fa")

        # ربط شريط التمرير بالكانفاس
        main_canvas.configure(yscrollcommand=main_scrollbar.set)

        # إعداد التخطيط
        main_canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        main_scrollbar.pack(side="right", fill="y")

        # إضافة الإطار للكانفاس
        canvas_frame = main_canvas.create_window((0, 0), window=main_frame, anchor="nw")

        # تحديث منطقة التمرير عند تغيير الحجم
        def configure_scroll_region(event=None):
            main_canvas.configure(scrollregion=main_canvas.bbox("all"))
            # تحديث عرض الإطار ليتناسب مع الكانفاس
            canvas_width = main_canvas.winfo_width()
            main_canvas.itemconfig(canvas_frame, width=canvas_width)

        main_frame.bind("<Configure>", configure_scroll_region)
        main_canvas.bind("<Configure>", configure_scroll_region)

        # دعم التمرير بالماوس
        def on_mousewheel(event):
            main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        main_canvas.bind("<MouseWheel>", on_mousewheel)

        # حفظ المراجع للاستخدام لاحقاً
        self.main_canvas = main_canvas
        self.main_scrollbar = main_scrollbar
        
        # العنوان
        title_label = ctk.CTkLabel(
            main_frame,
            text="📈 التقارير اليومية",
            font=get_stable_font("large", "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)
        
        # إطار اختيار التاريخ
        self.create_date_selection_frame(main_frame)
        
        # إطار الإحصائيات اليومية
        self.create_daily_stats_frame(main_frame)
        
        # إطار الجداول
        self.create_tables_frame(main_frame)
        
        # إطار الأزرار
        self.create_buttons_frame(main_frame)
    
    def create_date_selection_frame(self, parent):
        """إنشاء إطار اختيار التاريخ"""
        date_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        date_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الإطار
        date_title = ctk.CTkLabel(
            date_frame,
            text="📅 اختيار التاريخ",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        date_title.pack(pady=(10, 5))
        
        # إطار التحكم في التاريخ
        date_controls = ctk.CTkFrame(date_frame, fg_color="transparent")
        date_controls.pack(fill="x", padx=20, pady=(0, 15))
        
        # أزرار التاريخ السريعة
        quick_dates = [
            ("اليوم", 0),
            ("أمس", 1),
            ("آخر 7 أيام", 7),
            ("آخر 30 يوم", 30)
        ]
        
        for text, days_back in quick_dates:
            btn = ctk.CTkButton(
                date_controls, text=text, width=100,
                command=lambda d=days_back: self.set_quick_date(d),
                fg_color="#007bff", hover_color="#0056b3"
            )
            btn.pack(side="left", padx=5)
        
        # عرض التاريخ المختار
        self.selected_date_label = ctk.CTkLabel(
            date_controls,
            text=f"التاريخ المختار: {self.selected_date}",
            font=get_stable_font("normal", "bold"),
            text_color="#28a745"
        )
        self.selected_date_label.pack(side="right", padx=20)
    
    def create_daily_stats_frame(self, parent):
        """إنشاء إطار الإحصائيات اليومية"""
        stats_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        stats_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        # عنوان الإحصائيات
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📊 إحصائيات اليوم",
            font=get_stable_font("medium", "bold"),
            text_color="#495057"
        )
        stats_title.pack(pady=(10, 5))
        
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # بطاقات الإحصائيات
        self.total_filters_card = self.create_stat_card(cards_frame, "📝", "إجمالي التصفيات", "0", "#007bff")
        self.total_amount_card = self.create_stat_card(cards_frame, "💰", "إجمالي المبلغ", "0", "#28a745")
        self.avg_filter_card = self.create_stat_card(cards_frame, "📊", "متوسط التصفية", "0", "#6f42c1")
        self.active_users_card = self.create_stat_card(cards_frame, "👥", "المستخدمين النشطين", "0", "#17a2b8")
    
    def create_stat_card(self, parent, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=15, width=200, height=100)
        card.pack(side="left", padx=10, fill="y")
        card.pack_propagate(False)
        
        # الأيقونة
        icon_label = ctk.CTkLabel(card, text=icon, font=("Arial", 24), text_color="white")
        icon_label.pack(pady=(10, 0))
        
        # القيمة
        value_label = ctk.CTkLabel(card, text=value, font=get_stable_font("large", "bold"), text_color="white")
        value_label.pack()
        
        # العنوان
        title_label = ctk.CTkLabel(card, text=title, font=get_stable_font("small"), text_color="white")
        title_label.pack(pady=(0, 10))
        
        return {"card": card, "value": value_label, "title": title_label}
    
    def create_tables_frame(self, parent):
        """إنشاء إطار الجداول"""
        tables_frame = ctk.CTkFrame(parent, fg_color="transparent")
        tables_frame.pack(fill="both", expand=True, padx=10)
        
        # تكوين الشبكة
        tables_frame.grid_columnconfigure(0, weight=1)
        tables_frame.grid_columnconfigure(1, weight=1)
        tables_frame.grid_rowconfigure(0, weight=1)
        
        # جدول التصفيات اليومية
        self.create_daily_filters_table(tables_frame)
        
        # جدول نشاط المستخدمين
        self.create_user_activity_table(tables_frame)
    
    def create_daily_filters_table(self, parent):
        """إنشاء جدول التصفيات اليومية"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="📝 التصفيات اليومية", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("الوقت", "الكاشير", "المبلغ", "الحالة")
        self.daily_filters_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        self.daily_filters_tree.heading("الوقت", text="🕐 الوقت")
        self.daily_filters_tree.heading("الكاشير", text="👤 الكاشير")
        self.daily_filters_tree.heading("المبلغ", text="💰 المبلغ")
        self.daily_filters_tree.heading("الحالة", text="✅ الحالة")
        
        self.daily_filters_tree.column("الوقت", width=120, anchor="center")
        self.daily_filters_tree.column("الكاشير", width=120, anchor="center")
        self.daily_filters_tree.column("المبلغ", width=100, anchor="center")
        self.daily_filters_tree.column("الحالة", width=100, anchor="center")
        
        scrollbar1 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.daily_filters_tree.yview)
        self.daily_filters_tree.configure(yscrollcommand=scrollbar1.set)
        
        self.daily_filters_tree.pack(side="left", fill="both", expand=True)
        scrollbar1.pack(side="right", fill="y")
    
    def create_user_activity_table(self, parent):
        """إنشاء جدول نشاط المستخدمين"""
        frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        
        # العنوان
        title = ctk.CTkLabel(frame, text="👥 نشاط المستخدمين", 
                           font=get_stable_font("medium", "bold"), text_color="#495057")
        title.pack(pady=(10, 5))
        
        # الجدول
        tree_frame = tk.Frame(frame, bg="#ffffff")
        tree_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        columns = ("المستخدم", "العمليات", "آخر نشاط")
        self.user_activity_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        self.user_activity_tree.heading("المستخدم", text="👤 المستخدم")
        self.user_activity_tree.heading("العمليات", text="📝 العمليات")
        self.user_activity_tree.heading("آخر نشاط", text="🕐 آخر نشاط")
        
        self.user_activity_tree.column("المستخدم", width=120, anchor="center")
        self.user_activity_tree.column("العمليات", width=100, anchor="center")
        self.user_activity_tree.column("آخر نشاط", width=120, anchor="center")
        
        scrollbar2 = ttk.Scrollbar(tree_frame, orient="vertical", command=self.user_activity_tree.yview)
        self.user_activity_tree.configure(yscrollcommand=scrollbar2.set)
        
        self.user_activity_tree.pack(side="left", fill="both", expand=True)
        scrollbar2.pack(side="right", fill="y")
    
    def create_buttons_frame(self, parent):
        """إنشاء إطار الأزرار"""
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=10, pady=10)
        
        # زر تحديث
        refresh_btn = ctk.CTkButton(
            buttons_frame, text="🔄 تحديث", width=120,
            command=self.load_daily_data,
            fg_color="#28a745", hover_color="#218838"
        )
        refresh_btn.pack(side="left", padx=5)
        
        # زر طباعة
        print_btn = ctk.CTkButton(
            buttons_frame, text="🖨️ طباعة", width=120,
            command=self.print_report,
            fg_color="#6f42c1", hover_color="#5a32a3"
        )
        print_btn.pack(side="left", padx=5)
        
        # زر تصدير
        export_btn = ctk.CTkButton(
            buttons_frame, text="📤 تصدير", width=120,
            command=self.export_report,
            fg_color="#17a2b8", hover_color="#138496"
        )
        export_btn.pack(side="left", padx=5)
        
        # زر إغلاق
        close_btn = ctk.CTkButton(
            buttons_frame, text="❌ إغلاق", width=120,
            command=self.destroy,
            fg_color="#dc3545", hover_color="#c82333"
        )
        close_btn.pack(side="right", padx=5)

    def set_quick_date(self, days_back):
        """تعيين تاريخ سريع"""
        if days_back == 0:
            self.selected_date = datetime.now().strftime('%Y-%m-%d')
        else:
            target_date = datetime.now() - timedelta(days=days_back)
            self.selected_date = target_date.strftime('%Y-%m-%d')

        self.selected_date_label.configure(text=f"التاريخ المختار: {self.selected_date}")
        self.load_daily_data()

    def load_daily_data(self):
        """تحميل البيانات اليومية"""
        try:
            # تحميل الإحصائيات اليومية
            self.load_daily_statistics()

            # تحميل جداول البيانات
            self.load_daily_filters()
            self.load_user_activity()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات اليومية: {e}")

    def load_daily_statistics(self):
        """تحميل الإحصائيات اليومية"""
        try:
            if not os.path.exists(DB_PATH):
                # إذا لم تكن قاعدة البيانات موجودة، عرض قيم افتراضية
                self.total_filters_card["value"].configure(text="0")
                self.total_amount_card["value"].configure(text="0.00")
                self.avg_filter_card["value"].configure(text="0.00")
                self.active_users_card["value"].configure(text="0")
                return

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # إجمالي التصفيات لليوم - مع التعامل مع أسماء الأعمدة المختلفة
            try:
                c.execute('''
                    SELECT COUNT(*) FROM filters
                    WHERE DATE(created_at) = ?
                ''', (self.selected_date,))
                total_filters = c.fetchone()[0] or 0
            except:
                total_filters = 0
            self.total_filters_card["value"].configure(text=str(total_filters))

            # إجمالي المبلغ لليوم
            try:
                c.execute('''
                    SELECT SUM(total_amount) FROM filters
                    WHERE DATE(created_at) = ?
                ''', (self.selected_date,))
                result = c.fetchone()[0]
                total_amount = result if result else 0
            except:
                try:
                    c.execute('''
                        SELECT SUM(amount) FROM filters
                        WHERE DATE(created_at) = ?
                    ''', (self.selected_date,))
                    result = c.fetchone()[0]
                    total_amount = result if result else 0
                except:
                    total_amount = 0
            self.total_amount_card["value"].configure(text=f"{total_amount:,.2f}")

            # متوسط التصفية
            avg_filter = (total_amount / total_filters) if total_filters > 0 else 0
            self.avg_filter_card["value"].configure(text=f"{avg_filter:,.2f}")

            # المستخدمين النشطين لليوم
            c.execute('''
                SELECT COUNT(DISTINCT username) FROM operations_log
                WHERE DATE(timestamp) = ?
            ''', (self.selected_date,))
            active_users = c.fetchone()[0] or 0
            self.active_users_card["value"].configure(text=str(active_users))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات اليومية: {e}")
            # عرض قيم افتراضية في حالة الخطأ
            self.total_filters_card["value"].configure(text="0")
            self.total_amount_card["value"].configure(text="0.00")
            self.avg_filter_card["value"].configure(text="0.00")
            self.active_users_card["value"].configure(text="0")

    def load_daily_filters(self):
        """تحميل التصفيات اليومية"""
        try:
            # مسح الجدول
            for item in self.daily_filters_tree.get_children():
                self.daily_filters_tree.delete(item)

            if not os.path.exists(DB_PATH):
                return

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # استعلام التصفيات لليوم - مع التعامل مع أسماء الأعمدة المختلفة
            try:
                # محاولة الاستعلام الأول
                c.execute('''
                    SELECT
                        strftime('%H:%M', created_at) as time,
                        cashier_name,
                        total_amount,
                        'مكتمل' as status
                    FROM filters
                    WHERE DATE(created_at) = ?
                    ORDER BY created_at DESC
                    LIMIT 50
                ''', (self.selected_date,))
            except sqlite3.OperationalError:
                # إذا فشل، جرب أسماء أعمدة مختلفة
                try:
                    c.execute('''
                        SELECT
                            strftime('%H:%M', created_at) as time,
                            'كاشير' as cashier_name,
                            amount as total_amount,
                            'مكتمل' as status
                        FROM filters
                        WHERE DATE(created_at) = ?
                        ORDER BY created_at DESC
                        LIMIT 50
                    ''', (self.selected_date,))
                except sqlite3.OperationalError:
                    # إذا فشل أيضاً، استخدم استعلام بسيط
                    c.execute('''
                        SELECT
                            strftime('%H:%M', datetime('now')) as time,
                            'كاشير عام' as cashier_name,
                            0 as total_amount,
                            'مكتمل' as status
                        LIMIT 1
                    ''')

            filters = c.fetchall()

            for time, cashier, amount, status in filters:
                # تنسيق الحالة
                status_text = "✅ مكتمل" if status == "completed" else "⏳ قيد المعالجة"

                self.daily_filters_tree.insert("", "end", values=(
                    time, cashier, f"{amount:,.2f}", status_text
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل التصفيات اليومية: {e}")

    def load_user_activity(self):
        """تحميل نشاط المستخدمين"""
        try:
            # مسح الجدول
            for item in self.user_activity_tree.get_children():
                self.user_activity_tree.delete(item)

            if not os.path.exists(DB_PATH):
                return

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # استعلام نشاط المستخدمين لليوم
            c.execute('''
                SELECT
                    username,
                    COUNT(*) as operations_count,
                    MAX(strftime('%H:%M', timestamp)) as last_activity
                FROM operations_log
                WHERE DATE(timestamp) = ?
                GROUP BY username
                ORDER BY operations_count DESC
                LIMIT 20
            ''', (self.selected_date,))

            activities = c.fetchall()

            for username, count, last_activity in activities:
                self.user_activity_tree.insert("", "end", values=(
                    username, count, last_activity or "غير محدد"
                ))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل نشاط المستخدمين: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء نافذة معاينة الطباعة
            self.show_print_preview()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح معاينة الطباعة: {e}")

    def show_print_preview(self):
        """عرض معاينة الطباعة"""
        try:
            # إنشاء نافذة معاينة الطباعة
            preview_window = ctk.CTkToplevel(self)
            preview_window.title("🖨️ معاينة الطباعة - التقرير اليومي")
            preview_window.geometry("800x900")
            preview_window.transient(self)
            preview_window.grab_set()

            # تعيين أيقونة
            try:
                preview_window.iconbitmap("assets/icon.ico")
            except:
                pass

            # الإطار الرئيسي
            main_frame = ctk.CTkFrame(preview_window, fg_color="#ffffff")
            main_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # شريط الأدوات
            toolbar_frame = ctk.CTkFrame(main_frame, fg_color="#f8f9fa", height=50)
            toolbar_frame.pack(fill="x", padx=5, pady=(5, 10))
            toolbar_frame.pack_propagate(False)

            # أزرار شريط الأدوات
            print_btn = ctk.CTkButton(
                toolbar_frame, text="🖨️ طباعة", width=100,
                command=lambda: self.execute_print(preview_window),
                fg_color="#007bff", hover_color="#0056b3"
            )
            print_btn.pack(side="left", padx=5, pady=10)

            save_pdf_btn = ctk.CTkButton(
                toolbar_frame, text="📄 حفظ PDF", width=100,
                command=lambda: self.save_as_pdf(preview_window),
                fg_color="#28a745", hover_color="#218838"
            )
            save_pdf_btn.pack(side="left", padx=5, pady=10)

            close_btn = ctk.CTkButton(
                toolbar_frame, text="❌ إغلاق", width=100,
                command=preview_window.destroy,
                fg_color="#dc3545", hover_color="#c82333"
            )
            close_btn.pack(side="right", padx=5, pady=10)

            # منطقة المعاينة مع شريط التمرير
            preview_canvas = tk.Canvas(main_frame, bg="#ffffff", highlightthickness=1, highlightcolor="#dee2e6")
            preview_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=preview_canvas.yview)
            preview_content = ctk.CTkFrame(preview_canvas, fg_color="#ffffff")

            preview_canvas.configure(yscrollcommand=preview_scrollbar.set)

            preview_canvas.pack(side="left", fill="both", expand=True, padx=(5, 0), pady=5)
            preview_scrollbar.pack(side="right", fill="y", padx=(0, 5), pady=5)

            canvas_frame = preview_canvas.create_window((0, 0), window=preview_content, anchor="nw")

            # إنشاء محتوى التقرير للطباعة
            self.create_print_content(preview_content)

            # تحديث منطقة التمرير
            def update_scroll_region(event=None):
                preview_canvas.configure(scrollregion=preview_canvas.bbox("all"))
                canvas_width = preview_canvas.winfo_width()
                preview_canvas.itemconfig(canvas_frame, width=canvas_width)

            preview_content.bind("<Configure>", update_scroll_region)
            preview_canvas.bind("<Configure>", update_scroll_region)

            # دعم التمرير بالماوس
            def on_mousewheel(event):
                preview_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

            preview_canvas.bind("<MouseWheel>", on_mousewheel)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء معاينة الطباعة: {e}")

    def create_print_content(self, parent):
        """إنشاء محتوى التقرير للطباعة"""
        try:
            # رأس التقرير
            header_frame = ctk.CTkFrame(parent, fg_color="#2c3e50", corner_radius=10)
            header_frame.pack(fill="x", padx=20, pady=20)

            # عنوان التقرير
            title_label = ctk.CTkLabel(
                header_frame,
                text="📈 التقرير اليومي",
                font=get_stable_font("large", "bold"),
                text_color="white"
            )
            title_label.pack(pady=15)

            # معلومات التقرير
            info_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=10)
            info_frame.pack(fill="x", padx=20, pady=(0, 20))

            info_text = f"""
📅 التاريخ: {self.selected_date}
🕐 وقت الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
👤 المستخدم: admin
🏪 نظام تصفية الكاشير v3.5.0
            """.strip()

            info_label = ctk.CTkLabel(
                info_frame,
                text=info_text,
                font=get_stable_font("normal"),
                text_color="#495057",
                justify="left"
            )
            info_label.pack(pady=15, padx=20)

            # الإحصائيات العامة
            stats_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
            stats_frame.pack(fill="x", padx=20, pady=(0, 20))

            stats_title = ctk.CTkLabel(
                stats_frame,
                text="📊 الإحصائيات العامة",
                font=get_stable_font("medium", "bold"),
                text_color="#2c3e50"
            )
            stats_title.pack(pady=(15, 10))

            # بطاقات الإحصائيات للطباعة
            stats_grid = ctk.CTkFrame(stats_frame, fg_color="transparent")
            stats_grid.pack(fill="x", padx=20, pady=(0, 15))

            stats_data = [
                ("📝 إجمالي التصفيات", self.total_filters_card["value"].cget("text")),
                ("💰 إجمالي المبلغ", self.total_amount_card["value"].cget("text")),
                ("📊 متوسط التصفية", self.avg_filter_card["value"].cget("text")),
                ("👥 المستخدمين النشطين", self.active_users_card["value"].cget("text"))
            ]

            for i, (title, value) in enumerate(stats_data):
                stat_frame = ctk.CTkFrame(stats_grid, fg_color="#e9ecef", corner_radius=8)
                stat_frame.pack(fill="x", pady=5)

                stat_label = ctk.CTkLabel(
                    stat_frame,
                    text=f"{title}: {value}",
                    font=get_stable_font("normal", "bold"),
                    text_color="#495057"
                )
                stat_label.pack(pady=10, padx=15)

            # جدول التصفيات اليومية
            self.create_print_table(parent, "📝 التصفيات اليومية", self.daily_filters_tree)

            # جدول نشاط المستخدمين
            self.create_print_table(parent, "👥 نشاط المستخدمين", self.user_activity_tree)

            # تذييل التقرير
            footer_frame = ctk.CTkFrame(parent, fg_color="#6c757d", corner_radius=10)
            footer_frame.pack(fill="x", padx=20, pady=20)

            footer_text = f"""
تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير المتقدم
© 2025 - جميع الحقوق محفوظة
            """.strip()

            footer_label = ctk.CTkLabel(
                footer_frame,
                text=footer_text,
                font=get_stable_font("small"),
                text_color="white"
            )
            footer_label.pack(pady=10)

        except Exception as e:
            print(f"خطأ في إنشاء محتوى الطباعة: {e}")

    def create_print_table(self, parent, title, tree_widget):
        """إنشاء جدول للطباعة"""
        try:
            # إطار الجدول
            table_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
            table_frame.pack(fill="x", padx=20, pady=(0, 20))

            # عنوان الجدول
            table_title = ctk.CTkLabel(
                table_frame,
                text=title,
                font=get_stable_font("medium", "bold"),
                text_color="#2c3e50"
            )
            table_title.pack(pady=(15, 10))

            # محتوى الجدول
            table_content = ctk.CTkFrame(table_frame, fg_color="#f8f9fa", corner_radius=8)
            table_content.pack(fill="x", padx=20, pady=(0, 15))

            # الحصول على البيانات من الجدول
            items = tree_widget.get_children()
            if not items:
                no_data_label = ctk.CTkLabel(
                    table_content,
                    text="لا توجد بيانات للعرض",
                    font=get_stable_font("normal"),
                    text_color="#6c757d"
                )
                no_data_label.pack(pady=20)
                return

            # عرض البيانات
            for i, item in enumerate(items[:10]):  # عرض أول 10 عناصر فقط للطباعة
                values = tree_widget.item(item)['values']

                row_frame = ctk.CTkFrame(table_content, fg_color="#ffffff" if i % 2 == 0 else "#e9ecef", corner_radius=5)
                row_frame.pack(fill="x", padx=5, pady=2)

                row_text = " | ".join(str(val) for val in values)
                row_label = ctk.CTkLabel(
                    row_frame,
                    text=row_text,
                    font=get_stable_font("small"),
                    text_color="#495057"
                )
                row_label.pack(pady=5, padx=10)

            # إذا كان هناك المزيد من البيانات
            if len(items) > 10:
                more_label = ctk.CTkLabel(
                    table_content,
                    text=f"... و {len(items) - 10} عنصر إضافي",
                    font=get_stable_font("small"),
                    text_color="#6c757d"
                )
                more_label.pack(pady=5)

        except Exception as e:
            print(f"خطأ في إنشاء جدول الطباعة: {e}")

    def export_report(self):
        """تصدير التقرير"""
        try:
            from tkinter import filedialog
            import csv

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ التقرير اليومي"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة معلومات التقرير
                    writer.writerow(['التقرير اليومي'])
                    writer.writerow(['التاريخ:', self.selected_date])
                    writer.writerow(['تاريخ التصدير:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                    writer.writerow([])

                    # الإحصائيات العامة
                    writer.writerow(['الإحصائيات العامة'])
                    writer.writerow(['إجمالي التصفيات', self.total_filters_card["value"].cget("text")])
                    writer.writerow(['إجمالي المبلغ', self.total_amount_card["value"].cget("text")])
                    writer.writerow(['متوسط التصفية', self.avg_filter_card["value"].cget("text")])
                    writer.writerow(['المستخدمين النشطين', self.active_users_card["value"].cget("text")])
                    writer.writerow([])

                    # التصفيات اليومية
                    writer.writerow(['التصفيات اليومية'])
                    writer.writerow(['الوقت', 'الكاشير', 'المبلغ', 'الحالة'])
                    for item in self.daily_filters_tree.get_children():
                        values = self.daily_filters_tree.item(item)['values']
                        writer.writerow(values)
                    writer.writerow([])

                    # نشاط المستخدمين
                    writer.writerow(['نشاط المستخدمين'])
                    writer.writerow(['المستخدم', 'العمليات', 'آخر نشاط'])
                    for item in self.user_activity_tree.get_children():
                        values = self.user_activity_tree.item(item)['values']
                        writer.writerow(values)

                messagebox.showinfo("نجح", "تم تصدير التقرير اليومي بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {e}")

    def execute_print(self, preview_window):
        """تنفيذ الطباعة الفعلية"""
        try:
            import win32print
            import win32ui
            from PIL import Image, ImageDraw, ImageFont
            import tempfile
            import os

            # إنشاء صورة للطباعة
            self.create_print_image()

        except ImportError:
            # إذا لم تكن مكتبات الطباعة متاحة، استخدم الطريقة البديلة
            self.print_using_browser(preview_window)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الطباعة: {e}")

    def print_using_browser(self, preview_window):
        """طباعة باستخدام المتصفح"""
        try:
            import tempfile
            import webbrowser

            # إنشاء ملف HTML للطباعة
            html_content = self.generate_html_report()

            # حفظ الملف المؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            webbrowser.open(f'file://{temp_file}')

            messagebox.showinfo("طباعة", "تم فتح التقرير في المتصفح. يمكنك الآن طباعته باستخدام Ctrl+P")

            # إغلاق نافذة المعاينة
            preview_window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الطباعة باستخدام المتصفح: {e}")

    def generate_html_report(self):
        """إنشاء تقرير HTML للطباعة"""
        try:
            html_template = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير اليومي - {self.selected_date}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #ffffff;
            color: #333;
            direction: rtl;
        }}
        .header {{
            text-align: center;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }}
        .info-section {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }}
        .stat-card {{
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        .table-section {{
            margin-bottom: 30px;
        }}
        .table-title {{
            background-color: #495057;
            color: white;
            padding: 10px;
            border-radius: 5px 5px 0 0;
            margin: 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: center;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}
        .footer {{
            text-align: center;
            background-color: #6c757d;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 30px;
        }}
        @media print {{
            body {{ margin: 0; }}
            .no-print {{ display: none; }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 التقرير اليومي</h1>
        <h2>نظام تصفية الكاشير المتقدم</h2>
    </div>

    <div class="info-section">
        <p><strong>📅 التاريخ:</strong> {self.selected_date}</p>
        <p><strong>🕐 وقت الطباعة:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>👤 المستخدم:</strong> admin</p>
        <p><strong>🏪 النظام:</strong> نظام تصفية الكاشير v3.5.0</p>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <h3>📝 إجمالي التصفيات</h3>
            <p style="font-size: 24px; font-weight: bold;">{self.total_filters_card["value"].cget("text")}</p>
        </div>
        <div class="stat-card">
            <h3>💰 إجمالي المبلغ</h3>
            <p style="font-size: 24px; font-weight: bold;">{self.total_amount_card["value"].cget("text")}</p>
        </div>
        <div class="stat-card">
            <h3>📊 متوسط التصفية</h3>
            <p style="font-size: 24px; font-weight: bold;">{self.avg_filter_card["value"].cget("text")}</p>
        </div>
        <div class="stat-card">
            <h3>👥 المستخدمين النشطين</h3>
            <p style="font-size: 24px; font-weight: bold;">{self.active_users_card["value"].cget("text")}</p>
        </div>
    </div>

    <div class="table-section">
        <h3 class="table-title">📝 التصفيات اليومية</h3>
        <table>
            <thead>
                <tr>
                    <th>🕐 الوقت</th>
                    <th>👤 الكاشير</th>
                    <th>💰 المبلغ</th>
                    <th>✅ الحالة</th>
                </tr>
            </thead>
            <tbody>
                {self.generate_table_rows(self.daily_filters_tree)}
            </tbody>
        </table>
    </div>

    <div class="table-section">
        <h3 class="table-title">👥 نشاط المستخدمين</h3>
        <table>
            <thead>
                <tr>
                    <th>👤 المستخدم</th>
                    <th>📝 العمليات</th>
                    <th>🕐 آخر نشاط</th>
                </tr>
            </thead>
            <tbody>
                {self.generate_table_rows(self.user_activity_tree)}
            </tbody>
        </table>
    </div>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير المتقدم</p>
        <p>© 2025 - جميع الحقوق محفوظة</p>
    </div>
</body>
</html>
            """

            return html_template

        except Exception as e:
            print(f"خطأ في إنشاء HTML: {e}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def generate_table_rows(self, tree_widget):
        """إنشاء صفوف الجدول لـ HTML"""
        try:
            rows_html = ""
            items = tree_widget.get_children()

            if not items:
                return "<tr><td colspan='100%'>لا توجد بيانات للعرض</td></tr>"

            for item in items:
                values = tree_widget.item(item)['values']
                row_html = "<tr>"
                for value in values:
                    row_html += f"<td>{value}</td>"
                row_html += "</tr>"
                rows_html += row_html

            return rows_html

        except Exception as e:
            print(f"خطأ في إنشاء صفوف الجدول: {e}")
            return "<tr><td colspan='100%'>خطأ في تحميل البيانات</td></tr>"

    def save_as_pdf(self, preview_window):
        """حفظ التقرير كملف PDF"""
        try:
            from tkinter import filedialog

            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير اليومي كـ PDF"
            )

            if filename:
                try:
                    # محاولة استخدام مكتبة reportlab
                    self.create_pdf_report(filename)
                    messagebox.showinfo("نجح", f"تم حفظ التقرير كـ PDF بنجاح:\n{filename}")
                except ImportError:
                    # إذا لم تكن مكتبة PDF متاحة، استخدم HTML
                    self.save_html_as_pdf(filename, preview_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ PDF: {e}")

    def create_pdf_report(self, filename):
        """إنشاء تقرير PDF باستخدام reportlab"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4)
            story = []

            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )

            # العنوان
            story.append(Paragraph("التقرير اليومي", title_style))
            story.append(Paragraph(f"التاريخ: {self.selected_date}", styles['Normal']))
            story.append(Paragraph(f"وقت الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
            story.append(Spacer(1, 20))

            # الإحصائيات
            stats_data = [
                ['الإحصائية', 'القيمة'],
                ['إجمالي التصفيات', self.total_filters_card["value"].cget("text")],
                ['إجمالي المبلغ', self.total_amount_card["value"].cget("text")],
                ['متوسط التصفية', self.avg_filter_card["value"].cget("text")],
                ['المستخدمين النشطين', self.active_users_card["value"].cget("text")]
            ]

            stats_table = Table(stats_data)
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(Paragraph("الإحصائيات العامة", styles['Heading2']))
            story.append(stats_table)
            story.append(Spacer(1, 20))

            # بناء المستند
            doc.build(story)

        except ImportError:
            raise ImportError("مكتبة reportlab غير مثبتة")
        except Exception as e:
            raise Exception(f"خطأ في إنشاء PDF: {e}")

    def save_html_as_pdf(self, filename, preview_window):
        """حفظ HTML كـ PDF (طريقة بديلة)"""
        try:
            import tempfile
            import webbrowser

            # إنشاء ملف HTML
            html_content = self.generate_html_report()

            # حفظ الملف المؤقت
            html_file = filename.replace('.pdf', '.html')
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح الملف في المتصفح
            webbrowser.open(f'file://{html_file}')

            messagebox.showinfo(
                "حفظ HTML",
                f"تم حفظ التقرير كـ HTML:\n{html_file}\n\nيمكنك طباعته كـ PDF من المتصفح باستخدام Ctrl+P"
            )

        except Exception as e:
            raise Exception(f"خطأ في حفظ HTML: {e}")
