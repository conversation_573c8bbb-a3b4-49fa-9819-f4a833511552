# نافذة إدارة الكاشير
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

class ManageCashierWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        super().__init__(master)
        self.title("إدارة الكاشير")
        self.geometry("800x600")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        # جعل النافذة تظهر في المقدمة
        self.lift()
        self.attributes('-topmost', True)
        self.after(100, lambda: self.attributes('-topmost', False))
        self.focus_force()

        self.create_widgets()
        self.load_cashiers()

    def create_widgets(self):
        # العنوان
        title_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=20)
        title_frame.pack(pady=20, padx=20, fill="x")
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="👤 إدارة الكاشير",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=15)

        # إطار إضافة كاشير جديد
        add_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        add_frame.pack(pady=10, padx=20, fill="x")
        
        add_title = ctk.CTkLabel(
            add_frame,
            text="إضافة كاشير جديد",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        add_title.pack(pady=10)

        # حقول الإدخال
        input_frame = ctk.CTkFrame(add_frame, fg_color="#f2f3f7", corner_radius=10)
        input_frame.pack(pady=10, padx=20, fill="x")

        # اسم الكاشير
        name_label = ctk.CTkLabel(input_frame, text="اسم الكاشير:", font=("Arial", 14))
        name_label.grid(row=0, column=0, padx=10, pady=10, sticky="e")
        
        self.name_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل اسم الكاشير",
            width=200,
            font=("Arial", 12)
        )
        self.name_entry.grid(row=0, column=1, padx=10, pady=10)

        # رقم الكاشير
        number_label = ctk.CTkLabel(input_frame, text="رقم الكاشير:", font=("Arial", 14))
        number_label.grid(row=1, column=0, padx=10, pady=10, sticky="e")
        
        self.number_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="أدخل رقم الكاشير",
            width=200,
            font=("Arial", 12)
        )
        self.number_entry.grid(row=1, column=1, padx=10, pady=10)

        # أزرار العمليات
        btn_frame = ctk.CTkFrame(add_frame, fg_color="#e0e5ec", corner_radius=0)
        btn_frame.pack(pady=10)

        add_btn = ctk.CTkButton(
            btn_frame,
            text="➕ إضافة",
            command=self.add_cashier,
            fg_color="#4CAF50",
            hover_color="#45a049",
            width=100,
            font=("Arial", 12, "bold")
        )
        add_btn.pack(side="left", padx=5)

        update_btn = ctk.CTkButton(
            btn_frame,
            text="🔄 تحديث",
            command=self.update_cashier,
            fg_color="#2196F3",
            hover_color="#1976D2",
            width=100,
            font=("Arial", 12, "bold")
        )
        update_btn.pack(side="left", padx=5)

        delete_btn = ctk.CTkButton(
            btn_frame,
            text="🗑️ حذف",
            command=self.delete_cashier,
            fg_color="#f44336",
            hover_color="#d32f2f",
            width=100,
            font=("Arial", 12, "bold")
        )
        delete_btn.pack(side="left", padx=5)

        # جدول الكاشيرين
        table_frame = ctk.CTkFrame(self, fg_color="#e0e5ec", corner_radius=15)
        table_frame.pack(pady=10, padx=20, fill="both", expand=True)

        table_title = ctk.CTkLabel(
            table_frame,
            text="قائمة الكاشيرين",
            font=("Arial", 16, "bold"),
            text_color="#34495e"
        )
        table_title.pack(pady=10)

        # إنشاء الجدول
        columns = ("ID", "الاسم", "الرقم", "آخر تصفية")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=12)

        # تعيين عناوين الأعمدة
        column_widths = {"ID": 80, "الاسم": 150, "الرقم": 120, "آخر تصفية": 120}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 150), anchor="center")

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # ربط النقر المزدوج
        self.tree.bind("<Double-1>", self.on_select)

    def load_cashiers(self):
        """تحميل قائمة الكاشيرين من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # استعلام محسن للحصول على آخر رقم تسلسلي لكل كاشير
            c.execute("""
                SELECT c.id, c.name, c.number,
                       COALESCE(MAX(f.sequence_number), 'لا توجد') as last_sequence
                FROM cashiers c
                LEFT JOIN filters f ON c.id = f.cashier_id
                GROUP BY c.id, c.name, c.number
                ORDER BY c.name
            """)

            for row in c.fetchall():
                # تنسيق آخر تصفية
                last_sequence = row[3] if row[3] != 'لا توجد' else 'لا توجد'
                display_row = (row[0], row[1], row[2], last_sequence)
                self.tree.insert("", "end", values=display_row)

            conn.close()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {e}")

    def add_cashier(self):
        """إضافة كاشير جديد"""
        name = self.name_entry.get().strip()
        number = self.number_entry.get().strip()

        if not name or not number:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("INSERT INTO cashiers (name, number) VALUES (?, ?)", (name, number))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة الكاشير بنجاح!")
            self.clear_entries()
            self.load_cashiers()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "رقم الكاشير موجود مسبقاً!")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def update_cashier(self):
        """تحديث بيانات الكاشير المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار كاشير للتحديث")
            return

        name = self.name_entry.get().strip()
        number = self.number_entry.get().strip()

        if not name or not number:
            messagebox.showwarning("تحذير", "يرجى ملء جميع الحقول")
            return

        cashier_id = self.tree.item(selected[0])["values"][0]

        try:
            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()
            c.execute("UPDATE cashiers SET name=?, number=? WHERE id=?", (name, number, cashier_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تحديث بيانات الكاشير بنجاح!")
            self.clear_entries()
            self.load_cashiers()

        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "رقم الكاشير موجود مسبقاً!")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def delete_cashier(self):
        """حذف الكاشير المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار كاشير للحذف")
            return

        cashier_name = self.tree.item(selected[0])["values"][1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الكاشير '{cashier_name}'؟"):
            cashier_id = self.tree.item(selected[0])["values"][0]

            try:
                conn = sqlite3.connect(DB_PATH)
                c = conn.cursor()
                c.execute("DELETE FROM cashiers WHERE id=?", (cashier_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف الكاشير بنجاح!")
                self.clear_entries()
                self.load_cashiers()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {e}")

    def on_select(self, event):
        """عند النقر المزدوج على عنصر في الجدول"""
        selected = self.tree.selection()
        if selected:
            values = self.tree.item(selected[0])["values"]
            self.name_entry.delete(0, "end")
            self.name_entry.insert(0, values[1])
            self.number_entry.delete(0, "end")
            self.number_entry.insert(0, values[2])

    def clear_entries(self):
        """مسح حقول الإدخال"""
        self.name_entry.delete(0, "end")
        self.number_entry.delete(0, "end")
