# واجهة التقارير المخصصة المتطورة
import customtkinter as ctk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import json
from datetime import datetime, timedelta
import calendar
from collections import defaultdict
import webbrowser
import os
from pathlib import Path

# استيراد matplotlib اختياري
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import matplotlib.dates as mdates
    import numpy as np
    import seaborn as sns
    
    # تعيين الخط العربي لـ matplotlib
    plt.rcParams['font.family'] = ['Tahoma', 'Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10
    sns.set_style("whitegrid")
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("تحذير: matplotlib أو seaborn غير مثبت. الرسوم البيانية المتقدمة غير متاحة.")

# استيراد pandas اختياري
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("تحذير: pandas غير مثبت. التحليلات المتقدمة غير متاحة.")

# استخدام مسار نسبي مع معالجة الأخطاء
try:
    BASE_DIR = Path(__file__).parent.parent
    DB_PATH = BASE_DIR / "db" / "cashier_filter.db"

    # التحقق من وجود قاعدة البيانات
    if not DB_PATH.exists():
        # محاولة المسار المطلق كبديل
        DB_PATH = Path("c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db")
        if not DB_PATH.exists():
            print(f"تحذير: لم يتم العثور على قاعدة البيانات في: {DB_PATH}")

except Exception as e:
    print(f"خطأ في تحديد مسار قاعدة البيانات: {e}")
    # استخدام المسار المطلق كبديل
    DB_PATH = Path("c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db")

class CustomReportsWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        try:
            super().__init__(master)
            self.title("🎯 التقارير المخصصة المتطورة")
            self.geometry("1600x1000")
            self.configure(bg="#f0f2f5")
            self.resizable(True, True)

            # متغيرات البيانات
            self.filters_data = []
            self.cashiers_data = []
            self.current_report_data = {}

            # إنشاء الواجهة
            self.create_widgets()

            # تحميل البيانات مع معالجة الأخطاء
            try:
                self.load_initial_data()
            except Exception as e:
                print(f"تحذير: فشل في تحميل البيانات الأولية: {e}")
                messagebox.showwarning("تحذير",
                    f"فشل في تحميل البيانات من قاعدة البيانات.\n"
                    f"ستعمل الواجهة بالبيانات الافتراضية.\n\n"
                    f"تفاصيل الخطأ: {e}")

        except Exception as e:
            print(f"خطأ في إنشاء نافذة التقارير المخصصة: {e}")
            messagebox.showerror("خطأ",
                f"فشل في فتح نافذة التقارير المخصصة:\n{e}\n\n"
                f"تأكد من تثبيت المكتبات المطلوبة:\n"
                f"pip install matplotlib seaborn pandas")

    def create_widgets(self):
        """إنشاء عناصر الواجهة المتطورة"""
        
        # الشريط العلوي مع العنوان والأدوات
        self.create_header()
        
        # شريط الأدوات والفلاتر
        self.create_toolbar()
        
        # المحتوى الرئيسي مع التبويبات
        self.create_main_content()
        
        # شريط الحالة
        self.create_status_bar()

    def create_header(self):
        """إنشاء الشريط العلوي"""
        header_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=0, height=80)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎯 التقارير المخصصة المتطورة",
            font=("Arial", 28, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(side="left", padx=30, pady=20)
        
        # معلومات النظام
        info_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        info_frame.pack(side="right", padx=30, pady=20)
        
        date_label = ctk.CTkLabel(
            info_frame,
            text=f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=("Arial", 12),
            text_color="#bdc3c7"
        )
        date_label.pack(anchor="e")
        
        version_label = ctk.CTkLabel(
            info_frame,
            text="الإصدار 3.0.0 - نظام تصفية الكاشير",
            font=("Arial", 10),
            text_color="#95a5a6"
        )
        version_label.pack(anchor="e")

    def create_toolbar(self):
        """إنشاء شريط الأدوات والفلاتر"""
        toolbar_frame = ctk.CTkFrame(self, fg_color="#34495e", corner_radius=0, height=120)
        toolbar_frame.pack(fill="x", padx=0, pady=0)
        toolbar_frame.pack_propagate(False)
        
        # الصف الأول - الفلاتر الأساسية
        filters_row1 = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        filters_row1.pack(fill="x", padx=20, pady=10)
        
        # فلتر التاريخ
        date_frame = ctk.CTkFrame(filters_row1, fg_color="#2c3e50", corner_radius=8)
        date_frame.pack(side="left", padx=5)
        
        ctk.CTkLabel(date_frame, text="📅 الفترة:", font=("Arial", 12, "bold"), 
                    text_color="#ffffff").pack(side="left", padx=10, pady=8)
        
        self.start_date_entry = ctk.CTkEntry(date_frame, width=120, placeholder_text="من تاريخ")
        self.start_date_entry.pack(side="left", padx=5, pady=8)
        
        ctk.CTkLabel(date_frame, text="إلى", font=("Arial", 10), 
                    text_color="#bdc3c7").pack(side="left", padx=5)
        
        self.end_date_entry = ctk.CTkEntry(date_frame, width=120, placeholder_text="إلى تاريخ")
        self.end_date_entry.pack(side="left", padx=5, pady=8)
        
        # فلتر نوع التقرير
        report_frame = ctk.CTkFrame(filters_row1, fg_color="#2c3e50", corner_radius=8)
        report_frame.pack(side="left", padx=5)
        
        ctk.CTkLabel(report_frame, text="📊 نوع التقرير:", font=("Arial", 12, "bold"), 
                    text_color="#ffffff").pack(side="left", padx=10, pady=8)
        
        self.report_type_combo = ctk.CTkComboBox(
            report_frame,
            values=[
                "تقرير شامل متقدم",
                "تحليل أداء الكاشيرين",
                "تحليل المقبوضات المفصل",
                "تقرير الاتجاهات والتوقعات",
                "مقارنة الفترات الزمنية",
                "تحليل الذكاء الاصطناعي",
                "تقرير الشذوذ والاستثناءات",
                "تقرير الإنتاجية"
            ],
            width=200
        )
        self.report_type_combo.set("تقرير شامل متقدم")
        self.report_type_combo.pack(side="left", padx=5, pady=8)
        
        # فلتر الكاشير
        cashier_frame = ctk.CTkFrame(filters_row1, fg_color="#2c3e50", corner_radius=8)
        cashier_frame.pack(side="left", padx=5)
        
        ctk.CTkLabel(cashier_frame, text="👤 الكاشير:", font=("Arial", 12, "bold"), 
                    text_color="#ffffff").pack(side="left", padx=10, pady=8)
        
        self.cashier_combo = ctk.CTkComboBox(
            cashier_frame,
            values=["جميع الكاشيرين"],
            width=150
        )
        self.cashier_combo.set("جميع الكاشيرين")
        self.cashier_combo.pack(side="left", padx=5, pady=8)
        
        # الصف الثاني - أزرار الإجراءات
        actions_row = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        actions_row.pack(fill="x", padx=20, pady=5)
        
        # أزرار الإجراءات الرئيسية
        generate_btn = ctk.CTkButton(
            actions_row,
            text="🚀 إنشاء التقرير",
            command=self.generate_custom_report,
            fg_color="#27ae60",
            hover_color="#229954",
            width=140,
            height=35,
            font=("Arial", 12, "bold")
        )
        generate_btn.pack(side="left", padx=5)
        
        preview_btn = ctk.CTkButton(
            actions_row,
            text="👁️ معاينة",
            command=self.preview_report,
            fg_color="#3498db",
            hover_color="#2980b9",
            width=120,
            height=35
        )
        preview_btn.pack(side="left", padx=5)
        
        export_pdf_btn = ctk.CTkButton(
            actions_row,
            text="📄 PDF",
            command=self.export_pdf,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=100,
            height=35
        )
        export_pdf_btn.pack(side="left", padx=5)
        
        export_excel_btn = ctk.CTkButton(
            actions_row,
            text="📊 Excel",
            command=self.export_excel,
            fg_color="#f39c12",
            hover_color="#e67e22",
            width=100,
            height=35
        )
        export_excel_btn.pack(side="left", padx=5)
        
        print_btn = ctk.CTkButton(
            actions_row,
            text="🖨️ طباعة",
            command=self.print_report,
            fg_color="#9b59b6",
            hover_color="#8e44ad",
            width=100,
            height=35
        )
        print_btn.pack(side="left", padx=5)
        
        # أزرار متقدمة
        ai_analysis_btn = ctk.CTkButton(
            actions_row,
            text="🤖 تحليل ذكي",
            command=self.ai_analysis,
            fg_color="#16a085",
            hover_color="#138d75",
            width=120,
            height=35
        )
        ai_analysis_btn.pack(side="left", padx=5)
        
        schedule_btn = ctk.CTkButton(
            actions_row,
            text="⏰ جدولة",
            command=self.schedule_report,
            fg_color="#8e44ad",
            hover_color="#7d3c98",
            width=100,
            height=35
        )
        schedule_btn.pack(side="left", padx=5)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي مع التبويبات"""
        
        # دفتر التبويبات المتقدم
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=10)
        
        # تبويب التقرير الرئيسي
        self.main_report_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.main_report_frame, text="📊 التقرير الرئيسي")
        
        # تبويب الرسوم البيانية المتقدمة
        self.advanced_charts_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.advanced_charts_frame, text="📈 الرسوم البيانية المتقدمة")
        
        # تبويب التحليلات الذكية
        self.ai_analysis_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.ai_analysis_frame, text="🤖 التحليلات الذكية")
        
        # تبويب المقارنات المتقدمة
        self.comparisons_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.comparisons_frame, text="⚖️ المقارنات المتقدمة")
        
        # تبويب التوقعات والاتجاهات
        self.predictions_frame = ctk.CTkFrame(self.notebook, fg_color="#ffffff")
        self.notebook.add(self.predictions_frame, text="🔮 التوقعات والاتجاهات")
        
        # إنشاء محتوى التبويبات
        self.create_main_report_tab()
        self.create_advanced_charts_tab()
        self.create_ai_analysis_tab()
        self.create_comparisons_tab()
        self.create_predictions_tab()

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=0, height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="🟢 جاهز - اختر نوع التقرير وانقر على 'إنشاء التقرير'",
            font=("Arial", 10),
            text_color="#ffffff"
        )
        self.status_label.pack(side="left", padx=20, pady=5)
        
        # معلومات البيانات
        self.data_info_label = ctk.CTkLabel(
            status_frame,
            text="📊 البيانات: جاري التحميل...",
            font=("Arial", 10),
            text_color="#bdc3c7"
        )
        self.data_info_label.pack(side="right", padx=20, pady=5)

    def create_main_report_tab(self):
        """إنشاء تبويب التقرير الرئيسي"""
        
        # إطار التمرير
        scrollable_frame = ctk.CTkScrollableFrame(self.main_report_frame, fg_color="#f8f9fa")
        scrollable_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان التقرير
        title_frame = ctk.CTkFrame(scrollable_frame, fg_color="#ffffff", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 التقرير الشامل المتطور",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)
        
        # ملخص سريع
        self.create_quick_summary(scrollable_frame)
        
        # جدول البيانات المفصل
        self.create_detailed_table(scrollable_frame)

    def create_quick_summary(self, parent):
        """إنشاء ملخص سريع للبيانات"""
        
        summary_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        summary_frame.pack(fill="x", padx=10, pady=10)
        
        summary_title = ctk.CTkLabel(
            summary_frame,
            text="⚡ الملخص السريع",
            font=("Arial", 18, "bold"),
            text_color="#34495e"
        )
        summary_title.pack(pady=15)
        
        # بطاقات الإحصائيات
        stats_container = ctk.CTkFrame(summary_frame, fg_color="#f8f9fa", corner_radius=8)
        stats_container.pack(fill="x", padx=20, pady=10)
        
        # إنشاء بطاقات الإحصائيات
        self.create_stat_cards(stats_container)

    def create_stat_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        
        # إطار البطاقات
        cards_frame = ctk.CTkFrame(parent, fg_color="transparent")
        cards_frame.pack(fill="x", padx=10, pady=10)
        
        # بطاقة إجمالي التصفيات
        total_card = self.create_stat_card(
            cards_frame, 
            "📊 إجمالي التصفيات", 
            "0", 
            "#3498db",
            "منذ بداية النظام"
        )
        total_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        # بطاقة إجمالي المبالغ
        amount_card = self.create_stat_card(
            cards_frame, 
            "💰 إجمالي المبالغ", 
            "0.00 ريال", 
            "#27ae60",
            "جميع المقبوضات"
        )
        amount_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        # بطاقة متوسط التصفية
        avg_card = self.create_stat_card(
            cards_frame, 
            "📈 متوسط التصفية", 
            "0.00 ريال", 
            "#f39c12",
            "متوسط المبلغ"
        )
        avg_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        # بطاقة أفضل كاشير
        best_card = self.create_stat_card(
            cards_frame, 
            "🏆 أفضل كاشير", 
            "غير محدد", 
            "#e74c3c",
            "أعلى أداء"
        )
        best_card.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    def create_stat_card(self, parent, title, value, color, subtitle):
        """إنشاء بطاقة إحصائية واحدة"""
        
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)
        
        # العنوان
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 20, "bold"),
            text_color="#ffffff"
        )
        value_label.pack(pady=5)
        
        # العنوان الفرعي
        subtitle_label = ctk.CTkLabel(
            card,
            text=subtitle,
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        subtitle_label.pack(pady=(5, 15))
        
        return card

    def create_detailed_table(self, parent):
        """إنشاء جدول البيانات المفصل"""

        table_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)

        table_title = ctk.CTkLabel(
            table_frame,
            text="📋 البيانات التفصيلية",
            font=("Arial", 18, "bold"),
            text_color="#34495e"
        )
        table_title.pack(pady=15)

        # إنشاء الجدول
        columns = ("التاريخ", "الكاشير", "المقبوضات البنكية", "المقبوضات النقدية",
                  "المبيعات الآجلة", "الإجمالي", "الحالة")

        self.detailed_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.detailed_tree.heading(col, text=col)
            self.detailed_tree.column(col, width=120, anchor="center")

        # شريط التمرير
        scrollbar_detailed = ttk.Scrollbar(table_frame, orient="vertical", command=self.detailed_tree.yview)
        self.detailed_tree.configure(yscrollcommand=scrollbar_detailed.set)

        self.detailed_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_detailed.pack(side="right", fill="y", pady=10)

    def create_advanced_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية المتقدمة"""

        charts_container = ctk.CTkScrollableFrame(self.advanced_charts_frame, fg_color="#f8f9fa")
        charts_container.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        charts_title = ctk.CTkLabel(
            charts_container,
            text="📈 الرسوم البيانية المتقدمة",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        charts_title.pack(pady=20)

        if MATPLOTLIB_AVAILABLE:
            # رسم بياني للاتجاهات
            self.create_trend_chart(charts_container)

            # رسم بياني دائري للتوزيع
            self.create_distribution_chart(charts_container)

            # رسم بياني للمقارنات
            self.create_comparison_chart(charts_container)
        else:
            no_charts_label = ctk.CTkLabel(
                charts_container,
                text="📊 الرسوم البيانية غير متاحة\n(يتطلب تثبيت matplotlib و seaborn)",
                font=("Arial", 16),
                text_color="#7f8c8d"
            )
            no_charts_label.pack(expand=True, pady=100)

    def create_ai_analysis_tab(self):
        """إنشاء تبويب التحليلات الذكية"""

        ai_container = ctk.CTkScrollableFrame(self.ai_analysis_frame, fg_color="#f8f9fa")
        ai_container.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        ai_title = ctk.CTkLabel(
            ai_container,
            text="🤖 التحليلات الذكية والتوقعات",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        ai_title.pack(pady=20)

        # قسم التحليل الذكي
        self.create_ai_insights(ai_container)

        # قسم التوقعات
        self.create_predictions_section(ai_container)

        # قسم التوصيات
        self.create_recommendations_section(ai_container)

    def create_comparisons_tab(self):
        """إنشاء تبويب المقارنات المتقدمة"""

        comp_container = ctk.CTkScrollableFrame(self.comparisons_frame, fg_color="#f8f9fa")
        comp_container.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        comp_title = ctk.CTkLabel(
            comp_container,
            text="⚖️ المقارنات المتقدمة",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        comp_title.pack(pady=20)

        # مقارنة الفترات الزمنية
        self.create_period_comparison(comp_container)

        # مقارنة الكاشيرين
        self.create_cashier_comparison(comp_container)

    def create_predictions_tab(self):
        """إنشاء تبويب التوقعات والاتجاهات"""

        pred_container = ctk.CTkScrollableFrame(self.predictions_frame, fg_color="#f8f9fa")
        pred_container.pack(fill="both", expand=True, padx=10, pady=10)

        # عنوان التبويب
        pred_title = ctk.CTkLabel(
            pred_container,
            text="🔮 التوقعات والاتجاهات المستقبلية",
            font=("Arial", 24, "bold"),
            text_color="#2c3e50"
        )
        pred_title.pack(pady=20)

        # قسم التوقعات
        self.create_future_predictions(pred_container)

    def load_initial_data(self):
        """تحميل البيانات الأولية مع معالجة محسنة للأخطاء"""
        try:
            # التحقق من وجود قاعدة البيانات
            if not DB_PATH.exists():
                print(f"تحذير: قاعدة البيانات غير موجودة في: {DB_PATH}")
                self.data_info_label.configure(text="⚠️ قاعدة البيانات غير موجودة")
                self.load_default_data()
                return

            conn = sqlite3.connect(str(DB_PATH))
            c = conn.cursor()

            # تحميل بيانات التصفيات مع معالجة الأخطاء
            try:
                # استخدام LEFT JOIN للحصول على اسم الكاشير
                c.execute("""
                    SELECT f.date, f.data, COALESCE(c.name, 'غير محدد') as cashier_name
                    FROM filters f
                    LEFT JOIN cashiers c ON f.cashier_id = c.id
                    ORDER BY f.date DESC
                    LIMIT 1000
                """)
                self.filters_data = c.fetchall() or []
            except sqlite3.OperationalError as e:
                print(f"خطأ في جدول filters: {e}")
                # محاولة بديلة بدون JOIN
                try:
                    c.execute("SELECT date, data, admin_name FROM filters ORDER BY date DESC LIMIT 1000")
                    self.filters_data = c.fetchall() or []
                except:
                    self.filters_data = []

            # تحميل بيانات الكاشيرين مع معالجة الأخطاء
            try:
                c.execute("SELECT name, number FROM cashiers")
                cashiers = c.fetchall() or []
            except sqlite3.OperationalError as e:
                print(f"خطأ في جدول cashiers: {e}")
                cashiers = []

            conn.close()

            # تحديث قائمة الكاشيرين
            cashier_names = ["جميع الكاشيرين"] + [name for name, _ in cashiers if name]
            if hasattr(self, 'cashier_combo'):
                self.cashier_combo.configure(values=cashier_names)

            # تحديث شريط الحالة
            if hasattr(self, 'data_info_label'):
                self.data_info_label.configure(
                    text=f"📊 البيانات: {len(self.filters_data)} تصفية، {len(cashiers)} كاشير"
                )

            # تحديث البطاقات الإحصائية
            self.update_stat_cards()

            print(f"تم تحميل {len(self.filters_data)} تصفية و {len(cashiers)} كاشير بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            if hasattr(self, 'data_info_label'):
                self.data_info_label.configure(text="❌ خطأ في تحميل البيانات")
            self.load_default_data()

    def load_default_data(self):
        """تحميل بيانات افتراضية في حالة فشل تحميل قاعدة البيانات"""
        try:
            self.filters_data = []

            # بيانات افتراضية للاختبار
            default_cashiers = ["جميع الكاشيرين", "كاشير 1", "كاشير 2", "كاشير 3"]
            if hasattr(self, 'cashier_combo'):
                self.cashier_combo.configure(values=default_cashiers)

            if hasattr(self, 'data_info_label'):
                self.data_info_label.configure(text="📊 البيانات: 0 تصفية (وضع تجريبي)")

            print("تم تحميل البيانات الافتراضية")

        except Exception as e:
            print(f"خطأ في تحميل البيانات الافتراضية: {e}")

    def update_stat_cards(self):
        """تحديث بطاقات الإحصائيات مع معالجة محسنة للأخطاء"""
        try:
            total_filters = len(self.filters_data) if self.filters_data else 0
            total_amount = 0
            cashier_totals = defaultdict(float)

            # معالجة البيانات إذا كانت متوفرة
            if self.filters_data:
                for row in self.filters_data:
                    try:
                        # التحقق من صحة البيانات
                        if len(row) >= 2:
                            date_str = row[0] if row[0] else ""
                            data_str = row[1] if row[1] else "{}"
                            cashier_name = row[2] if len(row) > 2 and row[2] else "غير محدد"

                            # تحليل JSON
                            data = json.loads(data_str) if data_str else {}
                            totals = data.get('totals', {})

                            # حساب المبلغ الإجمالي
                            amount = (
                                float(totals.get('bank', 0)) +
                                float(totals.get('cash', 0)) +
                                float(totals.get('credit', 0)) +
                                float(totals.get('return', 0)) -
                                float(totals.get('client', 0))
                            )

                            total_amount += amount
                            cashier_totals[cashier_name] += amount

                    except (json.JSONDecodeError, ValueError, TypeError) as e:
                        print(f"خطأ في معالجة صف البيانات: {e}")
                        continue
                    except Exception as e:
                        print(f"خطأ غير متوقع في معالجة البيانات: {e}")
                        continue

            # حساب الإحصائيات
            avg_amount = total_amount / total_filters if total_filters > 0 else 0
            best_cashier = "غير محدد"

            if cashier_totals:
                try:
                    best_cashier = max(cashier_totals.items(), key=lambda x: x[1])[0]
                except Exception:
                    best_cashier = "غير محدد"

            # طباعة الإحصائيات للتحقق
            print(f"إحصائيات محدثة: {total_filters} تصفية، {total_amount:.2f} ريال، أفضل كاشير: {best_cashier}")

            # تحديث البطاقات (سيتم تنفيذها لاحقاً عند إنشاء البطاقات الفعلية)

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
            # في حالة الخطأ، استخدم قيم افتراضية
            total_filters = 0
            total_amount = 0.0
            best_cashier = "غير محدد"

    def generate_custom_report(self):
        """إنشاء التقرير المخصص"""
        try:
            self.status_label.configure(text="🔄 جاري إنشاء التقرير...")

            report_type = self.report_type_combo.get()
            start_date = self.start_date_entry.get()
            end_date = self.end_date_entry.get()
            selected_cashier = self.cashier_combo.get()

            # تصفية البيانات حسب المعايير
            filtered_data = self.filter_data(start_date, end_date, selected_cashier)

            # إنشاء التقرير حسب النوع
            if report_type == "تقرير شامل متقدم":
                self.generate_comprehensive_report(filtered_data)
            elif report_type == "تحليل أداء الكاشيرين":
                self.generate_cashier_performance_report(filtered_data)
            elif report_type == "تحليل المقبوضات المفصل":
                self.generate_receipts_analysis_report(filtered_data)
            elif report_type == "تقرير الاتجاهات والتوقعات":
                self.generate_trends_report(filtered_data)
            elif report_type == "مقارنة الفترات الزمنية":
                self.generate_period_comparison_report(filtered_data)
            elif report_type == "تحليل الذكاء الاصطناعي":
                self.generate_ai_analysis_report(filtered_data)
            elif report_type == "تقرير الشذوذ والاستثناءات":
                self.generate_anomaly_report(filtered_data)
            elif report_type == "تقرير الإنتاجية":
                self.generate_productivity_report(filtered_data)

            self.status_label.configure(text="✅ تم إنشاء التقرير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {e}")
            self.status_label.configure(text="❌ فشل في إنشاء التقرير")

    def filter_data(self, start_date, end_date, cashier):
        """تصفية البيانات حسب المعايير المحددة"""
        filtered = []

        for date_str, data_str, cashier_name in self.filters_data:
            # فلتر التاريخ
            if start_date and date_str < start_date:
                continue
            if end_date and date_str > end_date:
                continue

            # فلتر الكاشير
            if cashier != "جميع الكاشيرين" and cashier_name != cashier:
                continue

            filtered.append((date_str, data_str, cashier_name))

        return filtered

    def generate_comprehensive_report(self, data):
        """إنشاء تقرير شامل متقدم"""
        # مسح الجدول الحالي
        for item in self.detailed_tree.get_children():
            self.detailed_tree.delete(item)

        # ملء الجدول بالبيانات المفلترة
        for date_str, data_str, cashier_name in data:
            try:
                data_obj = json.loads(data_str)
                totals = data_obj.get('totals', {})

                bank_total = totals.get('bank', 0)
                cash_total = totals.get('cash', 0)
                credit_total = totals.get('credit', 0)
                total_amount = bank_total + cash_total + credit_total

                status = "✅ مكتمل" if total_amount > 0 else "⚠️ فارغ"

                self.detailed_tree.insert("", "end", values=(
                    date_str,
                    cashier_name or "غير محدد",
                    f"{bank_total:.2f}",
                    f"{cash_total:.2f}",
                    f"{credit_total:.2f}",
                    f"{total_amount:.2f}",
                    status
                ))

            except Exception:
                continue

    def generate_cashier_performance_report(self, data):
        """إنشاء تقرير أداء الكاشيرين"""
        messagebox.showinfo("تم", "تم إنشاء تقرير أداء الكاشيرين!")

    def generate_receipts_analysis_report(self, data):
        """إنشاء تقرير تحليل المقبوضات المفصل"""
        messagebox.showinfo("تم", "تم إنشاء تقرير تحليل المقبوضات!")

    def generate_trends_report(self, data):
        """إنشاء تقرير الاتجاهات والتوقعات"""
        messagebox.showinfo("تم", "تم إنشاء تقرير الاتجاهات!")

    def generate_period_comparison_report(self, data):
        """إنشاء تقرير مقارنة الفترات الزمنية"""
        messagebox.showinfo("تم", "تم إنشاء تقرير مقارنة الفترات!")

    def generate_ai_analysis_report(self, data):
        """إنشاء تقرير التحليل بالذكاء الاصطناعي"""
        messagebox.showinfo("تم", "تم إنشاء تقرير التحليل الذكي!")

    def generate_anomaly_report(self, data):
        """إنشاء تقرير الشذوذ والاستثناءات"""
        messagebox.showinfo("تم", "تم إنشاء تقرير الشذوذ!")

    def generate_productivity_report(self, data):
        """إنشاء تقرير الإنتاجية"""
        messagebox.showinfo("تم", "تم إنشاء تقرير الإنتاجية!")

    def preview_report(self):
        """معاينة التقرير في نافذة منفصلة"""
        try:
            # إنشاء نافذة المعاينة
            preview_window = ctk.CTkToplevel(self)
            preview_window.title("👁️ معاينة التقرير")
            preview_window.geometry("1000x700")
            preview_window.configure(bg="#f8f9fa")

            # جعل النافذة في المقدمة
            preview_window.transient(self)
            preview_window.grab_set()
            preview_window.focus_set()

            # الشريط العلوي
            header_frame = ctk.CTkFrame(preview_window, fg_color="#3498db", corner_radius=0, height=60)
            header_frame.pack(fill="x", padx=0, pady=0)
            header_frame.pack_propagate(False)

            title_label = ctk.CTkLabel(
                header_frame,
                text="👁️ معاينة التقرير المخصص",
                font=("Arial", 20, "bold"),
                text_color="#ffffff"
            )
            title_label.pack(pady=15)

            # محتوى المعاينة
            content_frame = ctk.CTkScrollableFrame(preview_window, fg_color="#ffffff")
            content_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # إنشاء معاينة سريعة للبيانات
            self.create_preview_content(content_frame)

            # أزرار الإجراءات
            buttons_frame = ctk.CTkFrame(preview_window, fg_color="#f8f9fa", corner_radius=0, height=60)
            buttons_frame.pack(fill="x", side="bottom")
            buttons_frame.pack_propagate(False)

            close_btn = ctk.CTkButton(
                buttons_frame,
                text="❌ إغلاق",
                command=preview_window.destroy,
                fg_color="#e74c3c",
                width=100,
                height=35
            )
            close_btn.pack(side="right", padx=20, pady=12)

            print_btn = ctk.CTkButton(
                buttons_frame,
                text="🖨️ طباعة",
                command=lambda: self.print_from_preview(preview_window),
                fg_color="#27ae60",
                width=100,
                height=35
            )
            print_btn.pack(side="right", padx=5, pady=12)

        except Exception as e:
            print(f"خطأ في فتح معاينة التقرير: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح معاينة التقرير:\n{e}")

    def create_preview_content(self, parent):
        """إنشاء محتوى المعاينة"""

        # ملخص سريع
        summary_frame = ctk.CTkFrame(parent, fg_color="#e8f4fd", corner_radius=10)
        summary_frame.pack(fill="x", padx=10, pady=10)

        summary_title = ctk.CTkLabel(
            summary_frame,
            text="📊 ملخص التقرير",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        summary_title.pack(pady=15)

        # إحصائيات سريعة
        if self.filters_data:
            total_amount = 0
            cashier_count = set()

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})
                    amount = (
                        float(totals.get('bank', 0)) +
                        float(totals.get('cash', 0)) +
                        float(totals.get('credit', 0))
                    )
                    total_amount += amount
                    if cashier_name:
                        cashier_count.add(cashier_name)
                except:
                    continue

            stats_text = f"""
📈 إجمالي التصفيات: {len(self.filters_data)}
💰 إجمالي المبيعات: {total_amount:.2f} ريال
👥 عدد الكاشيرين: {len(cashier_count)}
📅 نوع التقرير: {self.report_type_combo.get()}
🎯 الكاشير المحدد: {self.cashier_combo.get()}
            """
        else:
            stats_text = "📊 لا توجد بيانات للعرض"

        stats_label = ctk.CTkLabel(
            summary_frame,
            text=stats_text,
            font=("Arial", 12),
            text_color="#34495e",
            justify="right"
        )
        stats_label.pack(pady=10)

        # عينة من البيانات
        if self.filters_data:
            sample_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=10)
            sample_frame.pack(fill="x", padx=10, pady=10)

            sample_title = ctk.CTkLabel(
                sample_frame,
                text="📋 عينة من البيانات",
                font=("Arial", 14, "bold"),
                text_color="#2c3e50"
            )
            sample_title.pack(pady=10)

            # عرض أول 5 تصفيات
            for i, (date_str, data_str, cashier_name) in enumerate(self.filters_data[:5]):
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})
                    amount = (
                        float(totals.get('bank', 0)) +
                        float(totals.get('cash', 0)) +
                        float(totals.get('credit', 0))
                    )

                    item_text = f"{i+1}. {date_str} | {cashier_name or 'غير محدد'} | {amount:.2f} ريال"

                    item_label = ctk.CTkLabel(
                        sample_frame,
                        text=item_text,
                        font=("Arial", 11),
                        text_color="#5a6c7d"
                    )
                    item_label.pack(anchor="w", padx=20, pady=2)

                except:
                    continue

    def print_from_preview(self, preview_window):
        """طباعة من نافذة المعاينة"""
        preview_window.destroy()
        self.print_report()

    def export_pdf(self):
        """تصدير التقرير كـ PDF"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf")],
                title="حفظ التقرير المخصص كـ PDF"
            )
            if filename:
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {e}")

    def export_excel(self):
        """تصدير التقرير كـ Excel"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="حفظ التقرير المخصص كـ Excel"
            )
            if filename:
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى: {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel: {e}")

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء تقرير HTML للطباعة
            html_content = self.generate_print_html()

            # حفظ في ملف مؤقت
            reports_dir = BASE_DIR / "reports" / "generated"
            reports_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = reports_dir / f"custom_report_{timestamp}.html"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # فتح في المتصفح
            webbrowser.open(f'file://{filename.absolute()}')

            messagebox.showinfo("نجح", "تم فتح التقرير في المتصفح للطباعة!")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {e}")

    def ai_analysis(self):
        """تحليل ذكي متقدم للبيانات"""
        try:
            # إنشاء نافذة التحليل الذكي
            ai_window = ctk.CTkToplevel(self)
            ai_window.title("🤖 التحليل الذكي المتقدم")
            ai_window.geometry("1200x800")
            ai_window.configure(bg="#0f1419")

            # جعل النافذة في المقدمة
            ai_window.transient(self)
            ai_window.grab_set()
            ai_window.focus_set()

            # الشريط العلوي المتطور
            header_frame = ctk.CTkFrame(ai_window, fg_color="#1e3a8a", corner_radius=0, height=80)
            header_frame.pack(fill="x", padx=0, pady=0)
            header_frame.pack_propagate(False)

            title_label = ctk.CTkLabel(
                header_frame,
                text="🤖 التحليل الذكي المتقدم بالذكاء الاصطناعي",
                font=("Arial", 22, "bold"),
                text_color="#ffffff"
            )
            title_label.pack(side="left", padx=30, pady=20)

            # مؤشر الحالة
            status_label = ctk.CTkLabel(
                header_frame,
                text="🔄 جاري التحليل...",
                font=("Arial", 12),
                text_color="#bfdbfe"
            )
            status_label.pack(side="right", padx=30, pady=20)

            # المحتوى الرئيسي
            main_frame = ctk.CTkScrollableFrame(ai_window, fg_color="#1e293b")
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # تشغيل التحليل الذكي
            self.run_ai_analysis(main_frame, status_label)

            # أزرار الإجراءات
            buttons_frame = ctk.CTkFrame(ai_window, fg_color="#0f1419", corner_radius=0, height=60)
            buttons_frame.pack(fill="x", side="bottom")
            buttons_frame.pack_propagate(False)

            close_btn = ctk.CTkButton(
                buttons_frame,
                text="❌ إغلاق",
                command=ai_window.destroy,
                fg_color="#dc2626",
                width=100,
                height=35
            )
            close_btn.pack(side="right", padx=20, pady=12)

            export_btn = ctk.CTkButton(
                buttons_frame,
                text="📤 تصدير التحليل",
                command=lambda: self.export_ai_analysis(ai_window),
                fg_color="#059669",
                width=120,
                height=35
            )
            export_btn.pack(side="right", padx=5, pady=12)

            refresh_btn = ctk.CTkButton(
                buttons_frame,
                text="🔄 إعادة تحليل",
                command=lambda: self.refresh_ai_analysis(main_frame, status_label),
                fg_color="#3b82f6",
                width=120,
                height=35
            )
            refresh_btn.pack(side="right", padx=5, pady=12)

        except Exception as e:
            print(f"خطأ في فتح نافذة التحليل الذكي: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التحليل الذكي:\n{e}")

    def run_ai_analysis(self, parent, status_label):
        """تشغيل التحليل الذكي"""

        try:
            # تحديث الحالة
            status_label.configure(text="🔄 جاري تحليل البيانات...")
            parent.update()

            # إنشاء الرؤى الذكية
            insights = self.generate_ai_insights()

            # عرض الرؤى
            insights_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
            insights_frame.pack(fill="x", padx=10, pady=10)

            insights_title = ctk.CTkLabel(
                insights_frame,
                text="🧠 الرؤى الذكية المكتشفة",
                font=("Arial", 18, "bold"),
                text_color="#2c3e50"
            )
            insights_title.pack(pady=15)

            for i, insight in enumerate(insights):
                insight_card = self.create_insight_card(insights_frame, insight, i)
                insight_card.pack(fill="x", padx=15, pady=8)

            # إنشاء التوقعات
            status_label.configure(text="🔮 جاري إنشاء التوقعات...")
            parent.update()

            predictions = self.generate_predictions()

            # عرض التوقعات
            predictions_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
            predictions_frame.pack(fill="x", padx=10, pady=10)

            predictions_title = ctk.CTkLabel(
                predictions_frame,
                text="🔮 التوقعات المستقبلية",
                font=("Arial", 18, "bold"),
                text_color="#2c3e50"
            )
            predictions_title.pack(pady=15)

            for prediction in predictions:
                prediction_card = self.create_prediction_card(predictions_frame, prediction)
                prediction_card.pack(fill="x", padx=15, pady=8)

            # إنشاء التوصيات
            status_label.configure(text="💡 جاري إنشاء التوصيات...")
            parent.update()

            recommendations = self.generate_recommendations()

            # عرض التوصيات
            recommendations_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
            recommendations_frame.pack(fill="x", padx=10, pady=10)

            recommendations_title = ctk.CTkLabel(
                recommendations_frame,
                text="💡 التوصيات الذكية",
                font=("Arial", 18, "bold"),
                text_color="#2c3e50"
            )
            recommendations_title.pack(pady=15)

            for recommendation in recommendations:
                recommendation_card = self.create_recommendation_card(recommendations_frame, recommendation)
                recommendation_card.pack(fill="x", padx=15, pady=8)

            # تحديث الحالة النهائية
            status_label.configure(text="✅ اكتمل التحليل الذكي")

        except Exception as e:
            print(f"خطأ في تشغيل التحليل الذكي: {e}")
            status_label.configure(text="❌ فشل في التحليل")

            error_label = ctk.CTkLabel(
                parent,
                text=f"❌ حدث خطأ في التحليل الذكي:\n{e}",
                font=("Arial", 14),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True, pady=50)

    def refresh_ai_analysis(self, parent, status_label):
        """إعادة تشغيل التحليل الذكي"""
        # مسح المحتوى الحالي
        for widget in parent.winfo_children():
            widget.destroy()

        # إعادة تشغيل التحليل
        self.run_ai_analysis(parent, status_label)

    def export_ai_analysis(self, ai_window):
        """تصدير التحليل الذكي"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("Text files", "*.txt")],
                title="تصدير التحليل الذكي"
            )
            if filename:
                # إنشاء تقرير التحليل الذكي
                ai_report = self.generate_ai_report_html()

                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(ai_report)

                messagebox.showinfo("نجح", f"تم تصدير التحليل الذكي إلى:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التحليل الذكي:\n{e}")

    def generate_ai_report_html(self):
        """إنشاء تقرير HTML للتحليل الذكي"""

        insights = self.generate_ai_insights()
        predictions = self.generate_predictions()
        recommendations = self.generate_recommendations()

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>التحليل الذكي المتقدم</title>
            <style>
                body {{ font-family: 'Tahoma', Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 15px; }}
                .section {{ background: white; margin: 20px 0; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .insight-card {{ background: #e8f4fd; padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #3498db; }}
                .prediction-card {{ background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #27ae60; }}
                .recommendation-card {{ background: #fff3cd; padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #f39c12; }}
                h1, h2 {{ color: #2c3e50; }}
                .confidence {{ background: #3498db; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 التحليل الذكي المتقدم</h1>
                <p>تاريخ التحليل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <div class="section">
                <h2>🧠 الرؤى الذكية</h2>
        """

        for insight in insights:
            html += f"""
                <div class="insight-card">
                    <h3>{insight['icon']} {insight['title']} <span class="confidence">الثقة: {insight['confidence']}%</span></h3>
                    <p>{insight['description']}</p>
                </div>
            """

        html += """
            </div>

            <div class="section">
                <h2>🔮 التوقعات المستقبلية</h2>
        """

        for prediction in predictions:
            html += f"""
                <div class="prediction-card">
                    <h3>🔮 {prediction['period']} <span class="confidence">الثقة: {prediction['confidence']}%</span></h3>
                    <p><strong>{prediction['metric']}:</strong> {prediction['value']}</p>
                    <p>{prediction['description']}</p>
                </div>
            """

        html += """
            </div>

            <div class="section">
                <h2>💡 التوصيات الذكية</h2>
        """

        for recommendation in recommendations:
            html += f"""
                <div class="recommendation-card">
                    <h3>💡 {recommendation['title']} <span class="confidence">الأولوية: {recommendation['priority']}</span></h3>
                    <p><strong>الفئة:</strong> {recommendation['category']}</p>
                    <p>{recommendation['description']}</p>
                    <p><strong>التأثير:</strong> {recommendation['impact']} | <strong>الجهد:</strong> {recommendation['effort']}</p>
                </div>
            """

        html += """
            </div>
        </body>
        </html>
        """

        return html

    def schedule_report(self):
        """جدولة التقرير المتقدمة"""
        try:
            # إنشاء نافذة الجدولة
            schedule_window = ctk.CTkToplevel(self)
            schedule_window.title("⏰ جدولة التقرير المتقدمة")
            schedule_window.geometry("800x600")
            schedule_window.configure(bg="#f8fafc")

            # جعل النافذة في المقدمة
            schedule_window.transient(self)
            schedule_window.grab_set()
            schedule_window.focus_set()

            # الشريط العلوي
            header_frame = ctk.CTkFrame(schedule_window, fg_color="#8b5cf6", corner_radius=0, height=70)
            header_frame.pack(fill="x", padx=0, pady=0)
            header_frame.pack_propagate(False)

            title_label = ctk.CTkLabel(
                header_frame,
                text="⏰ جدولة التقرير المتقدمة",
                font=("Arial", 20, "bold"),
                text_color="#ffffff"
            )
            title_label.pack(pady=20)

            # نموذج الجدولة
            form_frame = ctk.CTkScrollableFrame(schedule_window, fg_color="#ffffff")
            form_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # اسم التقرير المجدول
            name_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            name_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(name_frame, text="📝 اسم التقرير المجدول:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            schedule_name_entry = ctk.CTkEntry(name_frame, width=400, height=35,
                                             placeholder_text="مثال: تقرير المبيعات اليومي")
            schedule_name_entry.pack(padx=15, pady=(0, 15))

            # نوع التكرار
            frequency_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            frequency_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(frequency_frame, text="🔄 تكرار التقرير:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            frequency_combo = ctk.CTkComboBox(
                frequency_frame,
                values=["يومي", "أسبوعي", "شهري", "ربع سنوي", "سنوي"],
                width=200,
                height=35
            )
            frequency_combo.set("يومي")
            frequency_combo.pack(anchor="w", padx=15, pady=(0, 15))

            # وقت التنفيذ
            time_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            time_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(time_frame, text="🕐 وقت التنفيذ:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            time_container = ctk.CTkFrame(time_frame, fg_color="transparent")
            time_container.pack(anchor="w", padx=15, pady=(0, 15))

            hour_combo = ctk.CTkComboBox(
                time_container,
                values=[f"{i:02d}" for i in range(24)],
                width=80,
                height=35
            )
            hour_combo.set("09")
            hour_combo.pack(side="left", padx=(0, 5))

            ctk.CTkLabel(time_container, text=":", font=("Arial", 16, "bold")).pack(side="left", padx=5)

            minute_combo = ctk.CTkComboBox(
                time_container,
                values=[f"{i:02d}" for i in range(0, 60, 15)],
                width=80,
                height=35
            )
            minute_combo.set("00")
            minute_combo.pack(side="left", padx=(5, 0))

            # نوع التقرير المجدول
            report_type_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            report_type_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(report_type_frame, text="📊 نوع التقرير:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            scheduled_report_type = ctk.CTkComboBox(
                report_type_frame,
                values=[
                    "تقرير شامل متقدم",
                    "تحليل أداء الكاشيرين",
                    "تحليل المقبوضات المفصل",
                    "تقرير الاتجاهات والتوقعات"
                ],
                width=300,
                height=35
            )
            scheduled_report_type.set("تقرير شامل متقدم")
            scheduled_report_type.pack(anchor="w", padx=15, pady=(0, 15))

            # خيارات التصدير
            export_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            export_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(export_frame, text="📤 خيارات التصدير:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            export_options_frame = ctk.CTkFrame(export_frame, fg_color="transparent")
            export_options_frame.pack(anchor="w", padx=15, pady=(0, 15))

            pdf_var = ctk.BooleanVar(value=True)
            excel_var = ctk.BooleanVar(value=False)
            html_var = ctk.BooleanVar(value=False)

            pdf_check = ctk.CTkCheckBox(export_options_frame, text="PDF", variable=pdf_var)
            pdf_check.pack(side="left", padx=(0, 20))

            excel_check = ctk.CTkCheckBox(export_options_frame, text="Excel", variable=excel_var)
            excel_check.pack(side="left", padx=(0, 20))

            html_check = ctk.CTkCheckBox(export_options_frame, text="HTML", variable=html_var)
            html_check.pack(side="left")

            # مجلد الحفظ
            save_frame = ctk.CTkFrame(form_frame, fg_color="#f8fafc", corner_radius=10)
            save_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(save_frame, text="📁 مجلد الحفظ:",
                        font=("Arial", 14, "bold")).pack(anchor="w", padx=15, pady=(15, 5))

            save_path_frame = ctk.CTkFrame(save_frame, fg_color="transparent")
            save_path_frame.pack(fill="x", padx=15, pady=(0, 15))

            save_path_entry = ctk.CTkEntry(save_path_frame, width=300, height=35,
                                         placeholder_text="اختر مجلد الحفظ...")
            save_path_entry.pack(side="left", padx=(0, 10))

            browse_btn = ctk.CTkButton(
                save_path_frame,
                text="📁 تصفح",
                command=lambda: self.browse_save_folder(save_path_entry),
                width=80,
                height=35
            )
            browse_btn.pack(side="left")

            # أزرار الإجراءات
            buttons_frame = ctk.CTkFrame(schedule_window, fg_color="#f8fafc", corner_radius=0, height=70)
            buttons_frame.pack(fill="x", side="bottom")
            buttons_frame.pack_propagate(False)

            cancel_btn = ctk.CTkButton(
                buttons_frame,
                text="❌ إلغاء",
                command=schedule_window.destroy,
                fg_color="#6b7280",
                width=100,
                height=40
            )
            cancel_btn.pack(side="right", padx=20, pady=15)

            save_schedule_btn = ctk.CTkButton(
                buttons_frame,
                text="💾 حفظ الجدولة",
                command=lambda: self.save_schedule(
                    schedule_window,
                    schedule_name_entry.get(),
                    frequency_combo.get(),
                    f"{hour_combo.get()}:{minute_combo.get()}",
                    scheduled_report_type.get(),
                    {
                        'pdf': pdf_var.get(),
                        'excel': excel_var.get(),
                        'html': html_var.get()
                    },
                    save_path_entry.get()
                ),
                fg_color="#059669",
                width=120,
                height=40
            )
            save_schedule_btn.pack(side="right", padx=5, pady=15)

            test_btn = ctk.CTkButton(
                buttons_frame,
                text="🧪 اختبار",
                command=lambda: self.test_schedule(
                    scheduled_report_type.get(),
                    {
                        'pdf': pdf_var.get(),
                        'excel': excel_var.get(),
                        'html': html_var.get()
                    }
                ),
                fg_color="#3b82f6",
                width=100,
                height=40
            )
            test_btn.pack(side="right", padx=5, pady=15)

        except Exception as e:
            print(f"خطأ في فتح نافذة الجدولة: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الجدولة:\n{e}")

    def browse_save_folder(self, entry):
        """تصفح مجلد الحفظ"""
        folder = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder:
            entry.delete(0, 'end')
            entry.insert(0, folder)

    def save_schedule(self, window, name, frequency, time, report_type, export_options, save_path):
        """حفظ جدولة التقرير"""
        try:
            if not name.strip():
                messagebox.showwarning("تحذير", "يرجى إدخال اسم للتقرير المجدول")
                return

            if not save_path.strip():
                save_path = str(BASE_DIR / "reports" / "scheduled")
                os.makedirs(save_path, exist_ok=True)

            # إنشاء ملف الجدولة
            schedule_data = {
                'name': name,
                'frequency': frequency,
                'time': time,
                'report_type': report_type,
                'export_options': export_options,
                'save_path': save_path,
                'created_at': datetime.now().isoformat(),
                'status': 'نشط'
            }

            # حفظ في ملف JSON
            schedules_file = BASE_DIR / "config" / "scheduled_reports.json"
            schedules_file.parent.mkdir(exist_ok=True)

            try:
                with open(schedules_file, 'r', encoding='utf-8') as f:
                    schedules = json.load(f)
            except:
                schedules = []

            schedules.append(schedule_data)

            with open(schedules_file, 'w', encoding='utf-8') as f:
                json.dump(schedules, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح", f"تم حفظ جدولة التقرير '{name}' بنجاح!")
            window.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الجدولة:\n{e}")

    def test_schedule(self, report_type, export_options):
        """اختبار الجدولة"""
        try:
            messagebox.showinfo("اختبار",
                f"اختبار الجدولة:\n"
                f"نوع التقرير: {report_type}\n"
                f"خيارات التصدير: {', '.join([k for k, v in export_options.items() if v])}\n"
                f"سيتم تنفيذ التقرير الآن كاختبار...")

            # تنفيذ التقرير كاختبار
            self.generate_custom_report()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في اختبار الجدولة:\n{e}")

    # الوظائف المساعدة للرسوم البيانية والتحليلات
    def create_trend_chart(self, parent):
        """إنشاء رسم بياني للاتجاهات المتقدم"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        chart_frame.pack(fill="x", padx=15, pady=10)

        # عنوان الرسم البياني
        title_frame = ctk.CTkFrame(chart_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        chart_title = ctk.CTkLabel(
            title_frame,
            text="📈 تحليل الاتجاهات الزمنية المتقدم",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        chart_title.pack(pady=15)

        if MATPLOTLIB_AVAILABLE and self.filters_data:
            try:
                # تحضير البيانات للرسم البياني
                dates = []
                amounts = []
                daily_totals = defaultdict(float)

                for date_str, data_str, cashier_name in self.filters_data:
                    try:
                        data = json.loads(data_str or "{}")
                        totals = data.get('totals', {})

                        amount = (
                            float(totals.get('bank', 0)) +
                            float(totals.get('cash', 0)) +
                            float(totals.get('credit', 0))
                        )

                        daily_totals[date_str] += amount

                    except Exception:
                        continue

                # تحويل البيانات للرسم
                sorted_dates = sorted(daily_totals.keys())
                dates = [datetime.strptime(date, "%Y-%m-%d") for date in sorted_dates]
                amounts = [daily_totals[date] for date in sorted_dates]

                if dates and amounts:
                    # إنشاء الرسم البياني
                    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
                    fig.patch.set_facecolor('#ffffff')

                    # الرسم البياني الأول: الاتجاه العام
                    ax1.plot(dates, amounts, marker='o', linewidth=3, markersize=8,
                            color='#3498db', markerfacecolor='#e74c3c', markeredgecolor='#2c3e50')
                    ax1.fill_between(dates, amounts, alpha=0.3, color='#3498db')
                    ax1.set_title('الاتجاه العام للمبيعات', fontsize=14, fontweight='bold', pad=20)
                    ax1.set_ylabel('المبلغ (ريال)', fontsize=12)
                    ax1.grid(True, alpha=0.3)
                    ax1.tick_params(axis='x', rotation=45)

                    # الرسم البياني الثاني: التحليل الإحصائي
                    if len(amounts) > 1:
                        # حساب المتوسط المتحرك
                        window_size = min(3, len(amounts))
                        moving_avg = []
                        for i in range(len(amounts)):
                            start_idx = max(0, i - window_size + 1)
                            avg = sum(amounts[start_idx:i+1]) / (i - start_idx + 1)
                            moving_avg.append(avg)

                        ax2.bar(dates, amounts, alpha=0.6, color='#27ae60', label='المبيعات اليومية')
                        ax2.plot(dates, moving_avg, color='#e74c3c', linewidth=3,
                                marker='s', markersize=6, label='المتوسط المتحرك')

                        # خط المتوسط العام
                        avg_line = sum(amounts) / len(amounts)
                        ax2.axhline(y=avg_line, color='#f39c12', linestyle='--',
                                   linewidth=2, label=f'المتوسط العام: {avg_line:.2f}')

                        ax2.set_title('التحليل الإحصائي المتقدم', fontsize=14, fontweight='bold', pad=20)
                        ax2.set_ylabel('المبلغ (ريال)', fontsize=12)
                        ax2.set_xlabel('التاريخ', fontsize=12)
                        ax2.legend(loc='upper right')
                        ax2.grid(True, alpha=0.3)
                        ax2.tick_params(axis='x', rotation=45)

                    plt.tight_layout()

                    # إضافة الرسم البياني للواجهة
                    canvas = FigureCanvasTkAgg(fig, chart_frame)
                    canvas.draw()
                    canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

                    # شريط أدوات التفاعل
                    toolbar = NavigationToolbar2Tk(canvas, chart_frame)
                    toolbar.update()

                else:
                    self.create_no_data_message(chart_frame, "لا توجد بيانات كافية لإنشاء رسم الاتجاهات")

            except Exception as e:
                print(f"خطأ في إنشاء رسم الاتجاهات: {e}")
                self.create_no_data_message(chart_frame, f"خطأ في إنشاء الرسم البياني: {e}")
        else:
            if not MATPLOTLIB_AVAILABLE:
                self.create_no_data_message(chart_frame, "matplotlib غير مثبت. قم بتثبيته لرؤية الرسوم البيانية")
            else:
                self.create_no_data_message(chart_frame, "لا توجد بيانات لعرضها")

    def create_distribution_chart(self, parent):
        """إنشاء رسم بياني للتوزيع المتقدم"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        chart_frame.pack(fill="x", padx=15, pady=10)

        # عنوان الرسم البياني
        title_frame = ctk.CTkFrame(chart_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        chart_title = ctk.CTkLabel(
            title_frame,
            text="🥧 تحليل التوزيع والنسب المئوية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        chart_title.pack(pady=15)

        if MATPLOTLIB_AVAILABLE and self.filters_data:
            try:
                # تحضير البيانات للتوزيع
                payment_types = defaultdict(float)
                cashier_performance = defaultdict(float)

                for date_str, data_str, cashier_name in self.filters_data:
                    try:
                        data = json.loads(data_str or "{}")
                        totals = data.get('totals', {})

                        bank_amount = float(totals.get('bank', 0))
                        cash_amount = float(totals.get('cash', 0))
                        credit_amount = float(totals.get('credit', 0))

                        payment_types['مقبوضات بنكية'] += bank_amount
                        payment_types['مقبوضات نقدية'] += cash_amount
                        payment_types['مبيعات آجلة'] += credit_amount

                        total_amount = bank_amount + cash_amount + credit_amount
                        cashier_performance[cashier_name or 'غير محدد'] += total_amount

                    except Exception:
                        continue

                if payment_types or cashier_performance:
                    # إنشاء الرسوم البيانية
                    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
                    fig.patch.set_facecolor('#ffffff')

                    # الرسم الأول: توزيع أنواع المقبوضات (دائري)
                    if payment_types:
                        labels = list(payment_types.keys())
                        sizes = list(payment_types.values())
                        colors = ['#3498db', '#27ae60', '#f39c12']

                        wedges, texts, autotexts = ax1.pie(sizes, labels=labels, colors=colors,
                                                          autopct='%1.1f%%', startangle=90,
                                                          explode=(0.05, 0.05, 0.05))
                        ax1.set_title('توزيع أنواع المقبوضات', fontsize=14, fontweight='bold', pad=20)

                        # تحسين النصوص
                        for autotext in autotexts:
                            autotext.set_color('white')
                            autotext.set_fontweight('bold')

                    # الرسم الثاني: أداء الكاشيرين (عمودي)
                    if cashier_performance:
                        cashiers = list(cashier_performance.keys())[:8]  # أفضل 8 كاشيرين
                        performance = [cashier_performance[c] for c in cashiers]

                        bars = ax2.bar(range(len(cashiers)), performance,
                                      color=['#e74c3c', '#9b59b6', '#34495e', '#16a085',
                                            '#f39c12', '#27ae60', '#3498db', '#e67e22'][:len(cashiers)])

                        ax2.set_title('أداء الكاشيرين', fontsize=14, fontweight='bold', pad=20)
                        ax2.set_ylabel('إجمالي المبيعات (ريال)')
                        ax2.set_xticks(range(len(cashiers)))
                        ax2.set_xticklabels(cashiers, rotation=45, ha='right')

                        # إضافة قيم على الأعمدة
                        for bar, value in zip(bars, performance):
                            height = bar.get_height()
                            ax2.text(bar.get_x() + bar.get_width()/2., height + max(performance)*0.01,
                                    f'{value:.0f}', ha='center', va='bottom', fontweight='bold')

                    # الرسم الثالث: مقارنة الأداء (خطي)
                    if len(payment_types) > 1:
                        categories = list(payment_types.keys())
                        values = list(payment_types.values())

                        ax3.plot(categories, values, marker='o', linewidth=3, markersize=10,
                                color='#2c3e50', markerfacecolor='#e74c3c')
                        ax3.fill_between(categories, values, alpha=0.3, color='#3498db')
                        ax3.set_title('مقارنة أنواع المقبوضات', fontsize=14, fontweight='bold', pad=20)
                        ax3.set_ylabel('المبلغ (ريال)')
                        ax3.tick_params(axis='x', rotation=45)
                        ax3.grid(True, alpha=0.3)

                    # الرسم الرابع: التحليل الإحصائي
                    if cashier_performance:
                        values = list(cashier_performance.values())
                        ax4.hist(values, bins=min(10, len(values)), alpha=0.7, color='#27ae60',
                                edgecolor='black', linewidth=1.2)
                        ax4.axvline(sum(values)/len(values), color='#e74c3c', linestyle='--',
                                   linewidth=2, label=f'المتوسط: {sum(values)/len(values):.2f}')
                        ax4.set_title('توزيع الأداء الإحصائي', fontsize=14, fontweight='bold', pad=20)
                        ax4.set_xlabel('المبلغ (ريال)')
                        ax4.set_ylabel('عدد الكاشيرين')
                        ax4.legend()
                        ax4.grid(True, alpha=0.3)

                    plt.tight_layout()

                    # إضافة الرسم البياني للواجهة
                    canvas = FigureCanvasTkAgg(fig, chart_frame)
                    canvas.draw()
                    canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

                else:
                    self.create_no_data_message(chart_frame, "لا توجد بيانات كافية لإنشاء رسم التوزيع")

            except Exception as e:
                print(f"خطأ في إنشاء رسم التوزيع: {e}")
                self.create_no_data_message(chart_frame, f"خطأ في إنشاء الرسم البياني: {e}")
        else:
            if not MATPLOTLIB_AVAILABLE:
                self.create_no_data_message(chart_frame, "matplotlib غير مثبت. قم بتثبيته لرؤية الرسوم البيانية")
            else:
                self.create_no_data_message(chart_frame, "لا توجد بيانات لعرضها")

    def create_comparison_chart(self, parent):
        """إنشاء رسم بياني للمقارنات المتقدم"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        chart_frame.pack(fill="x", padx=15, pady=10)

        # عنوان الرسم البياني
        title_frame = ctk.CTkFrame(chart_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        chart_title = ctk.CTkLabel(
            title_frame,
            text="⚖️ مقارنات الأداء المتقدمة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        chart_title.pack(pady=15)

        if MATPLOTLIB_AVAILABLE and self.filters_data:
            try:
                # تحضير البيانات للمقارنة
                monthly_data = defaultdict(lambda: defaultdict(float))
                cashier_daily = defaultdict(lambda: defaultdict(float))

                for date_str, data_str, cashier_name in self.filters_data:
                    try:
                        data = json.loads(data_str or "{}")
                        totals = data.get('totals', {})

                        total_amount = (
                            float(totals.get('bank', 0)) +
                            float(totals.get('cash', 0)) +
                            float(totals.get('credit', 0))
                        )

                        # تجميع حسب الشهر
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        month_key = date_obj.strftime("%Y-%m")
                        monthly_data[month_key]['total'] += total_amount
                        monthly_data[month_key]['count'] += 1

                        # تجميع حسب الكاشير واليوم
                        cashier_daily[cashier_name or 'غير محدد'][date_str] += total_amount

                    except Exception:
                        continue

                if monthly_data or cashier_daily:
                    # إنشاء الرسوم البيانية
                    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
                    fig.patch.set_facecolor('#ffffff')

                    # الرسم الأول: مقارنة شهرية
                    if monthly_data:
                        months = sorted(monthly_data.keys())
                        totals = [monthly_data[m]['total'] for m in months]
                        averages = [monthly_data[m]['total']/monthly_data[m]['count'] for m in months]

                        x = range(len(months))
                        width = 0.35

                        bars1 = ax1.bar([i - width/2 for i in x], totals, width,
                                       label='إجمالي المبيعات', color='#3498db', alpha=0.8)
                        bars2 = ax1.bar([i + width/2 for i in x], averages, width,
                                       label='متوسط التصفية', color='#e74c3c', alpha=0.8)

                        ax1.set_title('مقارنة الأداء الشهري', fontsize=14, fontweight='bold', pad=20)
                        ax1.set_ylabel('المبلغ (ريال)')
                        ax1.set_xticks(x)
                        ax1.set_xticklabels(months)
                        ax1.legend()
                        ax1.grid(True, alpha=0.3)

                        # إضافة قيم على الأعمدة
                        for bar in bars1:
                            height = bar.get_height()
                            ax1.text(bar.get_x() + bar.get_width()/2., height,
                                    f'{height:.0f}', ha='center', va='bottom', fontsize=8)

                    # الرسم الثاني: مقارنة الكاشيرين
                    if cashier_daily:
                        top_cashiers = sorted(cashier_daily.items(),
                                            key=lambda x: sum(x[1].values()), reverse=True)[:6]

                        cashier_names = [c[0] for c in top_cashiers]
                        cashier_totals = [sum(c[1].values()) for c in top_cashiers]

                        colors = plt.cm.Set3(range(len(cashier_names)))
                        bars = ax2.barh(cashier_names, cashier_totals, color=colors)

                        ax2.set_title('مقارنة أداء أفضل الكاشيرين', fontsize=14, fontweight='bold', pad=20)
                        ax2.set_xlabel('إجمالي المبيعات (ريال)')

                        # إضافة قيم على الأعمدة
                        for bar, value in zip(bars, cashier_totals):
                            width = bar.get_width()
                            ax2.text(width + max(cashier_totals)*0.01, bar.get_y() + bar.get_height()/2,
                                    f'{value:.0f}', ha='left', va='center', fontweight='bold')

                    # الرسم الثالث: تحليل الاتجاه
                    if len(monthly_data) > 1:
                        months = sorted(monthly_data.keys())
                        growth_rates = []

                        for i in range(1, len(months)):
                            prev_total = monthly_data[months[i-1]]['total']
                            curr_total = monthly_data[months[i]]['total']
                            if prev_total > 0:
                                growth_rate = ((curr_total - prev_total) / prev_total) * 100
                                growth_rates.append(growth_rate)
                            else:
                                growth_rates.append(0)

                        if growth_rates:
                            colors = ['#27ae60' if rate >= 0 else '#e74c3c' for rate in growth_rates]
                            bars = ax3.bar(range(len(growth_rates)), growth_rates, color=colors, alpha=0.7)

                            ax3.set_title('معدل النمو الشهري (%)', fontsize=14, fontweight='bold', pad=20)
                            ax3.set_ylabel('معدل النمو (%)')
                            ax3.set_xticks(range(len(growth_rates)))
                            ax3.set_xticklabels(months[1:], rotation=45)
                            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                            ax3.grid(True, alpha=0.3)

                            # إضافة قيم على الأعمدة
                            for bar, rate in zip(bars, growth_rates):
                                height = bar.get_height()
                                ax3.text(bar.get_x() + bar.get_width()/2.,
                                        height + (1 if height >= 0 else -3),
                                        f'{rate:.1f}%', ha='center',
                                        va='bottom' if height >= 0 else 'top', fontweight='bold')

                    # الرسم الرابع: تحليل الأداء اليومي
                    if cashier_daily:
                        # حساب متوسط الأداء اليومي لكل كاشير
                        daily_averages = {}
                        for cashier, daily_data in cashier_daily.items():
                            if daily_data:
                                daily_averages[cashier] = sum(daily_data.values()) / len(daily_data)

                        if daily_averages:
                            cashiers = list(daily_averages.keys())[:8]
                            averages = [daily_averages[c] for c in cashiers]

                            # إنشاء رسم بياني قطبي
                            ax4 = plt.subplot(2, 2, 4, projection='polar')

                            angles = [i * 2 * 3.14159 / len(cashiers) for i in range(len(cashiers))]
                            angles += angles[:1]  # إغلاق الدائرة
                            averages += averages[:1]

                            ax4.plot(angles, averages, 'o-', linewidth=2, color='#3498db')
                            ax4.fill(angles, averages, alpha=0.25, color='#3498db')
                            ax4.set_xticks(angles[:-1])
                            ax4.set_xticklabels(cashiers)
                            ax4.set_title('متوسط الأداء اليومي\n(رسم قطبي)',
                                         fontsize=14, fontweight='bold', pad=30)

                    plt.tight_layout()

                    # إضافة الرسم البياني للواجهة
                    canvas = FigureCanvasTkAgg(fig, chart_frame)
                    canvas.draw()
                    canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

                else:
                    self.create_no_data_message(chart_frame, "لا توجد بيانات كافية لإنشاء رسم المقارنات")

            except Exception as e:
                print(f"خطأ في إنشاء رسم المقارنات: {e}")
                self.create_no_data_message(chart_frame, f"خطأ في إنشاء الرسم البياني: {e}")
        else:
            if not MATPLOTLIB_AVAILABLE:
                self.create_no_data_message(chart_frame, "matplotlib غير مثبت. قم بتثبيته لرؤية الرسوم البيانية")
            else:
                self.create_no_data_message(chart_frame, "لا توجد بيانات لعرضها")

    def create_no_data_message(self, parent, message):
        """إنشاء رسالة عدم وجود بيانات"""
        no_data_label = ctk.CTkLabel(
            parent,
            text=f"📊 {message}",
            font=("Arial", 14),
            text_color="#7f8c8d"
        )
        no_data_label.pack(expand=True, pady=50)

    def create_ai_insights(self, parent):
        """إنشاء قسم الرؤى الذكية المتقدم"""

        insights_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        insights_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(insights_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        insights_title = ctk.CTkLabel(
            title_frame,
            text="🧠 الرؤى الذكية والتحليل المتقدم",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        insights_title.pack(pady=15)

        # تحليل البيانات للحصول على الرؤى
        insights = self.generate_ai_insights()

        # عرض الرؤى في بطاقات
        insights_container = ctk.CTkFrame(insights_frame, fg_color="#f8f9fa", corner_radius=10)
        insights_container.pack(fill="x", padx=10, pady=10)

        for i, insight in enumerate(insights):
            insight_card = self.create_insight_card(insights_container, insight, i)
            insight_card.pack(fill="x", padx=10, pady=5)

    def create_predictions_section(self, parent):
        """إنشاء قسم التوقعات المتقدم"""

        predictions_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        predictions_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(predictions_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        predictions_title = ctk.CTkLabel(
            title_frame,
            text="🔮 التوقعات المستقبلية الذكية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        predictions_title.pack(pady=15)

        # إنشاء التوقعات
        predictions = self.generate_predictions()

        # عرض التوقعات
        predictions_container = ctk.CTkFrame(predictions_frame, fg_color="#f8f9fa", corner_radius=10)
        predictions_container.pack(fill="x", padx=10, pady=10)

        for prediction in predictions:
            prediction_card = self.create_prediction_card(predictions_container, prediction)
            prediction_card.pack(fill="x", padx=10, pady=5)

    def create_recommendations_section(self, parent):
        """إنشاء قسم التوصيات المتقدم"""

        recommendations_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        recommendations_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(recommendations_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        recommendations_title = ctk.CTkLabel(
            title_frame,
            text="💡 التوصيات الذكية لتحسين الأداء",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        recommendations_title.pack(pady=15)

        # إنشاء التوصيات
        recommendations = self.generate_recommendations()

        # عرض التوصيات
        recommendations_container = ctk.CTkFrame(recommendations_frame, fg_color="#f8f9fa", corner_radius=10)
        recommendations_container.pack(fill="x", padx=10, pady=10)

        for recommendation in recommendations:
            recommendation_card = self.create_recommendation_card(recommendations_container, recommendation)
            recommendation_card.pack(fill="x", padx=10, pady=5)

    def generate_ai_insights(self):
        """توليد الرؤى الذكية من البيانات"""
        insights = []

        try:
            if not self.filters_data:
                return [
                    {
                        'icon': '📊',
                        'title': 'لا توجد بيانات كافية',
                        'description': 'يحتاج النظام إلى المزيد من البيانات لتوليد رؤى ذكية',
                        'type': 'info',
                        'confidence': 100
                    }
                ]

            # تحليل الأداء العام
            total_amount = 0
            daily_totals = defaultdict(float)
            cashier_performance = defaultdict(float)
            payment_types = defaultdict(float)

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})

                    bank_amount = float(totals.get('bank', 0))
                    cash_amount = float(totals.get('cash', 0))
                    credit_amount = float(totals.get('credit', 0))

                    amount = bank_amount + cash_amount + credit_amount
                    total_amount += amount
                    daily_totals[date_str] += amount
                    cashier_performance[cashier_name or 'غير محدد'] += amount

                    payment_types['بنكي'] += bank_amount
                    payment_types['نقدي'] += cash_amount
                    payment_types['آجل'] += credit_amount

                except Exception:
                    continue

            # رؤية 1: تحليل الاتجاه العام
            if len(daily_totals) > 1:
                dates = sorted(daily_totals.keys())
                amounts = [daily_totals[date] for date in dates]

                # حساب الاتجاه
                if len(amounts) >= 2:
                    recent_avg = sum(amounts[-2:]) / 2 if len(amounts) >= 2 else amounts[-1]
                    early_avg = sum(amounts[:2]) / 2 if len(amounts) >= 2 else amounts[0]

                    if recent_avg > early_avg * 1.1:
                        insights.append({
                            'icon': '📈',
                            'title': 'اتجاه إيجابي في المبيعات',
                            'description': f'المبيعات في تحسن مستمر بنسبة {((recent_avg/early_avg-1)*100):.1f}%',
                            'type': 'success',
                            'confidence': 85
                        })
                    elif recent_avg < early_avg * 0.9:
                        insights.append({
                            'icon': '📉',
                            'title': 'انخفاض في المبيعات',
                            'description': f'المبيعات في انخفاض بنسبة {((1-recent_avg/early_avg)*100):.1f}%',
                            'type': 'warning',
                            'confidence': 80
                        })
                    else:
                        insights.append({
                            'icon': '📊',
                            'title': 'استقرار في المبيعات',
                            'description': 'المبيعات مستقرة نسبياً مع تذبذب طفيف',
                            'type': 'info',
                            'confidence': 75
                        })

            # رؤية 2: تحليل أداء الكاشيرين
            if cashier_performance:
                best_cashier = max(cashier_performance.items(), key=lambda x: x[1])
                worst_cashier = min(cashier_performance.items(), key=lambda x: x[1])

                if best_cashier[1] > worst_cashier[1] * 2:
                    insights.append({
                        'icon': '👑',
                        'title': f'أداء متميز للكاشير {best_cashier[0]}',
                        'description': f'يحقق أداءً أعلى بـ {(best_cashier[1]/worst_cashier[1]):.1f} مرة من أقل الكاشيرين',
                        'type': 'success',
                        'confidence': 90
                    })

                avg_performance = sum(cashier_performance.values()) / len(cashier_performance)
                underperformers = [name for name, perf in cashier_performance.items()
                                 if perf < avg_performance * 0.7]

                if underperformers:
                    insights.append({
                        'icon': '⚠️',
                        'title': 'كاشيرين يحتاجون تطوير',
                        'description': f'{len(underperformers)} كاشير أداؤهم أقل من 70% من المتوسط',
                        'type': 'warning',
                        'confidence': 85
                    })

            # رؤية 3: تحليل أنواع المدفوعات
            if payment_types:
                total_payments = sum(payment_types.values())
                if total_payments > 0:
                    cash_ratio = payment_types['نقدي'] / total_payments * 100
                    bank_ratio = payment_types['بنكي'] / total_payments * 100

                    if cash_ratio > 60:
                        insights.append({
                            'icon': '💰',
                            'title': 'اعتماد عالي على النقد',
                            'description': f'{cash_ratio:.1f}% من المدفوعات نقدية - فرصة لتعزيز الدفع الإلكتروني',
                            'type': 'info',
                            'confidence': 80
                        })
                    elif bank_ratio > 70:
                        insights.append({
                            'icon': '💳',
                            'title': 'تفوق في الدفع الإلكتروني',
                            'description': f'{bank_ratio:.1f}% من المدفوعات إلكترونية - أداء ممتاز',
                            'type': 'success',
                            'confidence': 85
                        })

            # رؤية 4: تحليل الكفاءة
            if len(daily_totals) > 0:
                avg_daily = sum(daily_totals.values()) / len(daily_totals)
                max_daily = max(daily_totals.values())

                efficiency_ratio = avg_daily / max_daily if max_daily > 0 else 0

                if efficiency_ratio > 0.8:
                    insights.append({
                        'icon': '⚡',
                        'title': 'كفاءة عالية ومستقرة',
                        'description': f'الأداء اليومي مستقر بنسبة {efficiency_ratio*100:.1f}%',
                        'type': 'success',
                        'confidence': 75
                    })
                elif efficiency_ratio < 0.5:
                    insights.append({
                        'icon': '🔄',
                        'title': 'تذبذب في الأداء',
                        'description': 'يوجد تفاوت كبير في الأداء اليومي - يحتاج استقرار',
                        'type': 'warning',
                        'confidence': 70
                    })

            return insights[:6]  # أفضل 6 رؤى

        except Exception as e:
            print(f"خطأ في توليد الرؤى الذكية: {e}")
            return [
                {
                    'icon': '❌',
                    'title': 'خطأ في التحليل',
                    'description': f'حدث خطأ أثناء تحليل البيانات: {e}',
                    'type': 'error',
                    'confidence': 0
                }
            ]

    def create_insight_card(self, parent, insight, index):
        """إنشاء بطاقة رؤية ذكية"""

        # تحديد لون البطاقة حسب النوع
        colors = {
            'success': '#27ae60',
            'warning': '#f39c12',
            'error': '#e74c3c',
            'info': '#3498db'
        }

        color = colors.get(insight['type'], '#34495e')

        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        # الصف الأول: الأيقونة والعنوان
        header_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 5))

        icon_title = ctk.CTkLabel(
            header_frame,
            text=f"{insight['icon']} {insight['title']}",
            font=("Arial", 14, "bold"),
            text_color="#ffffff"
        )
        icon_title.pack(side="left")

        confidence_label = ctk.CTkLabel(
            header_frame,
            text=f"الثقة: {insight['confidence']}%",
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        confidence_label.pack(side="right")

        # الوصف
        description_label = ctk.CTkLabel(
            content_frame,
            text=insight['description'],
            font=("Arial", 12),
            text_color="#ffffff",
            wraplength=600
        )
        description_label.pack(anchor="w", pady=(0, 5))

        return card

    def generate_predictions(self):
        """توليد التوقعات المستقبلية"""
        predictions = []

        try:
            if not self.filters_data or len(self.filters_data) < 2:
                return [
                    {
                        'period': 'الأسبوع القادم',
                        'metric': 'المبيعات المتوقعة',
                        'value': 'غير متاح',
                        'confidence': 0,
                        'trend': 'مستقر',
                        'description': 'يحتاج النظام إلى المزيد من البيانات التاريخية للتوقع'
                    }
                ]

            # تحليل البيانات التاريخية
            daily_totals = defaultdict(float)
            cashier_trends = defaultdict(list)

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})

                    amount = (
                        float(totals.get('bank', 0)) +
                        float(totals.get('cash', 0)) +
                        float(totals.get('credit', 0))
                    )

                    daily_totals[date_str] += amount
                    cashier_trends[cashier_name or 'غير محدد'].append(amount)

                except Exception:
                    continue

            # توقع المبيعات الأسبوعية
            if len(daily_totals) >= 3:
                dates = sorted(daily_totals.keys())
                amounts = [daily_totals[date] for date in dates]

                # حساب المتوسط المتحرك
                recent_avg = sum(amounts[-3:]) / 3 if len(amounts) >= 3 else sum(amounts) / len(amounts)
                overall_avg = sum(amounts) / len(amounts)

                # حساب الاتجاه
                if len(amounts) >= 2:
                    trend_slope = (amounts[-1] - amounts[0]) / (len(amounts) - 1)
                    next_week_prediction = recent_avg * 7 + (trend_slope * 7)

                    # تحديد الاتجاه
                    if trend_slope > overall_avg * 0.05:
                        trend = 'صاعد'
                        confidence = 75
                    elif trend_slope < -overall_avg * 0.05:
                        trend = 'هابط'
                        confidence = 70
                    else:
                        trend = 'مستقر'
                        confidence = 80

                    predictions.append({
                        'period': 'الأسبوع القادم',
                        'metric': 'إجمالي المبيعات',
                        'value': f'{next_week_prediction:.2f} ريال',
                        'confidence': confidence,
                        'trend': trend,
                        'description': f'بناءً على تحليل آخر {len(amounts)} يوم من البيانات'
                    })

                    # توقع متوسط التصفية اليومية
                    daily_prediction = next_week_prediction / 7
                    predictions.append({
                        'period': 'يومياً الأسبوع القادم',
                        'metric': 'متوسط المبيعات اليومية',
                        'value': f'{daily_prediction:.2f} ريال',
                        'confidence': confidence - 5,
                        'trend': trend,
                        'description': 'متوسط المبيعات المتوقعة لكل يوم'
                    })

            # توقع أداء الكاشيرين
            if cashier_trends:
                best_cashier = None
                best_trend = -float('inf')

                for cashier, amounts in cashier_trends.items():
                    if len(amounts) >= 2:
                        recent_performance = sum(amounts[-2:]) / 2 if len(amounts) >= 2 else amounts[-1]
                        if recent_performance > best_trend:
                            best_trend = recent_performance
                            best_cashier = cashier

                if best_cashier:
                    predictions.append({
                        'period': 'الفترة القادمة',
                        'metric': 'أفضل كاشير متوقع',
                        'value': best_cashier,
                        'confidence': 70,
                        'trend': 'متميز',
                        'description': f'بناءً على الأداء الحالي: {best_trend:.2f} ريال'
                    })

            # توقع نمو الأعمال
            if len(daily_totals) >= 5:
                dates = sorted(daily_totals.keys())
                amounts = [daily_totals[date] for date in dates]

                # حساب معدل النمو
                first_half = amounts[:len(amounts)//2]
                second_half = amounts[len(amounts)//2:]

                first_avg = sum(first_half) / len(first_half)
                second_avg = sum(second_half) / len(second_half)

                if first_avg > 0:
                    growth_rate = ((second_avg - first_avg) / first_avg) * 100

                    # توقع النمو الشهري
                    monthly_growth = growth_rate * 4  # تقدير شهري

                    predictions.append({
                        'period': 'الشهر القادم',
                        'metric': 'معدل النمو المتوقع',
                        'value': f'{monthly_growth:.1f}%',
                        'confidence': 65,
                        'trend': 'إيجابي' if monthly_growth > 0 else 'سلبي',
                        'description': f'بناءً على معدل النمو الحالي: {growth_rate:.1f}%'
                    })

            return predictions[:5]  # أفضل 5 توقعات

        except Exception as e:
            print(f"خطأ في توليد التوقعات: {e}")
            return [
                {
                    'period': 'غير متاح',
                    'metric': 'خطأ في التحليل',
                    'value': 'غير متاح',
                    'confidence': 0,
                    'trend': 'غير محدد',
                    'description': f'حدث خطأ أثناء توليد التوقعات: {e}'
                }
            ]

    def create_prediction_card(self, parent, prediction):
        """إنشاء بطاقة توقع"""

        # تحديد لون البطاقة حسب الاتجاه
        colors = {
            'صاعد': '#27ae60',
            'إيجابي': '#27ae60',
            'متميز': '#27ae60',
            'هابط': '#e74c3c',
            'سلبي': '#e74c3c',
            'مستقر': '#3498db'
        }

        color = colors.get(prediction['trend'], '#34495e')

        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        # الصف الأول: الفترة والثقة
        header_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 5))

        period_label = ctk.CTkLabel(
            header_frame,
            text=f"🔮 {prediction['period']}",
            font=("Arial", 12, "bold"),
            text_color="#ffffff"
        )
        period_label.pack(side="left")

        confidence_label = ctk.CTkLabel(
            header_frame,
            text=f"الثقة: {prediction['confidence']}%",
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        confidence_label.pack(side="right")

        # المقياس والقيمة
        metric_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        metric_frame.pack(fill="x", pady=(0, 5))

        metric_label = ctk.CTkLabel(
            metric_frame,
            text=prediction['metric'],
            font=("Arial", 11),
            text_color="#ecf0f1"
        )
        metric_label.pack(side="left")

        value_label = ctk.CTkLabel(
            metric_frame,
            text=prediction['value'],
            font=("Arial", 14, "bold"),
            text_color="#ffffff"
        )
        value_label.pack(side="right")

        # الاتجاه والوصف
        trend_label = ctk.CTkLabel(
            content_frame,
            text=f"الاتجاه: {prediction['trend']}",
            font=("Arial", 10, "bold"),
            text_color="#ffffff"
        )
        trend_label.pack(anchor="w", pady=(0, 3))

        description_label = ctk.CTkLabel(
            content_frame,
            text=prediction['description'],
            font=("Arial", 10),
            text_color="#ecf0f1",
            wraplength=500
        )
        description_label.pack(anchor="w")

        return card

    def generate_recommendations(self):
        """توليد التوصيات الذكية"""
        recommendations = []

        try:
            if not self.filters_data:
                return [
                    {
                        'category': 'البيانات',
                        'title': 'إضافة المزيد من البيانات',
                        'description': 'قم بإدخال المزيد من التصفيات للحصول على توصيات أكثر دقة',
                        'priority': 'عالية',
                        'impact': 'كبير',
                        'effort': 'منخفض'
                    }
                ]

            # تحليل البيانات للتوصيات
            cashier_performance = defaultdict(float)
            payment_types = defaultdict(float)
            daily_totals = defaultdict(float)

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})

                    bank_amount = float(totals.get('bank', 0))
                    cash_amount = float(totals.get('cash', 0))
                    credit_amount = float(totals.get('credit', 0))

                    total_amount = bank_amount + cash_amount + credit_amount
                    cashier_performance[cashier_name or 'غير محدد'] += total_amount

                    payment_types['بنكي'] += bank_amount
                    payment_types['نقدي'] += cash_amount
                    payment_types['آجل'] += credit_amount

                    daily_totals[date_str] += total_amount

                except Exception:
                    continue

            # توصية 1: تحسين أداء الكاشيرين
            if cashier_performance:
                avg_performance = sum(cashier_performance.values()) / len(cashier_performance)
                underperformers = [name for name, perf in cashier_performance.items()
                                 if perf < avg_performance * 0.8]

                if underperformers:
                    recommendations.append({
                        'category': 'الموارد البشرية',
                        'title': 'تطوير أداء الكاشيرين',
                        'description': f'يحتاج {len(underperformers)} كاشير لتدريب إضافي لتحسين الأداء',
                        'priority': 'متوسطة',
                        'impact': 'كبير',
                        'effort': 'متوسط'
                    })

                # تحديد أفضل الممارسات
                best_cashier = max(cashier_performance.items(), key=lambda x: x[1])
                if best_cashier[1] > avg_performance * 1.5:
                    recommendations.append({
                        'category': 'أفضل الممارسات',
                        'title': f'تطبيق أسلوب {best_cashier[0]}',
                        'description': f'دراسة وتطبيق أساليب العمل المتميزة للكاشير {best_cashier[0]}',
                        'priority': 'عالية',
                        'impact': 'كبير',
                        'effort': 'منخفض'
                    })

            # توصية 2: تحسين طرق الدفع
            if payment_types:
                total_payments = sum(payment_types.values())
                if total_payments > 0:
                    cash_ratio = payment_types['نقدي'] / total_payments * 100

                    if cash_ratio > 50:
                        recommendations.append({
                            'category': 'التكنولوجيا',
                            'title': 'تعزيز الدفع الإلكتروني',
                            'description': f'{cash_ratio:.1f}% من المدفوعات نقدية - فرصة لتقليل المخاطر وزيادة الكفاءة',
                            'priority': 'متوسطة',
                            'impact': 'متوسط',
                            'effort': 'متوسط'
                        })

                    bank_ratio = payment_types['بنكي'] / total_payments * 100
                    if bank_ratio < 30:
                        recommendations.append({
                            'category': 'التسويق',
                            'title': 'تشجيع الدفع البنكي',
                            'description': 'تقديم حوافز للعملاء لاستخدام البطاقات البنكية',
                            'priority': 'منخفضة',
                            'impact': 'متوسط',
                            'effort': 'منخفض'
                        })

            # توصية 3: تحسين الكفاءة التشغيلية
            if daily_totals:
                amounts = list(daily_totals.values())
                if len(amounts) > 1:
                    avg_daily = sum(amounts) / len(amounts)
                    max_daily = max(amounts)
                    min_daily = min(amounts)

                    variation = (max_daily - min_daily) / avg_daily if avg_daily > 0 else 0

                    if variation > 1.0:  # تذبذب عالي
                        recommendations.append({
                            'category': 'العمليات',
                            'title': 'تحسين استقرار الأداء',
                            'description': 'يوجد تذبذب كبير في الأداء اليومي - يحتاج تحليل الأسباب',
                            'priority': 'عالية',
                            'impact': 'كبير',
                            'effort': 'متوسط'
                        })

            # توصية 4: التحليل والمراقبة
            recommendations.append({
                'category': 'التحليل',
                'title': 'مراجعة دورية للأداء',
                'description': 'إجراء مراجعة أسبوعية للأداء باستخدام هذه التقارير',
                'priority': 'متوسطة',
                'impact': 'متوسط',
                'effort': 'منخفض'
            })

            # توصية 5: التدريب والتطوير
            recommendations.append({
                'category': 'التطوير',
                'title': 'برنامج تدريب مستمر',
                'description': 'تطوير برنامج تدريب مستمر للكاشيرين لتحسين الأداء',
                'priority': 'متوسطة',
                'impact': 'كبير',
                'effort': 'عالي'
            })

            return recommendations[:6]  # أفضل 6 توصيات

        except Exception as e:
            print(f"خطأ في توليد التوصيات: {e}")
            return [
                {
                    'category': 'خطأ',
                    'title': 'خطأ في التحليل',
                    'description': f'حدث خطأ أثناء توليد التوصيات: {e}',
                    'priority': 'عالية',
                    'impact': 'غير محدد',
                    'effort': 'غير محدد'
                }
            ]

    def create_recommendation_card(self, parent, recommendation):
        """إنشاء بطاقة توصية"""

        # تحديد لون البطاقة حسب الأولوية
        colors = {
            'عالية': '#e74c3c',
            'متوسطة': '#f39c12',
            'منخفضة': '#27ae60'
        }

        color = colors.get(recommendation['priority'], '#34495e')

        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=10)

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=10)

        # الصف الأول: الفئة والأولوية
        header_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 5))

        category_label = ctk.CTkLabel(
            header_frame,
            text=f"📋 {recommendation['category']}",
            font=("Arial", 11, "bold"),
            text_color="#ffffff"
        )
        category_label.pack(side="left")

        priority_label = ctk.CTkLabel(
            header_frame,
            text=f"الأولوية: {recommendation['priority']}",
            font=("Arial", 10, "bold"),
            text_color="#ffffff"
        )
        priority_label.pack(side="right")

        # العنوان
        title_label = ctk.CTkLabel(
            content_frame,
            text=recommendation['title'],
            font=("Arial", 13, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(anchor="w", pady=(0, 5))

        # الوصف
        description_label = ctk.CTkLabel(
            content_frame,
            text=recommendation['description'],
            font=("Arial", 11),
            text_color="#ecf0f1",
            wraplength=500
        )
        description_label.pack(anchor="w", pady=(0, 5))

        # التأثير والجهد
        impact_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        impact_frame.pack(fill="x")

        impact_label = ctk.CTkLabel(
            impact_frame,
            text=f"التأثير: {recommendation['impact']}",
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        impact_label.pack(side="left")

        effort_label = ctk.CTkLabel(
            impact_frame,
            text=f"الجهد: {recommendation['effort']}",
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        effort_label.pack(side="right")

        return card

    def create_period_comparison(self, parent):
        """إنشاء مقارنة الفترات الزمنية المتقدمة"""

        comparison_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        comparison_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(comparison_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        comparison_title = ctk.CTkLabel(
            title_frame,
            text="📊 مقارنة الفترات الزمنية المتقدمة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        comparison_title.pack(pady=15)

        # أدوات التحكم في المقارنة
        controls_frame = ctk.CTkFrame(comparison_frame, fg_color="#f8f9fa", corner_radius=10)
        controls_frame.pack(fill="x", padx=10, pady=5)

        # اختيار نوع المقارنة
        ctk.CTkLabel(controls_frame, text="نوع المقارنة:",
                    font=("Arial", 12, "bold")).pack(side="left", padx=10, pady=10)

        comparison_type = ctk.CTkComboBox(
            controls_frame,
            values=["مقارنة شهرية", "مقارنة أسبوعية", "مقارنة يومية", "مقارنة فصلية"],
            width=150
        )
        comparison_type.set("مقارنة شهرية")
        comparison_type.pack(side="left", padx=5, pady=10)

        # زر تحديث المقارنة
        update_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 تحديث المقارنة",
            command=lambda: self.update_period_comparison(comparison_frame, comparison_type.get()),
            fg_color="#3498db",
            width=120
        )
        update_btn.pack(side="left", padx=10, pady=10)

        # منطقة عرض المقارنة
        self.comparison_display = ctk.CTkFrame(comparison_frame, fg_color="#f8f9fa", corner_radius=10)
        self.comparison_display.pack(fill="both", expand=True, padx=10, pady=10)

        # تحديث المقارنة الأولية
        self.update_period_comparison(comparison_frame, "مقارنة شهرية")

    def create_cashier_comparison(self, parent):
        """إنشاء مقارنة الكاشيرين المتقدمة"""

        comparison_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        comparison_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(comparison_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        comparison_title = ctk.CTkLabel(
            title_frame,
            text="👥 مقارنة أداء الكاشيرين المتقدمة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        comparison_title.pack(pady=15)

        # إنشاء مقارنة الكاشيرين
        self.create_cashier_performance_analysis(comparison_frame)

    def create_future_predictions(self, parent):
        """إنشاء التوقعات المستقبلية المتقدمة"""

        predictions_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=15)
        predictions_frame.pack(fill="x", padx=15, pady=10)

        # عنوان القسم
        title_frame = ctk.CTkFrame(predictions_frame, fg_color="#f8f9fa", corner_radius=10)
        title_frame.pack(fill="x", padx=10, pady=10)

        predictions_title = ctk.CTkLabel(
            title_frame,
            text="🔮 التوقعات المستقبلية والتحليل التنبؤي",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        predictions_title.pack(pady=15)

        # إنشاء التوقعات المتقدمة
        self.create_advanced_predictions(predictions_frame)

    def update_period_comparison(self, parent, comparison_type):
        """تحديث مقارنة الفترات الزمنية"""

        # مسح العرض الحالي
        for widget in self.comparison_display.winfo_children():
            widget.destroy()

        try:
            if not self.filters_data:
                no_data_label = ctk.CTkLabel(
                    self.comparison_display,
                    text="📊 لا توجد بيانات للمقارنة",
                    font=("Arial", 14),
                    text_color="#7f8c8d"
                )
                no_data_label.pack(expand=True, pady=50)
                return

            # تحليل البيانات حسب نوع المقارنة
            period_data = defaultdict(float)
            period_counts = defaultdict(int)

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    data = json.loads(data_str or "{}")
                    totals = data.get('totals', {})

                    amount = (
                        float(totals.get('bank', 0)) +
                        float(totals.get('cash', 0)) +
                        float(totals.get('credit', 0))
                    )

                    date_obj = datetime.strptime(date_str, "%Y-%m-%d")

                    # تحديد المفتاح حسب نوع المقارنة
                    if comparison_type == "مقارنة شهرية":
                        key = date_obj.strftime("%Y-%m")
                    elif comparison_type == "مقارنة أسبوعية":
                        week_num = date_obj.isocalendar()[1]
                        key = f"{date_obj.year}-W{week_num:02d}"
                    elif comparison_type == "مقارنة يومية":
                        key = date_str
                    elif comparison_type == "مقارنة فصلية":
                        quarter = (date_obj.month - 1) // 3 + 1
                        key = f"{date_obj.year}-Q{quarter}"
                    else:
                        key = date_str

                    period_data[key] += amount
                    period_counts[key] += 1

                except Exception:
                    continue

            if period_data:
                # إنشاء جدول المقارنة
                self.create_comparison_table(self.comparison_display, period_data, period_counts, comparison_type)

                # إنشاء رسم بياني للمقارنة
                if MATPLOTLIB_AVAILABLE:
                    self.create_comparison_chart_widget(self.comparison_display, period_data, comparison_type)
            else:
                no_data_label = ctk.CTkLabel(
                    self.comparison_display,
                    text="📊 لا توجد بيانات كافية للمقارنة",
                    font=("Arial", 14),
                    text_color="#7f8c8d"
                )
                no_data_label.pack(expand=True, pady=50)

        except Exception as e:
            print(f"خطأ في تحديث مقارنة الفترات: {e}")
            error_label = ctk.CTkLabel(
                self.comparison_display,
                text=f"❌ خطأ في تحديث المقارنة: {e}",
                font=("Arial", 12),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True, pady=50)

    def create_comparison_table(self, parent, period_data, period_counts, comparison_type):
        """إنشاء جدول المقارنة"""

        table_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        table_frame.pack(fill="x", padx=10, pady=10)

        # عنوان الجدول
        table_title = ctk.CTkLabel(
            table_frame,
            text=f"📋 جدول {comparison_type}",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        table_title.pack(pady=10)

        # إنشاء الجدول
        columns = ("الفترة", "إجمالي المبيعات", "عدد التصفيات", "متوسط التصفية", "النمو %")

        comparison_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)

        for col in columns:
            comparison_tree.heading(col, text=col)
            comparison_tree.column(col, width=120, anchor="center")

        # ملء البيانات
        sorted_periods = sorted(period_data.keys())
        previous_amount = None

        for period in sorted_periods:
            amount = period_data[period]
            count = period_counts[period]
            avg = amount / count if count > 0 else 0

            # حساب النمو
            growth = ""
            if previous_amount is not None and previous_amount > 0:
                growth_rate = ((amount - previous_amount) / previous_amount) * 100
                growth = f"{growth_rate:+.1f}%"

            comparison_tree.insert("", "end", values=(
                period,
                f"{amount:.2f}",
                str(count),
                f"{avg:.2f}",
                growth
            ))

            previous_amount = amount

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=comparison_tree.yview)
        comparison_tree.configure(yscrollcommand=scrollbar.set)

        comparison_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

    def create_comparison_chart_widget(self, parent, period_data, comparison_type):
        """إنشاء رسم بياني للمقارنة"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        chart_frame.pack(fill="x", padx=10, pady=10)

        try:
            # إعداد البيانات
            periods = sorted(period_data.keys())
            amounts = [period_data[period] for period in periods]

            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(12, 6))
            fig.patch.set_facecolor('#ffffff')

            # رسم بياني خطي مع نقاط
            ax.plot(range(len(periods)), amounts, marker='o', linewidth=3, markersize=8,
                   color='#3498db', markerfacecolor='#e74c3c', markeredgecolor='#2c3e50')
            ax.fill_between(range(len(periods)), amounts, alpha=0.3, color='#3498db')

            # تحسين المظهر
            ax.set_title(f'مقارنة {comparison_type}', fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel('المبلغ (ريال)', fontsize=12)
            ax.set_xlabel('الفترة', fontsize=12)
            ax.set_xticks(range(len(periods)))
            ax.set_xticklabels(periods, rotation=45, ha='right')
            ax.grid(True, alpha=0.3)

            # إضافة قيم على النقاط
            for i, amount in enumerate(amounts):
                ax.annotate(f'{amount:.0f}', (i, amount), textcoords="offset points",
                           xytext=(0,10), ha='center', fontweight='bold')

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

        except Exception as e:
            print(f"خطأ في إنشاء رسم المقارنة: {e}")
            error_label = ctk.CTkLabel(
                chart_frame,
                text=f"❌ خطأ في إنشاء الرسم البياني: {e}",
                font=("Arial", 12),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True, pady=50)

    def create_cashier_performance_analysis(self, parent):
        """إنشاء تحليل أداء الكاشيرين المتقدم"""

        if not self.filters_data:
            no_data_label = ctk.CTkLabel(
                parent,
                text="📊 لا توجد بيانات لتحليل أداء الكاشيرين",
                font=("Arial", 14),
                text_color="#7f8c8d"
            )
            no_data_label.pack(expand=True, pady=50)
            return

        # تحليل أداء الكاشيرين
        cashier_stats = defaultdict(lambda: {
            'total_amount': 0,
            'total_transactions': 0,
            'daily_performance': [],
            'payment_types': defaultdict(float)
        })

        for date_str, data_str, cashier_name in self.filters_data:
            try:
                data = json.loads(data_str or "{}")
                totals = data.get('totals', {})

                bank_amount = float(totals.get('bank', 0))
                cash_amount = float(totals.get('cash', 0))
                credit_amount = float(totals.get('credit', 0))
                total_amount = bank_amount + cash_amount + credit_amount

                cashier = cashier_name or 'غير محدد'
                cashier_stats[cashier]['total_amount'] += total_amount
                cashier_stats[cashier]['total_transactions'] += 1
                cashier_stats[cashier]['daily_performance'].append(total_amount)
                cashier_stats[cashier]['payment_types']['بنكي'] += bank_amount
                cashier_stats[cashier]['payment_types']['نقدي'] += cash_amount
                cashier_stats[cashier]['payment_types']['آجل'] += credit_amount

            except Exception:
                continue

        # إنشاء جدول الأداء
        performance_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        performance_frame.pack(fill="x", padx=10, pady=10)

        performance_title = ctk.CTkLabel(
            performance_frame,
            text="📊 جدول أداء الكاشيرين المفصل",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        performance_title.pack(pady=10)

        # إنشاء الجدول
        columns = ("الكاشير", "إجمالي المبيعات", "عدد التصفيات", "متوسط التصفية",
                  "أفضل يوم", "الاستقرار", "التقييم")

        performance_tree = ttk.Treeview(performance_frame, columns=columns, show="headings", height=10)

        for col in columns:
            performance_tree.heading(col, text=col)
            performance_tree.column(col, width=120, anchor="center")

        # ملء البيانات مع التحليل المتقدم
        cashier_rankings = []

        for cashier, stats in cashier_stats.items():
            total_amount = stats['total_amount']
            total_transactions = stats['total_transactions']
            avg_transaction = total_amount / total_transactions if total_transactions > 0 else 0

            # حساب أفضل يوم
            daily_performance = stats['daily_performance']
            best_day = max(daily_performance) if daily_performance else 0

            # حساب الاستقرار (معامل التباين)
            if len(daily_performance) > 1:
                mean_perf = sum(daily_performance) / len(daily_performance)
                variance = sum((x - mean_perf) ** 2 for x in daily_performance) / len(daily_performance)
                stability = 100 - min(100, (variance ** 0.5 / mean_perf * 100)) if mean_perf > 0 else 0
            else:
                stability = 100

            # تحديد التقييم
            if avg_transaction > 5000 and stability > 80:
                rating = "⭐⭐⭐ ممتاز"
            elif avg_transaction > 3000 and stability > 60:
                rating = "⭐⭐ جيد"
            elif avg_transaction > 1000:
                rating = "⭐ مقبول"
            else:
                rating = "❌ يحتاج تحسين"

            cashier_rankings.append({
                'name': cashier,
                'total': total_amount,
                'transactions': total_transactions,
                'average': avg_transaction,
                'best_day': best_day,
                'stability': stability,
                'rating': rating
            })

        # ترتيب الكاشيرين حسب الأداء
        cashier_rankings.sort(key=lambda x: x['total'], reverse=True)

        for cashier_data in cashier_rankings:
            performance_tree.insert("", "end", values=(
                cashier_data['name'],
                f"{cashier_data['total']:.2f}",
                str(cashier_data['transactions']),
                f"{cashier_data['average']:.2f}",
                f"{cashier_data['best_day']:.2f}",
                f"{cashier_data['stability']:.1f}%",
                cashier_data['rating']
            ))

        # شريط التمرير
        scrollbar_perf = ttk.Scrollbar(performance_frame, orient="vertical", command=performance_tree.yview)
        performance_tree.configure(yscrollcommand=scrollbar_perf.set)

        performance_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar_perf.pack(side="right", fill="y", pady=10)

        # إنشاء رسم بياني للمقارنة
        if MATPLOTLIB_AVAILABLE and cashier_rankings:
            self.create_cashier_comparison_chart(parent, cashier_rankings)

    def create_cashier_comparison_chart(self, parent, cashier_rankings):
        """إنشاء رسم بياني لمقارنة الكاشيرين"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        chart_frame.pack(fill="x", padx=10, pady=10)

        try:
            # أفضل 8 كاشيرين
            top_cashiers = cashier_rankings[:8]

            names = [c['name'] for c in top_cashiers]
            totals = [c['total'] for c in top_cashiers]
            averages = [c['average'] for c in top_cashiers]
            stability = [c['stability'] for c in top_cashiers]

            # إنشاء الرسم البياني
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.patch.set_facecolor('#ffffff')

            # الرسم الأول: إجمالي المبيعات
            colors1 = plt.cm.viridis(range(len(names)))
            bars1 = ax1.bar(range(len(names)), totals, color=colors1)
            ax1.set_title('إجمالي المبيعات لكل كاشير', fontsize=12, fontweight='bold')
            ax1.set_ylabel('المبلغ (ريال)')
            ax1.set_xticks(range(len(names)))
            ax1.set_xticklabels(names, rotation=45, ha='right')

            # إضافة قيم على الأعمدة
            for bar, value in zip(bars1, totals):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{value:.0f}', ha='center', va='bottom', fontsize=8)

            # الرسم الثاني: متوسط التصفية
            colors2 = plt.cm.plasma(range(len(names)))
            bars2 = ax2.bar(range(len(names)), averages, color=colors2)
            ax2.set_title('متوسط التصفية لكل كاشير', fontsize=12, fontweight='bold')
            ax2.set_ylabel('المبلغ (ريال)')
            ax2.set_xticks(range(len(names)))
            ax2.set_xticklabels(names, rotation=45, ha='right')

            # الرسم الثالث: الاستقرار
            colors3 = ['#27ae60' if s > 80 else '#f39c12' if s > 60 else '#e74c3c' for s in stability]
            bars3 = ax3.bar(range(len(names)), stability, color=colors3)
            ax3.set_title('مؤشر الاستقرار (%)', fontsize=12, fontweight='bold')
            ax3.set_ylabel('نسبة الاستقرار (%)')
            ax3.set_xticks(range(len(names)))
            ax3.set_xticklabels(names, rotation=45, ha='right')
            ax3.set_ylim(0, 100)

            # الرسم الرابع: مقارنة شاملة (رادار)
            if len(names) <= 6:  # رسم رادار للكاشيرين الأفضل
                ax4 = plt.subplot(2, 2, 4, projection='polar')

                # تطبيع البيانات للرسم الرادار
                max_total = max(totals) if totals else 1
                max_avg = max(averages) if averages else 1

                normalized_totals = [t/max_total*100 for t in totals[:6]]
                normalized_averages = [a/max_avg*100 for a in averages[:6]]
                normalized_stability = stability[:6]

                angles = [i * 2 * 3.14159 / len(names[:6]) for i in range(len(names[:6]))]
                angles += angles[:1]

                # رسم البيانات
                for i, (total, avg, stab) in enumerate(zip(normalized_totals, normalized_averages, normalized_stability)):
                    values = [total, avg, stab]
                    values += values[:1]
                    ax4.plot(angles, values, 'o-', linewidth=2, label=names[i])
                    ax4.fill(angles, values, alpha=0.1)

                ax4.set_xticks(angles[:-1])
                ax4.set_xticklabels(['الإجمالي', 'المتوسط', 'الاستقرار'])
                ax4.set_title('مقارنة شاملة (رادار)', fontsize=12, fontweight='bold', pad=20)
                ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

        except Exception as e:
            print(f"خطأ في إنشاء رسم مقارنة الكاشيرين: {e}")
            error_label = ctk.CTkLabel(
                chart_frame,
                text=f"❌ خطأ في إنشاء الرسم البياني: {e}",
                font=("Arial", 12),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True, pady=50)

    def create_advanced_predictions(self, parent):
        """إنشاء التوقعات المتقدمة"""

        predictions_container = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=10)
        predictions_container.pack(fill="x", padx=10, pady=10)

        if not self.filters_data or len(self.filters_data) < 3:
            no_data_label = ctk.CTkLabel(
                predictions_container,
                text="🔮 يحتاج النظام إلى المزيد من البيانات التاريخية للتوقعات المتقدمة",
                font=("Arial", 14),
                text_color="#7f8c8d"
            )
            no_data_label.pack(expand=True, pady=50)
            return

        # تحليل البيانات للتوقعات
        daily_totals = defaultdict(float)
        weekly_totals = defaultdict(float)
        monthly_totals = defaultdict(float)

        for date_str, data_str, cashier_name in self.filters_data:
            try:
                data = json.loads(data_str or "{}")
                totals = data.get('totals', {})

                amount = (
                    float(totals.get('bank', 0)) +
                    float(totals.get('cash', 0)) +
                    float(totals.get('credit', 0))
                )

                date_obj = datetime.strptime(date_str, "%Y-%m-%d")

                daily_totals[date_str] += amount

                # تجميع أسبوعي
                week_key = f"{date_obj.year}-W{date_obj.isocalendar()[1]:02d}"
                weekly_totals[week_key] += amount

                # تجميع شهري
                month_key = date_obj.strftime("%Y-%m")
                monthly_totals[month_key] += amount

            except Exception:
                continue

        # إنشاء التوقعات المتقدمة
        advanced_predictions = self.generate_advanced_predictions(daily_totals, weekly_totals, monthly_totals)

        # عرض التوقعات في بطاقات متقدمة
        for prediction in advanced_predictions:
            prediction_card = self.create_advanced_prediction_card(predictions_container, prediction)
            prediction_card.pack(fill="x", padx=10, pady=5)

        # إنشاء رسم بياني للتوقعات
        if MATPLOTLIB_AVAILABLE:
            self.create_predictions_chart(parent, daily_totals, advanced_predictions)

    def generate_advanced_predictions(self, daily_totals, weekly_totals, monthly_totals):
        """توليد التوقعات المتقدمة"""
        predictions = []

        try:
            # توقع الأسبوع القادم بناءً على الاتجاه
            if len(daily_totals) >= 7:
                recent_days = sorted(daily_totals.keys())[-7:]
                recent_amounts = [daily_totals[day] for day in recent_days]

                # حساب الاتجاه الخطي
                x = list(range(len(recent_amounts)))
                n = len(x)
                sum_x = sum(x)
                sum_y = sum(recent_amounts)
                sum_xy = sum(x[i] * recent_amounts[i] for i in range(n))
                sum_x2 = sum(x[i] ** 2 for i in range(n))

                if n * sum_x2 - sum_x ** 2 != 0:
                    slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
                    intercept = (sum_y - slope * sum_x) / n

                    # توقع الأيام السبعة القادمة
                    next_week_total = 0
                    for i in range(7, 14):
                        predicted_value = slope * i + intercept
                        next_week_total += max(0, predicted_value)  # تجنب القيم السالبة

                    confidence = min(95, 60 + abs(slope) * 10)  # ثقة أعلى مع اتجاه أوضح

                    predictions.append({
                        'type': 'weekly',
                        'title': 'توقع الأسبوع القادم',
                        'value': f'{next_week_total:.2f} ريال',
                        'confidence': confidence,
                        'method': 'الانحدار الخطي',
                        'details': f'بناءً على اتجاه آخر 7 أيام (ميل: {slope:.2f})',
                        'trend': 'صاعد' if slope > 0 else 'هابط' if slope < 0 else 'مستقر'
                    })

            # توقع الشهر القادم بناءً على البيانات الشهرية
            if len(monthly_totals) >= 2:
                months = sorted(monthly_totals.keys())
                amounts = [monthly_totals[month] for month in months]

                # حساب متوسط النمو الشهري
                growth_rates = []
                for i in range(1, len(amounts)):
                    if amounts[i-1] > 0:
                        growth_rate = (amounts[i] - amounts[i-1]) / amounts[i-1]
                        growth_rates.append(growth_rate)

                if growth_rates:
                    avg_growth = sum(growth_rates) / len(growth_rates)
                    last_month_amount = amounts[-1]
                    next_month_prediction = last_month_amount * (1 + avg_growth)

                    confidence = min(90, 50 + (len(growth_rates) * 5))

                    predictions.append({
                        'type': 'monthly',
                        'title': 'توقع الشهر القادم',
                        'value': f'{next_month_prediction:.2f} ريال',
                        'confidence': confidence,
                        'method': 'متوسط النمو التاريخي',
                        'details': f'معدل النمو الشهري: {avg_growth*100:.1f}%',
                        'trend': 'نمو' if avg_growth > 0 else 'انخفاض' if avg_growth < 0 else 'استقرار'
                    })

            # توقع الذروة اليومية
            if daily_totals:
                daily_amounts = list(daily_totals.values())
                avg_daily = sum(daily_amounts) / len(daily_amounts)
                max_daily = max(daily_amounts)

                # توقع الذروة القادمة
                peak_prediction = max_daily * 1.1  # توقع زيادة 10%

                predictions.append({
                    'type': 'peak',
                    'title': 'توقع الذروة القادمة',
                    'value': f'{peak_prediction:.2f} ريال',
                    'confidence': 70,
                    'method': 'تحليل الذروات التاريخية',
                    'details': f'أعلى ذروة سابقة: {max_daily:.2f} ريال',
                    'trend': 'ذروة متوقعة'
                })

            # توقع الأداء الفصلي
            if len(monthly_totals) >= 3:
                months = sorted(monthly_totals.keys())
                recent_quarter = months[-3:] if len(months) >= 3 else months
                quarter_total = sum(monthly_totals[month] for month in recent_quarter)

                # توقع الربع القادم
                quarterly_prediction = quarter_total * 1.05  # توقع نمو 5%

                predictions.append({
                    'type': 'quarterly',
                    'title': 'توقع الربع القادم',
                    'value': f'{quarterly_prediction:.2f} ريال',
                    'confidence': 65,
                    'method': 'التحليل الفصلي',
                    'details': f'أداء الربع الحالي: {quarter_total:.2f} ريال',
                    'trend': 'نمو متوقع'
                })

            return predictions

        except Exception as e:
            print(f"خطأ في توليد التوقعات المتقدمة: {e}")
            return [
                {
                    'type': 'error',
                    'title': 'خطأ في التوقعات',
                    'value': 'غير متاح',
                    'confidence': 0,
                    'method': 'غير متاح',
                    'details': f'حدث خطأ: {e}',
                    'trend': 'غير محدد'
                }
            ]

    def create_advanced_prediction_card(self, parent, prediction):
        """إنشاء بطاقة توقع متقدمة"""

        # تحديد لون البطاقة حسب النوع
        colors = {
            'weekly': '#3498db',
            'monthly': '#27ae60',
            'peak': '#e74c3c',
            'quarterly': '#9b59b6',
            'error': '#95a5a6'
        }

        color = colors.get(prediction['type'], '#34495e')

        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=12)

        # محتوى البطاقة
        content_frame = ctk.CTkFrame(card, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=15)

        # الصف الأول: العنوان والثقة
        header_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(0, 8))

        title_label = ctk.CTkLabel(
            header_frame,
            text=f"🔮 {prediction['title']}",
            font=("Arial", 14, "bold"),
            text_color="#ffffff"
        )
        title_label.pack(side="left")

        confidence_frame = ctk.CTkFrame(header_frame, fg_color="rgba(255,255,255,0.2)", corner_radius=15)
        confidence_frame.pack(side="right")

        confidence_label = ctk.CTkLabel(
            confidence_frame,
            text=f"الثقة: {prediction['confidence']}%",
            font=("Arial", 10, "bold"),
            text_color="#ffffff"
        )
        confidence_label.pack(padx=10, pady=3)

        # القيمة المتوقعة
        value_label = ctk.CTkLabel(
            content_frame,
            text=prediction['value'],
            font=("Arial", 20, "bold"),
            text_color="#ffffff"
        )
        value_label.pack(pady=(0, 8))

        # الطريقة والاتجاه
        method_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        method_frame.pack(fill="x", pady=(0, 8))

        method_label = ctk.CTkLabel(
            method_frame,
            text=f"الطريقة: {prediction['method']}",
            font=("Arial", 10),
            text_color="#ecf0f1"
        )
        method_label.pack(side="left")

        trend_label = ctk.CTkLabel(
            method_frame,
            text=f"الاتجاه: {prediction['trend']}",
            font=("Arial", 10, "bold"),
            text_color="#ffffff"
        )
        trend_label.pack(side="right")

        # التفاصيل
        details_label = ctk.CTkLabel(
            content_frame,
            text=prediction['details'],
            font=("Arial", 10),
            text_color="#ecf0f1",
            wraplength=400
        )
        details_label.pack(anchor="w")

        return card

    def create_predictions_chart(self, parent, daily_totals, predictions):
        """إنشاء رسم بياني للتوقعات"""

        chart_frame = ctk.CTkFrame(parent, fg_color="#ffffff", corner_radius=10)
        chart_frame.pack(fill="x", padx=10, pady=10)

        try:
            if not daily_totals:
                return

            # إعداد البيانات التاريخية
            dates = sorted(daily_totals.keys())
            amounts = [daily_totals[date] for date in dates]
            date_objects = [datetime.strptime(date, "%Y-%m-%d") for date in dates]

            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(14, 8))
            fig.patch.set_facecolor('#ffffff')

            # رسم البيانات التاريخية
            ax.plot(date_objects, amounts, marker='o', linewidth=2, markersize=6,
                   color='#3498db', label='البيانات التاريخية', alpha=0.8)
            ax.fill_between(date_objects, amounts, alpha=0.3, color='#3498db')

            # إضافة خط الاتجاه
            if len(amounts) >= 3:
                # حساب خط الاتجاه
                x_numeric = list(range(len(amounts)))
                z = np.polyfit(x_numeric, amounts, 1)
                p = np.poly1d(z)
                trend_line = [p(x) for x in x_numeric]

                ax.plot(date_objects, trend_line, '--', color='#e74c3c',
                       linewidth=2, label='خط الاتجاه', alpha=0.8)

                # توقع الأيام القادمة
                future_days = 7
                future_x = list(range(len(amounts), len(amounts) + future_days))
                future_predictions = [p(x) for x in future_x]

                # تواريخ مستقبلية
                last_date = date_objects[-1]
                future_dates = [last_date + timedelta(days=i+1) for i in range(future_days)]

                ax.plot(future_dates, future_predictions, 'o--', color='#27ae60',
                       linewidth=2, markersize=8, label='التوقعات المستقبلية', alpha=0.9)
                ax.fill_between(future_dates, future_predictions, alpha=0.2, color='#27ae60')

            # تحسين المظهر
            ax.set_title('التوقعات المستقبلية مع البيانات التاريخية',
                        fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel('المبلغ (ريال)', fontsize=12)
            ax.set_xlabel('التاريخ', fontsize=12)
            ax.legend(loc='upper left')
            ax.grid(True, alpha=0.3)

            # تنسيق التواريخ
            ax.tick_params(axis='x', rotation=45)

            # إضافة خط المتوسط
            avg_amount = sum(amounts) / len(amounts)
            ax.axhline(y=avg_amount, color='#f39c12', linestyle=':',
                      linewidth=2, label=f'المتوسط: {avg_amount:.2f}', alpha=0.7)

            plt.tight_layout()

            # إضافة الرسم للواجهة
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=10)

        except Exception as e:
            print(f"خطأ في إنشاء رسم التوقعات: {e}")
            error_label = ctk.CTkLabel(
                chart_frame,
                text=f"❌ خطأ في إنشاء رسم التوقعات: {e}",
                font=("Arial", 12),
                text_color="#e74c3c"
            )
            error_label.pack(expand=True, pady=50)

    def generate_print_html(self):
        """إنشاء HTML متطور للطباعة"""

        # جمع البيانات للتقرير
        report_data = self.collect_report_data()

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>التقرير المخصص المتطور - نظام تصفية الكاشير</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Tajawal', 'Tahoma', Arial, sans-serif;
                    line-height: 1.6;
                    color: #2c3e50;
                    background: #ffffff;
                }}

                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 40px 20px;
                    text-align: center;
                    margin-bottom: 30px;
                    border-radius: 0 0 20px 20px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}

                .header h1 {{
                    font-size: 2.5em;
                    margin-bottom: 10px;
                    font-weight: 700;
                }}

                .header .subtitle {{
                    font-size: 1.2em;
                    opacity: 0.9;
                    margin-bottom: 5px;
                }}

                .header .date {{
                    font-size: 1em;
                    opacity: 0.8;
                }}

                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 0 20px;
                }}

                .section {{
                    background: #ffffff;
                    margin: 30px 0;
                    padding: 25px;
                    border-radius: 15px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    border-left: 5px solid #3498db;
                }}

                .section-title {{
                    font-size: 1.5em;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #ecf0f1;
                }}

                .stats-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}

                .stat-card {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 12px;
                    text-align: center;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}

                .stat-card.success {{
                    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
                }}

                .stat-card.warning {{
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                }}

                .stat-card.info {{
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                }}

                .stat-value {{
                    font-size: 2.2em;
                    font-weight: 700;
                    margin-bottom: 5px;
                }}

                .stat-label {{
                    font-size: 1em;
                    opacity: 0.9;
                }}

                .table-container {{
                    overflow-x: auto;
                    margin: 20px 0;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}

                table {{
                    width: 100%;
                    border-collapse: collapse;
                    background: white;
                }}

                th {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: 600;
                    font-size: 0.95em;
                }}

                td {{
                    padding: 12px 10px;
                    text-align: center;
                    border-bottom: 1px solid #ecf0f1;
                    font-size: 0.9em;
                }}

                tr:nth-child(even) {{
                    background: #f8f9fa;
                }}

                tr:hover {{
                    background: #e3f2fd;
                    transition: background 0.3s ease;
                }}

                .insights-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 20px 0;
                }}

                .insight-card {{
                    background: #ffffff;
                    border: 1px solid #e1e8ed;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }}

                .insight-card.success {{
                    border-left: 4px solid #27ae60;
                }}

                .insight-card.warning {{
                    border-left: 4px solid #f39c12;
                }}

                .insight-card.info {{
                    border-left: 4px solid #3498db;
                }}

                .insight-title {{
                    font-size: 1.1em;
                    font-weight: 600;
                    margin-bottom: 10px;
                    color: #2c3e50;
                }}

                .insight-description {{
                    color: #5a6c7d;
                    line-height: 1.5;
                }}

                .confidence-badge {{
                    display: inline-block;
                    background: #3498db;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 0.8em;
                    font-weight: 500;
                    margin-top: 10px;
                }}

                .footer {{
                    background: #2c3e50;
                    color: white;
                    text-align: center;
                    padding: 30px 20px;
                    margin-top: 50px;
                    border-radius: 20px 20px 0 0;
                }}

                .footer h3 {{
                    margin-bottom: 10px;
                    font-size: 1.3em;
                }}

                .footer p {{
                    opacity: 0.8;
                    margin: 5px 0;
                }}

                @media print {{
                    body {{
                        font-size: 12px;
                    }}

                    .header {{
                        background: #667eea !important;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}

                    .stat-card {{
                        background: #667eea !important;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}

                    th {{
                        background: #667eea !important;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                    }}

                    .section {{
                        break-inside: avoid;
                        page-break-inside: avoid;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎯 التقرير المخصص المتطور</h1>
                <div class="subtitle">نظام تصفية الكاشير المتكامل</div>
                <div class="date">تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
            </div>

            <div class="container">
                <div class="section">
                    <div class="section-title">📊 الإحصائيات العامة</div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{report_data['total_filters']}</div>
                            <div class="stat-label">إجمالي التصفيات</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-value">{report_data['total_amount']:.2f}</div>
                            <div class="stat-label">إجمالي المبيعات (ريال)</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-value">{report_data['avg_amount']:.2f}</div>
                            <div class="stat-label">متوسط التصفية (ريال)</div>
                        </div>
                        <div class="stat-card info">
                            <div class="stat-value">{len(report_data['cashier_performance'])}</div>
                            <div class="stat-label">عدد الكاشيرين النشطين</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">👥 أداء الكاشيرين</div>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>الترتيب</th>
                                    <th>اسم الكاشير</th>
                                    <th>عدد التصفيات</th>
                                    <th>إجمالي المبيعات (ريال)</th>
                                    <th>متوسط التصفية (ريال)</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
        """

        # إضافة بيانات الكاشيرين
        for i, (name, count, total_amount) in enumerate(report_data['cashier_performance'], 1):
            avg_amount = total_amount / count if count > 0 else 0

            if avg_amount > 5000:
                rating = "⭐⭐⭐ ممتاز"
            elif avg_amount > 3000:
                rating = "⭐⭐ جيد"
            else:
                rating = "⭐ مقبول"

            html += f"""
                                <tr>
                                    <td><strong>{i}</strong></td>
                                    <td>{name}</td>
                                    <td>{count}</td>
                                    <td>{total_amount:.2f}</td>
                                    <td>{avg_amount:.2f}</td>
                                    <td>{rating}</td>
                                </tr>
            """

        # إضافة الرؤى الذكية
        insights = self.generate_ai_insights()

        html += f"""
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="section">
                    <div class="section-title">🧠 الرؤى الذكية</div>
                    <div class="insights-grid">
        """

        for insight in insights[:6]:
            insight_class = insight['type']
            html += f"""
                        <div class="insight-card {insight_class}">
                            <div class="insight-title">{insight['icon']} {insight['title']}</div>
                            <div class="insight-description">{insight['description']}</div>
                            <span class="confidence-badge">الثقة: {insight['confidence']}%</span>
                        </div>
            """

        html += """
                    </div>
                </div>
            </div>

            <div class="footer">
                <h3>نظام تصفية الكاشير المتكامل</h3>
                <p>تقرير متطور مع تحليلات ذكية</p>
                <p>الإصدار 3.0.0 - تطوير: محمد الكامل</p>
                <p>© 2025 - جميع الحقوق محفوظة</p>
            </div>

            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 1500);
                };
            </script>
        </body>
        </html>
        """

        return html

    def collect_report_data(self):
        """جمع بيانات التقرير"""

        data = {
            'total_filters': 0,
            'total_amount': 0,
            'avg_amount': 0,
            'cashier_performance': []
        }

        try:
            if not self.filters_data:
                return data

            cashier_stats = defaultdict(lambda: {'count': 0, 'total': 0})
            total_amount = 0

            for date_str, data_str, cashier_name in self.filters_data:
                try:
                    filter_data = json.loads(data_str or "{}")
                    totals = filter_data.get('totals', {})

                    amount = (
                        float(totals.get('bank', 0)) +
                        float(totals.get('cash', 0)) +
                        float(totals.get('credit', 0))
                    )

                    total_amount += amount
                    cashier = cashier_name or 'غير محدد'
                    cashier_stats[cashier]['count'] += 1
                    cashier_stats[cashier]['total'] += amount

                except Exception:
                    continue

            data['total_filters'] = len(self.filters_data)
            data['total_amount'] = total_amount
            data['avg_amount'] = total_amount / len(self.filters_data) if self.filters_data else 0

            # ترتيب الكاشيرين حسب الأداء
            cashier_performance = []
            for cashier, stats in cashier_stats.items():
                cashier_performance.append((cashier, stats['count'], stats['total']))

            cashier_performance.sort(key=lambda x: x[2], reverse=True)
            data['cashier_performance'] = cashier_performance

        except Exception as e:
            print(f"خطأ في جمع بيانات التقرير: {e}")

        return data
