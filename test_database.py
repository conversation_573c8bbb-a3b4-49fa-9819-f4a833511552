#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار قاعدة البيانات والتحقق من البيانات
"""

import sqlite3
from pathlib import Path

def test_database():
    """اختبار قاعدة البيانات"""
    
    db_path = Path("cashier_filter/db/cashier_filter.db")
    
    if not db_path.exists():
        print(f"❌ قاعدة البيانات غير موجودة: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        c = conn.cursor()
        
        print("🔍 اختبار قاعدة البيانات...")
        print("=" * 50)
        
        # اختبار جدول filters
        print("\n📋 جدول filters:")
        try:
            c.execute("PRAGMA table_info(filters)")
            columns = c.fetchall()
            print("الأعمدة المتاحة:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # عدد السجلات
            c.execute("SELECT COUNT(*) FROM filters")
            count = c.fetchone()[0]
            print(f"عدد التصفيات: {count}")
            
            # عينة من البيانات
            if count > 0:
                c.execute("SELECT date, admin_name FROM filters LIMIT 5")
                samples = c.fetchall()
                print("عينة من البيانات:")
                for sample in samples:
                    print(f"  - {sample[0]} | {sample[1]}")
                    
        except Exception as e:
            print(f"❌ خطأ في جدول filters: {e}")
        
        # اختبار جدول cashiers
        print("\n👥 جدول cashiers:")
        try:
            c.execute("PRAGMA table_info(cashiers)")
            columns = c.fetchall()
            print("الأعمدة المتاحة:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # عدد السجلات
            c.execute("SELECT COUNT(*) FROM cashiers")
            count = c.fetchone()[0]
            print(f"عدد الكاشيرين: {count}")
            
            # عينة من البيانات
            if count > 0:
                c.execute("SELECT id, name FROM cashiers LIMIT 10")
                samples = c.fetchall()
                print("قائمة الكاشيرين:")
                for sample in samples:
                    print(f"  - {sample[0]}: {sample[1]}")
                    
        except Exception as e:
            print(f"❌ خطأ في جدول cashiers: {e}")
        
        # اختبار JOIN
        print("\n🔗 اختبار JOIN:")
        try:
            c.execute("""
                SELECT f.date, f.data, COALESCE(c.name, f.admin_name, 'غير محدد') as cashier_name
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                ORDER BY f.date DESC 
                LIMIT 5
            """)
            results = c.fetchall()
            print(f"نتائج JOIN: {len(results)} سجل")
            for result in results:
                print(f"  - {result[0]} | {result[2]}")
                
        except Exception as e:
            print(f"❌ خطأ في JOIN: {e}")
        
        conn.close()
        print("\n✅ انتهى اختبار قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")

if __name__ == "__main__":
    test_database()
