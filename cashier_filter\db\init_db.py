# Database setup for cashier filter system
import sqlite3

DB_PATH = "c:/Users/<USER>/Music/pro/cashier_filter/db/cashier_filter.db"

def init_db():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    # Cashiers
    c.execute('''CREATE TABLE IF NOT EXISTS cashiers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        number TEXT NOT NULL UNIQUE
    )''')
    # Admins
    c.execute('''CREATE TABLE IF NOT EXISTS admins (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL
    )''')
    # Filters
    c.execute('''CREATE TABLE IF NOT EXISTS filters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        cashier_id INTEGER,
        admin_id INTEGER,
        admin_name TEXT,
        date TEXT,
        data TEXT,
        notes TEXT,
        FOREI<PERSON><PERSON> KEY(cashier_id) REFERENCES cashiers(id),
        FOREIG<PERSON> KEY(admin_id) REFERENCES admins(id)
    )''')
    conn.commit()
    conn.close()

if __name__ == "__main__":
    init_db()
