# Cashier Filter System 2025 - Enhanced Version with AI
# Entry point for the application with login system
#
# تطوير: محمد الكامل - الإصدار 3.0.0
# Developed by: <PERSON> - Version 3.0.0
# © 2025 - جميع الحقوق محفوظة

import sys
import gc
import atexit
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def cleanup_on_exit():
    """تنظيف الذاكرة عند الخروج"""
    try:
        # تنظيف الذاكرة
        gc.collect()

        # إغلاق اتصالات قاعدة البيانات المفتوحة
        try:
            from utils.performance import db_pool
            db_pool.close_all()
        except:
            pass

        # مسح التخزين المؤقت
        try:
            from utils.performance import cache_manager
            cache_manager.clear()
        except:
            pass

    except Exception:
        pass  # تجاهل الأخطاء عند الخروج

def optimize_memory():
    """تحسين استخدام الذاكرة"""
    # تعيين حد أقصى لـ garbage collection
    gc.set_threshold(700, 10, 10)

    # تمكين garbage collection التلقائي
    gc.enable()

    # تسجيل دالة التنظيف عند الخروج
    atexit.register(cleanup_on_exit)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # تحسين إدارة الذاكرة
        optimize_memory()

        # تهيئة قاعدة البيانات إذا لم تكن موجودة
        from db.init_db import init_db
        init_db()

        # إنشاء المجلدات المطلوبة
        from config import create_required_directories
        create_required_directories()

        # تشغيل نافذة تسجيل الدخول
        from ui.login import run_login
        run_login()

    except ImportError as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        sys.exit(1)

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

    finally:
        # تنظيف نهائي
        cleanup_on_exit()

if __name__ == "__main__":
    main()
