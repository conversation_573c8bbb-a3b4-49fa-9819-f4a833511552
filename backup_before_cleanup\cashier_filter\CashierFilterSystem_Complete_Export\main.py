# Cashier Filter System 2025 - Enhanced Version with AI
# Entry point for the application with login system
#
# تطوير: محمد الكامل - الإصدار 3.0.0
# Developed by: <PERSON> - Version 3.0.0
# © 2025 - جميع الحقوق محفوظة

import sys
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # تهيئة قاعدة البيانات إذا لم تكن موجودة
        from db.init_db import init_db
        init_db()

        # إنشاء المجلدات المطلوبة
        from config import create_required_directories
        create_required_directories()

        # تشغيل نافذة تسجيل الدخول
        from ui.login import run_login
        run_login()

    except ImportError as e:
        print(f"خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        sys.exit(1)

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
