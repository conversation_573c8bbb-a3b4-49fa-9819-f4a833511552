#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة تثبيت نهائية لنظام تصفية الكاشير
Create Final Installer Package for Cashier Filter System
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_installer_package():
    """إنشاء حزمة التثبيت النهائية"""
    print("📦 إنشاء حزمة التثبيت النهائية...")
    
    # اسم الحزمة
    package_name = "نظام_تصفية_الكاشير_2025_التثبيت_النهائي"
    package_dir = Path(package_name)
    
    # حذف المجلد إذا كان موجوداً
    if package_dir.exists():
        shutil.rmtree(package_dir)
    
    # إنشاء المجلد
    package_dir.mkdir()
    
    print(f"📁 إنشاء مجلد: {package_dir}")
    
    # نسخ الملف التنفيذي
    exe_files = [
        "dist/CashierSystem2025.exe",
        "dist/CashierFilterSystem2025.exe"
    ]
    
    exe_copied = False
    for exe_file in exe_files:
        exe_path = Path(exe_file)
        if exe_path.exists():
            # نسخ الملف التنفيذي
            dest_name = "نظام_تصفية_الكاشير_2025.exe"
            shutil.copy2(exe_path, package_dir / dest_name)
            
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✅ نسخ الملف التنفيذي: {dest_name} ({size_mb:.1f} MB)")
            exe_copied = True
            break
    
    if not exe_copied:
        print("❌ لم يتم العثور على ملف تنفيذي")
        return None
    
    # نسخ ملفات التوثيق
    doc_files = {
        "README.md": "دليل_النظام.md",
        "USER_GUIDE.md": "دليل_المستخدم.md",
        "INSTALL.md": "دليل_التثبيت.md"
    }
    
    for src, dst in doc_files.items():
        if Path(src).exists():
            shutil.copy2(src, package_dir / dst)
            print(f"✅ نسخ: {dst}")
    
    # إنشاء ملف تشغيل مبسط
    run_script = """@echo off
chcp 65001 > nul
title نظام تصفية الكاشير المتكامل 2025

echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                                                                      ║
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║         Cashier Filter System 2025                                   ║
echo ║                                                                      ║
echo ║    الإصدار 3.0.0 - مع ذكاء اصطناعي متطور                          ║
echo ║    Version 3.0.0 - With Advanced AI                                 ║
echo ║                                                                      ║
echo ║    تطوير: محمد الكامل ^| Developed by: Mohamed Al-Kamel              ║
echo ║    البريد: <EMAIL>                                     ║
echo ║                                                                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 جاري تشغيل النظام...
echo.

"نظام_تصفية_الكاشير_2025.exe"

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo 💡 تأكد من أن النظام يدعم Windows 10 أو أحدث
    echo.
)

echo.
echo 👋 شكراً لاستخدام نظام تصفية الكاشير المتكامل 2025
echo    تطوير: محمد الكامل
echo.
pause
"""
    
    with open(package_dir / "تشغيل_النظام.bat", "w", encoding="utf-8") as f:
        f.write(run_script)
    
    print("✅ إنشاء ملف التشغيل")
    
    # إنشاء ملف معلومات شامل
    info_content = f"""# 🏪 نظام تصفية الكاشير المتكامل 2025

## 📋 معلومات الحزمة
- **الإصدار**: 3.0.0
- **تاريخ البناء**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **المطور**: محمد الكامل
- **البريد الإلكتروني**: <EMAIL>
- **نوع الحزمة**: ملف تنفيذي مستقل

## 🚀 طريقة التشغيل

### الطريقة الأولى (الأسهل):
1. النقر المزدوج على: **تشغيل_النظام.bat**

### الطريقة الثانية:
1. النقر المزدوج على: **نظام_تصفية_الكاشير_2025.exe**

## 🔐 بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: 123456

## ✨ الميزات الرئيسية
- ✅ **لا يحتاج تثبيت Python** - يعمل مباشرة
- ✅ **يعمل على أي كمبيوتر Windows** (Windows 10 أو أحدث)
- ✅ **جميع المكتبات مدمجة** - لا يحتاج تثبيت إضافي
- ✅ **قاعدة بيانات مدمجة** - جاهز للاستخدام
- ✅ **واجهة عربية كاملة** - سهلة الاستخدام
- ✅ **ذكاء اصطناعي متطور** - تحليل ذكي للبيانات
- ✅ **لوحة معلومات تفاعلية** - مراقبة في الوقت الفعلي
- ✅ **تقارير متقدمة** - طباعة وتصدير

## 📊 الوظائف المتاحة

### 💰 إدارة التصفيات
- إضافة تصفيات يومية
- تتبع جميع أنواع المعاملات
- حساب الأرباح والخسائر
- إدارة ديون العملاء

### 👥 إدارة المستخدمين
- نظام صلاحيات متقدم
- إدارة الكاشيرين والمسؤولين
- تتبع أنشطة المستخدمين

### 📈 التقارير والتحليلات
- تقارير مالية تفصيلية
- تحليل أداء الكاشيرين
- رسوم بيانية تفاعلية
- تصدير بصيغ متعددة

### 🤖 الذكاء الاصطناعي
- تحليل ذكي للبيانات المالية
- توقعات مستقبلية للإيرادات
- اكتشاف الأنماط والاتجاهات
- توصيات ذكية لتحسين الأداء

### 📊 لوحة المعلومات
- مراقبة الأداء في الوقت الفعلي
- إحصائيات سريعة
- تنبيهات ذكية
- تحديث تلقائي

### 🔍 البحث المتقدم
- بحث في جميع السجلات
- فلترة متقدمة
- حفظ عمليات البحث
- طباعة النتائج

## 💻 متطلبات النظام
- **نظام التشغيل**: Windows 10 أو أحدث
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **مساحة القرص**: 200 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

## 🛠️ استكشاف الأخطاء

### إذا لم يعمل التطبيق:
1. تأكد من أن نظام التشغيل Windows 10 أو أحدث
2. تأكد من وجود مساحة كافية على القرص
3. جرب تشغيل التطبيق كمسؤول (Run as Administrator)
4. تأكد من عدم حجب برنامج مكافح الفيروسات للتطبيق

### إذا ظهرت رسالة خطأ:
1. أعد تشغيل الكمبيوتر وجرب مرة أخرى
2. تأكد من إغلاق جميع البرامج الأخرى
3. جرب نسخ الملف لمكان آخر وتشغيله

## 📞 الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: متاح 24/7
- **التدريب**: متاح عند الطلب

## 📄 الترخيص
هذا البرنامج مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**© 2025 محمد الكامل - جميع الحقوق محفوظة**

شكراً لاستخدام نظام تصفية الكاشير المتكامل 2025!
"""
    
    with open(package_dir / "اقرأني_أولاً.txt", "w", encoding="utf-8") as f:
        f.write(info_content)
    
    print("✅ إنشاء ملف المعلومات الشامل")
    
    return package_dir

def create_zip_archive(package_dir):
    """إنشاء أرشيف مضغوط"""
    print("\n🗜️ إنشاء أرشيف مضغوط...")
    
    zip_name = f"{package_dir.name}.zip"
    
    try:
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(package_dir.parent)
                    zipf.write(file_path, arc_path)
        
        zip_size = Path(zip_name).stat().st_size / (1024 * 1024)
        print(f"✅ تم إنشاء الأرشيف: {zip_name}")
        print(f"📏 حجم الأرشيف: {zip_size:.1f} MB")
        
        return zip_name
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الأرشيف: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("📦 إنشاء حزمة التثبيت النهائية")
    print("=" * 60)
    
    # إنشاء حزمة التثبيت
    package_dir = create_installer_package()
    if not package_dir:
        print("❌ فشل في إنشاء حزمة التثبيت")
        return
    
    # إنشاء أرشيف مضغوط
    zip_name = create_zip_archive(package_dir)
    if not zip_name:
        print("❌ فشل في إنشاء الأرشيف")
        return
    
    # طباعة النتيجة النهائية
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء حزمة التثبيت النهائية بنجاح!")
    print("=" * 60)
    print(f"📁 مجلد الحزمة: {package_dir.absolute()}")
    print(f"🗜️ الأرشيف المضغوط: {Path(zip_name).absolute()}")
    print("\n📋 محتويات الحزمة:")
    print("   • نظام_تصفية_الكاشير_2025.exe - الملف التنفيذي")
    print("   • تشغيل_النظام.bat - ملف تشغيل مبسط")
    print("   • اقرأني_أولاً.txt - دليل شامل")
    print("   • أدلة المستخدم والتثبيت")
    print("\n🚀 طريقة التوزيع:")
    print("   1. إرسال الأرشيف المضغوط للمستخدمين")
    print("   2. فك الضغط في أي مكان")
    print("   3. تشغيل تشغيل_النظام.bat")
    print("\n💬 الدعم الفني: <EMAIL>")

if __name__ == "__main__":
    main()
    input("\nاضغط Enter للخروج...")
