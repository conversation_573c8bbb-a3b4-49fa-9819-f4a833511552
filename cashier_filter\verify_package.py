#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من سلامة حزمة التصدير
Verify Export Package Integrity
"""

import os
import zipfile
import json
from pathlib import Path

def verify_package():
    """التحقق من سلامة الحزمة"""
    print("🔍 التحقق من سلامة حزمة التصدير...")
    print("=" * 60)
    
    # البحث عن ملف ZIP (أحدث ملف)
    zip_files = list(Path(".").glob("CashierFilterSystem_v3.5.0_Complete_*.zip"))

    if not zip_files:
        print("❌ لم يتم العثور على ملف ZIP للحزمة")
        return False

    # اختيار أحدث ملف
    zip_file = max(zip_files, key=lambda f: f.stat().st_mtime)
    print(f"📦 فحص الحزمة: {zip_file.name}")
    
    try:
        with zipfile.ZipFile(zip_file, 'r') as zf:
            # فحص سلامة الملف
            print("🔍 فحص سلامة ملف ZIP...")
            bad_file = zf.testzip()
            if bad_file:
                print(f"❌ ملف تالف: {bad_file}")
                return False
            print("✅ ملف ZIP سليم")
            
            # قائمة الملفات
            file_list = zf.namelist()
            print(f"📁 عدد الملفات: {len(file_list)}")
            
            # فحص الملفات الأساسية
            essential_files = [
                "main.py",
                "web_server.py", 
                "config.py",
                "requirements.txt",
                "INSTALLATION_GUIDE.md",
                "README_COMPLETE.md",
                "تشغيل_النظام_الكامل.bat",
                "تشغيل_خادم_التقارير_الكامل.bat",
                "وصول_عالمي_كامل.bat"
            ]
            
            print("\n🔍 فحص الملفات الأساسية:")
            missing_files = []
            for file_name in essential_files:
                # البحث عن الملف في أي مجلد فرعي
                found = any(file_name in path for path in file_list)
                if found:
                    print(f"   ✅ {file_name}")
                else:
                    print(f"   ❌ {file_name} (مفقود)")
                    missing_files.append(file_name)
            
            # فحص المجلدات الأساسية
            essential_dirs = ["ui/", "db/", "reports/", "web_templates/"]
            print("\n🔍 فحص المجلدات الأساسية:")
            missing_dirs = []
            for dir_name in essential_dirs:
                found = any(path.startswith(f"CashierFilterSystem_Complete_Export/{dir_name}") for path in file_list)
                if found:
                    print(f"   ✅ {dir_name}")
                else:
                    print(f"   ❌ {dir_name} (مفقود)")
                    missing_dirs.append(dir_name)
            
            # فحص ملف معلومات الحزمة
            package_info_path = None
            for path in file_list:
                if "PACKAGE_INFO.json" in path:
                    package_info_path = path
                    break
            
            if package_info_path:
                print("\n📋 فحص معلومات الحزمة:")
                try:
                    with zf.open(package_info_path) as f:
                        package_info = json.load(f)
                    
                    print(f"   ✅ اسم الحزمة: {package_info.get('name', 'غير محدد')}")
                    print(f"   ✅ الإصدار: {package_info.get('version', 'غير محدد')}")
                    print(f"   ✅ تاريخ البناء: {package_info.get('build_date', 'غير محدد')}")
                    print(f"   ✅ عدد الملفات: {package_info.get('files_count', 0)}")
                    print(f"   ✅ عدد المجلدات: {package_info.get('dirs_count', 0)}")
                    
                    # فحص الميزات
                    features = package_info.get('features', [])
                    print(f"   ✅ عدد الميزات: {len(features)}")
                    for feature in features[:5]:  # أول 5 ميزات
                        print(f"      • {feature}")
                    
                except Exception as e:
                    print(f"   ⚠️ خطأ في قراءة معلومات الحزمة: {e}")
            else:
                print("\n⚠️ ملف معلومات الحزمة غير موجود")
            
            # النتيجة النهائية
            print("\n" + "=" * 60)
            if not missing_files and not missing_dirs:
                print("🎉 الحزمة سليمة وجاهزة للتوزيع!")
                print("✅ جميع الملفات والمجلدات الأساسية موجودة")
                print("✅ ملف ZIP سليم وغير تالف")
                print("✅ معلومات الحزمة صحيحة")
                
                # حساب حجم الحزمة
                size_mb = zip_file.stat().st_size / (1024 * 1024)
                print(f"📏 حجم الحزمة: {size_mb:.1f} MB")
                
                print(f"\n🚀 الحزمة جاهزة للتوزيع:")
                print(f"   📦 الملف: {zip_file.name}")
                print(f"   📁 الملفات: {len(file_list)}")
                print(f"   📏 الحجم: {size_mb:.1f} MB")
                
                return True
            else:
                print("❌ الحزمة غير مكتملة!")
                if missing_files:
                    print(f"   📄 ملفات مفقودة: {len(missing_files)}")
                    for file in missing_files:
                        print(f"      • {file}")
                if missing_dirs:
                    print(f"   📁 مجلدات مفقودة: {len(missing_dirs)}")
                    for dir in missing_dirs:
                        print(f"      • {dir}")
                return False
                
    except zipfile.BadZipFile:
        print("❌ ملف ZIP تالف أو غير صالح")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص الحزمة: {e}")
        return False

def create_distribution_info():
    """إنشاء ملف معلومات التوزيع"""
    print("\n📋 إنشاء ملف معلومات التوزيع...")
    
    # البحث عن ملف ZIP
    zip_files = list(Path(".").glob("CashierFilterSystem_v3.5.0_Complete_*.zip"))
    if not zip_files:
        return
    
    zip_file = zip_files[0]
    size_mb = zip_file.stat().st_size / (1024 * 1024)
    
    distribution_info = f"""# 📦 معلومات التوزيع - نظام تصفية الكاشير v3.5.0

## 📊 تفاصيل الحزمة
- **اسم الملف:** {zip_file.name}
- **حجم الحزمة:** {size_mb:.1f} MB
- **تاريخ الإنشاء:** {zip_file.stat().st_mtime}
- **الحالة:** ✅ جاهز للتوزيع

## 🎯 للاستخدام الفوري
1. فك الضغط عن الملف
2. شغل: `تشغيل_النظام_الكامل.bat`
3. سجل الدخول: admin / 123456
4. استمتع بجميع الميزات الجديدة!

## 🌟 الميزات المدمجة
- 💳 طريقة الدفع في مقبوضات العملاء
- 🏭 جدول الموردين منفصل
- 👥 أسماء العملاء في التقارير
- ⚖️ حساب الفارق الدقيق
- 📊 التقرير الشامل المحسن
- 🌐 الوصول العالمي

## 🔧 متطلبات النظام
- Python 3.8+
- Windows 10+, Linux, أو macOS
- 4GB RAM
- 500MB مساحة فارغة

## 📚 الأدلة المتاحة
- INSTALLATION_GUIDE.md
- دليل_الميزات_الجديدة.md
- دليل_التقارير_المحسنة.md
- دليل_الوصول_العالمي.md

## ✅ تم التحقق من سلامة الحزمة
- جميع الملفات الأساسية موجودة
- ملف ZIP سليم وغير تالف
- معلومات الحزمة صحيحة
- جاهز للتوزيع والاستخدام الفوري

---
**المطور:** محمد الكامل  
**الإصدار:** 3.5.0  
**التاريخ:** 9 يوليو 2025  
**الحالة:** ✅ جاهز للإنتاج
"""
    
    with open("DISTRIBUTION_INFO.md", 'w', encoding='utf-8') as f:
        f.write(distribution_info)
    
    print("✅ تم إنشاء DISTRIBUTION_INFO.md")

def main():
    """الدالة الرئيسية"""
    print("🔍 التحقق من سلامة حزمة التصدير الكاملة")
    print("=" * 70)
    
    # التحقق من سلامة الحزمة
    is_valid = verify_package()
    
    if is_valid:
        # إنشاء ملف معلومات التوزيع
        create_distribution_info()
        
        print("\n🎉 الحزمة جاهزة للتوزيع!")
        print("📦 يمكن توزيعها واستخدامها على أي كمبيوتر")
        print("🚀 للاستخدام الفوري: فك الضغط وشغل تشغيل_النظام_الكامل.bat")
    else:
        print("\n❌ الحزمة تحتاج إصلاح قبل التوزيع")
        print("💡 أعد تشغيل create_complete_package.py")

if __name__ == "__main__":
    main()
