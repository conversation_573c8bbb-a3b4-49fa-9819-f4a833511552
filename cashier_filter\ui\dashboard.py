# لوحة المعلومات التفاعلية
import customtkinter as ctk
from tkinter import ttk, messagebox
import sqlite3
import json
from datetime import datetime, timedelta
import threading
import time
import os
import sys

# إضافة مسار utils للوصول لنظام الإشعارات
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))

try:
    from utils.dashboard_notifier import register_dashboard, unregister_dashboard, get_recent_dashboard_events
except ImportError as e:
    print(f"تحذير: فشل في استيراد نظام الإشعارات: {e}")
    # إذا لم يكن النظام متاحاً، استخدم دوال وهمية
    def register_dashboard(dashboard_window):
        print("تسجيل لوحة المعلومات (وضع وهمي)")

    def unregister_dashboard(dashboard_window):
        print("إلغاء تسجيل لوحة المعلومات (وضع وهمي)")

    def get_recent_dashboard_events(limit=10):
        return []

DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "db", "cashier_filter.db")

class DashboardWindow(ctk.CTkToplevel):
    def __init__(self, master=None):
        try:
            print("🔄 بدء إنشاء نافذة لوحة المعلومات...")
            super().__init__(master)
            self.title("📊 لوحة المعلومات التفاعلية")
            self.geometry("1400x900")
            self.configure(bg="#f2f3f7")
            self.resizable(True, True)
            print("✅ تم إعداد النافذة الأساسية")

            # متغيرات البيانات
            self.dashboard_data = {}
            self.auto_refresh = True
            self.refresh_interval = 30  # ثانية
            self.last_update_time = None
            print("✅ تم إعداد متغيرات البيانات")

            # تسجيل النافذة في نظام الإشعارات
            try:
                register_dashboard(self)
                print("✅ تم تسجيل النافذة في نظام الإشعارات")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في تسجيل نظام الإشعارات: {e}")

            # جعل النافذة تظهر في المقدمة
            try:
                self.setup_window_focus()
                print("✅ تم إعداد تركيز النافذة")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في إعداد تركيز النافذة: {e}")

            # إنشاء عناصر الواجهة
            try:
                self.create_widgets()
                print("✅ تم إنشاء عناصر الواجهة")
            except Exception as e:
                print(f"❌ خطأ في إنشاء عناصر الواجهة: {e}")
                raise

            # تحميل بيانات لوحة المعلومات
            try:
                self.load_dashboard_data()
                print("✅ تم تحميل بيانات لوحة المعلومات")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في تحميل البيانات: {e}")

            # بدء التحديث التلقائي
            try:
                self.start_auto_refresh()
                print("✅ تم بدء التحديث التلقائي")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في بدء التحديث التلقائي: {e}")

            # ربط حدث إغلاق النافذة
            try:
                self.protocol("WM_DELETE_WINDOW", self.on_closing)
                print("✅ تم ربط حدث إغلاق النافذة")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في ربط حدث الإغلاق: {e}")
                # استخدام دالة إغلاق بديلة
                self.protocol("WM_DELETE_WINDOW", self.safe_close)

            print("🎉 تم إنشاء نافذة لوحة المعلومات بنجاح!")

        except Exception as e:
            print(f"❌ خطأ خطير في إنشاء نافذة لوحة المعلومات: {e}")
            import traceback
            traceback.print_exc()
            raise

    def safe_close(self):
        """دالة إغلاق آمنة في حالة عدم توفر on_closing"""
        try:
            print("🔄 إغلاق نافذة لوحة المعلومات (الوضع الآمن)...")
            # محاولة إلغاء تسجيل النافذة
            try:
                unregister_dashboard(self)
            except:
                pass
            # إغلاق النافذة
            self.destroy()
            print("✅ تم إغلاق النافذة بأمان")
        except Exception as e:
            print(f"خطأ في الإغلاق الآمن: {e}")
            # إغلاق قسري
            try:
                self.destroy()
            except:
                pass

    def setup_window_focus(self):
        """إعداد النافذة لتظهر في المقدمة"""
        try:
            # التأكد من أن النافذة مرئية
            self.deiconify()

            # رفع النافذة للمقدمة
            self.lift()

            # جعل النافذة في المقدمة مؤقتاً
            self.attributes('-topmost', True)

            # إعطاء التركيز للنافذة
            self.focus_force()

            # محاولة جعل النافذة نشطة
            try:
                self.wm_state('normal')
                self.tkraise()
            except:
                pass

            # إزالة خاصية البقاء في المقدمة بعد 300 مللي ثانية
            def remove_topmost():
                try:
                    self.attributes('-topmost', False)
                    self.focus_set()
                except:
                    pass

            self.after(300, remove_topmost)

        except Exception as e:
            print(f"خطأ في إعداد تركيز النافذة: {e}")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        try:
            print("🔄 إغلاق نافذة لوحة المعلومات...")
            # إلغاء تسجيل النافذة من نظام الإشعارات
            unregister_dashboard(self)
            print("✅ تم إلغاء تسجيل لوحة المعلومات من نظام الإشعارات")
        except Exception as e:
            print(f"خطأ في إلغاء تسجيل لوحة المعلومات: {e}")
        finally:
            # إغلاق النافذة
            try:
                self.destroy()
                print("✅ تم إغلاق النافذة بنجاح")
            except Exception as e:
                print(f"خطأ في إغلاق النافذة: {e}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        
        # العنوان الرئيسي
        self.create_header()
        
        # إطار المحتوى الرئيسي
        main_container = ctk.CTkFrame(self, fg_color="#f2f3f7", corner_radius=0)
        main_container.pack(pady=10, padx=20, fill="both", expand=True)
        
        # الصف الأول - البطاقات الإحصائية
        self.create_stats_cards(main_container)
        
        # الصف الثاني - الرسوم البيانية والجداول
        self.create_charts_section(main_container)
        
        # الصف الثالث - الأنشطة الحديثة والتنبيهات
        self.create_activities_section(main_container)
        
        # شريط التحكم السفلي
        self.create_control_bar(main_container)

    def create_header(self):
        """إنشاء العنوان الرئيسي"""
        header_frame = ctk.CTkFrame(self, fg_color="#2c3e50", corner_radius=20)
        header_frame.pack(pady=20, padx=20, fill="x")
        
        # العنوان والوقت
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=15)
        
        title_label = ctk.CTkLabel(
            title_frame,
            text="📊 لوحة المعلومات التفاعلية",
            font=("Arial", 28, "bold"),
            text_color="white"
        )
        title_label.pack(side="left")
        
        # الوقت الحالي
        self.time_label = ctk.CTkLabel(
            title_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            font=("Arial", 16),
            text_color="#ecf0f1"
        )
        self.time_label.pack(side="right")
        
        # شريط الحالة
        status_frame = ctk.CTkFrame(header_frame, fg_color="#34495e", corner_radius=10)
        status_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="🔄 جاري تحديث البيانات...",
            font=("Arial", 12),
            text_color="#ecf0f1"
        )
        self.status_label.pack(pady=8)

    def create_stats_cards(self, parent):
        """إنشاء البطاقات الإحصائية"""
        stats_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        stats_frame.pack(pady=10, fill="x")
        
        stats_title = ctk.CTkLabel(
            stats_frame,
            text="📈 الإحصائيات السريعة",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        stats_title.pack(pady=15)
        
        # إطار البطاقات
        cards_container = ctk.CTkFrame(stats_frame, fg_color="transparent")
        cards_container.pack(fill="x", padx=20, pady=10)
        
        # بطاقة إجمالي الإيرادات اليوم
        self.today_revenue_card = self.create_stat_card(
            cards_container, "💰 إيرادات اليوم", "0 ريال", "#27ae60"
        )
        
        # بطاقة عدد التصفيات اليوم
        self.today_filters_card = self.create_stat_card(
            cards_container, "📋 تصفيات اليوم", "0", "#3498db"
        )
        
        # بطاقة متوسط التصفية
        self.avg_filter_card = self.create_stat_card(
            cards_container, "📊 متوسط التصفية", "0 ريال", "#9b59b6"
        )
        
        # بطاقة أفضل كاشير
        self.best_cashier_card = self.create_stat_card(
            cards_container, "🏆 أفضل كاشير", "غير محدد", "#f39c12"
        )
        
        # بطاقة إجمالي هذا الشهر
        self.month_revenue_card = self.create_stat_card(
            cards_container, "📅 إيرادات الشهر", "0 ريال", "#e74c3c"
        )

        # بطاقة آخر رقم تسلسلي
        self.last_sequence_card = self.create_stat_card(
            cards_container, "🔢 آخر تصفية", "0", "#34495e"
        )

    def create_stat_card(self, parent, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = ctk.CTkFrame(parent, fg_color=color, corner_radius=12)
        card.pack(side="left", fill="both", expand=True, padx=8)
        
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=("Arial", 12, "bold"),
            text_color="white"
        )
        title_label.pack(pady=(15, 5))
        
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=("Arial", 20, "bold"),
            text_color="white"
        )
        value_label.pack(pady=(0, 15))
        
        return {"card": card, "title": title_label, "value": value_label}

    def create_charts_section(self, parent):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        charts_frame.pack(pady=10, fill="both", expand=True)
        
        charts_title = ctk.CTkLabel(
            charts_frame,
            text="📊 التحليلات والرسوم البيانية",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        charts_title.pack(pady=15)
        
        # إطار المحتوى
        content_frame = ctk.CTkFrame(charts_frame, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # الجانب الأيسر - جدول أفضل الكاشيرين
        left_panel = ctk.CTkFrame(content_frame, fg_color="#ffffff", corner_radius=10)
        left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        self.create_top_cashiers_table(left_panel)
        
        # الجانب الأيمن - إحصائيات الأسبوع
        right_panel = ctk.CTkFrame(content_frame, fg_color="#ffffff", corner_radius=10)
        right_panel.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        self.create_weekly_stats(right_panel)

    def create_top_cashiers_table(self, parent):
        """إنشاء جدول أفضل الكاشيرين"""
        table_title = ctk.CTkLabel(
            parent,
            text="🏆 أفضل الكاشيرين هذا الأسبوع",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        table_title.pack(pady=15)
        
        # إطار الجدول
        table_frame = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=8)
        table_frame.pack(fill="both", expand=True, padx=15, pady=10)
        
        # جدول الكاشيرين
        columns = ("الترتيب", "اسم الكاشير", "عدد التصفيات", "إجمالي الإيرادات")
        self.cashiers_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.cashiers_tree.heading(col, text=col)
            self.cashiers_tree.column(col, width=120, anchor="center")
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.cashiers_tree.yview)
        self.cashiers_tree.configure(yscrollcommand=scrollbar.set)
        
        self.cashiers_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

    def create_weekly_stats(self, parent):
        """إنشاء إحصائيات الأسبوع"""
        stats_title = ctk.CTkLabel(
            parent,
            text="📈 إحصائيات الأسبوع الحالي",
            font=("Arial", 16, "bold"),
            text_color="#2c3e50"
        )
        stats_title.pack(pady=15)
        
        # إطار الإحصائيات
        stats_container = ctk.CTkFrame(parent, fg_color="#f8f9fa", corner_radius=8)
        stats_container.pack(fill="both", expand=True, padx=15, pady=10)
        
        # إحصائيات يومية
        self.daily_stats_frame = ctk.CTkFrame(stats_container, fg_color="transparent")
        self.daily_stats_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # سيتم ملء هذا القسم بالبيانات الفعلية

    def create_activities_section(self, parent):
        """إنشاء قسم الأنشطة الحديثة"""
        activities_frame = ctk.CTkFrame(parent, fg_color="#e0e5ec", corner_radius=15)
        activities_frame.pack(pady=10, fill="x")
        
        activities_title = ctk.CTkLabel(
            activities_frame,
            text="🔔 الأنشطة الحديثة والتنبيهات",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        activities_title.pack(pady=15)
        
        # إطار المحتوى
        content_frame = ctk.CTkFrame(activities_frame, fg_color="transparent")
        content_frame.pack(fill="x", padx=20, pady=10)
        
        # الأنشطة الحديثة
        activities_panel = ctk.CTkFrame(content_frame, fg_color="#ffffff", corner_radius=10)
        activities_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        self.create_recent_activities(activities_panel)
        
        # التنبيهات
        alerts_panel = ctk.CTkFrame(content_frame, fg_color="#ffffff", corner_radius=10)
        alerts_panel.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        self.create_alerts_panel(alerts_panel)

    def create_recent_activities(self, parent):
        """إنشاء قائمة الأنشطة الحديثة"""
        activities_title = ctk.CTkLabel(
            parent,
            text="📝 آخر الأنشطة",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        activities_title.pack(pady=10)
        
        # إطار قابل للتمرير للأنشطة
        self.activities_scrollable = ctk.CTkScrollableFrame(
            parent,
            fg_color="#f8f9fa",
            corner_radius=8,
            height=150
        )
        self.activities_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

    def create_alerts_panel(self, parent):
        """إنشاء لوحة التنبيهات"""
        alerts_title = ctk.CTkLabel(
            parent,
            text="⚠️ التنبيهات",
            font=("Arial", 14, "bold"),
            text_color="#2c3e50"
        )
        alerts_title.pack(pady=10)
        
        # إطار قابل للتمرير للتنبيهات
        self.alerts_scrollable = ctk.CTkScrollableFrame(
            parent,
            fg_color="#f8f9fa",
            corner_radius=8,
            height=150
        )
        self.alerts_scrollable.pack(fill="both", expand=True, padx=10, pady=10)

    def create_control_bar(self, parent):
        """إنشاء شريط التحكم"""
        control_frame = ctk.CTkFrame(parent, fg_color="#34495e", corner_radius=15)
        control_frame.pack(pady=10, fill="x")
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        buttons_frame.pack(pady=15)
        
        # زر التحديث اليدوي
        refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث البيانات",
            command=self.manual_refresh,
            fg_color="#27ae60",
            hover_color="#229954",
            width=150,
            height=35,
            font=("Arial", 12, "bold")
        )
        refresh_btn.pack(side="left", padx=10)
        
        # زر التحديث التلقائي
        self.auto_refresh_btn = ctk.CTkButton(
            buttons_frame,
            text="⏸️ إيقاف التحديث التلقائي",
            command=self.toggle_auto_refresh,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=200,
            height=35,
            font=("Arial", 12, "bold")
        )
        self.auto_refresh_btn.pack(side="left", padx=10)
        
        # زر إعدادات اللوحة
        settings_btn = ctk.CTkButton(
            buttons_frame,
            text="⚙️ إعدادات اللوحة",
            command=self.show_dashboard_settings,
            fg_color="#3498db",
            hover_color="#2980b9",
            width=150,
            height=35,
            font=("Arial", 12, "bold")
        )
        settings_btn.pack(side="left", padx=10)
        
        # زر تصدير التقرير
        export_btn = ctk.CTkButton(
            buttons_frame,
            text="📊 تصدير تقرير",
            command=self.export_dashboard_report,
            fg_color="#9b59b6",
            hover_color="#8e44ad",
            width=150,
            height=35,
            font=("Arial", 12, "bold")
        )
        export_btn.pack(side="left", padx=10)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            self.status_label.configure(text="🔄 جاري تحديث البيانات...")

            # تحديث الوقت
            self.time_label.configure(text=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            # تحميل البيانات في خيط منفصل
            threading.Thread(target=self._load_data_thread, daemon=True).start()

        except Exception as e:
            print(f"خطأ في تحميل بيانات اللوحة: {e}")
            self.status_label.configure(text="❌ خطأ في تحميل البيانات")

    def _load_data_thread(self):
        """تحميل البيانات في خيط منفصل"""
        try:
            # جمع البيانات من قاعدة البيانات
            data = self.collect_dashboard_data()

            # تحديث الواجهة في الخيط الرئيسي
            try:
                self.after(0, lambda: self.update_dashboard_ui(data))
            except:
                # إذا فشل after، قم بالتحديث مباشرة
                self.update_dashboard_ui(data)

        except Exception as e:
            print(f"خطأ في خيط تحميل البيانات: {e}")
            try:
                self.after(0, lambda: self.status_label.configure(text="❌ خطأ في تحميل البيانات"))
            except:
                # إذا فشل after، قم بالتحديث مباشرة
                try:
                    self.status_label.configure(text="❌ خطأ في تحميل البيانات")
                except:
                    pass

    def collect_dashboard_data(self):
        """جمع البيانات من قاعدة البيانات"""
        data = {
            'today_revenue': 0,
            'today_filters': 0,
            'avg_filter': 0,
            'best_cashier': 'غير محدد',
            'month_revenue': 0,
            'last_sequence': 0,
            'top_cashiers': [],
            'weekly_stats': {},
            'recent_activities': [],
            'alerts': []
        }

        try:
            if not os.path.exists(DB_PATH):
                return data

            conn = sqlite3.connect(DB_PATH)
            c = conn.cursor()

            # التحقق من وجود الجداول
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='filters'")
            if not c.fetchone():
                conn.close()
                return data

            today = datetime.now().strftime('%Y-%m-%d')
            month_start = datetime.now().replace(day=1).strftime('%Y-%m-%d')
            week_start = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

            # إيرادات اليوم
            c.execute("""
                SELECT SUM(
                    CASE
                        WHEN json_extract(data, '$.totals') IS NOT NULL
                        THEN (
                            COALESCE(json_extract(data, '$.totals.bank'), 0) +
                            COALESCE(json_extract(data, '$.totals.cash'), 0) +
                            COALESCE(json_extract(data, '$.totals.credit'), 0) +
                            COALESCE(json_extract(data, '$.totals.return'), 0) -
                            COALESCE(json_extract(data, '$.totals.client'), 0)
                        )
                        ELSE 0
                    END
                ) as total_revenue
                FROM filters
                WHERE date = ?
            """, (today,))

            result = c.fetchone()
            data['today_revenue'] = result[0] if result[0] else 0

            # عدد تصفيات اليوم
            c.execute("SELECT COUNT(*) FROM filters WHERE date = ?", (today,))
            data['today_filters'] = c.fetchone()[0]

            # متوسط التصفية
            if data['today_filters'] > 0:
                data['avg_filter'] = data['today_revenue'] / data['today_filters']

            # إيرادات الشهر
            c.execute("""
                SELECT SUM(
                    CASE
                        WHEN json_extract(data, '$.totals') IS NOT NULL
                        THEN (
                            COALESCE(json_extract(data, '$.totals.bank'), 0) +
                            COALESCE(json_extract(data, '$.totals.cash'), 0) +
                            COALESCE(json_extract(data, '$.totals.credit'), 0) +
                            COALESCE(json_extract(data, '$.totals.return'), 0) -
                            COALESCE(json_extract(data, '$.totals.client'), 0)
                        )
                        ELSE 0
                    END
                ) as total_revenue
                FROM filters
                WHERE date >= ?
            """, (month_start,))

            result = c.fetchone()
            data['month_revenue'] = result[0] if result[0] else 0

            # أفضل الكاشيرين
            c.execute("""
                SELECT
                    c.name,
                    COUNT(f.id) as filter_count,
                    SUM(
                        CASE
                            WHEN json_extract(f.data, '$.totals') IS NOT NULL
                            THEN (
                                COALESCE(json_extract(f.data, '$.totals.bank'), 0) +
                                COALESCE(json_extract(f.data, '$.totals.cash'), 0) +
                                COALESCE(json_extract(f.data, '$.totals.credit'), 0) +
                                COALESCE(json_extract(f.data, '$.totals.return'), 0) -
                                COALESCE(json_extract(f.data, '$.totals.client'), 0)
                            )
                            ELSE 0
                        END
                    ) as total_revenue
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                WHERE f.date >= ? AND c.name IS NOT NULL
                GROUP BY c.name
                ORDER BY total_revenue DESC
                LIMIT 5
            """, (week_start,))

            cashiers_data = c.fetchall()
            data['top_cashiers'] = [
                {
                    'rank': i + 1,
                    'name': row[0],
                    'filters': row[1],
                    'revenue': row[2] if row[2] else 0
                }
                for i, row in enumerate(cashiers_data)
            ]

            # أفضل كاشير
            if data['top_cashiers']:
                data['best_cashier'] = data['top_cashiers'][0]['name']

            # الأنشطة الحديثة مع الرقم التسلسلي
            c.execute("""
                SELECT f.date, c.name,
                       COALESCE(f.admin_name, a.name, 'غير محدد') as admin_name,
                       f.sequence_number
                FROM filters f
                LEFT JOIN cashiers c ON f.cashier_id = c.id
                LEFT JOIN admins a ON f.admin_id = a.id
                ORDER BY f.sequence_number DESC
                LIMIT 10
            """)

            activities_data = c.fetchall()
            data['recent_activities'] = [
                {
                    'date': row[0],
                    'cashier': row[1] or 'غير محدد',
                    'admin': row[2] or 'غير محدد',
                    'sequence_number': row[3] or 'غير محدد',
                    'action': 'تصفية جديدة'
                }
                for row in activities_data
            ]

            # آخر رقم تسلسلي
            c.execute("SELECT MAX(sequence_number) FROM filters")
            last_sequence_result = c.fetchone()
            data['last_sequence'] = last_sequence_result[0] if last_sequence_result[0] is not None else 0

            # إحصائيات أسبوعية
            c.execute("""
                SELECT
                    date,
                    COUNT(*) as filter_count,
                    SUM(
                        CASE
                            WHEN json_extract(data, '$.totals') IS NOT NULL
                            THEN (
                                COALESCE(json_extract(data, '$.totals.bank'), 0) +
                                COALESCE(json_extract(data, '$.totals.cash'), 0) +
                                COALESCE(json_extract(data, '$.totals.credit'), 0) +
                                COALESCE(json_extract(data, '$.totals.return'), 0) -
                                COALESCE(json_extract(data, '$.totals.client'), 0)
                            )
                            ELSE 0
                        END
                    ) as daily_revenue
                FROM filters
                WHERE date >= ?
                GROUP BY date
                ORDER BY date DESC
                LIMIT 7
            """, (week_start,))

            weekly_data = c.fetchall()
            data['weekly_stats'] = {
                row[0]: {
                    'filters': row[1],
                    'revenue': row[2] if row[2] else 0
                }
                for row in weekly_data
            }

            # التنبيهات
            data['alerts'] = self.generate_alerts(data)

            conn.close()

        except Exception as e:
            print(f"خطأ في جمع بيانات اللوحة: {e}")

        return data

    def generate_alerts(self, data):
        """إنشاء التنبيهات"""
        alerts = []

        # تنبيه إذا لم تكن هناك تصفيات اليوم
        if data['today_filters'] == 0:
            alerts.append({
                'type': 'warning',
                'title': 'لا توجد تصفيات اليوم',
                'message': 'لم يتم إجراء أي تصفيات اليوم حتى الآن',
                'time': datetime.now().strftime('%H:%M')
            })

        # تنبيه إذا كانت الإيرادات منخفضة
        if data['today_revenue'] < 1000 and data['today_filters'] > 0:
            alerts.append({
                'type': 'info',
                'title': 'إيرادات منخفضة',
                'message': f'إيرادات اليوم {data["today_revenue"]:.0f} ريال أقل من المتوقع',
                'time': datetime.now().strftime('%H:%M')
            })

        # تنبيه إيجابي للأداء الجيد
        if data['today_revenue'] > 5000:
            alerts.append({
                'type': 'success',
                'title': 'أداء ممتاز',
                'message': f'إيرادات اليوم تجاوزت {data["today_revenue"]:.0f} ريال!',
                'time': datetime.now().strftime('%H:%M')
            })

        return alerts

    def update_dashboard_ui(self, data):
        """تحديث واجهة لوحة المعلومات"""
        try:
            # تحديث البطاقات الإحصائية
            self.today_revenue_card['value'].configure(text=f"{data['today_revenue']:,.0f} ريال")
            self.today_filters_card['value'].configure(text=str(data['today_filters']))
            self.avg_filter_card['value'].configure(text=f"{data['avg_filter']:,.0f} ريال")
            self.best_cashier_card['value'].configure(text=data['best_cashier'])
            self.month_revenue_card['value'].configure(text=f"{data['month_revenue']:,.0f} ريال")
            self.last_sequence_card['value'].configure(text=str(data['last_sequence']))

            # تحديث جدول أفضل الكاشيرين
            self.update_cashiers_table(data['top_cashiers'])

            # تحديث الإحصائيات الأسبوعية
            self.update_weekly_stats(data['weekly_stats'])

            # تحديث الأنشطة الحديثة
            self.update_recent_activities(data['recent_activities'])

            # تحديث التنبيهات
            self.update_alerts(data['alerts'])

            # تحديث حالة النظام
            self.status_label.configure(text="✅ تم تحديث البيانات بنجاح")

            # حفظ البيانات
            self.dashboard_data = data

        except Exception as e:
            print(f"خطأ في تحديث واجهة اللوحة: {e}")
            self.status_label.configure(text="❌ خطأ في تحديث الواجهة")

    def update_cashiers_table(self, cashiers_data):
        """تحديث جدول أفضل الكاشيرين"""
        # مسح البيانات الحالية
        for item in self.cashiers_tree.get_children():
            self.cashiers_tree.delete(item)

        # إضافة البيانات الجديدة
        for cashier in cashiers_data:
            self.cashiers_tree.insert("", "end", values=(
                f"#{cashier['rank']}",
                cashier['name'],
                cashier['filters'],
                f"{cashier['revenue']:,.0f} ريال"
            ))

    def update_weekly_stats(self, weekly_data):
        """تحديث الإحصائيات الأسبوعية"""
        # مسح المحتوى السابق
        for widget in self.daily_stats_frame.winfo_children():
            widget.destroy()

        if not weekly_data:
            no_data_label = ctk.CTkLabel(
                self.daily_stats_frame,
                text="لا توجد بيانات للأسبوع الحالي",
                font=("Arial", 12),
                text_color="#7f8c8d"
            )
            no_data_label.pack(pady=20)
            return

        # عرض البيانات اليومية
        for date, stats in sorted(weekly_data.items(), reverse=True):
            day_frame = ctk.CTkFrame(self.daily_stats_frame, fg_color="#ffffff", corner_radius=8)
            day_frame.pack(fill="x", pady=5, padx=10)

            date_label = ctk.CTkLabel(
                day_frame,
                text=date,
                font=("Arial", 12, "bold"),
                text_color="#2c3e50"
            )
            date_label.pack(side="left", padx=15, pady=10)

            stats_label = ctk.CTkLabel(
                day_frame,
                text=f"{stats['filters']} تصفية - {stats['revenue']:,.0f} ريال",
                font=("Arial", 11),
                text_color="#7f8c8d"
            )
            stats_label.pack(side="right", padx=15, pady=10)

    def update_recent_activities(self, activities_data):
        """تحديث الأنشطة الحديثة"""
        # مسح المحتوى السابق
        for widget in self.activities_scrollable.winfo_children():
            widget.destroy()

        if not activities_data:
            no_activities_label = ctk.CTkLabel(
                self.activities_scrollable,
                text="لا توجد أنشطة حديثة",
                font=("Arial", 12),
                text_color="#7f8c8d"
            )
            no_activities_label.pack(pady=20)
            return

        # عرض الأنشطة
        for activity in activities_data:
            activity_frame = ctk.CTkFrame(
                self.activities_scrollable,
                fg_color="#ffffff",
                corner_radius=8
            )
            activity_frame.pack(fill="x", pady=3, padx=5)

            # أيقونة النشاط
            icon_label = ctk.CTkLabel(
                activity_frame,
                text="📋",
                font=("Arial", 16)
            )
            icon_label.pack(side="left", padx=10, pady=8)

            # تفاصيل النشاط
            details_frame = ctk.CTkFrame(activity_frame, fg_color="transparent")
            details_frame.pack(side="left", fill="x", expand=True, padx=5, pady=8)

            # عرض النشاط مع الرقم التسلسلي
            sequence_text = f"رقم {activity['sequence_number']}" if activity['sequence_number'] != 'غير محدد' else ""
            action_text = f"{activity['action']} {sequence_text} - {activity['cashier']}"

            action_label = ctk.CTkLabel(
                details_frame,
                text=action_text,
                font=("Arial", 11, "bold"),
                text_color="#2c3e50"
            )
            action_label.pack(anchor="w")

            time_label = ctk.CTkLabel(
                details_frame,
                text=f"بواسطة {activity['admin']} - {activity['date']}",
                font=("Arial", 10),
                text_color="#7f8c8d"
            )
            time_label.pack(anchor="w")

    def update_alerts(self, alerts_data):
        """تحديث التنبيهات"""
        # مسح المحتوى السابق
        for widget in self.alerts_scrollable.winfo_children():
            widget.destroy()

        if not alerts_data:
            no_alerts_label = ctk.CTkLabel(
                self.alerts_scrollable,
                text="لا توجد تنبيهات",
                font=("Arial", 12),
                text_color="#7f8c8d"
            )
            no_alerts_label.pack(pady=20)
            return

        # عرض التنبيهات
        alert_colors = {
            'success': '#27ae60',
            'warning': '#f39c12',
            'info': '#3498db',
            'error': '#e74c3c'
        }

        alert_icons = {
            'success': '✅',
            'warning': '⚠️',
            'info': 'ℹ️',
            'error': '❌'
        }

        for alert in alerts_data:
            alert_frame = ctk.CTkFrame(
                self.alerts_scrollable,
                fg_color=alert_colors.get(alert['type'], '#3498db'),
                corner_radius=8
            )
            alert_frame.pack(fill="x", pady=3, padx=5)

            # أيقونة التنبيه
            icon_label = ctk.CTkLabel(
                alert_frame,
                text=alert_icons.get(alert['type'], 'ℹ️'),
                font=("Arial", 16),
                text_color="white"
            )
            icon_label.pack(side="left", padx=10, pady=8)

            # تفاصيل التنبيه
            details_frame = ctk.CTkFrame(alert_frame, fg_color="transparent")
            details_frame.pack(side="left", fill="x", expand=True, padx=5, pady=8)

            title_label = ctk.CTkLabel(
                details_frame,
                text=alert['title'],
                font=("Arial", 11, "bold"),
                text_color="white"
            )
            title_label.pack(anchor="w")

            message_label = ctk.CTkLabel(
                details_frame,
                text=alert['message'],
                font=("Arial", 10),
                text_color="white",
                wraplength=200
            )
            message_label.pack(anchor="w")

            # وقت التنبيه
            time_label = ctk.CTkLabel(
                alert_frame,
                text=alert['time'],
                font=("Arial", 10),
                text_color="white"
            )
            time_label.pack(side="right", padx=10, pady=8)

    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        if self.auto_refresh:
            self.after(self.refresh_interval * 1000, self.auto_refresh_cycle)

    def auto_refresh_cycle(self):
        """دورة التحديث التلقائي"""
        if self.auto_refresh:
            self.load_dashboard_data()
            self.after(self.refresh_interval * 1000, self.auto_refresh_cycle)

    def manual_refresh(self):
        """التحديث اليدوي"""
        self.load_dashboard_data()

    def toggle_auto_refresh(self):
        """تبديل التحديث التلقائي"""
        self.auto_refresh = not self.auto_refresh

        if self.auto_refresh:
            self.auto_refresh_btn.configure(
                text="⏸️ إيقاف التحديث التلقائي",
                fg_color="#e74c3c"
            )
            self.start_auto_refresh()
        else:
            self.auto_refresh_btn.configure(
                text="▶️ تشغيل التحديث التلقائي",
                fg_color="#27ae60"
            )

    def show_dashboard_settings(self):
        """عرض إعدادات اللوحة"""
        settings_window = DashboardSettingsWindow(self)

    def export_dashboard_report(self):
        """تصدير تقرير اللوحة"""
        if not self.dashboard_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            # إنشاء تقرير HTML
            html_content = self.generate_dashboard_report()

            # حفظ الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dashboard_report_{timestamp}.html"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            messagebox.showinfo("نجح", f"تم تصدير تقرير اللوحة:\n{filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {e}")

    def generate_dashboard_report(self):
        """إنشاء تقرير HTML للوحة المعلومات"""
        data = self.dashboard_data

        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير لوحة المعلومات التفاعلية</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}

        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}

        .stat-value {{
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }}

        .stat-label {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}

        .section {{
            padding: 30px;
            border-bottom: 1px solid #ecf0f1;
        }}

        .section-title {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        }}

        .cashiers-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}

        .cashiers-table th,
        .cashiers-table td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }}

        .cashiers-table th {{
            background: #3498db;
            color: white;
        }}

        .footer {{
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تقرير لوحة المعلومات التفاعلية</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{data['today_revenue']:,.0f}</div>
                <div class="stat-label">إيرادات اليوم (ريال)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{data['today_filters']}</div>
                <div class="stat-label">تصفيات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{data['avg_filter']:,.0f}</div>
                <div class="stat-label">متوسط التصفية (ريال)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{data['month_revenue']:,.0f}</div>
                <div class="stat-label">إيرادات الشهر (ريال)</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">🏆 أفضل الكاشيرين</div>
            <table class="cashiers-table">
                <thead>
                    <tr>
                        <th>الترتيب</th>
                        <th>اسم الكاشير</th>
                        <th>عدد التصفيات</th>
                        <th>إجمالي الإيرادات</th>
                    </tr>
                </thead>
                <tbody>
"""

        # إضافة بيانات الكاشيرين
        for cashier in data['top_cashiers']:
            html_content += f"""
                    <tr>
                        <td>#{cashier['rank']}</td>
                        <td>{cashier['name']}</td>
                        <td>{cashier['filters']}</td>
                        <td>{cashier['revenue']:,.0f} ريال</td>
                    </tr>
"""

        html_content += """
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>تطوير: محمد الكامل | نظام تصفية الكاشير | جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>
"""

        return html_content


class DashboardSettingsWindow(ctk.CTkToplevel):
    """نافذة إعدادات لوحة المعلومات"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.title("⚙️ إعدادات لوحة المعلومات")
        self.geometry("400x300")
        self.configure(bg="#f2f3f7")
        self.resizable(False, False)

        self.create_widgets()

        # جعل النافذة في المقدمة
        self.transient(parent)
        self.grab_set()

    def create_widgets(self):
        """إنشاء عناصر نافذة الإعدادات"""

        # العنوان
        title_label = ctk.CTkLabel(
            self,
            text="⚙️ إعدادات لوحة المعلومات",
            font=("Arial", 18, "bold"),
            text_color="#2c3e50"
        )
        title_label.pack(pady=20)

        # إطار الإعدادات
        settings_frame = ctk.CTkFrame(self, fg_color="#ffffff", corner_radius=15)
        settings_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # فترة التحديث التلقائي
        refresh_frame = ctk.CTkFrame(settings_frame, fg_color="transparent")
        refresh_frame.pack(fill="x", padx=20, pady=15)

        refresh_label = ctk.CTkLabel(
            refresh_frame,
            text="فترة التحديث التلقائي (ثانية):",
            font=("Arial", 12, "bold")
        )
        refresh_label.pack(anchor="w")

        self.refresh_entry = ctk.CTkEntry(
            refresh_frame,
            width=100,
            placeholder_text="30"
        )
        self.refresh_entry.pack(anchor="w", pady=5)
        self.refresh_entry.insert(0, str(self.parent.refresh_interval))

        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self, fg_color="transparent")
        buttons_frame.pack(pady=20)

        save_btn = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_settings,
            fg_color="#27ae60",
            hover_color="#229954",
            width=100
        )
        save_btn.pack(side="left", padx=10)

        cancel_btn = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.destroy,
            fg_color="#e74c3c",
            hover_color="#c0392b",
            width=100
        )
        cancel_btn.pack(side="left", padx=10)

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            new_interval = int(self.refresh_entry.get())
            if new_interval < 5:
                messagebox.showwarning("تحذير", "فترة التحديث يجب أن تكون 5 ثوانٍ على الأقل")
                return

            self.parent.refresh_interval = new_interval
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")
            self.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم صحيح لفترة التحديث")

    def handle_dashboard_event(self, event):
        """معالجة أحداث لوحة المعلومات"""
        try:
            event_type = event.get('type', '')
            message = event.get('message', '')

            print(f"📊 لوحة المعلومات تلقت حدث: {event_type} - {message}")

            if event_type == 'filter_deleted':
                self.handle_filter_deleted_event(event)
            elif event_type == 'filter_added':
                self.handle_filter_added_event(event)
            elif event_type == 'filter_updated':
                self.handle_filter_updated_event(event)
            elif event_type == 'data_changed':
                self.handle_data_changed_event(event)

            # تحديث الأنشطة الحديثة
            self.update_recent_activities_with_event(event)

        except Exception as e:
            print(f"خطأ في معالجة حدث لوحة المعلومات: {e}")

    def handle_filter_deleted_event(self, event):
        """معالجة حدث حذف تصفية"""
        try:
            filter_id = event.get('filter_id', 0)
            filter_data = event.get('filter_data', {})

            # عرض تنبيه في لوحة المعلومات
            self.show_dashboard_alert(
                f"🗑️ تم حذف التصفية رقم {filter_id}",
                f"الكاشير: {filter_data.get('cashier_name', 'غير محدد')}\n"
                f"المبلغ: {filter_data.get('total_amount', 0):,.0f} ريال",
                "warning"
            )

            # تحديث البيانات فوراً
            self.load_dashboard_data()

        except Exception as e:
            print(f"خطأ في معالجة حدث حذف التصفية: {e}")

    def handle_filter_added_event(self, event):
        """معالجة حدث إضافة تصفية"""
        try:
            filter_id = event.get('filter_id', 0)
            filter_data = event.get('filter_data', {})
            sequence_number = event.get('sequence_number', filter_id)

            # عرض تنبيه في لوحة المعلومات مع الرقم التسلسلي
            title = f"➕ تم إضافة التصفية رقم {sequence_number}"
            if sequence_number != filter_id:
                title += f" (ID: {filter_id})"

            self.show_dashboard_alert(
                title,
                f"الكاشير: {filter_data.get('cashier_name', 'غير محدد')}\n"
                f"المبلغ: {filter_data.get('total_amount', 0):,.0f} ريال",
                "success"
            )

            # تحديث البيانات فوراً
            self.load_dashboard_data()

        except Exception as e:
            print(f"خطأ في معالجة حدث إضافة التصفية: {e}")

    def handle_filter_updated_event(self, event):
        """معالجة حدث تحديث تصفية"""
        try:
            filter_id = event.get('filter_id', 0)

            # عرض تنبيه في لوحة المعلومات
            self.show_dashboard_alert(
                f"✏️ تم تحديث التصفية رقم {filter_id}",
                "تم تحديث بيانات التصفية",
                "info"
            )

            # تحديث البيانات فوراً
            self.load_dashboard_data()

        except Exception as e:
            print(f"خطأ في معالجة حدث تحديث التصفية: {e}")

    def handle_data_changed_event(self, event):
        """معالجة حدث تغيير البيانات"""
        try:
            change_type = event.get('change_type', '')
            details = event.get('details', {})

            if details.get('impact') == 'dashboard_update_required':
                # تحديث البيانات فوراً
                self.load_dashboard_data()

                # عرض رسالة تحديث
                self.status_label.configure(text="🔄 تم تحديث البيانات بسبب تغيير في النظام")

        except Exception as e:
            print(f"خطأ في معالجة حدث تغيير البيانات: {e}")

    def show_dashboard_alert(self, title, message, alert_type="info"):
        """عرض تنبيه في لوحة المعلومات"""
        try:
            # تحديد لون التنبيه حسب النوع
            colors = {
                "success": "#10b981",
                "warning": "#f59e0b",
                "error": "#ef4444",
                "info": "#3b82f6"
            }

            color = colors.get(alert_type, "#3b82f6")

            # إنشاء تنبيه مؤقت
            alert_frame = ctk.CTkFrame(self, fg_color=color, corner_radius=10)
            alert_frame.place(relx=0.02, rely=0.02, relwidth=0.3, relheight=0.15)

            title_label = ctk.CTkLabel(
                alert_frame,
                text=title,
                font=("Arial", 12, "bold"),
                text_color="white"
            )
            title_label.pack(pady=5)

            message_label = ctk.CTkLabel(
                alert_frame,
                text=message,
                font=("Arial", 10),
                text_color="white",
                wraplength=200
            )
            message_label.pack(pady=5)

            # إزالة التنبيه بعد 5 ثوان
            self.after(5000, alert_frame.destroy)

        except Exception as e:
            print(f"خطأ في عرض تنبيه لوحة المعلومات: {e}")

    def update_recent_activities_with_event(self, event):
        """تحديث الأنشطة الحديثة مع الحدث الجديد"""
        try:
            if hasattr(self, 'activities_tree'):
                # إضافة النشاط الجديد في أعلى القائمة
                timestamp = datetime.now().strftime("%H:%M:%S")
                message = event.get('message', 'نشاط جديد')
                event_type = event.get('type', 'unknown')

                # تحديد أيقونة النشاط
                icons = {
                    'filter_deleted': '🗑️',
                    'filter_added': '➕',
                    'filter_updated': '✏️',
                    'data_changed': '🔄'
                }

                icon = icons.get(event_type, '📋')

                # إدراج النشاط في أعلى القائمة
                self.activities_tree.insert("", 0, values=(
                    timestamp,
                    f"{icon} {message}",
                    "نظام"
                ))

                # الاحتفاظ بآخر 10 أنشطة فقط
                children = self.activities_tree.get_children()
                if len(children) > 10:
                    for item in children[10:]:
                        self.activities_tree.delete(item)

        except Exception as e:
            print(f"خطأ في تحديث الأنشطة الحديثة: {e}")


def show_dashboard(parent=None):
    """عرض لوحة المعلومات التفاعلية"""
    window = DashboardWindow(parent)
    # تطبيق تركيز النافذة
    if hasattr(parent, 'after'):
        parent.after(100, lambda: bring_window_to_front_dashboard(window))
    return window

def bring_window_to_front_dashboard(window):
    """جعل نافذة لوحة المعلومات تظهر في المقدمة"""
    try:
        window.deiconify()
        window.lift()
        window.attributes('-topmost', True)
        window.focus_force()
        window.wm_state('normal')
        window.tkraise()
        def remove_topmost():
            try:
                window.attributes('-topmost', False)
                window.focus_set()
            except:
                pass
        window.after(300, remove_topmost)
    except Exception as e:
        print(f"خطأ في إظهار نافذة لوحة المعلومات في المقدمة: {e}")
