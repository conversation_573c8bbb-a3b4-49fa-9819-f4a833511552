{% extends "base.html" %}

{% block title %}واجهة الهاتف - نظام تصفية الكاشير{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات خاصة بالهاتف */
    .mobile-card {
        margin-bottom: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .mobile-stats {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        text-align: center;
    }
    
    .mobile-filter-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-right: 4px solid #667eea;
    }
    
    .mobile-amount {
        font-size: 1.1em;
        font-weight: bold;
        color: #2e7d32;
    }
    
    .mobile-date {
        color: #666;
        font-size: 0.9em;
    }
    
    .mobile-cashier {
        color: #1976d2;
        font-weight: 600;
        font-size: 1.1em;
    }
    
    .mobile-btn {
        width: 100%;
        margin: 5px 0;
        border-radius: 25px;
        padding: 12px;
    }
    
    .quick-stats {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        margin: 20px 0;
    }
    
    .quick-stat-item {
        background: rgba(255,255,255,0.9);
        border-radius: 10px;
        padding: 15px;
        margin: 5px;
        text-align: center;
        flex: 1;
        min-width: 100px;
    }
    
    .floating-refresh {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    
    @media (max-width: 576px) {
        .container {
            padding: 5px;
        }
        
        .mobile-filter-item {
            margin: 5px 0;
            padding: 10px;
        }
        
        .quick-stat-item {
            margin: 2px;
            padding: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Mobile Header -->
<div class="mobile-card">
    <div class="card-body text-center">
        <h3>
            <i class="fas fa-mobile-alt text-primary"></i>
            تقارير الكاشير - الهاتف
        </h3>
        <p class="text-muted">واجهة محسنة للهواتف الذكية</p>
    </div>
</div>

<!-- Quick Stats -->
<div class="quick-stats">
    <div class="quick-stat-item">
        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
        <h4>{{ stats.total_filters or 0 }}</h4>
        <small>إجمالي التصفيات</small>
    </div>
    <div class="quick-stat-item">
        <i class="fas fa-calendar fa-2x text-success mb-2"></i>
        <h4>{{ stats.monthly_filters or 0 }}</h4>
        <small>هذا الشهر</small>
    </div>
    <div class="quick-stat-item">
        <i class="fas fa-coins fa-2x text-warning mb-2"></i>
        <h4>{{ "{:,.0f}".format(stats.total_amount or 0) }}</h4>
        <small>إجمالي المبالغ</small>
    </div>
</div>

<!-- Quick Actions -->
<div class="mobile-card">
    <div class="card-body">
        <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
        <div class="row">
            <div class="col-6">
                <button class="btn btn-primary mobile-btn" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i><br>تحديث
                </button>
            </div>
            <div class="col-6">
                <a href="/reports" class="btn btn-info mobile-btn">
                    <i class="fas fa-list"></i><br>جميع التقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Search -->
<div class="mobile-card">
    <div class="card-body">
        <h6><i class="fas fa-search"></i> بحث سريع</h6>
        <input type="text" class="form-control" id="mobile-search" 
               placeholder="ابحث بالكاشير أو التاريخ..." 
               onkeyup="mobileSearch()">
    </div>
</div>

<!-- Recent Filters -->
<div class="mobile-card">
    <div class="card-header">
        <h5><i class="fas fa-clock"></i> آخر التصفيات</h5>
    </div>
    <div class="card-body" id="mobile-filters-container">
        {% if filters %}
            {% for filter in filters %}
            <div class="mobile-filter-item" data-search="{{ (filter.cashier_name or '') + ' ' + filter.date + ' ' + (filter.admin_name or '') }}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <div class="mobile-cashier">
                            <i class="fas fa-user"></i>
                            {{ filter.cashier_name or 'غير محدد' }}
                        </div>
                        <div class="mobile-date">
                            <i class="fas fa-calendar"></i>
                            {{ filter.date }}
                            {% if filter.sequence_number %}
                            | #{{ filter.sequence_number }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-end">
                        <a href="/filter/{{ filter.id }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">بنكي</small>
                        <div class="mobile-amount">
                            {{ "{:,.0f}".format(filter.details.bank_total or 0) }}
                        </div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">نقدي</small>
                        <div class="mobile-amount">
                            {{ "{:,.0f}".format(filter.details.cash_total or 0) }}
                        </div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">المجموع</small>
                        <div class="mobile-amount text-primary">
                            {{ "{:,.0f}".format((filter.details.bank_total or 0) + (filter.details.cash_total or 0)) }}
                        </div>
                    </div>
                </div>
                
                {% if filter.admin_name %}
                <div class="mt-2 text-center">
                    <small class="text-muted">
                        <i class="fas fa-user-tie"></i>
                        المسؤول: {{ filter.admin_name }}
                    </small>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد تصفيات</h6>
                <p class="text-muted small">ابدأ بإنشاء تصفية جديدة</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Load More Button -->
{% if filters and filters|length >= 20 %}
<div class="text-center mb-4">
    <button class="btn btn-outline-primary mobile-btn" onclick="loadMore()">
        <i class="fas fa-plus"></i> تحميل المزيد
    </button>
</div>
{% endif %}

<!-- Last Update -->
<div class="mobile-card">
    <div class="card-body text-center">
        <small class="text-muted">
            <i class="fas fa-sync-alt"></i>
            آخر تحديث: <span id="last-update">{{ stats.last_update or 'غير متاح' }}</span>
        </small>
    </div>
</div>

<!-- Floating Refresh Button -->
<button class="btn btn-primary floating-refresh" onclick="refreshData()" title="تحديث">
    <i class="fas fa-sync-alt"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    
    // البحث في الهاتف
    function mobileSearch() {
        const searchTerm = document.getElementById('mobile-search').value.toLowerCase();
        const filterItems = document.querySelectorAll('.mobile-filter-item');
        
        filterItems.forEach(item => {
            const searchData = item.dataset.search.toLowerCase();
            item.style.display = searchData.includes(searchTerm) ? 'block' : 'none';
        });
    }
    
    // تحديث البيانات للهاتف
    function refreshData() {
        const container = document.getElementById('mobile-filters-container');
        showLoading(container);
        
        fetch('/api/filters?limit=20')
            .then(response => response.json())
            .then(data => {
                updateMobileFiltersDisplay(data);
                updateLastUpdateTime();
            })
            .catch(error => {
                console.error('خطأ في تحديث البيانات:', error);
                container.innerHTML = '<div class="text-center text-danger">حدث خطأ في تحديث البيانات</div>';
            });
    }
    
    // تحديث عرض التصفيات للهاتف
    function updateMobileFiltersDisplay(filters) {
        const container = document.getElementById('mobile-filters-container');
        
        if (filters.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لا توجد تصفيات</h6>
                    <p class="text-muted small">ابدأ بإنشاء تصفية جديدة</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        filters.forEach(filter => {
            const details = filter.details || {};
            const bankTotal = details.bank_total || 0;
            const cashTotal = details.cash_total || 0;
            const total = bankTotal + cashTotal;
            
            html += `
                <div class="mobile-filter-item" data-search="${(filter.cashier_name || '') + ' ' + filter.date + ' ' + (filter.admin_name || '')}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <div class="mobile-cashier">
                                <i class="fas fa-user"></i>
                                ${filter.cashier_name || 'غير محدد'}
                            </div>
                            <div class="mobile-date">
                                <i class="fas fa-calendar"></i>
                                ${filter.date}
                                ${filter.sequence_number ? `| #${filter.sequence_number}` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="/filter/${filter.id}" class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">بنكي</small>
                            <div class="mobile-amount">${formatArabicNumber(bankTotal)}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">نقدي</small>
                            <div class="mobile-amount">${formatArabicNumber(cashTotal)}</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">المجموع</small>
                            <div class="mobile-amount text-primary">${formatArabicNumber(total)}</div>
                        </div>
                    </div>
                    
                    ${filter.admin_name ? `
                        <div class="mt-2 text-center">
                            <small class="text-muted">
                                <i class="fas fa-user-tie"></i>
                                المسؤول: ${filter.admin_name}
                            </small>
                        </div>
                    ` : ''}
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // تحميل المزيد
    function loadMore() {
        currentPage++;
        const limit = 20 * currentPage;
        
        fetch(`/api/filters?limit=${limit}`)
            .then(response => response.json())
            .then(data => {
                updateMobileFiltersDisplay(data);
            })
            .catch(error => {
                console.error('خطأ في تحميل المزيد:', error);
                alert('حدث خطأ في تحميل المزيد من البيانات');
            });
    }
    
    // تحديث وقت آخر تحديث
    function updateLastUpdateTime() {
        document.getElementById('last-update').textContent = new Date().toLocaleString('ar-SA');
    }
    
    // تحسينات للهاتف
    document.addEventListener('DOMContentLoaded', function() {
        // إخفاء شريط العنوان عند التمرير لأسفل
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar');
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // التمرير لأسفل
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // التمرير لأعلى
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
        
        // إضافة تأثير اللمس للعناصر
        const filterItems = document.querySelectorAll('.mobile-filter-item');
        filterItems.forEach(item => {
            item.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            item.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
    
    // تحديث تلقائي كل دقيقة للهاتف
    setInterval(function() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(stats => {
                // تحديث الإحصائيات السريعة
                const statItems = document.querySelectorAll('.quick-stat-item h4');
                if (statItems.length >= 3) {
                    statItems[0].textContent = stats.total_filters || 0;
                    statItems[1].textContent = stats.monthly_filters || 0;
                    statItems[2].textContent = formatArabicNumber(stats.total_amount || 0);
                }
                updateLastUpdateTime();
            })
            .catch(error => console.log('تحديث تلقائي فاشل:', error));
    }, 60000); // كل دقيقة
</script>
{% endblock %}
