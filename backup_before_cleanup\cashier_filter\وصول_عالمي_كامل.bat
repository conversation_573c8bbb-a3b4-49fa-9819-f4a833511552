@echo off
chcp 65001 > nul
title الوصول العالمي الكامل v3.5.0
color 0E
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██            🌍 الوصول العالمي الكامل v3.5.0                ██
echo ██                                                            ██
echo ██  🚀 الوصول من أي مكان في العالم:                          ██
echo ██  📱 الهاتف المحمول                                        ██
echo ██  💻 أي كمبيوتر                                            ██
echo ██  🌐 أي متصفح                                              ██
echo ██  🔒 اتصال آمن ومشفر                                       ██
echo ██                                                            ██
echo ██  ⚡ إعداد سريع في دقيقتين!                               ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
echo 🔍 التحقق من متطلبات النظام...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 💡 يرجى تثبيت Python من: https://python.org/downloads
    pause
    exit /b 1
)

echo ✅ Python متاح
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    الإصدار: %PYTHON_VERSION%

REM التحقق من الاتصال بالإنترنت
echo.
echo 🌐 التحقق من الاتصال بالإنترنت...
ping -n 1 ******* > nul 2>&1
if errorlevel 1 (
    echo ❌ لا يوجد اتصال بالإنترنت!
    echo 💡 تأكد من اتصال الكمبيوتر بالإنترنت
    pause
    exit /b 1
)
echo ✅ الاتصال بالإنترنت متاح

REM التحقق من المتطلبات
echo.
echo 📦 التحقق من المتطلبات...
python -c "import requests" 2>nul
if errorlevel 1 (
    echo    تثبيت requests...
    pip install requests --quiet
)

python -c "import flask" 2>nul
if errorlevel 1 (
    echo    تثبيت flask...
    pip install flask --quiet
)

echo ✅ جميع المتطلبات متاحة

REM التحقق من ملفات الوصول العالمي
echo.
echo 📄 التحقق من ملفات الوصول العالمي...
if not exist "setup_global_access.py" (
    echo ⚠️ ملف setup_global_access.py غير موجود
    echo 💡 سيتم استخدام الطريقة البديلة
    
    REM إنشاء ملف إعداد بسيط
    echo import subprocess > temp_global_setup.py
    echo import time >> temp_global_setup.py
    echo import requests >> temp_global_setup.py
    echo. >> temp_global_setup.py
    echo print("🚀 بدء إعداد الوصول العالمي...") >> temp_global_setup.py
    echo print("💡 سيتم تشغيل خادم التقارير أولاً") >> temp_global_setup.py
    echo. >> temp_global_setup.py
    echo # تشغيل خادم التقارير في الخلفية >> temp_global_setup.py
    echo subprocess.Popen(["python", "web_server.py"]) >> temp_global_setup.py
    echo time.sleep(3) >> temp_global_setup.py
    echo. >> temp_global_setup.py
    echo print("✅ خادم التقارير يعمل على http://localhost:5000") >> temp_global_setup.py
    echo print("🌐 للوصول العالمي، استخدم خدمة مثل ngrok أو cloudflare tunnel") >> temp_global_setup.py
    echo print("💡 راجع دليل_الوصول_العالمي.md للتفاصيل") >> temp_global_setup.py
    echo input("اضغط Enter للمتابعة...") >> temp_global_setup.py
    
    set SETUP_FILE=temp_global_setup.py
) else (
    echo ✅ ملفات الوصول العالمي موجودة
    set SETUP_FILE=setup_global_access.py
)

echo.
echo 🚀 بدء إعداد الوصول العالمي...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                                                             │
echo │  🌍 الوصول العالمي - خطوات الإعداد:                       │
echo │                                                             │
echo │  1️⃣ سيتم تشغيل خادم التقارير المحلي                       │
echo │  2️⃣ سيتم إنشاء نفق آمن للإنترنت                           │
echo │  3️⃣ ستحصل على رابط عالمي للوصول                          │
echo │  4️⃣ شارك الرابط مع فريقك للوصول عن بُعد                  │
echo │                                                             │
echo │  🔒 الاتصال آمن ومشفر                                      │
echo │  📱 يعمل على جميع الأجهزة                                  │
echo │  🌐 وصول من أي مكان في العالم                             │
echo │                                                             │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM تشغيل إعداد الوصول العالمي
echo ⏳ جاري الإعداد...
echo.
python %SETUP_FILE%

REM تنظيف الملفات المؤقتة
if exist "temp_global_setup.py" del "temp_global_setup.py"

REM التحقق من حالة الخروج
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في إعداد الوصول العالمي
    echo.
    echo 🔍 خطوات استكشاف الأخطاء:
    echo    1. تأكد من اتصال الإنترنت
    echo    2. تحقق من عدم حجب Firewall للاتصالات
    echo    3. تأكد من تثبيت جميع المتطلبات
    echo    4. جرب إعادة تشغيل الكمبيوتر
    echo.
    echo 💡 طرق بديلة للوصول العالمي:
    echo    • استخدم ngrok: https://ngrok.com
    echo    • استخدم cloudflare tunnel
    echo    • استخدم خدمة VPN
    echo.
    echo 📖 راجع دليل_الوصول_العالمي.md للتفاصيل
    echo.
    pause
) else (
    echo.
    echo ✅ تم إعداد الوصول العالمي بنجاح!
    echo.
    echo 🎉 الآن يمكن الوصول للنظام من أي مكان في العالم
    echo 📱 جرب الرابط على هاتفك أو أي جهاز آخر
    echo 👥 شارك الرابط مع فريقك للوصول المشترك
    echo.
    echo 💡 نصائح مهمة:
    echo    • احتفظ بالرابط في مكان آمن
    echo    • لا تشارك الرابط مع أشخاص غير مخولين
    echo    • أغلق النفق عند عدم الحاجة إليه
    echo.
)

echo 🔄 يمكنك إعادة تشغيل الوصول العالمي بالنقر على هذا الملف
pause
