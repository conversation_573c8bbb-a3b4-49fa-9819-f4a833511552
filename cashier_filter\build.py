#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة بناء وتصدير نظام تصفية الكاشير المتكامل 2025
Build and Export Tool for Cashier Filter System 2025

تطوير: محمد الكامل
Developed by: <PERSON>
"""

import os
import sys
import subprocess
import shutil
import zipfile
import platform
from pathlib import Path
from datetime import datetime

def print_header():
    """طباعة رأس أداة البناء"""
    header = """
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🔧 أداة بناء وتصدير نظام تصفية الكاشير المتكامل 2025             ║
║       Build and Export Tool for Cashier Filter System 2025          ║
║                                                                      ║
║    الإصدار 3.0.0 - تطوير: محمد الكامل                              ║
║    Version 3.0.0 - Developed by: <PERSON>                   ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(header)

def check_pyinstaller():
    """التحقق من وجود PyInstaller"""
    print("🔍 التحقق من PyInstaller...")
    
    try:
        import PyInstaller
        print("   ✅ PyInstaller متاح")
        return True
    except ImportError:
        print("   ❌ PyInstaller غير متاح")
        print("   📦 جاري تثبيت PyInstaller...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller"
            ])
            print("   ✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("   ❌ فشل تثبيت PyInstaller")
            return False

def create_spec_file():
    """إنشاء ملف .spec لـ PyInstaller"""
    print("\n📝 إنشاء ملف التكوين...")

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# إضافة المسار الحالي
sys.path.insert(0, os.getcwd())

block_cipher = None

# البيانات المطلوبة
added_files = [
    ('db', 'db'),
    ('ui', 'ui'),
    ('utils', 'utils'),
    ('requirements.txt', '.'),
    ('README.md', '.'),
    ('INSTALL.md', '.'),
    ('USER_GUIDE.md', '.'),
]

# المكتبات المخفية
hidden_imports = [
    'customtkinter',
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'sqlite3',
    'json',
    'datetime',
    'threading',
    'hashlib',
    'webbrowser',
    'tempfile',
    'shutil',
    'pathlib',
    'os',
    'sys',
    'platform',
    'subprocess',
    'time',
    'math',
    'statistics',
    're',
    'urllib.request',
    'zipfile',
]

# المكتبات الاختيارية
optional_imports = [
    'fpdf2',
    'pandas',
    'openpyxl',
    'reportlab',
    'numpy',
    'matplotlib',
    'weasyprint',
]

# إضافة المكتبات الاختيارية إذا كانت متاحة
for module in optional_imports:
    try:
        __import__(module)
        hidden_imports.append(module)
        print(f"   ✅ إضافة مكتبة اختيارية: {module}")
    except ImportError:
        print(f"   ⚠️ تخطي مكتبة اختيارية: {module}")

a = Analysis(
    ['main.py'],
    pathex=[os.getcwd()],
    binaries=[],
    datas=added_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib.tests', 'numpy.tests', 'pandas.tests'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CashierFilterSystem2025',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''

    with open("cashier_system.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)

    print("   ✅ تم إنشاء ملف التكوين: cashier_system.spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("\n🔨 بناء الملف التنفيذي...")
    print("   ⏳ هذا قد يستغرق عدة دقائق...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "cashier_system.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ تم بناء الملف التنفيذي بنجاح")
            return True
        else:
            print("   ❌ فشل في بناء الملف التنفيذي")
            print(f"   خطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في البناء: {e}")
        return False

def create_portable_package():
    """إنشاء حزمة محمولة"""
    print("\n📦 إنشاء حزمة محمولة...")

    # إنشاء مجلد الحزمة
    package_name = f"CashierFilterSystem2025_v3.0.0_{platform.system()}"
    package_dir = Path(package_name)

    if package_dir.exists():
        shutil.rmtree(package_dir)

    package_dir.mkdir()

    try:
        # نسخ الملف التنفيذي
        exe_name = "CashierFilterSystem2025.exe" if platform.system() == "Windows" else "CashierFilterSystem2025"
        exe_path = Path("dist") / exe_name

        if exe_path.exists():
            shutil.copy2(exe_path, package_dir / exe_name)
            print(f"   ✅ نسخ الملف التنفيذي: {exe_name}")
        else:
            print(f"   ❌ الملف التنفيذي غير موجود: {exe_path}")
            return False
        
        # نسخ الملفات المطلوبة
        files_to_copy = [
            "README.md",
            "requirements.txt",
            "install.py"
        ]
        
        for file_name in files_to_copy:
            if Path(file_name).exists():
                shutil.copy2(file_name, package_dir / file_name)
                print(f"   ✅ نسخ ملف: {file_name}")
        
        # نسخ المجلدات المطلوبة
        dirs_to_copy = ["db", "ui", "utils"]
        
        for dir_name in dirs_to_copy:
            if Path(dir_name).exists():
                shutil.copytree(dir_name, package_dir / dir_name)
                print(f"   ✅ نسخ مجلد: {dir_name}")
        
        # إنشاء ملف تشغيل مبسط
        if platform.system() == "Windows":
            run_script = f"""@echo off
chcp 65001 > nul
title نظام تصفية الكاشير المتكامل 2025
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║
echo ║    تطوير: محمد الكامل - الإصدار 3.0.0                              ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 جاري تشغيل النظام...
echo.
"{exe_name}"
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo.
)
pause
"""
            with open(package_dir / "تشغيل_النظام.bat", "w", encoding="utf-8") as f:
                f.write(run_script)
        
        else:
            run_script = f"""#!/bin/bash
clear
echo "╔══════════════════════════════════════════════════════════════════════╗"
echo "║    🏪 نظام تصفية الكاشير المتكامل 2025                              ║"
echo "║    تطوير: محمد الكامل - الإصدار 3.0.0                              ║"
echo "╚══════════════════════════════════════════════════════════════════════╝"
echo ""
echo "🚀 جاري تشغيل النظام..."
echo ""

cd "$(dirname "$0")"
./{exe_name}

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ حدث خطأ في تشغيل النظام"
    echo ""
fi

read -p "اضغط Enter للمتابعة..."
"""
            script_path = package_dir / "تشغيل_النظام.sh"
            with open(script_path, "w", encoding="utf-8") as f:
                f.write(run_script)
            os.chmod(script_path, 0o755)
        
        print("   ✅ تم إنشاء ملف التشغيل")
        
        # إنشاء ملف معلومات
        info_content = f"""# نظام تصفية الكاشير المتكامل 2025

## معلومات الحزمة
- الإصدار: 3.0.0
- تاريخ البناء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- نظام التشغيل: {platform.system()} {platform.release()}
- المطور: محمد الكامل
- البريد الإلكتروني: <EMAIL>

## طريقة التشغيل
### Windows:
- النقر المزدوج على: تشغيل_النظام.bat
- أو النقر المزدوج على: {exe_name}

### Linux/Mac:
- تشغيل: ./تشغيل_النظام.sh
- أو تشغيل: ./{exe_name}

## بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: 123456

## الدعم الفني
للدعم الفني والاستفسارات:
البريد الإلكتروني: <EMAIL>

© 2025 محمد الكامل - جميع الحقوق محفوظة
"""
        
        with open(package_dir / "معلومات_الحزمة.txt", "w", encoding="utf-8") as f:
            f.write(info_content)
        
        print("   ✅ تم إنشاء ملف المعلومات")
        print(f"   📁 مجلد الحزمة: {package_dir}")
        
        return package_dir
        
    except Exception as e:
        print(f"   ❌ فشل في إنشاء الحزمة: {e}")
        return None

def create_zip_archive(package_dir):
    """إنشاء أرشيف مضغوط"""
    print("\n🗜️ إنشاء أرشيف مضغوط...")
    
    try:
        zip_name = f"{package_dir.name}.zip"
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(package_dir.parent)
                    zipf.write(file_path, arc_path)
        
        zip_size = Path(zip_name).stat().st_size / (1024 * 1024)  # MB
        print(f"   ✅ تم إنشاء الأرشيف: {zip_name}")
        print(f"   📏 حجم الأرشيف: {zip_size:.1f} MB")
        
        return zip_name
        
    except Exception as e:
        print(f"   ❌ فشل في إنشاء الأرشيف: {e}")
        return None

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("\n🧹 تنظيف الملفات المؤقتة...")
    
    temp_items = [
        "build",
        "dist", 
        "cashier_system.spec",
        "__pycache__"
    ]
    
    for item in temp_items:
        item_path = Path(item)
        try:
            if item_path.is_file():
                item_path.unlink()
                print(f"   ✅ حذف ملف: {item}")
            elif item_path.is_dir():
                shutil.rmtree(item_path)
                print(f"   ✅ حذف مجلد: {item}")
        except Exception as e:
            print(f"   ⚠️ تعذر حذف {item}: {e}")

def print_completion(zip_name):
    """طباعة رسالة الإكمال"""
    completion = f"""
╔══════════════════════════════════════════════════════════════════════╗
║                                                                      ║
║    🎉 تم بناء وتصدير النظام بنجاح!                                  ║
║                                                                      ║
║    📦 الملف المُصدَّر: {zip_name:<45} ║
║                                                                      ║
║    📋 محتويات الحزمة:                                                ║
║    • الملف التنفيذي (لا يحتاج Python)                               ║
║    • ملفات التشغيل المبسطة                                          ║
║    • قاعدة البيانات والإعدادات                                      ║
║    • دليل الاستخدام والمعلومات                                      ║
║                                                                      ║
║    🚀 طريقة التثبيت:                                                ║
║    1. فك ضغط الملف في أي مكان                                       ║
║    2. تشغيل ملف التشغيل المناسب                                     ║
║    3. لا يحتاج تثبيت Python أو مكتبات إضافية                       ║
║                                                                      ║
║    💬 الدعم الفني: <EMAIL>                            ║
║                                                                      ║
╚══════════════════════════════════════════════════════════════════════╝
"""
    print(completion)

def main():
    """الدالة الرئيسية لأداة البناء"""
    print_header()
    
    # التحقق من PyInstaller
    if not check_pyinstaller():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء ملف التكوين
    create_spec_file()
    
    # بناء الملف التنفيذي
    if not build_executable():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء الحزمة المحمولة
    package_dir = create_portable_package()
    if not package_dir:
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إنشاء الأرشيف المضغوط
    zip_name = create_zip_archive(package_dir)
    if not zip_name:
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    # طباعة رسالة الإكمال
    print_completion(zip_name)
    
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
